import { Response } from "express";
import { json } from "body-parser";
import RequestW<PERSON>RawBody from "./requestWithRawBody.interface";

function rawBodyMiddleware() {
    return json({
        verify: (request: RequestWithRawBody, response: Response, buffer: Buffer) => {
            if (request.url === "/api/v1/Subscription/processSubscription" && Buffer.isBuffer(buffer)) {
                request.rawBody = Buffer.from(buffer);
            }
            return true;
        },
    });
}

export default rawBodyMiddleware;
