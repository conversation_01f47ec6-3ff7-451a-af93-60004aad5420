import { ApiProperty } from "@nestjs/swagger";
import { IsString, IsNotEmpty, Matches, <PERSON>Length, <PERSON><PERSON>ength } from "class-validator";

export class UpdatePasswordDto {
    @ApiProperty({ description: "Old Password" })
    @IsString()
    @IsNotEmpty()
    oldPassword: string;

    @ApiProperty({ description: "New Password" })
    @IsNotEmpty()
    @MinLength(8, { message: "Password must contain minimum of 8 characters" })
    @MaxLength(32, { message: "Password must contain maximum of 32 characters" })
    @Matches(/(?:(?=.*\d)|(?=.*\W+))(?![.\n])(?=.*[A-Z])(?=.*[a-z]).*$/, {
        message: "Weak Password",
    })
    newPassword: string;

    @ApiProperty({ description: "Confirm New Password" })
    @IsString()
    @IsNotEmpty()
    confirmNewPassword: string;
}
