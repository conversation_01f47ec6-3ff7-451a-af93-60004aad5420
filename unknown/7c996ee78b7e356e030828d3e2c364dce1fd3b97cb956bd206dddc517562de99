import { Modu<PERSON> } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { PositionModule } from "src/position/position.module";
import { RoleModule } from "src/role/role.module";
import { CityController } from "./city.controller";
import { CityService } from "./city.service";
import { CitySchema } from "./schema/city.schema";

@Module({
    imports: [MongooseModule.forFeature([{ name: "City", schema: CitySchema }]),
    //  PositionModule, RoleModule
    ],
    providers: [CityService],
    controllers: [CityController],
    exports: [CityService],
})
export class CityModule {}
