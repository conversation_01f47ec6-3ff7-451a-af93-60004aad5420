import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsNotEmpty, IsOptional, IsUUID, IsString, IsNumber } from "class-validator";

export class CreateMarketingChannelDto {
    @ApiProperty({ description: "name", required: true })
    @IsString()
    @IsNotEmpty()
    name: string;

    @ApiPropertyOptional({ description: "description" })
    @IsString()
    @IsOptional()
    description: string;

    @ApiPropertyOptional({ description: "order" })
    @IsNumber()
    @IsOptional()
    order?: number;

    @ApiProperty({ description: "Create By", required: true })
    @IsUUID()
    @IsNotEmpty()
    createdBy: string;
}
