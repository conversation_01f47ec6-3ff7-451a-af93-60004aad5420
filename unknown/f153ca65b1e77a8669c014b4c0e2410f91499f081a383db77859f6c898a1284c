import { createParamDecorator, ExecutionContext } from "@nestjs/common";
import { JwtAdminPayload, JwtUserPayload } from "../interface/auth.interface";
// import { User } from "../../user/entities/user.entity";
import { applyDecorators, UseGuards } from "@nestjs/common";
import { CombinedAuthGuard } from "../guards/auth.guard";

/**
 * Decorator
 * Returns the current logged in user data
 */
export const GetUser = createParamDecorator(
    (data: unknown, ctx: ExecutionContext): JwtAdminPayload | JwtUserPayload => {
        const req = ctx.switchToHttp().getRequest();
        return req.user;
    },
);

export function Auth() {
    return applyDecorators(UseGuards(CombinedAuthGuard));
}
