// src/contract/dto/create-contract.dto.ts

import {
    IsString,
    IsArray,
    IsOptional,
    IsUUID,
    IsNumber,
    IsNotEmpty,
    IsBoolean,
    IsObject,
} from "class-validator";
import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";

class UpdateDisplayDto {
    @ApiPropertyOptional({ description: "Unique identifier for the group" })
    @IsOptional()
    @IsUUID()
    groupId?: string;

    @ApiPropertyOptional({ description: "List of task IDs to be removed" })
    @IsOptional()
    @IsArray()
    removeTasks?: string[];

    @ApiProperty({ description: "Text description for the display" })
    @IsOptional()
    @IsString()
    text: string;

    @ApiPropertyOptional({ description: "Order of the displaye" })
    @IsOptional()
    @IsNumber()
    order?: number;
}

export class UpdateSectionDto {
    @ApiProperty({ description: "Title of the section" })
    @IsOptional()
    @IsString()
    title: string;

    @ApiPropertyOptional({ description: "Displays in the section", type: [UpdateDisplayDto] })
    @IsOptional()
    @IsArray()
    display?: UpdateDisplayDto[];
}

export class UpdateContractDto {
    @ApiPropertyOptional({ description: "The name of contract" })
    @IsNotEmpty()
    @IsString()
    contractName: string;

    @ApiPropertyOptional({ description: "The type of project" })
    @IsOptional()
    @IsUUID()
    projectType?: string;

    @ApiProperty({ description: "Default contract or not" })
    @IsOptional()
    @IsBoolean()
    isDefault?: boolean;

    @ApiPropertyOptional({ description: "Sections of the contract", type: [UpdateSectionDto] })
    @IsArray()
    @IsOptional()
    sections?: UpdateSectionDto[];

    @ApiPropertyOptional({
        description: "Fine print key-value pairs where keys are strings and values are UUIDs",
    })
    @IsOptional()
    @IsObject()
    finePrint?: Record<string, string>;
}

export class SectionOrders {
    @ApiPropertyOptional({ description: "The type of project" })
    @IsOptional()
    sectionId?: string;

    @ApiPropertyOptional({ description: "Order of the displaye" })
    @IsOptional()
    @IsNumber()
    order?: number;
}

export class UpdateSectionOrderDto {
    @ApiPropertyOptional({ type: [SectionOrders] })
    @IsNotEmpty()
    @IsArray()
    sections: SectionOrders[];
}
