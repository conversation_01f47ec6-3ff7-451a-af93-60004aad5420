import {
    Injectable,
    Logger,
    NotFoundException,
    UnauthorizedException,
    BadRequestException,
    InternalServerErrorException,
    ConflictException,
    HttpException,
} from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";
import { argon2hash, argon2verify } from "src/auth/agron2/argon2";
import { Admin, AdminDocument } from "./schema/admin.schema";
import { ConfigService } from "@nestjs/config";
import { CreateAdminDto } from "./dto/create-admin.dto";
import { SigninDto } from "src/auth/dto/signin.dto";
import { JwtService, JwtSignOptions } from "@nestjs/jwt";
import { AuthService } from "src/auth/auth.service";
import HttpResponse from "src/shared/http/response/response.http";
import OkResponse from "src/shared/http/response/ok.http";
import { UpdateExtraUserAmount } from "./dto/update-admin.dto";
import { AdminRoleEnum } from "src/company/enum/role.enum";
import { createHmac } from "crypto";

@Injectable()
export class AdminService {
    private readonly logger = new Logger("ADMIN");
    private _FR_HOST: string;
    private _adminEmail: string;
    private _hash_key: string;

    public get adminEmail(): string {
        return this._adminEmail;
    }

    public set adminEmail(value: string) {
        this._adminEmail = value;
    }

    /**
     *  returns the frontend host URL.
     */
    public get FR_HOST(): string {
        return this._FR_HOST;
    }

    /**
     *  used to set the frontend host URL.
     *  @param value the URL to the frontend host.
     */
    public set FR_HOST(value: string) {
        this._FR_HOST = value;
    }
    constructor(
        @InjectModel("Admin") private readonly adminModel: Model<AdminDocument>,
        private readonly configService: ConfigService,
        private readonly authService: AuthService,
        private readonly jwtService: JwtService, // private readonly userRepository: UserRepository, // private readonly addressRepository: AddressRepository, // private readonly mailService: MailService, // private readonly authService: AuthService, // @Inject(CACHE_MANAGER) private cacheManager: Cache,
    ) {
        this.FR_HOST = configService.get<string>(`FR_BASE_URL`);
        this.adminEmail = this.configService.get<string>("ADMIN_EMAIL");
        this._hash_key = this.configService.get<string>("HASH_KEY");
    }

    // async onModuleInit(): Promise<void> {
    //     const isAdminPresent = await this.adminModel.findOne({
    //         email: this.adminEmail,
    //         role: AdminRoleEnum.SuperAdmin,
    //     });

    //     if (!isAdminPresent) {
    //         const createAdminDto: CreateAdminDto = {
    //             email: this.adminEmail,
    //             role: AdminRoleEnum.SuperAdmin,
    //             password: this.configService.get<string>("ADMIN_PASSWORD"),
    //             extraUserChargeMonthly: 10,
    //             extraUserChargeYearly: 8,
    //             baseUserCount: 5,
    //         };

    //         // Creating Admin which App initializing
    //         await this.createAdmin(createAdminDto);
    //     }
    // }

    // Create a new admin with hashed password and additional data for SuperAdmin
    async createAdmin(createAdminDto: CreateAdminDto) {
        let { password } = createAdminDto;

        password = await argon2hash(password);

        try {
            let admin = new Admin();
            admin.email = createAdminDto.email.trim().toLowerCase();
            admin.password = password;

            // Additional fields for SuperAdmin
            if (createAdminDto.role === AdminRoleEnum.SuperAdmin) {
                admin.baseUserCount = createAdminDto.baseUserCount;
                admin.extraUserChargeMonthly = createAdminDto.extraUserChargeMonthly;
                admin.extraUserChargeYearly = createAdminDto.extraUserChargeYearly;
            }

            admin = await this.adminModel.create(admin);
            return new OkResponse({ message: "Admin added successfully" });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    // Get the list of active admins (excluding SuperAdmin)
    async adminList() {
        try {
            const admins = await this.adminModel.find(
                { role: AdminRoleEnum.Admin },
                { password: 0 }, // Exclude password from response
            );
            return new OkResponse({ admins });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    // Restore a deleted admin
    async restoreAdmin(adminId: string) {
        try {
            const adminData = await this.adminModel.findOne({ _id: adminId, role: AdminRoleEnum.Admin });
            if (!adminData.deleted) throw new BadRequestException("This admin is already active");

            await this.adminModel.updateOne({ _id: adminId, role: AdminRoleEnum.Admin }, { deleted: false });
            return new OkResponse({ message: "Admin restored successfully" });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    // Soft delete an admin (mark as deleted)
    async deleteAdmin(adminId: string) {
        try {
            const adminData = await this.adminModel.findOne({ _id: adminId, role: AdminRoleEnum.Admin });
            if (adminData.deleted) throw new BadRequestException("This admin is already deleted");

            await this.adminModel.updateOne({ _id: adminId, role: AdminRoleEnum.Admin }, { deleted: true });
            return new OkResponse({ message: "Admin deleted successfully" });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    // Permanently delete an admin
    async permanentDelete(adminId: string) {
        try {
            const adminData = await this.adminModel.findOne({ _id: adminId, role: AdminRoleEnum.Admin });
            if (!adminData.deleted) throw new BadRequestException("This admin is already active");

            await this.adminModel.deleteOne({ _id: adminId, role: AdminRoleEnum.Admin });
            return new OkResponse({ message: "Admin deleted permanently" });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     * used to Login Admin
     * @param loginAdminDto object containing email and phone of a user.
     * @returns if authenticated it returns user object otherwise returns null
     */
    async adminLogin(signinAdminDto: SigninDto) {
        const { email, password } = signinAdminDto;

        const admin = await this.adminModel
            .findOne({ email, deleted: false })
            .select("email _id password role");

        if (!admin) throw new NotFoundException("Admin Not Found!");

        if (!(await argon2verify(admin.password, password))) {
            throw new UnauthorizedException("Invalid Email or Password");
        }

        const token = await this.signToken({ _id: admin._id, email: admin.email, role: admin.role });

        return {
            admin: {
                _id: admin._id,
                email: admin.email,
                role: admin.role,
            },
            token,
        };
    }

    async getAdminById(_id: string) {
        try {
            const profile = await this.adminModel.findOne({ _id }).select("_id email apiKey");
            return new OkResponse({ profile });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     * used to Logout Admin
     * @returns returns null token
     */
    async signout(): Promise<HttpResponse> {
        try {
            return new OkResponse({ user: null, access_token: null });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     * used for signing a JWT token with admin id as payload.
     * @param admin http request object containing admin details provided by google.
     * @returns signed token which is used for authentication.
     */

    async signToken(payload: string | object, options?: JwtSignOptions): Promise<string> {
        return this.jwtService.sign(payload, options);
    }

    async getExtraUserAmount() {
        try {
            const extraAmount = await this.adminModel
                .findOne({ role: AdminRoleEnum.SuperAdmin })
                .select("extraUserChargeMonthly extraUserChargeYearly baseUserCount");
            return new OkResponse({ extraAmount });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updateExtraUserAmount(updateExtraUserAmount: UpdateExtraUserAmount) {
        try {
            const { extraUserChargeMonthly, extraUserChargeYearly, baseUserCount } = updateExtraUserAmount;
            const result = await this.adminModel.updateOne(
                { role: AdminRoleEnum.SuperAdmin },
                {
                    $set: {
                        extraUserChargeMonthly,
                        extraUserChargeYearly,
                        baseUserCount,
                    },
                },
            );

            if (result.modifiedCount === 0) throw new BadRequestException("Failed to update changes!");

            return new OkResponse({ message: "Amount updated" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     * Generates a new API key for the SuperAdmin by hashing the provided user secret with the email and a hash key.
     * The new API key is then stored in the database and returned.
     * @param userSecret The user secret that should be used to generate the new API key.
     * @returns The newly generated API key.
     * @throws {NotFoundException} If the SuperAdmin is not found.
     * @throws {InternalServerErrorException} If there is an error while creating the new API key.
     */
    async genrateAndUpdateApiKey(userSecret: string) {
        const admin = await this.adminModel.findOne({
            role: AdminRoleEnum.SuperAdmin,
        });
        if (!admin) throw new NotFoundException("Admin not found");

        //creating new api key
        const key = await this.randomKeyGenerator(admin.email, userSecret, this._hash_key);
        const apiKey = this.genHash(key);

        try {
            const { modifiedCount } = await this.adminModel.updateOne(
                { role: AdminRoleEnum.SuperAdmin },
                { $set: { apiKey } },
            );

            if (!modifiedCount) throw new BadRequestException("Failed to update new api key");

            return new OkResponse({ apiKey });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            } else throw new InternalServerErrorException("Error while creating ApiKey,Please try again");
        }
    }

    async validateApiKey(key: string): Promise<boolean> {
        const apiKey = await this.adminModel.exists({ role: AdminRoleEnum.SuperAdmin, apiKey: key });
        if (!apiKey) {
            throw new NotFoundException("Invalid or inactive API key");
        }
        return true;
    }

    /**
     * hash the data and generate the hash with hash-key
     * @param data string type data that need to be encrypted/hash
     * @returns hash of data
     */
    genHash(data: any) {
        if (typeof data === "object") data = JSON.stringify(data);
        const hash = createHmac("sha3-256", this._hash_key).update(data, "utf-8").digest("hex");
        return hash;
    }

    /**
     * This will generate a key using email id and api type
     * @param email email id of user
     * @param apiType for test or live apis
     * @returns api key
     */
    private async randomKeyGenerator(
        email: string,
        userSecret: string,
        adminSecret: string,
    ): Promise<string> {
        const data = email.split(".");
        const text = data[0].split("@");
        const date = Date.now().toString();

        const keyOne = Buffer.from(text[1] + userSecret + date).toString("hex");
        const keyTwo = Buffer.from(text[0] + adminSecret).toString("hex");

        return keyOne + keyTwo;
    }
}
