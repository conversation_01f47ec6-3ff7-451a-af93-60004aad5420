import { Schema, SchemaFactory, Prop } from "@nestjs/mongoose";
import { Document, Schema as MongooseSchema } from "mongoose";
import { UUIDProp } from "src/shared/decorator/uuid.decorator";
import { randomUUID } from "crypto";

export type FormDocument = Form & Document;

@Schema({ timestamps: true, collection: "Forms", strict: false })
export class Form {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @Prop({ type: String, required: false, default: null })
    oppId?: string;

    @UUIDProp()
    companyId: string;

    @UUIDProp() // id of form builder from company settings
    builderFormId: string;

    @UUIDProp() // id of media
    mediaId: string;

    @Prop({ type: String })
    mediaUrl: string;

    @UUIDProp()
    createdBy: string;

    @Prop({ type: Date, default: Date.now })
    submittedAt: Date;

    @Prop({ type: String })
    name: string;

    @Prop({ type: String })
    locationImage: string;

    @Prop({ type: Boolean, default: false })
    deleted: boolean;

    @Prop({
        type: Array,
        default: [],
    })
    fields: any[];

    @Prop()
    createdAt?: Date;

    @Prop()
    modifiedAt?: Date;
}

export const FormSchema = SchemaFactory.createForClass(Form);

// Indexes for performance
FormSchema.index({ companyId: 1, oppId: 1 });
FormSchema.index({ companyId: 1, createdBy: 1 });
