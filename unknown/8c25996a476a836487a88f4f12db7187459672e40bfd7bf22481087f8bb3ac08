import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsEnum, IsNotEmpty, IsNumber, IsOptional, IsUUID } from "class-validator";
import { UserRolesEnum } from "src/company/enum/role.enum";
import { MemberDto } from "src/shared/dto/member.dto";

export class UpdateMemberRoleDto extends MemberDto {
    @ApiPropertyOptional({ description: "Id", required: false })
    @IsUUID()
    @IsOptional()
    _id: string;

    @ApiProperty({ description: "Role", required: true })
    @IsNumber()
    @IsNotEmpty()
    @IsEnum(UserRolesEnum)
    role: UserRolesEnum;

    @ApiProperty({ description: "created by", required: true })
    @IsUUID()
    @IsNotEmpty()
    createdBy: string;

    @ApiPropertyOptional({ description: "Current Member", required: false })
    @IsUUID()
    @IsOptional()
    currentMember?: string; // Optional field
}
