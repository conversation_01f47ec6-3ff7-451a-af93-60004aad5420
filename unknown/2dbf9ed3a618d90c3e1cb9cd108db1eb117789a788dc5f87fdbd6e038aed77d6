import { Schema, SchemaFactory, Prop } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import mongoose, { Document } from "mongoose";
import { UUIDProp } from "src/shared/decorator/uuid.decorator";

export type OptionsDocument = Options & Document;

@Schema({ timestamps: true, id: false, strict: true, collection: "Options" })
export class Options {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @UUIDProp()
    companyId: string;

    @Prop({ required: true })
    type: string;

    @Prop({ required: true })
    name: string;

    @Prop({ required: false })
    description: string;

    @Prop({ required: false })
    order: number;

    @Prop({ default: true })
    active: boolean;

    @Prop({ required: false })
    upsell: number;

    @Prop({ required: false })
    selectedGroups?: any[];

    @Prop({ required: false })
    packagesId?: any[];

    @Prop({ required: false, type: mongoose.Schema.Types.Mixed })
    taskArray?: any;

    @Prop({ default: false })
    deleted: boolean;

    @Prop({ default: false })
    useMinPrice: boolean;

    @Prop({ required: false })
    minPrice: number;

    @Prop()
    createdAt?: Date;

    @Prop()
    modifiedAt?: Date;
}

export const OptionsSchema = SchemaFactory.createForClass(Options);
