import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import {
    IsString,
    IsNotEmpty,
    MaxLength,
    <PERSON><PERSON>ength,
    <PERSON>UUID,
    IsBoolean,
    IsOptional,
    ValidateNested,
    IsArray,
    IsEnum,
} from "class-validator";
import { Type } from "class-transformer";
import { PermissionsEnum } from "src/shared/enum/permission.enum";

class CrudDto {
    @ApiProperty({ description: "Read permission", required: true })
    @IsBoolean()
    @IsNotEmpty()
    read: boolean;

    @ApiProperty({ description: "Write permission", required: true })
    @IsBoolean()
    @IsNotEmpty()
    write: boolean;

    // @ApiProperty({ description: "Update permission", required: true })
    // @IsBoolean()
    // @IsNotEmpty()
    // update: boolean;

    // @ApiProperty({ description: "Delete permission", required: true })
    // @IsBoolean()
    // @IsNotEmpty()
    // delete: boolean;
}

class ResourceDto {
    @ApiProperty({ description: "Resource name", required: true })
    @IsString()
    @IsNotEmpty()
    name: string;

    @ApiProperty({ description: "Permissions enum value", required: true, enum: PermissionsEnum })
    @IsNotEmpty()
    @IsEnum(PermissionsEnum)
    permissions: PermissionsEnum;

    @ApiProperty({ description: "CRUD permissions", required: true })
    @ValidateNested()
    @Type(() => CrudDto)
    crud: CrudDto;
}

class PermissionDto {
    @ApiProperty({ description: "Category name", required: true })
    @IsString()
    @IsNotEmpty()
    category: string;

    @ApiProperty({ description: "Resources within the category", required: true, type: [ResourceDto] })
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => ResourceDto)
    resources: ResourceDto[];
}

export class CreatePositionDto {
    @ApiProperty({ description: "Position Name", required: true })
    @IsString()
    @MinLength(2)
    @MaxLength(30)
    @IsNotEmpty()
    position: string;

    @ApiPropertyOptional({ description: "Description", required: false })
    @IsOptional()
    @IsString()
    description?: string;

    // @ApiProperty({ description: "Company ID", required: true })
    // @IsUUID()
    // @IsNotEmpty()
    // companyId: string;

    @ApiProperty({ description: "Created By", required: true })
    @IsUUID()
    @IsNotEmpty()
    createdBy: string;

    @ApiProperty({ description: "Position Permissions", required: true, type: [PermissionDto] })
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => PermissionDto)
    permissions: PermissionDto[];

    @ApiPropertyOptional({ description: "Symbol for the position", required: false })
    @IsOptional()
    @IsString()
    symbol?: string;

    // @ApiPropertyOptional({ description: "Deletable status", required: false, default: true })
    // @IsBoolean()
    // @IsOptional()
    // deletable?: boolean;
}
