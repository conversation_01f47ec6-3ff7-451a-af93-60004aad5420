extends base

block content
  // HEADER
  // Set text color and font family ("sans-serif" or "Georgia, serif")
  tr
    td.header(align="center", valign="top", style="\
                        -webkit-font-smoothing: antialiased;\
                        text-size-adjust: 100%;\
                        -ms-text-size-adjust: 100%;\
                        -webkit-text-size-adjust: 100%;\
                        mso-table-lspace: 0pt;\
                        mso-table-rspace: 0pt;\
                        border-spacing: 0;\
                        margin: 0;\
                        padding: 0;\
                        padding-left: 6.25%;\
                        padding-right: 6.25%;\
                        width: 87.5%;\
                        font-size: 24px;\
                        font-weight: bold;\
                        line-height: 130%;\
                        padding-top: 25px;\
                        color: #28a745;\
                        font-family: sans-serif;\
                        border-collapse: collapse;\
                    ", width="87.5%")= header
   
  // PARAGRAPH
  // Set text color and font family ("sans-serif" or "Georgia, serif"). Duplicate all text styles in links, including line-height
  tr
    td.paragraph(align="center", valign="top", style="\
                        -webkit-font-smoothing: antialiased;\
                        text-size-adjust: 100%;\
                        -ms-text-size-adjust: 100%;\
                        -webkit-text-size-adjust: 100%;\
                        mso-table-lspace: 0pt;\
                        mso-table-rspace: 0pt;\
                        border-spacing: 0;\
                        margin: 0;\
                        padding: 0;\
                        padding-left: 6.25%;\
                        padding-right: 6.25%;\
                        width: 87.5%;\
                        font-size: 17px;\
                        font-weight: 400;\
                        line-height: 160%;\
                        padding-top: 25px;\
                        color: #000000;\
                        font-family: sans-serif;\
                        border-collapse: collapse;\
                    ", width="87.5%")

      p #{data}

  tr  
    td.paragraph(align="center", valign="middle", style="\
                        -webkit-font-smoothing: antialiased;\
                        text-size-adjust: 100%;\
                        -ms-text-size-adjust: 100%;\
                        -webkit-text-size-adjust: 100%;\
                        mso-table-lspace: 0pt;\
                        mso-table-rspace: 0pt;\
                        border-spacing: 0;\
                        margin: 0;\
                        padding: 0;\
                        padding-left: 6.25%;\
                        padding-right: 6.25%;\
                        width: 87.5%;\
                        font-size: 10px;\
                        font-weight: 400;\
                        line-height: 160%;\
                        padding-top: 25px;\
                        color: #000000;\
                        font-family: sans-serif;\
                        border-collapse: collapse;\
                        overflow-wrap: break-word;\
                    ", width="87.5%")
      p Disclaimer: This email has been generated automatically. Please do not reply to it.

  //- tr  
  //-   td.paragraph(align="left", valign="middle", style="\
  //-                       -webkit-font-smoothing: antialiased;\
  //-                       text-size-adjust: 100%;\
  //-                       -ms-text-size-adjust: 100%;\
  //-                       -webkit-text-size-adjust: 100%;\
  //-                       mso-table-lspace: 0pt;\
  //-                       mso-table-rspace: 0pt;\
  //-                       border-spacing: 0;\
  //-                       margin: 0;\
  //-                       padding: 0;\
  //-                       padding-left: 6.25%;\
  //-                       padding-right: 6.25%;\
  //-                       width: 87.5%;\
  //-                       font-size: 15px;\
  //-                       font-weight: 400;\
  //-                       line-height: 160%;\
  //-                       padding-top: 25px;\
  //-                       color: #000000;\
  //-                       font-family: sans-serif;\
  //-                       border-collapse: collapse;\
  //-                       overflow-wrap: break-word;\
  //-                   ", width="87.5%")
  //-     p #{footer}
  //-     p Best,
  //-     p - The Piece Work Pro team

