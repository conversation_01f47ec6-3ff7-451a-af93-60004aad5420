import { Module } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { CompanyModule } from "src/company/company.module";
import { MemberSchema } from "src/company/schema/member.schema";
import { DailyLogSchema } from "src/daily-log/schema/daily-log.schema";
import { PieceWorkSchema } from "src/piece-work/schema/piece-work.schema";
import { PositionModule } from "src/position/position.module";
import { RoleModule } from "src/role/role.module";
import { TimeCardSchema } from "src/time-card/schema/time-card.schema";
import { UserModule } from "src/user/user.module";
import { CrewController } from "./crew.controller";
import { CrewService } from "./crew.service";
import { CrewSchema } from "./schema/crew-management.schema";
import { CrewMemberSchema } from "./schema/crew-member.schema";
import { CompanySettingSchema } from "src/company/schema/company-setting.schema";
import { OpportunitySchema } from "src/opportunity/schema/opportunity.schema";
import { PieceWorkSettingSchema } from "src/piece-work/schema/piece-work-setting.schema";
import { MaterialSchema } from "src/project/schema/material.schema";
import { OrderSchema } from "src/project/schema/order.schema";
import { TaskSchema } from "src/project/schema/task.schema";
import { ProjectTypeSchema } from "src/project/schema/project-type.schema";
import { CrewPositionSchema } from "src/project/schema/crew-position.schema";
import { WorkTaskSchema } from "src/work-task/schema/work-task.schema";
import { CompensationSchema } from "src/compensation/schema/compensation.schema";

@Module({
    imports: [
        MongooseModule.forFeature([
            { name: "Crew", schema: CrewSchema },
            { name: "Member", schema: MemberSchema },
            { name: "CrewMember", schema: CrewMemberSchema },
            { name: "PieceWork", schema: PieceWorkSchema },
            { name: "TimeCard", schema: TimeCardSchema },
            { name: "CrewMember", schema: CrewMemberSchema },
            { name: "DailyLog", schema: DailyLogSchema },
            { name: "CompanySetting", schema: CompanySettingSchema },
            { name: "Opportunity", schema: OpportunitySchema },
            { name: "Order", schema: OrderSchema },
            { name: "PieceWorkSetting", schema: PieceWorkSettingSchema },
            { name: "Material", schema: MaterialSchema },
            { name: "Task", schema: TaskSchema },
            { name: "ProjectType", schema: ProjectTypeSchema },
            { name: "CrewPosition", schema: CrewPositionSchema },
            { name: "Compensation", schema: CompensationSchema },
            { name: "WorkTask", schema: WorkTaskSchema },
        ]),
        // PositionModule,
        // RoleModule,
        UserModule,
        // CompanyModule,
    ],
    providers: [CrewService],
    controllers: [CrewController],
    exports: [CrewService],
})
export class CrewModule {}
