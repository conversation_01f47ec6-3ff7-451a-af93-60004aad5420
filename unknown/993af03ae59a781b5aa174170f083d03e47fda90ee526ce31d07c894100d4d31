import { Schema, SchemaFactory, Prop } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import { Document } from "mongoose";
import { UUIDProp } from "src/shared/decorator/uuid.decorator";

export type PackageDocument = Package & Document;

@Schema({ timestamps: true, id: false, strict: true, collection: "Package" })
export class Package {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @UUIDProp()
    companyId: string;

    @Prop({ required: true })
    type: string;

    @Prop({ default: true })
    active: boolean;

    @Prop({ required: true })
    name: string;

    @Prop({ required: false })
    description: string;

    @Prop({ required: false })
    order: number;

    @Prop({ required: false })
    upsell: number;

    @Prop({ required: false })
    taskArray?: any[];

    @Prop({ default: false })
    deleted: boolean;

    @Prop()
    createdAt?: Date;

    @Prop()
    modifiedAt?: Date;
}

export const PackageSchema = SchemaFactory.createForClass(Package);
