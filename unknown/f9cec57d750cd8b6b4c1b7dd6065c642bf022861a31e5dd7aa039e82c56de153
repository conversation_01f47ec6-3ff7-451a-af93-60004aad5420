import { Body, Controller, Delete, Get, Param, Patch, Post, Query } from "@nestjs/common";
import {
    ApiTags,
    ApiOperation,
    ApiConflictResponse,
    ApiInternalServerErrorResponse,
    ApiNotFoundResponse,
    ApiUnauthorizedResponse,
    ApiBearerAuth,
} from "@nestjs/swagger";
import { Roles } from "src/auth/guards/auth.guard";
import { PaginationRequestDto } from "src/company/dto/pagination-request.dto";
import { UserRolesEnum } from "src/company/enum/role.enum";
import HttpResponse from "src/shared/http/response/response.http";
import { CityService } from "./city.service";
import { CreateCityDto } from "./dto/create-city.dto";
import { DeleteCityDto } from "./dto/delete-city.dto";
import { UpdateCityDto } from "./dto/update-city.dto";
import { Auth, GetUser } from "src/auth/decorator/auth.decorator";
import { JwtUserPayload } from "src/auth/interface/auth.interface";

@ApiTags("City")
@ApiBearerAuth()
@Auth()
@ApiInternalServerErrorResponse({ description: "Server Error" })
@ApiUnauthorizedResponse({ description: "Unauthorized request" })
@Controller({ path: "city", version: "1" })
export class CityController {
    constructor(private readonly cityService: CityService) {}

    /**
     * Creates a new city for a company.
     * @param userId The ID of the user creating the city.
     * @param createCityDto The data transfer object containing the city's information.
     * @returns An HTTP response with the result of the operation.
     * @throws ApiConflictException If a city with the same name already exists.
     * @throws ApiInternalServerErrorException If an unexpected error occurs in the server.
     * @throws ApiUnauthorizedException If the request is not authorized.
     */
    @ApiOperation({ summary: "Create City" })
    @ApiConflictResponse({ description: "City already exist" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Post("create-city")
    async createCity(
        @GetUser() user: JwtUserPayload,
        @Body() createCityDto: CreateCityDto,
    ): Promise<HttpResponse> {
        return this.cityService.createCity(user.companyId, createCityDto);
    }

    /**
    Endpoint to delete a city.
    *@summary Delete City
    *@param {string} userId The ID of the user making the request.
    *@param {DeleteCityDto} deleteCityDto The DTO containing the details of the city to be deleted.
    *@returns {Promise<HttpResponse>} A promise that resolves to an HTTP response indicating the success or failure of the operation.
    *@throws {ApiNotFoundException} If the specified city is not found.
    *@throws {ApiInternalServerErrorException} If an internal server error occurs.
    *@throws {ApiUnauthorizedException} If the user making the request is not authorized to perform the operation.
    */
    @ApiOperation({ summary: "Delete City" })
    @ApiNotFoundResponse({ description: "City not found" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Delete("delete-city")
    async deleteCity(@GetUser() userId: any, @Body() deleteCityDto: DeleteCityDto): Promise<HttpResponse> {
        return this.cityService.deleteCity(userId, deleteCityDto);
    }

    /**
     *Endpoint to restore a previously deleted city.
     *@summary Restore City
     *@param {string} userId - The ID of the user making the request.
     *@param {DeleteCityDto} restoreCityDto - The DTO containing the ID of the city to be restored.
     *@returns {Promise<HttpResponse>} - An HTTP response indicating whether the operation was successful.
     *@throws {NotFoundException} - If the specified city is not found.
     *@throws {InternalServerErrorException} - If a server error occurs during the operation.
     *@throws {UnauthorizedException} - If the user making the request is not authorized to perform the operation.
     */
    @ApiOperation({ summary: "Restore City" })
    @ApiNotFoundResponse({ description: "City not found" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Patch("restore-city")
    async restoreCity(@GetUser() userId: any, @Body() restoreCityDto: DeleteCityDto): Promise<HttpResponse> {
        return this.cityService.restoreCity(userId, restoreCityDto);
    }

    /**
     *Endpoint to update a city with the given ID.
     *Requires authentication and authorization as an Admin or Owner.
     *@summary Update City
     *@param {string} userId - The ID of the user performing the request.
     *@param {UpdateCityDto} updateCityDto - The data to use for updating the city.
     *@returns {Promise<HttpResponse>} An HTTP response containing the updated city or an error message.
     *@throws {NotFoundException} If the city with the given ID is not found.
     *@throws {InternalServerErrorException} If a server error occurs.
     */
    @ApiOperation({ summary: "Update City" })
    @ApiNotFoundResponse({ description: "City not found" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Patch("update-city")
    async updateCity(@GetUser() userId: any, @Body() updateCityDto: UpdateCityDto): Promise<HttpResponse> {
        return this.cityService.updateCity(userId, updateCityDto);
    }

    /**
     *Endpoint to get company city.
     *@summary Get City
     *@param {string} userId - The ID of the user performing the request.
     *@param {string} companyId - The ID of the company.
     *@param deleted - Whether to include deleted city in the results.
     *@param paginationRequestDto - The pagination options for the query.
     *@returns A response containing an array of city.
     *@throws {HttpException} When a client error occurs (e.g. invalid input parameters).
     *@throws {InternalServerErrorException} When a server error occurs.
     */
    @ApiOperation({ summary: "Get City" })
    @Get("get-city/deleted/:deleted")
    async getCity(
        @GetUser() user: JwtUserPayload,
        @Param("deleted") deleted: boolean,
        @Query() paginationRequestDto: PaginationRequestDto,
    ): Promise<HttpResponse> {
        return this.cityService.getCity(user._id, user.companyId, deleted, paginationRequestDto);
    }
}
