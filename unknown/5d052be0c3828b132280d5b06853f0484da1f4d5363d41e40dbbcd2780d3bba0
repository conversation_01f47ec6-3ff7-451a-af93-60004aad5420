import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsUUID } from "class-validator";

class MaterialDto {
    @ApiProperty({ description: "Material ID" })
    @IsUUID()
    _id: string;

    @ApiProperty({ description: "Inventory status" })
    inventory: boolean;

    @ApiProperty({ description: "Sequence number" })
    sequence: number;

    @ApiProperty({ description: "Vendor number" })
    vendor: number;

    @ApiProperty({ description: "Unit type" })
    unitEdit: string;

    @ApiProperty({ description: "Amount number" })
    amountEdit: number;

    @ApiProperty({ description: "Name Of the Material" })
    nameEdit: string;

    @ApiProperty({ description: "Material status" })
    deleted: boolean;

    @ApiProperty({ description: "Project Id" })
    projectId: string;
}

export class UpdateMaterialOrderDto {
    @ApiProperty({
        description: "Array of materials",
        type: [MaterialDto], // Reference the new MaterialDto class
    })
    @IsNotEmpty()
    matList: MaterialDto[];
}
