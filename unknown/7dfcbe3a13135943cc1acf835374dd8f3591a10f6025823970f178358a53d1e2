import { <PERSON>hem<PERSON>, SchemaFactory, Prop } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import mongoose, { Document } from "mongoose";
import { UUIDProp } from "src/shared/decorator/uuid.decorator";

export type PieceWorkDocument = PieceWork & Document;

class ExtraTime {
    @Prop({ required: false, default: 0 })
    extraHrs: number;

    @Prop({ required: false, default: 0 })
    extraMin: number;
}

class Work {
    @Prop({ type: [Object], required: false })
    workDone: any;

    @Prop({ type: ExtraTime, required: true })
    extraTime: ExtraTime;

    @Prop({ required: false, default: 0 })
    extraWorkTime: number;
}

@Schema({ timestamps: true, id: false, collection: "PieceWork" })
export class PieceWork {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @UUIDProp()
    companyId: string;

    @UUIDProp()
    timeCardId: string;

    @UUIDProp()
    memberId: string;

    @Prop({ required: true })
    date: Date;

    @Prop({ required: true, type: String })
    task: string;

    @Prop({ required: true, type: String })
    taskName: string;

    @UUIDProp()
    projectId: string;

    @Prop({ type: Work })
    work: Work;

    @Prop({ default: false })
    removeLeadBonus: boolean;

    @Prop({ required: false })
    sqsEarnings: number;

    @Prop({ required: false })
    extrasEarnings: number;

    @Prop({ required: false })
    hourlyEarnings: number;

    @Prop({ required: false })
    hourlyWages: number;

    @Prop({ required: false })
    earned: number;

    @Prop({ required: false, type: [mongoose.Schema.Types.Mixed] })
    auditLog: any;

    @Prop({ required: false })
    allHourly?: boolean;

    @UUIDProp()
    createdBy: string;

    @Prop({ default: false })
    deleted: boolean;

    @Prop()
    createdAt?: Date;

    @Prop()
    modifiedAt?: Date;
}

export const PieceWorkSchema = SchemaFactory.createForClass(PieceWork);
