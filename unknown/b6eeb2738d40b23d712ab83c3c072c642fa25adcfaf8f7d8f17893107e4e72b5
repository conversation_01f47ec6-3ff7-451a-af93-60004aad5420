import { ApiProperty } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsArray, IsDate, IsNotEmpty } from "class-validator";

export class FindAllGps {
    @ApiProperty({ description: "Array of department & postion ids", default: [] })
    // @IsNotEmpty()
    @Transform(({ value }) => (typeof value === "string" ? value.split(",") : []))
    // @IsArray()
    filter: string[];

    @ApiProperty({ description: "Start date" })
    @IsNotEmpty()
    @IsDate()
    @Transform(({ value }) => new Date(value))
    startDate: Date;

    @ApiProperty({ description: "End date" })
    @IsNotEmpty()
    @IsDate()
    @Transform(({ value }) => new Date(value))
    endDate: Date;
}
