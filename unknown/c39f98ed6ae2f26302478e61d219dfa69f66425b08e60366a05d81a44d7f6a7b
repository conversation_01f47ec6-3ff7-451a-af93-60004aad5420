import { Schema, SchemaFactory, Prop } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import { Document } from "mongoose";
import { UUIDProp } from "src/shared/decorator/uuid.decorator";

export type CrewMemberDocument = CrewMember & Document;

@Schema({ timestamps: true, id: false, collection: "CrewMember" })
export class CrewMember {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @UUIDProp()
    companyId: string;

    @UUIDProp()
    crewId: string;

    @UUIDProp()
    memberId: string;

    @Prop({ required: true })
    memberName: string;

    @Prop({ required: false })
    preferredName?: string;

    @Prop({ required: true })
    startDate: Date;

    @Prop()
    promoteDate: Date;

    @Prop()
    removeDate: Date;

    @Prop({ default: false })
    deleted: boolean;

    @Prop({ default: false })
    promoted: boolean;

    @Prop()
    createdAt?: Date;

    @Prop()
    modifiedAt?: Date;
}

export const CrewMemberSchema = SchemaFactory.createForClass(CrewMember);
