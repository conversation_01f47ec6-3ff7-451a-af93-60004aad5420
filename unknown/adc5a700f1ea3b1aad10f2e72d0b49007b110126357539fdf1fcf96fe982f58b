import { Schema, SchemaFactory, Prop, raw } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import { Document } from "mongoose";
import { UUIDProp } from "src/shared/decorator/uuid.decorator";

export type CompanyPayDocument = CompanyPay & Document;

export class Version {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @Prop({ required: true })
    name: string;
}

export class PieceWorkPay {
    @Prop({ type: [String], default: [] })
    positions: string[];

    @Prop({ type: () => [Version], required: false })
    versions: Version[];
}

@Schema({ timestamps: true, id: false, collection: "CompanyPay" })
export class CompanyPay {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @UUIDProp()
    companyId: string;

    @Prop({
        required: true,
        type: raw({
            typeBonus: { type: Object, required: false },
            benchmarks: { type: Array, required: true },
            positions: { type: [String], default: [] },
        }),
    })
    sales: {
        typeBonus: {
            [key: string]: number; //stored in decimal for percentage eg. 10/100
        };
        benchmarks: any;
        positions: string[];
    };

    @Prop({
        required: true,
        type: PieceWorkPay,
        _id: false,
    })
    pieceWork: PieceWorkPay;

    @UUIDProp()
    createdBy: string;

    @Prop()
    createdAt?: Date;

    @Prop()
    modifiedAt?: Date;
}

export const CompanyPaySchema = SchemaFactory.createForClass(CompanyPay);
