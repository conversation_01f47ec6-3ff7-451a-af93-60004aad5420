import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsString, IsNotEmpty, IsUUID, IsNumber, IsOptional } from "class-validator";
import { Transform } from "class-transformer";

export class UpdateTimeCardToClockOutDto {
    @ApiProperty({ description: "Time card Id", required: true })
    @IsUUID()
    @IsNotEmpty()
    _id: string;

    @ApiProperty({ description: "Project id", required: true })
    @IsString()
    @IsNotEmpty()
    projectId: string;

    @ApiProperty({ description: "Project PO", required: true })
    @IsString()
    @IsNotEmpty()
    projectPO: string;

    @ApiProperty({ description: "Task", required: true })
    @IsString()
    @IsNotEmpty()
    task: string;

    @IsOptional()
    taskName: string;

    @ApiProperty({ description: "Hours", required: true })
    @IsNumber()
    @IsNotEmpty()
    hrs: number;

    @ApiProperty({ description: "Time In", required: true })
    // @IsString()
    @Transform(({ value }) => new Date(value))
    @IsNotEmpty()
    timeOut: Date;

    @ApiPropertyOptional({ description: "Notes" })
    @IsString()
    @IsOptional()
    notes?: string;

    @ApiPropertyOptional({ description: "Manager Notes" })
    @IsString()
    @IsOptional()
    managerNotes?: string;
}
