import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { JwtService } from "@nestjs/jwt";
import { MongooseModule } from "@nestjs/mongoose";
import { MemberSchema } from "src/company/schema/member.schema";
import { PositionModule } from "src/position/position.module";
import { PositionSchema } from "src/position/schema/position.schema";
import { RoleModule } from "src/role/role.module";
import { CompensationController } from "./compensation.controller";
import { CompensationService } from "./compensation.service";
import { CompensationSchema } from "./schema/compensation.schema";
import { PayScheduleSchema } from "src/pay-schedule/schema/pay-schedule.schema";

@Module({
    imports: [
        MongooseModule.forFeature([
            { name: "Member", schema: MemberSchema },
            { name: "Position", schema: PositionSchema },
            { name: "Compensation", schema: CompensationSchema },
            { name: "PaySchedule", schema: PayScheduleSchema },
         
        ]),
        // PositionModule,
        // RoleModule,
    ],
    providers: [CompensationService],
    controllers: [CompensationController],
    exports: [CompensationService],
})
export class CompensationModule {}
