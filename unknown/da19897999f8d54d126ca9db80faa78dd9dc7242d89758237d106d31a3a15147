import { Body, Controller, Delete, Get, Param, ParseUUIDPipe, Patch, Post, Query } from "@nestjs/common";
import {
    ApiBearerAuth,
    ApiInternalServerErrorResponse,
    ApiNotFoundResponse,
    ApiOperation,
    ApiTags,
    ApiUnauthorizedResponse,
} from "@nestjs/swagger";
import { Roles } from "src/auth/guards/auth.guard";
import { PaginationRequestDto } from "src/company/dto/pagination-request.dto";
import { UserRolesEnum } from "src/company/enum/role.enum";
import HttpResponse from "src/shared/http/response/response.http";
import { CreateLeadSourceDto } from "./dto/create-lead-source.dto";
import { DeleteLeadSourceDto } from "./dto/delete-lead-source.dto";
import { RestoreLeadSourceDto } from "./dto/restore-lead-source.dto";
import { UpdateLeadSourceDto } from "./dto/update-lead-source.dto";
import { LeadSourceService } from "./lead-source.service";
import { Auth, GetUser } from "src/auth/decorator/auth.decorator";
import { JwtUserPayload } from "src/auth/interface/auth.interface";
import { GetLeadSourceDto } from "./dto/fetch-lead-source.dto";

@ApiBearerAuth()
@ApiTags("LeadSource")
@Auth()
@Controller({ path: "lead-source", version: "1" })
export class LeadSourceController {
    constructor(private readonly leadSourceService: LeadSourceService) {}

    /**
     *Creates a new lead source.
     *@param userId - The ID of the user creating the lead source.
     *@param createLeadSourceDto - The DTO containing the data for the new lead source.
     *@returns A Promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Create Lead source" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Post("create-lead-source")
    async createLeadSource(
        @GetUser() user: JwtUserPayload,
        @Body() createLeadSourceDto: CreateLeadSourceDto,
    ): Promise<HttpResponse> {
        return this.leadSourceService.createLeadSource(user.companyId, user.memberId, createLeadSourceDto);
    }

    /**
     *Deletes a Lead source.
     *@param userId - ID of the user making the request.
     *@param deleteLeadSourceDto - DTO containing the ID of the Lead source to delete.
     *@returns A Promise that resolves to an HttpResponse.
     */
    @ApiOperation({ summary: "Delete Lead source" })
    @ApiNotFoundResponse({ description: "Lead source not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Delete("delete-lead-source")
    async deleteLeadSource(
        @GetUser() user: JwtUserPayload,
        @Body() deleteLeadSourceDto: DeleteLeadSourceDto,
    ): Promise<HttpResponse> {
        return this.leadSourceService.deleteLeadSource(user._id, deleteLeadSourceDto);
    }

    /**
     *Permanently Deletes a Lead source.
     *@param userId - ID of the user making the request.
     *@param deleteLeadSourceDto - DTO containing the ID of the Lead source to delete.
     *@returns A Promise that resolves to an HttpResponse.
     */
    @ApiOperation({ summary: "Delete Lead source" })
    @ApiNotFoundResponse({ description: "Lead source not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Delete("perm-delete-lead-source")
    async permDeleteLeadSource(
        @GetUser() user: JwtUserPayload,
        @Body() deleteLeadSourceDto: DeleteLeadSourceDto,
    ): Promise<HttpResponse> {
        return this.leadSourceService.permDeleteLeadSource(user.companyId, deleteLeadSourceDto);
    }

    /**
     * Restores Lead source.
     * @param userId ID of the user making the request.
     * @param restoreLeadSourceDto - DTO containing the ID of the Lead source to restore.
     * @returns  An HTTP response indicating whether the operation was successful.
     */
    @ApiOperation({ summary: "Restore Lead source" })
    @ApiNotFoundResponse({ description: "Lead source not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Patch("restore-lead-source")
    async restoreLeadSource(
        @GetUser() user: JwtUserPayload,
        @Body() restoreLeadSourceDto: RestoreLeadSourceDto,
    ): Promise<HttpResponse> {
        return this.leadSourceService.restoreLeadSource(user._id, restoreLeadSourceDto);
    }

    /**
     *Update Lead source endpoint that allows users to update a lead source.
     *@param userId The ID of the user making the request.
     *@param updateLeadSourceDto The DTO that contains the updated lead source data.
     *@returns An HTTP response with the result of the operation.
     */
    @ApiOperation({ summary: "Update Lead source" })
    @ApiNotFoundResponse({ description: "Lead Source not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Patch("update-lead-source")
    async updateLeadSource(
        @GetUser() user: JwtUserPayload,
        @Body() updateLeadSourceDto: UpdateLeadSourceDto,
    ): Promise<HttpResponse> {
        return this.leadSourceService.updateLeadSource(user._id, updateLeadSourceDto);
    }

    /**
     * Retrieves a list of lead sources.
     * @param userId The ID of the user making the request.
     * @param companyId The ID of the company whose lead sources should be retrieved.
     * @param deleted A boolean indicating whether to include deleted lead sources in the result.
     * @param getLeadSourceDto An object containing pagination information (e.g. page number, page size).
     * @returns A Promise resolving to an HttpResponse containing the requested lead sources.
     */
    @ApiOperation({ summary: "Get Lead sources" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Get("get-lead-source/deleted/:deleted")
    async getLeadSources(
        @GetUser() user: JwtUserPayload,
        @Param("deleted") deleted: boolean,
        @Query() getLeadSourceDto: GetLeadSourceDto,
    ): Promise<HttpResponse> {
        return this.leadSourceService.getLeadSourcesWithChannelAndCampaign(
            user.companyId,
            deleted,
            getLeadSourceDto,
        );
    }

    /**
     * Returns a Lead source by its ID, filtered by company and deleted status.
     * @param userId The ID of the authenticated user.
     * @param companyId The ID of the company that the Lead source belongs to.
     * @param leadSourceId The ID of the Lead source to retrieve.
     * @param deleted A boolean value indicating whether to retrieve deleted Lead sources.
     * @returns A Promise that resolves to an HttpResponse containing the retrieved Lead source.
     */
    @ApiOperation({ summary: "Get lead source by id" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    // @UseGuards(UserAuthGuard)
    @Get("get-lead-source-by-id/leadSource/:leadSourceId/deleted/:deleted")
    async getLeadSourceById(
        @GetUser() user: JwtUserPayload,
        @Param("leadSourceId", ParseUUIDPipe) leadSourceId: string,
        @Param("deleted") deleted: boolean,
    ): Promise<HttpResponse> {
        return this.leadSourceService.getLeadSourceById(user._id, user.companyId, leadSourceId, deleted);
    }

    /**
     * Retrieves advertising costs for a given company, month, and year.
     * @param userId The ID of the user making the request.
     * @param companyId The ID of the company for which to retrieve advertising costs.
     * @param month The month for which to retrieve advertising costs.
     * @param year The year for which to retrieve advertising costs.
     * @returns A Promise that resolves to an HttpResponse containing the advertising costs.
     */
    @ApiOperation({ summary: "Get advertising cost" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Get("advertising-cost/:month/:year")
    async advertisingCost(
        @GetUser() user: JwtUserPayload,
        @Param("month") month: number,
        @Param("year") year: number,
    ): Promise<HttpResponse> {
        return this.leadSourceService.advertisingCost(user.companyId, month, year);
    }
}
