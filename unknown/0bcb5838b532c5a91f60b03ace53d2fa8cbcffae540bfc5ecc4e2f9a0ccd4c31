import { Mo<PERSON><PERSON> } from "@nestjs/common";
import { ConfigModule, ConfigService } from "@nestjs/config";
import { MailerModule } from "@nestjs-modules/mailer";
import { PugAdapter } from "@nestjs-modules/mailer/dist/adapters/pug.adapter";
import { join } from "path";
import { MailService } from "./mail.service";

@Module({
    imports: [
        MailerModule.forRootAsync({
            imports: [ConfigModule],
            inject: [ConfigService],
            useFactory: (configService: ConfigService) => {
                return {
                    transport: {
                        host: configService.get<string>("EMAIL_HOST"),
                        port: configService.get<number>("EMAIL_PORT"),
                        auth: {
                            user: configService.get<string>("EMAIL_USER"),
                            pass: configService.get<string>("EMAIL_PASSWORD"),
                        },
                    },
                    defaults: {
                        from: `'no-reply' <${configService.get<string>("SENDER_EMAIL")}>`,
                    },
                    template: {
                        dir: join(__dirname, "templates"),
                        adapter: new PugAdapter(),
                        options: {
                            strict: true,
                        },
                    },
                };
            },
        }),
        ConfigModule,
    ],
    providers: [MailService],
    exports: [MailService],
})
export class MailModule {}
