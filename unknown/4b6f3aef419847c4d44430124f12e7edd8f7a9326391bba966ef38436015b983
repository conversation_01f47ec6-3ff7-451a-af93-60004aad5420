import { Body, Controller, Delete, Get, Param, ParseUUIDPipe, Patch, Post, Query } from "@nestjs/common";
import {
    ApiTags,
    ApiOperation,
    ApiInternalServerErrorResponse,
    ApiNotFoundResponse,
    ApiUnauthorizedResponse,
    ApiBearerAuth,
} from "@nestjs/swagger";
import { Roles } from "src/auth/guards/auth.guard";
import { PaginationRequestDto } from "src/company/dto/pagination-request.dto";
import { UserRolesEnum } from "src/company/enum/role.enum";
import HttpResponse from "src/shared/http/response/response.http";
import { CreateMarketingChannelDto } from "./dto/create-channel.dto";
import { DeleteMarketingChannelDto } from "./dto/delete-channel.dto";
import { RestoreMarketingChannelDto } from "./dto/restore-channel.dto";
import { UpdateMarketingChannelDto } from "./dto/update-channel.dto";
import { Auth, GetUser } from "src/auth/decorator/auth.decorator";
import { JwtUserPayload } from "src/auth/interface/auth.interface";
import { ChannelService } from "./channel.service";

@ApiBearerAuth()
@ApiTags("MarketingChannel")
@Auth()
@Controller({ path: "marketing-channel", version: "1" })
export class ChannelController {
    constructor(private readonly channelService: ChannelService) {}

    /**
     * Creates a new marketing channel
     * @param userId - User ID of the user creating the marketing channel
     * @param createMarketingChannelDto - DTO containing the information for the new marketing channel
     * @returns - Promise that resolves to an HTTP response indicating the result of the operation
     */
    @ApiOperation({ summary: "Create MarketingChannel" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Post("create-marketing-channel")
    async createMarketingChannel(
        @GetUser() user: JwtUserPayload,
        @Body() createMarketingChannelDto: CreateMarketingChannelDto,
    ): Promise<HttpResponse> {
        return this.channelService.createMarketingChannel(user.companyId, createMarketingChannelDto);
    }

    /**
     *Delete a marketing channel.
     *@param userId The user ID.
     *@param deleteMarketingChannelDto The DTO containing the marketing channel ID.
     *@returns The HTTP response.
     */
    @ApiOperation({ summary: "Delete MarketingChannel" })
    @ApiNotFoundResponse({ description: "MarketingChannel not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Delete("delete-marketing-channel")
    async deleteMarketingChannel(
        @GetUser() user: JwtUserPayload,
        @Body() deleteMarketingChannelDto: DeleteMarketingChannelDto,
    ): Promise<HttpResponse> {
        return this.channelService.deleteMarketingChannel(user._id, deleteMarketingChannelDto);
    }

    /**
     *Perm Delete a marketing channel.
     *@param userId The user ID.
     *@param deleteMarketingChannelDto The DTO containing the marketing channel ID.
     *@returns The HTTP response.
     */
    @ApiOperation({ summary: "Delete MarketingChannel" })
    @ApiNotFoundResponse({ description: "MarketingChannel not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Delete("perm-delete-marketing-channel")
    async permDeleteMarketingChannele(
        @GetUser() user: JwtUserPayload,
        @Body() deleteMarketingChannelDto: DeleteMarketingChannelDto,
    ): Promise<HttpResponse> {
        return this.channelService.permDeleteMarketingChannel(user.companyId, deleteMarketingChannelDto);
    }

    /**
     * Restore a deleted marketing channel for the given user ID.
     * @param userId The user ID to restore the marketing channel for.
     * @param restoreMarketingChannelDto The data needed to restore the marketing channel.
     * @returns A Promise of an HTTP response indicating whether the operation was successful or not.
     */
    @ApiOperation({ summary: "Restore MarketingChannel" })
    @ApiNotFoundResponse({ description: "MarketingChannel not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Patch("restore-marketing-channel")
    async restoreMarketingChannel(
        @GetUser() user: JwtUserPayload,
        @Body() restoreMarketingChannelDto: RestoreMarketingChannelDto,
    ): Promise<HttpResponse> {
        return this.channelService.restoreMarketingChannel(user._id, restoreMarketingChannelDto);
    }

    /**
     * Update a marketing channel with the given user ID and marketing channel update data.
     * @param userId The ID of the user making the request.
     * @param updateMarketingChannelDto The marketing channel update data.
     * @returns A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Update MarketingChannel" })
    @ApiNotFoundResponse({ description: "MarketingChannel not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Patch("update-marketing-channel")
    async updateMarketingChannel(
        @GetUser() user: JwtUserPayload,
        @Body() updateMarketingChannelDto: UpdateMarketingChannelDto,
    ): Promise<HttpResponse> {
        return this.channelService.updateMarketingChannel(user._id, updateMarketingChannelDto);
    }

    /**
     * Get Marketing Channels for a Company
     * @param userId User ID of the requester
     * @param companyId Company ID of the Company to get Marketing Channels for
     * @param deleted Whether to include deleted Marketing Channels in the results
     * @param paginationRequestDto Pagination request details (offset, limit, etc.)
     * @returns List of Marketing Channels for the specified Company
     */
    @ApiOperation({ summary: "Get MarketingChannel" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    // @UseGuards(UserAuthGuard)
    @Get("get-marketing-channel/deleted/:deleted")
    async getMarketingChannel(
        @GetUser() user: JwtUserPayload,
        @Param("deleted") deleted: boolean,
        @Query() paginationRequestDto: PaginationRequestDto,
    ): Promise<HttpResponse> {
        return this.channelService.getMarketingChannel(
            user._id,
            user.companyId,
            deleted,
            paginationRequestDto,
        );
    }

    /**
     * Get a marketing channel by its ID
     * @param userId - the ID of the user making the request
     * @param companyId - the ID of the company associated with the marketing channel
     * @param marketingChannelId - the ID of the marketing channel to retrieve
     * @param deleted - whether to include deleted marketing channels in the results
     * @returns a Promise representing the HTTP response with the requested marketing channel
     */
    @ApiOperation({ summary: "Get MarketingChannel by id" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    // @UseGuards(UserAuthGuard)
    @Get("get-marketing-channel-by-id/marketingChannel/:marketingChannelId/deleted/:deleted")
    async getMarketingChannelById(
        @GetUser() user: JwtUserPayload,
        @Param("marketingChannelId", ParseUUIDPipe) marketingChannelId: string,
        @Param("deleted") deleted: boolean,
    ): Promise<HttpResponse> {
        return this.channelService.getMarketingChannelById(
            user._id,
            user.companyId,
            marketingChannelId,
            deleted,
        );
    }
}
