import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsString, IsNotEmpty, IsUUID, IsBoolean, IsOptional, IsEmail } from "class-validator";

export class UpsertSubcontractorDto {
    @ApiPropertyOptional({ description: "id" })
    @IsUUID()
    @IsOptional()
    _id?: string;

    @ApiPropertyOptional({ description: "Manager Id", required: true })
    @IsUUID()
    @IsOptional()
    managerId?: string;

    @ApiProperty({ description: "Subcontractor name", required: true })
    @IsString()
    @Transform(({ value }) => value.trim(), { toClassOnly: true })
    @IsNotEmpty()
    name: string;

    @ApiProperty({ description: "Address", required: true })
    @IsString()
    @IsNotEmpty()
    address: string;

    @ApiProperty({ description: "City", required: true })
    @IsString()
    @IsNotEmpty()
    city: string;

    @ApiProperty({ description: "State", required: true })
    @IsString()
    @IsNotEmpty()
    state: string;

    @ApiProperty({ description: "zip", required: true })
    @IsString()
    @IsNotEmpty()
    zip: string;

    @ApiPropertyOptional({ description: "Agreement Completed" })
    @IsBoolean()
    @IsOptional()
    agreementCompleted: boolean;

    @ApiPropertyOptional({ description: "isActive" })
    @IsBoolean()
    @IsOptional()
    isActive: boolean;

    @ApiProperty({ description: "Main Contractor name", required: true })
    @IsString()
    @Transform(({ value }) => value.trim(), { toClassOnly: true })
    @IsNotEmpty()
    mainContractorName: string;

    @ApiProperty({ description: "Phone number", required: true })
    @IsString()
    // @Matches(/^(\([0-9]{3}\) |[0-9]{3}-)[0-9]{3}-[0-9]{4}$/, {
    //     message: "Only US Phone numbers allowed",
    // })
    phone: string;

    @ApiProperty({ description: "Email", required: true })
    @IsEmail()
    @Transform(({ value }) => value.trim().toLowerCase(), { toClassOnly: true })
    @IsNotEmpty()
    email?: string;

    @ApiPropertyOptional({ description: "Retired" })
    @IsBoolean()
    @IsOptional()
    retired: boolean;

    @ApiPropertyOptional({ description: "Deleted" })
    @IsBoolean()
    @IsOptional()
    deleted: boolean;

    @ApiPropertyOptional({ description: "tear off" })
    @IsOptional()
    tearOff: any;

    @ApiPropertyOptional({ description: "roofing" })
    @IsOptional()
    roofing: any;

    @ApiPropertyOptional({ description: "miscellaneous" })
    @IsOptional()
    miscellaneous: any;
}
