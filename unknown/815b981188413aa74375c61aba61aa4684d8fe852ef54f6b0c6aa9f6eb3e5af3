import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import {
    IsString,
    IsNotEmpty,
    IsOptional,
    IsNumber,
    IsBoolean,
    IsEnum,
    MaxLength,
    MinLength,
    IsUUID,
    ValidateNested,
    IsArray,
    IsDate,
} from "class-validator";
import { PieceWorkSettingTypeEnum } from "../enum/piece-work-setting-type.enum";
import { PieceWorkUnitEnum } from "../enum/piece-work-unit.enum";
import { WorkerTypeTypeEnum } from "../enum/worker-type.enum";
import { Transform, Type } from "class-transformer";

export class CreatePieceWorkSettingDto {
    @ApiPropertyOptional({ description: "Piece work setting id" })
    @IsUUID()
    @IsOptional()
    _id: string;

    // @ApiProperty({ description: "Company", required: true })
    // @IsUUID()
    // @IsNotEmpty()
    // companyId: string;

    @ApiProperty({ description: "Name", required: true })
    @IsString()
    @MinLength(2)
    @MaxLength(30)
    @IsNotEmpty()
    name: string;

    @ApiProperty({ description: "Amount", required: true })
    @IsNumber()
    @IsNotEmpty()
    amount: number;

    @ApiProperty({ description: "Unit", required: true })
    @IsString()
    @IsNotEmpty()
    @IsEnum(PieceWorkUnitEnum)
    unit: PieceWorkUnitEnum;

    @ApiProperty({ description: "Type", required: true })
    @IsString()
    @IsNotEmpty()
    @IsEnum(PieceWorkSettingTypeEnum)
    type: PieceWorkSettingTypeEnum;

    @ApiProperty({ description: "SubType", required: true })
    @IsNumber()
    @IsNotEmpty()
    subType: number;

    @ApiProperty({ description: "Row number", required: true })
    @IsNumber()
    @IsNotEmpty()
    order: number;

    @ApiPropertyOptional({ description: "Description" })
    @IsString()
    @IsOptional()
    description?: string;

    @ApiProperty({ description: "Created By", required: true })
    @IsUUID()
    @IsNotEmpty()
    createdBy: string;

    @ApiPropertyOptional({ description: "Deleted" })
    @IsBoolean()
    @IsOptional()
    deleted: boolean;

    @ApiProperty({ description: "workerType", required: true, default: WorkerTypeTypeEnum.Default })
    @IsString()
    @IsNotEmpty()
    @IsEnum(WorkerTypeTypeEnum)
    workerType: WorkerTypeTypeEnum;
}

export class PitchDto {
    @ApiProperty({ description: "Pitch Order", required: true })
    @IsNumber()
    @IsNotEmpty()
    pitchOrder: number;

    @ApiProperty({ description: "Amount", required: true })
    @IsNumber()
    @IsNotEmpty()
    amount: number;
}

export class Layer {
    @ApiProperty({ description: "Fixed details", required: true })
    @IsOptional()
    fixed: {
        value: number;
        isActive: boolean;
    };

    @ApiProperty({ description: "Percent details", required: true })
    @IsOptional()
    percent: {
        value: number;
        isActive: boolean;
    };
}

export class NewCreatePieceWorkSettingDto {
    // @ApiProperty({ description: "Company", required: true })
    // @IsUUID()
    // @IsNotEmpty()
    // companyId: string;

    @ApiProperty({ description: "Name", required: true })
    @IsString()
    @MinLength(2)
    @MaxLength(30)
    @IsNotEmpty()
    name: string;

    @ApiProperty({ description: "Amount", required: true })
    @IsNumber()
    @IsNotEmpty()
    amount: number;

    @ApiProperty({ description: "Unit", required: true })
    @IsNotEmpty()
    @IsString()
    unit: string;

    @ApiProperty({ description: "unitId", required: true })
    @IsUUID()
    @IsNotEmpty()
    unitId: string;

    @ApiProperty({ description: "workTask", required: true })
    @IsUUID()
    @IsNotEmpty()
    workTask: string;

    @ApiPropertyOptional({ description: "Description" })
    @IsString()
    @IsOptional()
    description?: string;

    @ApiProperty({ description: "Use Pitch", required: false })
    @IsBoolean()
    @IsOptional()
    usesPitch?: boolean;

    @ApiPropertyOptional({ description: "if layer" })
    @IsOptional()
    @IsBoolean()
    hasLayer?: boolean;

    @ApiPropertyOptional({ description: "layer" })
    @IsOptional()
    @Type(() => Layer)
    layer?: Layer;

    @ApiPropertyOptional({ description: "if extra setting" })
    @IsOptional()
    @IsBoolean()
    isExtra?: boolean;

    @ApiProperty({ description: "Created By", required: true })
    @IsUUID()
    @IsNotEmpty()
    createdBy: string;

    @ApiPropertyOptional({ description: "Deleted" })
    @IsBoolean()
    @IsOptional()
    deleted?: boolean;

    @ApiProperty({ description: "Version", required: true })
    @IsUUID()
    @IsNotEmpty()
    version: string;
}

export class NewUpdatePieceWorkSettingDto {
    @ApiProperty({ description: "Company", required: false })
    @IsUUID()
    @IsOptional()
    companyId?: string;

    @ApiProperty({ description: "Name", required: false })
    @IsString()
    @MinLength(2)
    @MaxLength(30)
    @IsOptional()
    name?: string;

    @ApiProperty({ description: "workTask", required: false })
    @IsOptional()
    @IsUUID()
    workTask?: string;

    @ApiProperty({ description: "Pitch", required: false, type: [PitchDto] })
    @IsOptional()
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => PitchDto)
    pitch?: PitchDto[];

    @ApiProperty({ description: "Amount", required: false })
    @IsNumber()
    @IsOptional()
    amount: number;

    @ApiProperty({ description: "Unit", required: false })
    @IsOptional()
    @IsString()
    unit: string;

    @ApiProperty({ description: "unitId", required: false })
    @IsUUID()
    @IsOptional()
    unitId: string;

    @ApiPropertyOptional({ description: "Description" })
    @IsString()
    @IsOptional()
    description?: string;

    @ApiProperty({ description: "Uses Pitch", required: false })
    @IsBoolean()
    @IsOptional()
    usesPitch?: boolean;

    @ApiPropertyOptional({ description: "if layer" })
    @IsOptional()
    @IsBoolean()
    hasLayer?: boolean;

    @ApiPropertyOptional({ description: "layer" })
    @IsOptional()
    @Type(() => Layer)
    layer?: Layer;

    @ApiPropertyOptional({ description: "if extra setting" })
    @IsOptional()
    @IsBoolean()
    isExtra?: boolean;

    @ApiProperty({ description: "Created By", required: false })
    @IsOptional()
    @IsUUID()
    createdBy?: string;

    @ApiPropertyOptional({ description: "Deleted" })
    @IsBoolean()
    @IsOptional()
    deleted?: boolean;

    @ApiProperty({ description: "Version", required: false })
    @IsUUID()
    @IsOptional()
    version?: string;
}

export class UpdateAmount {
    @ApiProperty({ description: "Piecework setting ID", required: false })
    @IsUUID("4", { message: "Invalid UUID format for ID" })
    @IsOptional()
    id?: string;

    @ApiProperty({ description: "List of pitches", required: false, type: [PitchDto] })
    @IsOptional()
    @IsArray({ message: "Pitch should be an array of PitchDto" })
    @ValidateNested({ each: true })
    @Type(() => PitchDto)
    pitch?: PitchDto[];

    @ApiProperty({ description: "Amount associated with the piecework setting", required: false })
    @IsNumber({}, { message: "Amount must be a number" })
    @IsOptional()
    amount?: number;
}

export class UpdatePieceWorkSettingAmountsDto {
    @ApiProperty({ description: "Array of UpdateAmount", type: [UpdateAmount] })
    @IsArray({ message: "Input should be an array of UpdateAmount" })
    @Type(() => UpdateAmount)
    updateInputAmounts: UpdateAmount[];
}

export class FetchPieceWorkSettingDto {
    @ApiProperty({ description: "Version", required: false })
    @IsUUID()
    @IsOptional()
    version?: string;
}

export class FetchWorkTaskPieceWorkSettingsDto {
    @ApiProperty({ description: "Member Id", required: true })
    @IsUUID()
    @IsNotEmpty()
    memberId: string;

    @ApiProperty({ description: "Date" })
    @IsDate()
    @Transform(({ value }) => new Date(value))
    @IsNotEmpty()
    date: Date;
}

export class FetchPieceWorkSettingByTaskIdDto extends FetchWorkTaskPieceWorkSettingsDto {
    @ApiProperty({ description: "worktask Id", required: false })
    @IsUUID()
    @IsOptional()
    taskId: string;
}
