import { HttpException, HttpStatus, Injectable, InternalServerErrorException } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import { Model } from "mongoose";
import { PaginationRequestDto } from "src/company/dto/pagination-request.dto";
import { LeadSourceDocument } from "src/marketing-setting/schema/lead-source.schema";
import CreatedResponse from "src/shared/http/response/created.http";
import NoContentResponse from "src/shared/http/response/no-content.http";
import OkResponse from "src/shared/http/response/ok.http";
import { CreateMarketingChannelDto } from "./dto/create-channel.dto";
import { DeleteMarketingChannelDto } from "./dto/delete-channel.dto";
import { RestoreMarketingChannelDto } from "./dto/restore-channel.dto";
import { UpdateMarketingChannelDto } from "./dto/update-channel.dto";
import { MarketingChannelDocument } from "./schema/channel.schema.dto";

@Injectable()
export class ChannelService {
    constructor(
        @InjectModel("MarketingChannel")
        private readonly channelModel: Model<MarketingChannelDocument>,
        @InjectModel("LeadSource") private readonly leadSourceModel: Model<LeadSourceDocument>,
    ) {}

    async createMarketingChannel(companyId: string, createMarketingChannelDto: CreateMarketingChannelDto) {
        try {
            const marketingChannel = await this.channelModel
                .exists({
                    companyId,
                    name: createMarketingChannelDto.name,
                    deleted: false,
                })
                .exec();
            if (marketingChannel)
                throw new HttpException("MarketingChannel already exists", HttpStatus.BAD_REQUEST);
            const createdMarketingChannel = new this.channelModel({
                companyId,
                ...createMarketingChannelDto,
            });
            await createdMarketingChannel.save();
            return new CreatedResponse({ message: "MarketingChannel created successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async deleteMarketingChannel(userId: string, deleteMarketingChannelDto: DeleteMarketingChannelDto) {
        try {
            await this.channelModel.findOneAndUpdate(
                { _id: deleteMarketingChannelDto.id, deleted: false },
                {
                    $set: { deleted: true },
                },
                { new: true },
            );
            return new NoContentResponse({ message: "MarketingChannel deleted successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async permDeleteMarketingChannel(
        companyId: string,
        deleteMarketingChannelDto: DeleteMarketingChannelDto,
    ) {
        try {
            await this.channelModel.deleteOne({
                _id: deleteMarketingChannelDto.id,
                companyId,
            });
            return new NoContentResponse({ message: "MarketingChannel deleted permanently!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async restoreMarketingChannel(userId: string, restoreMarketingChannelDto: RestoreMarketingChannelDto) {
        try {
            await this.channelModel.findOneAndUpdate(
                { _id: restoreMarketingChannelDto.id, deleted: true },
                {
                    $set: { deleted: false },
                },
                { new: true },
            );
            return new OkResponse({ message: "MarketingChannel restored successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updateMarketingChannel(userId: string, updateMarketingChannelDto: UpdateMarketingChannelDto) {
        try {
            await this.channelModel.findOneAndUpdate(
                { _id: updateMarketingChannelDto.marketingChannelId, deleted: false },
                {
                    $set: { ...updateMarketingChannelDto },
                },
                { new: true },
            );
            return new OkResponse({ message: "MarketingChannel updated successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getMarketingChannel(
        userId: string,
        companyId: string,
        deleted: boolean,
        paginationRequestDto: PaginationRequestDto,
    ) {
        try {
            const limit = paginationRequestDto.limit || 10;
            const offset = limit * (paginationRequestDto.skip || 0);
            const marketingChannel = await this.channelModel
                .find({ companyId, deleted })
                .skip(offset)
                .limit(limit);
            return new OkResponse({ marketingChannel });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getMarketingChannelById(
        userId: string,
        companyId: string,
        marketingChannelId: string,
        deleted: boolean,
    ) {
        try {
            const marketingChannel = await this.channelModel.findOne({
                _id: marketingChannelId,
                companyId,
                deleted,
            });
            return new OkResponse({ marketingChannel });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async addDefaultMarketingChannelsAndLeadSourceSetting(companyId: string, memberId: string) {
        try {
            const buildInMarketingChannelSetting = [
                {
                    _id: randomUUID(),
                    name: "Online ads",
                },
                {
                    _id: randomUUID(),
                    name: "Print ads",
                },
                {
                    _id: randomUUID(),
                    name: "Word of mouth",
                },
                {
                    _id: randomUUID(),
                    name: "Repeat business",
                },
                {
                    _id: randomUUID(),
                    name: "Online organic",
                },
                {
                    _id: randomUUID(),
                    name: "Online Pay-per-lead",
                },
            ];

            const buildInLeadSourceSetting = [
                {
                    name: "Referral",
                    description: "A lead who is referred to us by an existing client",
                    channelId: buildInMarketingChannelSetting[2]._id,
                    code: "referral",
                },
                {
                    name: "Sales person Self Gen",
                    description: "A lead who is referred to us by an existing client",
                    channelId: buildInMarketingChannelSetting[2]._id,
                    code: "selfGen",
                },
                {
                    name: "Repeat Client",
                    description: "An existing client who gives us another opportunity to work for them",
                    channelId: buildInMarketingChannelSetting[3]._id,
                },
                {
                    name: "Website",
                    description: "Signed up on the website",
                    channelId: buildInMarketingChannelSetting[4]._id,
                },
                {
                    name: "Facebook",
                    description: "Finds us through our Facebook page",
                    channelId: buildInMarketingChannelSetting[4]._id,
                },
                {
                    name: "Facebook Ads",
                    description: "Finds us through an ad we ran on Facebook",
                    channelId: buildInMarketingChannelSetting[0]._id,
                },
                {
                    name: "Google Adwords",
                    description: "Finds us on an ad we ran on Google",
                    channelId: buildInMarketingChannelSetting[0]._id,
                },
                {
                    name: "Angi Leads",
                    description: "",
                    channelId: buildInMarketingChannelSetting[5]._id,
                },
                {
                    name: "Thumbtack",
                    description: "",
                    channelId: buildInMarketingChannelSetting[5]._id,
                },
                {
                    name: "Google",
                    description: "Found on Google Search (organic)",
                    channelId: buildInMarketingChannelSetting[4]._id,
                },
                {
                    name: "Phone Book",
                    description: "Hagadone phone book",
                    channelId: buildInMarketingChannelSetting[1]._id,
                },
                {
                    name: "Porch",
                    description: "",
                    channelId: buildInMarketingChannelSetting[5]._id,
                },
                {
                    name: "Contractors.com",
                    description: "",
                    channelId: buildInMarketingChannelSetting[5]._id,
                },
                {
                    name: "Angi's List",
                    description: "Found on Angie's list profile",
                    channelId: buildInMarketingChannelSetting[0]._id,
                },
                {
                    name: "Contractor Appointments",
                    description: "",
                    channelId: buildInMarketingChannelSetting[5]._id,
                },
                {
                    name: "Service.com",
                    description: "",
                    channelId: buildInMarketingChannelSetting[5]._id,
                },
                {
                    name: "Jobsite",
                    description: "Found us by seeing one of our jobsites",
                    channelId: buildInMarketingChannelSetting[2]._id,
                },
                {
                    name: "Walk-In",
                    description: "Someone who comes in to the office",
                    channelId: buildInMarketingChannelSetting[2]._id,
                },
                {
                    name: "Home Show",
                    description: "",
                    channelId: buildInMarketingChannelSetting[2]._id,
                },
                {
                    name: "Velux",
                    description: "From the velux rep.",
                    channelId: buildInMarketingChannelSetting[2]._id,
                },
                {
                    name: "Truck Wrap",
                    description: "A lead that called after seeing one of our trucks",
                    channelId: buildInMarketingChannelSetting[1]._id,
                },
                {
                    name: "Warranty",
                    description: "Free work covered under our warranties",
                    channelId: buildInMarketingChannelSetting[3]._id,
                },
                {
                    name: "Yelp",
                    description: "",
                    channelId: buildInMarketingChannelSetting[4]._id,
                },
                {
                    name: "Next Door",
                    description: "",
                    channelId: buildInMarketingChannelSetting[4]._id,
                },
                {
                    name: "City of CDA Permit Office",
                    description: "",
                    channelId: buildInMarketingChannelSetting[2]._id,
                },
                {
                    name: "YouTube",
                    description: "",
                    channelId: buildInMarketingChannelSetting[4]._id,
                },
            ];

            const marketingData = buildInMarketingChannelSetting.map((value) => ({
                ...value,
                companyId,
                createdBy: memberId,
                deleted: false,
            }));
            await this.channelModel.insertMany(marketingData);
            const leadData = buildInLeadSourceSetting.map((value) => ({
                ...value,
                companyId,
                createdBy: memberId,
            }));
            await this.leadSourceModel.insertMany(leadData);
            return new CreatedResponse({
                message: "Marketing Channel and Lead source Setting created successfully!",
            });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }
}
