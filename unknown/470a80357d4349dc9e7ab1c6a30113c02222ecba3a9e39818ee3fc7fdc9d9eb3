import { ApiProperty } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsNotEmpty, <PERSON>String, <PERSON><PERSON>ength, <PERSON><PERSON>ength } from "class-validator";

export class CreateReferrerDto {
    @ApiProperty({ description: "name of referrer", required: true })
    @IsString()
    @MinLength(1)
    @MaxLength(50)
    @Transform(({ value }) => value.trim())
    @IsNotEmpty()
    name: string;

    // @ApiProperty({ description: "company id", required: true })
    // @IsUUID()
    // @IsNotEmpty()
    // companyId: string;
}
