import { Schema, SchemaFactory, Prop } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import { Document } from "mongoose";
import { UUIDProp } from "src/shared/decorator/uuid.decorator";

export type ContractDocument = Contract & Document;

class Display {
    @Prop({ type: String })
    groupId: string;

    @Prop({ type: [String], default: [] })
    removeTasks: string[];

    @Prop({ type: String, required: true })
    text: string;

    @Prop({ type: Number, default: 0 })
    order: number;
}

class Section {
    @Prop({ type: String, default: randomUUID })
    _id: string;

    @Prop({ type: String })
    title: string;

    @Prop({ type: Number, default: 0 })
    order: number;

    @Prop({ type: () => [Display], default: [] })
    display: Display[];
}

@Schema({ timestamps: true, id: false, collection: "Contracts", versionKey: false })
export class Contract {
    @Prop({ type: String, default: randomUUID })
    _id: string;

    @Prop({ isRequired: true })
    contractName: string;

    @Prop({ default: false })
    isDefault: boolean;

    @UUIDProp()
    companyId: string;

    @Prop({ type: String, required: true })
    projectType: string;

    @Prop({ required: true, default: false })
    deleted: boolean;

    @Prop({ type: () => [Section], default: [] })
    sections: Section[];

    @Prop({
        type: Object,
        default: {},
    })
    finePrint?: Record<string, string>;
}

export const ContractSchema = SchemaFactory.createForClass(Contract);
