import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsUUID } from "class-validator";

class AddMaterialDto {
    @ApiProperty({ description: "Material ID" })
    @IsUUID()
    _id: string;

    @ApiProperty({ description: "Unit type" })
    unitEdit: string;

    @ApiProperty({ description: "Amount number" })
    amountEdit: number;

    @ApiProperty({ description: "Name Of the Material" })
    nameEdit: string;
}

export class AddMaterialToOrderDto {
    @ApiProperty({
        description: "Material object to add to matList",
        type: AddMaterialDto, // Single object type
    })
    @IsNotEmpty()
    matList: AddMaterialDto;
}
