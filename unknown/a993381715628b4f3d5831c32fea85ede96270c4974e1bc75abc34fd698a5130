import { ApiProperty } from "@nestjs/swagger";
import { IsEmail, IsNotEmpty, IsString, Matches, MaxLength, Min<PERSON>ength } from "class-validator";

export class CreateAdminDto {
    /**
     * Email of Admin
     */ @ApiProperty({
        name: "email",
        description: "Email of the admin",
        example: "<EMAIL>",
        required: true,
    })
    @IsEmail({ message: "Please provide a valid email" })
    @IsString({ message: "Email can not be only numbers" })
    @IsNotEmpty({ message: "email can not be empty" })
    email: string;

    /**
     * Password Admin wants to provide
     */ @ApiProperty({
        name: "password",
        description: "Password of the admin",
        example: "Password@123",
    })
    @IsNotEmpty({ message: "password can not be empty" })
    @MinLength(8, { message: "password must contain minimum of 8 characters" })
    @MaxLength(32, { message: "password must contain maximum of 32 characters" })
    @Matches(/(?:(?=.*\d)|(?=.*\W+))(?![.\n])(?=.*[A-Z])(?=.*[a-z]).*$/, {
        message: "Weak Password",
    })
    password: string;

    /**
     * Role of admin
     */
    @ApiProperty({
        name: "role",
        description: "role of the admin",
    })
    @IsString()
    @IsNotEmpty({ message: "role can not be empty" })
    role: string;

    extraUserChargeMonthly?: number;
    extraUserChargeYearly?: number;
    baseUserCount?: number;
}
