import { Aggregate, Schema } from "mongoose";

/**
 * Lean Query Plugin: Automatically calls `.lean()` on `find`, `findOne`, `aggregate` queries.
 * This speeds up responses by returning plain JavaScript objects instead of Mongoose documents.
 */
function LeanQueryPlugin(schema: Schema) {
    ["find", "findOne"].forEach((hook: any) => {
        schema.pre(hook, function () {
            this.lean();
        });
    });

    // Apply `.lean()` for aggregation queries
    schema.pre("aggregate", function (this: Aggregate<any>) {
        this.options.lean = true;
    });
}

/**
 * Exclude Fields Plugin: Automatically removes specified fields (e.g., `companyId`, `__v`) from all queries.
 * Helps improve security by preventing sensitive data leaks.
 */
function ExcludeFieldsPlugin(schema: Schema, fieldsToExclude: string[] = []) {
    // Exclude fields from `find` and `findOne` queries
    schema.pre(["find", "findOne"], function () {
        this.select(fieldsToExclude.map((field) => `-${field}`).join(" "));
    });

    // Exclude fields from `aggregate` queries
    schema.pre("aggregate", function (this: Aggregate<any>) {
        this.pipeline().push({
            $unset: fieldsToExclude,
        });
    });
}

// function QueryLoggerPlugin(schema: Schema, options: { threshold: number }) {
//     const { threshold } = options;

//     schema.pre("find", function () {
//         const start = Date.now();

//         this.post("find", function (docs) {
//             const duration = Date.now() - start;
//             if (duration > threshold) {
//                 console.warn(`Slow query detected: ${this.getQuery()} took ${duration}ms`);
//             }
//         });
//     });
// }

/**
 * Global Plugin Application: Applies all global plugins to the schema.
 * This function is future-proof and allows adding more plugins easily.
 */
export function applyGlobalPlugins(schema: Schema) {
    LeanQueryPlugin(schema);
    ExcludeFieldsPlugin(schema, ["companyId", "__v", "updatedAt", "modifiedAt"]);
}
