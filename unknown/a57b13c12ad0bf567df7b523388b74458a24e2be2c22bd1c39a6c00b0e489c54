import { ApiProperty } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsNotEmpty, IsUUID, IsDate } from "class-validator";
import { MemberDto } from "src/shared/dto/member.dto";

export class CreateCrewMemberDto extends MemberDto {
    @ApiProperty({ description: "Crew Id", required: true })
    @IsUUID()
    @IsNotEmpty()
    crewId: string;

    @ApiProperty({ description: "Start date", required: true })
    @IsDate()
    @Transform(({ value }) => new Date(value))
    @IsNotEmpty()
    startDate: Date;
}
