import { ApiPropertyOptional } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsOptional } from "class-validator";
import { PaginationDto } from "src/shared/dto/pagination.dto";

export class PaginationRequestDto extends PaginationDto {
    @ApiPropertyOptional({ description: "serach" })
    @IsOptional()
    @Transform(({ value }) => value.trim().toLowerCase(), { toClassOnly: true })
    search?: string;
}
