import { Controller, Get, Body, Param, UseGuards, Patch, ParseUUIDPipe } from "@nestjs/common";
import { UserService } from "./user.service";
import {
    ApiBearerAuth,
    ApiInternalServerErrorResponse,
    ApiNotFoundResponse,
    ApiOperation,
    ApiTags,
    ApiUnauthorizedResponse,
} from "@nestjs/swagger";
import HttpResponse from "src/shared/http/response/response.http";
import { UserAuthGuard } from "src/auth/guards/jwt-auth.guard";
import { UpdatePasswordDto } from "./dto/update-password.dto";
import { UpdateUserDto } from "./dto/update-user.dto";
import { Auth, GetUser } from "src/auth/decorator/auth.decorator";
import { JwtUserPayload } from "src/auth/interface/auth.interface";

@ApiTags("User")
@Controller({ path: "user", version: "1" })
@UseGuards(UserAuthGuard)
@ApiInternalServerErrorResponse({ description: "Server Error" })
@ApiUnauthorizedResponse({ description: "Unauthorized request" })
@ApiBearerAuth()
export class UserController {
    constructor(private readonly userService: UserService) {}

    @ApiOperation({ summary: "Get loged-in user profile" })
    @ApiUnauthorizedResponse({ description: "In case user is not logged in" })
    @Get("current-user")
    async getCurrentUser(@GetUser() userId: any): Promise<HttpResponse> {
        return this.userService.getUser(userId._id);
    }

    @ApiOperation({ summary: "Get any user profile" })
    @ApiUnauthorizedResponse({ description: "In case user is not logged in" })
    @Get("id/:userId")
    async getUser(@Param("userId", ParseUUIDPipe) userId: string): Promise<HttpResponse> {
        return this.userService.getUser(userId);
    }

    @ApiOperation({ summary: "UpdatePassword" })
    @ApiNotFoundResponse({ description: "Profile not found" })
    @Patch("update-password")
    async updatePassword(
        @GetUser() user: JwtUserPayload,
        @Body() updatePasswordDto: UpdatePasswordDto,
    ): Promise<HttpResponse> {
        return this.userService.updatePassword(user._id, updatePasswordDto);
    }

    @ApiOperation({ summary: "UpdateProfile" })
    @ApiNotFoundResponse({ description: "Profile not found" })
    @Patch("update-profile")
    async updateProfile(
        @GetUser() user: JwtUserPayload,
        @Body() updateUserDto: UpdateUserDto,
    ): Promise<HttpResponse> {
        return this.userService.updateProfile(updateUserDto);
    }
}
