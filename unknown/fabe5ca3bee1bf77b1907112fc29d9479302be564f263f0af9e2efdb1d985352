import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import {
    IsString,
    IsNotEmpty,
    IsBoolean,
    IsOptional,
    IsEnum,
    MaxLength,
    MinLength,
    IsUUID,
    IsNumber,
    IsDate,
} from "class-validator";
import { WorkTypeEnum } from "../enum/work-type.enum";
import { Transform } from "class-transformer";

export class UpdateCrewDto {
    @ApiProperty({ description: "Crew Id", required: true })
    @IsUUID()
    @IsNotEmpty()
    _id: string;

    // @ApiProperty({ description: "Company Id", required: true })
    // @IsUUID()
    // @IsNotEmpty()
    // companyId: string;

    @ApiPropertyOptional({ description: "Crew name" })
    @IsString()
    @MinLength(4)
    @MaxLength(30)
    @Transform(({ value }) => value.trim(), { toClassOnly: true })
    @IsOptional()
    name: string;

    @ApiPropertyOptional({ description: "Start date" })
    @IsDate()
    @Transform(({ value }) => new Date(value))
    @IsOptional()
    startDate: Date;

    @ApiPropertyOptional({ description: "Work type" })
    @IsNumber()
    @IsEnum(WorkTypeEnum)
    @IsOptional()
    workType: WorkTypeEnum;

    @ApiPropertyOptional({ description: "Manager id" })
    @IsString()
    @IsOptional()
    managerId: string;

    @ApiPropertyOptional({ description: "Foreman id" })
    @IsString()
    @IsOptional()
    foremanId?: string;

    @ApiPropertyOptional({ description: "Deleted" })
    @IsBoolean()
    @IsOptional()
    deleted?: boolean;

    @ApiPropertyOptional({ description: "Retired" })
    @IsBoolean()
    @IsOptional()
    retired?: boolean;

    @ApiPropertyOptional({ description: "Retire date" })
    @IsDate()
    @Transform(({ value }) => new Date(value))
    @IsOptional()
    retireDate?: Date;
}
