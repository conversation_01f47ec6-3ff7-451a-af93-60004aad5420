import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsEnum, IsEmail, IsUUID, IsNumber } from "class-validator";
import { InvitationStatusEnum } from "../enum/invitation-status.enum";
import { Transform } from "class-transformer";

export class InvitationResponseDto {
    @ApiProperty({ description: "Sender Email", required: true })
    @IsEmail()
    @Transform(({ value }) => value.trim().toLowerCase(), { toClassOnly: true })
    @IsNotEmpty()
    senderEmail: string;

    @ApiProperty({ description: "Recipient Email", required: true })
    @IsEmail()
    @Transform(({ value }) => value.trim().toLowerCase(), { toClassOnly: true })
    @IsNotEmpty()
    recipientEmail: string;

    @ApiProperty({ description: "Company", required: true })
    @IsUUID()
    @IsNotEmpty()
    company: string;

    @ApiProperty({ description: "Invitation Status", required: true })
    @IsNumber()
    @IsNotEmpty()
    @IsEnum(InvitationStatusEnum)
    status: InvitationStatusEnum;
}
