// src/contract/dto/create-contract.dto.ts

import {
    IsString,
    IsArray,
    IsOptional,
    IsUUID,
    IsNumber,
    IsNotEmpty,
    IsBoolean,
    IsObject,
} from "class-validator";
import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";

class CreateDisplayDto {
    @ApiPropertyOptional({ description: "Unique identifier for the group" })
    @IsUUID()
    @IsOptional()
    groupId?: string;

    @ApiPropertyOptional({ description: "List of task IDs to be removed" })
    @IsArray()
    @IsOptional()
    removeTasks?: string[];

    @ApiProperty({ description: "Text description for the display" })
    @IsString()
    text: string;

    @ApiPropertyOptional({ description: "Order of the display in the section" })
    @IsOptional()
    @IsNumber()
    order?: number;
}

export class CreateSectionDto {
    @ApiProperty({ description: "Title of the section" })
    @IsOptional()
    @IsString()
    title?: string;

    @ApiPropertyOptional({ description: "Order of the section" })
    @IsOptional()
    @IsNumber()
    order?: number;

    @ApiPropertyOptional({ description: "Displays in the section", type: [CreateDisplayDto] })
    @IsArray()
    @IsOptional()
    display?: CreateDisplayDto[];
}

export class CreateContractDto {
    @ApiProperty({ description: "The name of contract" })
    @IsNotEmpty()
    @IsString()
    contractName: string;

    @ApiProperty({ description: "The type of project" })
    @IsNotEmpty()
    @IsUUID()
    projectType: string;

    @ApiProperty({ description: "Default contract or not" })
    @IsNotEmpty()
    @IsBoolean()
    isDefault: boolean;

    @ApiPropertyOptional({ description: "Sections of the contract", type: [CreateSectionDto] })
    @IsOptional()
    @IsArray()
    sections?: CreateSectionDto[];

    @ApiPropertyOptional({
        description: "Fine print key-value pairs where keys are strings and values are UUIDs",
    })
    @IsOptional()
    @IsObject()
    finePrint?: Record<string, string>;
}
