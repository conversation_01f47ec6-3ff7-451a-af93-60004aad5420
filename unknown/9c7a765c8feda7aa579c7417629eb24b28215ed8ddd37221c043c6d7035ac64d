import { Schema, SchemaFactory, Prop } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import { Document, SchemaTypes } from "mongoose";
import { UUIDProp } from "src/shared/decorator/uuid.decorator";

export type OrderDocument = Order & Document;

interface Project {
    projectId?: string;
    type?: string;
    name?: string;
    notes?: string;
    colors?: any;
    basePackages?: string;
    chosenOptions?: string[];
    avgPitch?: number;
    workOrder?: any[];
    priceTotals?: any;
}

@Schema({ timestamps: true, id: false, collection: "Order" })
export class Order {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @UUIDProp()
    companyId: string;

    @UUIDProp()
    oppId: string;

    @UUIDProp()
    projectId: string;

    @UUIDProp()
    projectPriceId: string;

    @Prop({ required: true, type: SchemaTypes.Mixed })
    priceTotals: any;

    @Prop({ required: true })
    matList: any[];

    @Prop({ required: false, type: SchemaTypes.Mixed })
    actualTotals?: any;

    @Prop({ required: false })
    paymentType: string;

    @Prop({ default: false })
    deleted: boolean;

    @UUIDProp()
    createdBy: string;

    @Prop()
    createdAt?: Date;

    @Prop()
    modifiedAt?: Date;

    @Prop({ type: SchemaTypes.Mixed })
    projects: Project[];

    @Prop({ type: SchemaTypes.Mixed })
    modifiedBudget?: any;
}

export const OrderSchema = SchemaFactory.createForClass(Order);
