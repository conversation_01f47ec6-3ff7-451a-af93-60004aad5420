import {
    Body,
    Controller,
    Delete,
    Get,
    Param,
    ParseUUIDPipe,
    Patch,
    Post,
    Query,
    UseGuards,
} from "@nestjs/common";
import {
    ApiTags,
    ApiOperation,
    ApiConflictResponse,
    ApiInternalServerErrorResponse,
    ApiNotFoundResponse,
    ApiUnauthorizedResponse,
    ApiBearerAuth,
} from "@nestjs/swagger";
import { Roles } from "src/auth/guards/auth.guard";
import { UserAuthGuard } from "src/auth/guards/jwt-auth.guard";
import { PaginationRequestDto } from "src/company/dto/pagination-request.dto";
import { UserRolesEnum } from "src/company/enum/role.enum";
import HttpResponse from "src/shared/http/response/response.http";
import { DepartmentService } from "./department.service";
import { CreateDepartmentDto } from "./dto/create-department.dto";
import { DeleteDepartmentDto } from "./dto/delete-department.dto";
import { UpdateDepartmentDto } from "./dto/update-depatment.dto";
import { Auth, GetUser } from "src/auth/decorator/auth.decorator";
import { JwtUserPayload } from "src/auth/interface/auth.interface";

@ApiTags("Department")
@ApiBearerAuth()
@Auth()
@Controller({ path: "department", version: "1" })
export class DepartmentController {
    constructor(private readonly departmentService: DepartmentService) {}

    /**
     * Creates a new department using the provided information.
     * @param userId The ID of the user creating the department.
     * @param createDepartmentDto The DTO containing the department information.
     * @returns A Promise that resolves to an HttpResponse object.
     */
    @ApiOperation({ summary: "Create Department" })
    @ApiConflictResponse({ description: "Department already exist" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Post("create-department")
    async createDepartment(
        @GetUser() user: JwtUserPayload,
        @Body() createDepartmentDto: CreateDepartmentDto,
    ): Promise<HttpResponse> {
        return this.departmentService.createDepartment(user.companyId, createDepartmentDto);
    }

    /**
     * Deletes a department using the provided information.
     * @param userId The ID of the user deleting the department.
     * @param deleteDepartmentDto The DTO containing the department information to be deleted.
     * @returns A Promise that resolves to an HttpResponse object.
     */
    @ApiOperation({ summary: "Delete Department" })
    @ApiNotFoundResponse({ description: "Department not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Delete("delete-department")
    async deleteDepartment(
        @GetUser() user: JwtUserPayload,
        @Body() deleteDepartmentDto: DeleteDepartmentDto,
    ): Promise<HttpResponse> {
        return this.departmentService.deleteDepartment(user._id, deleteDepartmentDto);
    }

    /**
     * Restores a deleted department using the provided information.
     * @param userId The ID of the user restoring the department.
     * @param deleteDepartmentDto The DTO containing the department information to be restored.
     * @returns A Promise that resolves to an HttpResponse object.
     */
    @ApiOperation({ summary: "Restore Department" })
    @ApiNotFoundResponse({ description: "Department not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Patch("restore-department")
    async restoreDepartment(
        @GetUser() user: JwtUserPayload,
        @Body() deleteDepartmentDto: DeleteDepartmentDto,
    ): Promise<HttpResponse> {
        return this.departmentService.restoreDepartment(user._id, deleteDepartmentDto);
    }

    /**
     * Updates a department using the provided information.
     * @param userId The ID of the user updating the department.
     * @param updateDepartmentDto The DTO containing the updated department information.
     * @returns A Promise that resolves to an HttpResponse object.
     */
    @ApiOperation({ summary: "Update Department" })
    @ApiNotFoundResponse({ description: "Department not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Patch("update-department")
    async updateDepartment(
        @GetUser() user: JwtUserPayload,
        @Body() updateDepartmentDto: UpdateDepartmentDto,
    ): Promise<HttpResponse> {
        return this.departmentService.updateDepartment(user._id, updateDepartmentDto);
    }

    /**
     * Retrieves a list of departments based on the provided criteria.
     * @param userId The ID of the user making the request.
     * @param companyId The ID of the company to which the departments belong.
     * @param deleted A boolean value indicating whether to include deleted departments in the results.
     * @param paginationRequestDto The DTO containing pagination information.
     * @returns A Promise that resolves to an HttpResponse object.
     */
    @ApiOperation({ summary: "Get Department" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Get("get-department/deleted/:deleted")
    async getDepartment(
        @GetUser() user: JwtUserPayload,
        @Param("deleted") deleted: boolean,
        @Query() paginationRequestDto: PaginationRequestDto,
    ): Promise<HttpResponse> {
        return this.departmentService.getDepartment(user._id, user.companyId, deleted, paginationRequestDto);
    }
}
