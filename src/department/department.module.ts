import { Modu<PERSON> } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { PositionModule } from "src/position/position.module";
import { RoleModule } from "src/role/role.module";
import { DepartmentController } from "./department.controller";
import { DepartmentService } from "./department.service";
import { DepartmentSchema } from "./schema/department.schema";

@Module({
    imports: [
        MongooseModule.forFeature([{ name: "Department", schema: DepartmentSchema }]),
        // PositionModule,
        // RoleModule,
    ],
    providers: [DepartmentService],
    controllers: [DepartmentController],
    exports: [DepartmentService],
})
export class DepartmentModule {}
