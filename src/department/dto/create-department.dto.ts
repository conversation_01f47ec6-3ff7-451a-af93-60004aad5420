import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsString, IsNotEmpty, IsUUID, IsOptional } from "class-validator";

export class CreateDepartmentDto {
    // @ApiProperty({ description: "Company Id", required: true })
    // @IsUUID()
    // @IsNotEmpty()
    // companyId: string;

    @ApiProperty({ description: "Department Name", required: true })
    @IsString()
    @Transform(({ value }) => value.trim(), { toClassOnly: true })
    @IsNotEmpty()
    name: string;

    @ApiPropertyOptional({ description: "Department Description" })
    @IsString()
    @IsOptional()
    description: string;

    @ApiProperty({ description: "Created By", required: true })
    @IsUUID()
    @IsNotEmpty()
    createdBy: string;
}
