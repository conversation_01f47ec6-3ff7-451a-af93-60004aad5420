import { HttpException, HttpStatus, Injectable, InternalServerErrorException } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";
import { PaginationRequestDto } from "src/company/dto/pagination-request.dto";
import CreatedResponse from "src/shared/http/response/created.http";
import NoContentResponse from "src/shared/http/response/no-content.http";
import OkResponse from "src/shared/http/response/ok.http";
import { CreateDepartmentDto } from "./dto/create-department.dto";
import { DeleteDepartmentDto } from "./dto/delete-department.dto";
import { UpdateDepartmentDto } from "./dto/update-depatment.dto";
import { DepartmentDocument } from "./schema/department.schema";

@Injectable()
export class DepartmentService {
    constructor(@InjectModel("Department") private readonly departmentModel: Model<DepartmentDocument>) {}

    async createDepartment(companyId: string, createDepartmentDto: CreateDepartmentDto) {
        try {
            const department = await this.departmentModel
                .exists({
                    companyId,
                    name: createDepartmentDto.name,
                    deleted: false,
                })
                .exec();
            if (department) throw new HttpException("Department already exists", HttpStatus.BAD_REQUEST);
            const createdDepartment = new this.departmentModel({
                companyId,
                ...createDepartmentDto,
            });
            await createdDepartment.save();
            return new CreatedResponse({ message: "Department created successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async deleteDepartment(userId: string, deleteDepartmentDto: DeleteDepartmentDto) {
        try {
            // const department = await this.departmentModel
            //     .findOne({
            //         _id: deleteDepartmentDto.id,
            //     })
            //     .exec();
            // if (!department) throw new HttpException("Department does not exist", HttpStatus.BAD_REQUEST);
            await this.departmentModel.findOneAndUpdate(
                { _id: deleteDepartmentDto.id, deleted: false },
                {
                    $set: { deleted: true },
                },
                { new: true },
            );
            return new NoContentResponse({ message: "Department deleted successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async restoreDepartment(userId: string, deleteDepartmentDto: DeleteDepartmentDto) {
        try {
            await this.departmentModel.findOneAndUpdate(
                { _id: deleteDepartmentDto.id, deleted: true },
                {
                    $set: { deleted: false },
                },
                { new: true },
            );
            return new OkResponse({ message: "Department restored successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updateDepartment(userId: string, updateDepartmentDto: UpdateDepartmentDto) {
        try {
            await this.departmentModel.findOneAndUpdate(
                { _id: updateDepartmentDto.id, deleted: false },
                {
                    $set: { ...updateDepartmentDto },
                },
                { new: true },
            );
            return new OkResponse({ message: "Department updated successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getDepartment(
        userId: string,
        companyId: string,
        deleted: boolean,
        paginationRequestDto: PaginationRequestDto,
    ) {
        try {
            const limit = paginationRequestDto.limit || 10;
            const offset = limit * (paginationRequestDto.skip || 0);
            const department = await this.departmentModel
                .find({ companyId, deleted })
                .skip(offset)
                .limit(limit);
            return new OkResponse({ department });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async addDefaultDepartments(companyId: string, createdBy: string) {
        try {
            const defaultDepartments = [
                {
                    createdBy,
                    name: "Crew",
                    companyId,
                },
                {
                    createdBy,
                    name: "Office",
                    companyId,
                },
                {
                    createdBy,
                    name: "Sales",
                    companyId,
                },
            ];
            await this.departmentModel.insertMany(defaultDepartments);
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }
}
