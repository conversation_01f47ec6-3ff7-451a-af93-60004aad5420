import { CreateNewActionDto } from "./create-new-action.dto";
import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsOptional, IsString, IsBoolean, IsArray, IsNotEmpty, IsUUID } from "class-validator";
import { Transform } from "class-transformer";

export class MergeContactsDto {
    @ApiProperty({ description: "Array of contact IDs to merge from", type: [String] })
    @IsArray()
    @IsNotEmpty()
    @IsUUID("all", { each: true })
    fromContact: string[];

    @ApiPropertyOptional({ description: "Business name of the contact" })
    @IsOptional()
    @IsString()
    businessName?: string;

    @ApiProperty({ description: "Full name of the contact" })
    @IsString()
    @IsOptional()
    fullName: string;

    @ApiProperty({ description: "First name of the contact" })
    @IsString()
    @IsOptional()
    firstName: string;

    @ApiProperty({ description: "Last name of the contact" })
    @IsString()
    @IsOptional()
    lastName: string;

    @ApiPropertyOptional({ description: "Phone number of the contact" })
    @IsOptional()
    @IsString()
    @Transform(({ value }) => {
        if (value === null || value === undefined) return value;
        return String(value).replace(/\D/g, "");
    })
    phone?: string;

    @ApiPropertyOptional({ description: "Email of the contact" })
    @IsOptional()
    @IsString()
    email?: string;

    @ApiPropertyOptional({ description: "Street address of the contact" })
    @IsOptional()
    @IsString()
    street?: string;

    @ApiPropertyOptional({ description: "City of the contact" })
    @IsOptional()
    @IsString()
    city?: string;

    @ApiPropertyOptional({ description: "State of the contact" })
    @IsOptional()
    @IsString()
    state?: string;

    @ApiPropertyOptional({ description: "Zip code of the contact" })
    @IsOptional()
    @IsString()
    zip?: string;

    @ApiPropertyOptional({ description: "Type of contact" })
    @IsOptional()
    @IsString()
    type?: string;

    @ApiPropertyOptional({ description: "Is business contact" })
    @IsOptional()
    @IsBoolean()
    isBusiness?: boolean;

    @ApiPropertyOptional({ description: "Full address of the contact" })
    @IsOptional()
    @IsString()
    fullAddress?: string;

    @ApiPropertyOptional({ description: "Next action for the contact" })
    @IsOptional()
    nextAction?: any;
}
