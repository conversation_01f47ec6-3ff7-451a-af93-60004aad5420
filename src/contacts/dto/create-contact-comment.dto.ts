import { ApiProperty } from "@nestjs/swagger";
import { IsDate, IsNotEmpty, IsString, IsUUID } from "class-validator";
import { Transform } from "class-transformer";

export class CreateContactCommentDto {
    @ApiProperty({ description: "Comment body", required: true })
    @IsString()
    @IsNotEmpty()
    body: string;

    @ApiProperty({ description: "Current date" })
    @IsDate()
    @Transform(({ value }) => new Date(value))
    @IsNotEmpty()
    currDate: Date;

    @ApiProperty({ description: "Member ID", required: true })
    @IsUUID()
    @IsNotEmpty()
    memberId: string;
}
