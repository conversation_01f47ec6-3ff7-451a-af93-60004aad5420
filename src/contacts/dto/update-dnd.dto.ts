import { ApiPropertyOptional } from "@nestjs/swagger";
import { IsBoolean, IsOptional } from "class-validator";

export class UpdateDndDto {
    @ApiPropertyOptional()
    @IsOptional()
    @IsBoolean()
    email?: boolean;

    @ApiPropertyOptional()
    @IsOptional()
    @IsBoolean()
    sms?: boolean;

    @ApiPropertyOptional()
    @IsOptional()
    @IsBoolean()
    voice?: boolean;

    @ApiPropertyOptional()
    @IsOptional()
    @IsBoolean()
    gbp?: boolean;

    @ApiPropertyOptional()
    @IsOptional()
    @IsBoolean()
    inbound?: boolean;
}
