import { ApiProperty } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsDate, IsNotEmpty, IsOptional, IsString, IsUUID } from "class-validator";
import { ActivityTypeEnum } from "src/crm/enum/activity-type.enum";

export class CreateNewActionDto {
    @ApiProperty({ description: "id of step", required: true })
    @IsNotEmpty()
    id: string;

    @ApiProperty({ description: "Type", required: true })
    @IsString()
    @IsNotEmpty()
    type: ActivityTypeEnum;

    @ApiProperty({ description: "Body", required: true })
    @IsString()
    @IsNotEmpty()
    body: string;

    @ApiProperty({ description: "Due Date", required: true })
    @IsDate()
    @Transform(({ value }) => new Date(value))
    @IsNotEmpty()
    dueDate: Date;

    @ApiProperty({ description: "Current Date", required: true })
    @IsDate()
    @Transform(({ value }) => new Date(value))
    @IsNotEmpty()
    currDate: Date;

    @ApiProperty({ description: "Assign to id", required: false })
    @IsUUID()
    @IsOptional()
    assignTo?: string;
}
