import { ApiPropertyOptional } from "@nestjs/swagger";
import { IsOptional, IsEnum, IsBoolean } from "class-validator";
import { PaginationDto } from "src/shared/dto/pagination.dto";
import { ContactTypeEnum } from "../enum/contact.enum";
import { Transform } from "class-transformer";

export class GetContactDto extends PaginationDto {
    @ApiPropertyOptional({ description: "type" })
    @IsOptional()
    @IsEnum(ContactTypeEnum)
    type?: ContactTypeEnum;

    @ApiPropertyOptional({ description: "search" })
    @IsOptional()
    search?: string;

    @ApiPropertyOptional({ description: "deleted" })
    @IsOptional()
    @IsBoolean()
    @Transform(({ value }) => value === "true" || value === true)
    deleted?: boolean;

    @ApiPropertyOptional({ description: "filter" })
    @IsOptional()
    filter?: any;
}
