import { Modu<PERSON> } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { ContactsController } from "./contacts.controller";
import { ContactsService } from "./contacts.service";
import { ContactSchema } from "./schema/contact.schema";
import { ClientSchema } from "src/client/schema/client.schema";
import { OpportunitySchema } from "src/opportunity/schema/opportunity.schema";
import { PriceSchema } from "src/project/schema/price-schema";
import { ProjectSchema } from "src/project/schema/project.schema";
import { LeadSchema } from "src/lead/schema/lead.schema";
import { ActivityLogSchema } from "src/activity-log/schema/activity-log.schema";
import { OpportunityActivitySchema } from "src/opportunity/schema/opportunity-activity-log.schema";
import { ReferrersSchema } from "src/company/schema/referrers.schema";
import { CrmStageSchema } from "src/crm/schema/crm-stage.schema";

@Module({
    imports: [
        MongooseModule.forFeature([
            { name: "Contact", schema: ContactSchema },
            { name: "Client", schema: ClientSchema },
            { name: "Opportunity", schema: OpportunitySchema },
            { name: "Project", schema: ProjectSchema },
            { name: "Price", schema: PriceSchema },
            { name: "Lead", schema: LeadSchema },
            { name: "ActivityLog", schema: ActivityLogSchema },
            { name: "OpportunityActivity", schema: OpportunityActivitySchema },
            { name: "Referrers", schema: ReferrersSchema },
            { name: "CrmStage", schema: CrmStageSchema },
        ]),
    ],
    controllers: [ContactsController],
    providers: [ContactsService],
    exports: [ContactsService],
})
export class ContactsModule {}
