import { Injectable, InternalServerErrorException } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";
import { randomUUID } from "crypto";
import { ActivityLogDocument } from "./schema/activity-log.schema";
import OkResponse from "src/shared/http/response/ok.http";

@Injectable()
export class ActivityLogService {
    constructor(@InjectModel("ActivityLog") private activityLogModel: Model<ActivityLogDocument>) {}

    async createActivity(data: {
        companyId: string;
        oppId?: string;
        contactId?: string;
        moduleId?: string;
        moduleType: "Opportunity" | "Project" | "Contact";
        body: string;
        memberId: string;
    }) {
        try {
            const { companyId, oppId, contactId, moduleId, moduleType, body, memberId } = data;

            const filter: any = { companyId, moduleType };
            if (oppId) filter.oppId = oppId;
            if (contactId) filter.contactId = contactId;
            if (moduleId) filter.moduleId = moduleId;

            const activity = await this.activityLogModel.updateOne(
                filter,
                {
                    $push: {
                        activities: {
                            _id: randomUUID(),
                            body,
                            createdBy: memberId,
                            createdAt: new Date().toISOString(),
                        },
                    },
                },
                { upsert: true, new: true },
            );

            return new OkResponse({ data: activity, message: "Activity created successfully!" });
        } catch (error: any) {
            throw new InternalServerErrorException(error.message);
        }
    }

    async getActivities(data: {
        companyId: string;
        oppId?: string;
        contactId?: string;
        moduleId?: string;
        moduleType: "Opportunity" | "Project" | "Contact";
    }) {
        try {
            const { companyId, oppId, contactId, moduleId, moduleType } = data;

            const filter: any = { companyId, moduleType };
            if (oppId) filter.oppId = oppId;
            if (contactId) filter.contactId = contactId;
            if (moduleId) filter.moduleId = moduleId;

            const activities = await this.activityLogModel.findOne(filter).sort({ createdAt: -1 });

            return new OkResponse({ data: activities, message: "Activities retrieved successfully!" });
        } catch (error: any) {
            throw new InternalServerErrorException(error.message);
        }
    }
}
