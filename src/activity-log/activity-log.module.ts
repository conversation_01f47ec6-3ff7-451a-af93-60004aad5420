import { Module } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { ActivityLogService } from "./activity-log.service";
import { ActivityLogSchema } from "./schema/activity-log.schema";

@Module({
    imports: [MongooseModule.forFeature([{ name: "ActivityLog", schema: ActivityLogSchema }])],
    providers: [ActivityLogService],
    exports: [ActivityLogService],
})
export class ActivityLogModule {}
