import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsString, Matches, MaxLength, MinLength } from "class-validator";

export class ResetPasswordDto {
    @ApiProperty({ description: "Reset Token", required: true })
    @IsString()
    @IsNotEmpty()
    token: string;

    @ApiProperty({ description: "Password", required: true })
    @IsString()
    @MinLength(8, { message: "Password must contain minimum of 8 characters" })
    @MaxLength(32, { message: "Password must contain maximum of 32 characters" })
    @Matches(/(?:(?=.*\d)|(?=.*\W+))(?![.\n])(?=.*[A-Z])(?=.*[a-z]).*$/, {
        message: "Weak Password",
    })
    @IsNotEmpty()
    password: string;
}
