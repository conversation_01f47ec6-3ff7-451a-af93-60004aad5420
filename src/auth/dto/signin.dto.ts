import { ApiProperty } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsString, IsEmail, IsNotEmpty } from "class-validator";

export class SigninDto {
    @ApiProperty({ description: "Email", required: true })
    @IsEmail()
    @Transform(({ value }) => value.trim().toLowerCase(), { toClassOnly: true })
    @IsNotEmpty()
    email: string;

    @ApiProperty({ description: "Password", required: true })
    @IsString()
    @Transform(({ value }) => value.trim(), { toClassOnly: true })
    @IsNotEmpty()
    password: string;
}
