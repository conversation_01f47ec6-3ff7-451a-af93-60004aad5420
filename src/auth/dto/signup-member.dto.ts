import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { ValidateNested } from "class-validator";
import { InvitationResponseDto } from "src/company/dto/invitation-response.dto";
import { SignupDto } from "./signup.dto";

export class MemberSignupDto {
    @ApiProperty({ type: SignupDto })
    @ValidateNested()
    @Type(() => SignupDto)
    signupDto: SignupDto;

    @ApiProperty({ type: InvitationResponseDto })
    @ValidateNested()
    @Type(() => InvitationResponseDto)
    invitationResponseDto: InvitationResponseDto;
}
