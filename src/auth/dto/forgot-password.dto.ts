import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsEmail, IsOptional, IsString } from "class-validator";

export class ForgotPasswordDto {
    @ApiProperty({ description: "Email", required: true })
    @Transform(({ value }) => value.trim().toLowerCase(), { toClassOnly: true })
    @IsEmail()
    email: string;

    @ApiPropertyOptional({ description: "Domain Name" })
    @IsOptional()
    @IsString()
    domainName?: string;
}
