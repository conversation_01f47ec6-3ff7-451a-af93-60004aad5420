import { Body, Controller, Get, Param, Patch, Post, UseGuards } from "@nestjs/common";
import {
    ApiBearerAuth,
    ApiConflictResponse,
    ApiInternalServerErrorResponse,
    ApiOperation,
    ApiTags,
    ApiUnauthorizedResponse,
} from "@nestjs/swagger";
import HttpResponse from "src/shared/http/response/response.http";
import { AuthService } from "./auth.service";
import { ForgotPasswordDto } from "./dto/forgot-password.dto";
import { ResetPasswordDto } from "./dto/reset-password.dto";
import { SigninDto } from "./dto/signin.dto";
import { UserAuthGuard } from "./guards/jwt-auth.guard";
import { RegisterCompanyDto } from "src/company/dto/register-company.dto";
import { GetUser } from "./decorator/auth.decorator";
import { MemberSignupDto } from "./dto/signup-member.dto";

@ApiTags("Auth")
@Controller({ path: "auth", version: "1" })
export class AuthController {
    constructor(private readonly authService: AuthService) {}

    /**
     *Registers a new company with its user
     *@param registerCompanyDto - the data required to register a new user & its company
     *@returns A success response with a message indicating the user was registered successfully!
     *@throws HttpException if the email already exists or if there is an internal server error
     */
    @ApiOperation({ summary: "Register user" })
    @ApiConflictResponse({ description: "Email already exists" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Post("register-company")
    async registerCompany(@Body() registerCompanyDto: RegisterCompanyDto): Promise<HttpResponse> {
        return this.authService.registerCompany(registerCompanyDto);
    }

    /**
     *Registers a new invited user
     *@param signupDto - the data required to register a new user
     *@returns A success response with a message indicating the user was registered successfully!
     *@throws HttpException if the email already exists or if there is an internal server error
     */
    @ApiOperation({ summary: "Register user" })
    @ApiConflictResponse({ description: "Email already exists" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Post("signup-member")
    async signupMember(@Body() memberSignupDto: MemberSignupDto): Promise<HttpResponse> {
        return this.authService.signupMember(memberSignupDto);
    }

    /**
     *Logs-in an existing user
     *@param signinDto - the data required to log in a user
     *@returns A success response with a message indicating the user was logged in successfully!
     *@throws HttpException if the email or password is invalid or if there is an internal server error
     */
    @ApiOperation({ summary: "Login user" })
    @ApiUnauthorizedResponse({ description: "In case of invalid email or password" })
    @Post("login")
    async signin(@Body() signinDto: SigninDto): Promise<HttpResponse> {
        return this.authService.signin(signinDto, false);
    }

    /**
     * gives new token for expired token
     *@param refreshToken - the refreshToken used to generate new token
     *@returns A success response new token & refreshed token
     *@throws HttpException if the email or password is invalid or if there is an internal server error
     */
    @Post("refresh-token/:refreshToken")
    async refreshAccessToken(@Param("refreshToken") refreshToken: string) {
        return this.authService.refreshAccessToken(refreshToken);
    }

    /**
     *Sends an email to the user with instructions for resetting their password
     *@param forgotPasswordDto - the data required to reset the user's password
     *@returns A success response with a message indicating that the password reset email was sent successfully!
     *@throws HttpException if the email does not exist or if there is an internal server error
     */
    @ApiOperation({ summary: "ForgotPassword" })
    @ApiConflictResponse({ description: "Email doesnot exist" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Post("forgot-password")
    async forgotPassword(@Body() forgotPasswordDto: ForgotPasswordDto): Promise<HttpResponse> {
        return this.authService.forgotPassword(forgotPasswordDto);
    }

    /**
     *Resets the user's password using the provided token and new password
     *@param resetPasswordDto - the data required to reset the user's password
     *@returns A success response with a message indicating that the password was reset successfully!
     *@throws HttpException if the token is invalid or if there is an internal server error
     */
    @ApiOperation({ summary: "ResetPassword" })
    @ApiConflictResponse({ description: "Invalid token" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Post("reset-password")
    async resetPassword(@Body() resetPasswordDto: ResetPasswordDto): Promise<HttpResponse> {
        return this.authService.resetPassword(resetPasswordDto);
    }

    /**
     *Logs the user out by invalidating their token
     *@returns A success response with a message indicating that the user was logged out successfully!
     *@throws HttpException if the token is invalid or if there is an internal server error
     */
    @ApiOperation({ summary: "Logout" })
    @ApiConflictResponse({ description: "Invalid token" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Get("logout")
    @UseGuards(UserAuthGuard)
    async signout(): Promise<HttpResponse> {
        return this.authService.signout();
    }

    @ApiOperation({ summary: "Login mobile user" })
    @ApiUnauthorizedResponse({ description: "In case of invalid email or password" })
    @Post("mobile-login")
    async mobileLogin(@Body() signinDto: SigninDto): Promise<HttpResponse> {
        return this.authService.signin(signinDto, true);
    }

    @ApiOperation({ summary: "Deactivate account" })
    @ApiConflictResponse({ description: "Invalid token" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @ApiBearerAuth()
    @Patch("delete-mobile-account")
    @UseGuards(UserAuthGuard)
    async deleteMobileAccount(@GetUser() user: any): Promise<HttpResponse> {
        return this.authService.deleteMobileAccount(user._id);
    }
}
