import { PermissionsEnum } from "src/shared/enum/permission.enum";

export interface JwtUserPayload {
    _id: string;
    memberId: string;
    companyId: string;
    planType: string;
    positionId?: string;
    role: number;
    iat?: number;
    exp?: number;
    teamPermission?: PermissionsEnum;
    symbol?: string;
}

export interface JwtAdminPayload {
    _id: string;
    email: string;
    role: string;
    iat: number;
    exp: number;
}
