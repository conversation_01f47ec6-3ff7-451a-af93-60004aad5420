import { Injectable, UnauthorizedException } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { ExtractJwt, Strategy } from "passport-jwt";
import { PassportStrategy } from "@nestjs/passport";
import { UserService } from "../../user/user.service";
import { JwtUserPayload } from "../interface/auth.interface";
// import { User } from "src/user/schema/user.schema";

@Injectable()
export class UserJwtStrategy extends PassportStrategy(Strategy, "user-jwt") {
    constructor(private readonly configService: ConfigService) {
        super({
            jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
            ignoreExpiration: false,
            secretOrKey: configService.get<string>("JWT_SECRET"),
        });
    }

    async validate(payload: JwtUserPayload): Promise<JwtUserPayload> {
        if (!payload) throw new UnauthorizedException("Unauthorized token");
        return payload;
    }
}
