import {
    BadRequestException,
    HttpException,
    Injectable,
    InternalServerErrorException,
    HttpStatus,
    UnauthorizedException,
    NotFoundException,
} from "@nestjs/common";
import CreatedResponse from "src/shared/http/response/created.http";
import OkResponse from "src/shared/http/response/ok.http";
import HttpResponse from "src/shared/http/response/response.http";
import { UserService } from "src/user/user.service";
import { SigninDto } from "./dto/signin.dto";
import { ConfigService } from "@nestjs/config";
import { MailService } from "src/mail/mail.service";
import { ForgotPasswordDto } from "./dto/forgot-password.dto";
import { ResetPasswordDto } from "./dto/reset-password.dto";
import * as crypto from "crypto";
import { CompanyService } from "src/company/company.service";
import { InvitationStatusEnum } from "src/company/enum/invitation-status.enum";
import { argon2verify } from "./agron2/argon2";
import { JwtService, JwtSignOptions } from "@nestjs/jwt";
import { RegisterCompanyDto } from "src/company/dto/register-company.dto";
import { JsonWebTokenError, TokenExpiredError } from "jsonwebtoken";
import { MemberSignupDto } from "./dto/signup-member.dto";
import { JwtUserPayload } from "./interface/auth.interface";
import { CompanyAnalyticsService } from "src/company/company-analytics.service";
import { SubcontractorService } from "src/subcontractor/subcontractor.service";
import { UserRolesEnum } from "src/company/enum/role.enum";

@Injectable()
export class AuthService {
    private SECRET_EXPIRES: string;
    private REFRESH_EXPIRES: string;
    constructor(
        private readonly userService: UserService,
        private readonly mailService: MailService,
        private readonly jwtService: JwtService,
        private readonly configService: ConfigService,
        private readonly companyService: CompanyService,
        private readonly subcontractorService: SubcontractorService,
        private readonly companyAnalyticsService: CompanyAnalyticsService,
    ) {
        this.SECRET_EXPIRES = this.configService.get<string>("JWT_SECRET_EXPIRES");
        this.REFRESH_EXPIRES = this.configService.get<string>("JWT_REFRESH_EXPIRES");
    }

    async registerCompany(registerCompanyDto: RegisterCompanyDto): Promise<HttpResponse> {
        try {
            const { signupDto, companyDto } = registerCompanyDto;

            const checkUser = await this.userService.getUserByEmail(signupDto.email);
            if (checkUser) throw new BadRequestException("User Already Exists");

            // Creating User
            const { user } = await this.userService.createUser(signupDto);
            const { _id, email } = user;

            const name =
                signupDto.firstName +
                (signupDto?.lastName && signupDto?.lastName.trim() !== "" ? " " + signupDto.lastName : "");

            // Creating Company, Member & Default Data
            const { memberId, companyId, planType, positionId } = await this.companyService.createCompany(
                _id,
                companyDto,
                name,
                email,
            );

            const payload = {
                _id,
                memberId: memberId,
                companyId: companyId,
                planType: planType,
                positionId: positionId,
                role: UserRolesEnum.Owner, // as creater of company will be owner
            };

            // Sign Token
            //generate token & return token
            const { access_token, userResponse, refresh_token } = await this.generateTokens(payload);

            return new CreatedResponse({ user: userResponse, access_token, refresh_token });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async signupMember(memberSignupDto: MemberSignupDto): Promise<HttpResponse> {
        try {
            const { signupDto, invitationResponseDto } = memberSignupDto;

            let user;
            user = await this.userService.getUserByEmail(signupDto.email);
            if (!user) {
                const userData = await this.userService.createUser(signupDto);
                user = userData.user;
                await this.companyService.invitationResponse(
                    invitationResponseDto.company,
                    invitationResponseDto,
                );
            } else {
                await this.companyService.invitationResponse(
                    invitationResponseDto.company,
                    invitationResponseDto,
                );
                if (signupDto.password !== signupDto.confirmPassword)
                    throw new BadRequestException("Passwords don't match");
                await this.userService.updateUserById(user._id, signupDto.email, {
                    password: signupDto.password,
                    preferredName: memberSignupDto.signupDto?.preferredName,
                });
            }

            const { _id } = user;

            if (invitationResponseDto.status === InvitationStatusEnum.Accepted) {
                const { data } = await this.companyService.getMemberByUserId(
                    _id,
                    invitationResponseDto.company,
                );

                const { userResponse, access_token, refresh_token } = await this.createPayloadAndTokens(
                    _id,
                    signupDto.email,
                    [data.member._id],
                );

                // update company analatyics
                this.companyAnalyticsService.updateCompanyTeamAnalytics(invitationResponseDto.company, {
                    joined: 1,
                });
                return new OkResponse({ user: userResponse, access_token, refresh_token });
            } else return new OkResponse({ message: "Invitation Rejected!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async signin({ email, password }: SigninDto, fromMobile: boolean): Promise<HttpResponse> {
        try {
            const user = await this.userService.getUserByUsernameOrEmailOrToken(email);
            if (!user) throw new HttpException("User does not exist", HttpStatus.BAD_REQUEST);

            // mobile access check
            if (fromMobile && !user.mobileAccess) {
                throw new HttpException(
                    "Mobile login is not permitted for this user",
                    HttpStatus.BAD_REQUEST,
                );
            }

            if (!(await argon2verify(user.password, password))) {
                throw new HttpException("Invalid Email or Password", HttpStatus.BAD_REQUEST);
            }

            const { _id, members } = user;
            const { userResponse, access_token, refresh_token } = await this.createPayloadAndTokens(
                _id,
                email,
                members,
            );
            return new OkResponse({ user: userResponse, access_token, refresh_token });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async createPayloadAndTokens(_id: string, email: string, members: string[], newRefreshToken?: string) {
        const data = (await this.companyService.getUserMemberAndCompany(members)).member;

        if (data?.company?.deleted) throw new NotFoundException("company does not exist");

        const payload = {
            _id,
            memberId: data._id,
            companyId: data.company._id,
            planType: data.company.planType,
            positionId: data?.position?._id || "Undefined",
            role: data?.role,
        };

        //generate token & return token
        return await this.generateTokens(payload, newRefreshToken);
    }

    async refreshAccessToken(refreshToken: string): Promise<HttpResponse> {
        if (!refreshToken) {
            throw new UnauthorizedException("No refresh token provided");
        }

        try {
            // Verify the refresh token and decode its payload
            const refreshPayload = this.jwtService.verify(refreshToken, {
                secret: this.configService.get<string>("JWT_REFRESH_SECRET"),
            });

            if (!refreshPayload) {
                throw new UnauthorizedException("Refresh token data not found");
            }

            // Check if the refresh token is expired
            const currentTime = Math.floor(Date.now() / 1000); // Current time in seconds
            const tokenExpiry = refreshPayload.exp; // Expiration time of the refresh token

            if (currentTime > tokenExpiry) {
                throw new UnauthorizedException("Refresh token has expired. Please log in again.");
            }

            // Fetch the user
            const user = await this.userService.getUserById(refreshPayload._id);
            if (!user) {
                throw new HttpException("User does not exist", HttpStatus.BAD_REQUEST);
            }

            const { _id, members, email } = user;

            // Check if the refresh token is near expiry (e.g., within 1 hour)
            const timeUntilExpiry = tokenExpiry - currentTime;
            let newRefreshToken = refreshToken;

            // If the refresh token is near expiry, generate a new refresh token
            // 4 hour in seconds
            if (timeUntilExpiry < 14400) {
                newRefreshToken = await this.signRefreshToken({
                    _id,
                    memberId: refreshPayload.memberId,
                    companyId: refreshPayload.companyId,
                });
            }

            const { userResponse, access_token, refresh_token } = await this.createPayloadAndTokens(
                _id,
                email,
                members,
                newRefreshToken,
            );

            return new OkResponse({ user: userResponse, access_token, refresh_token });
        } catch (error) {
            if (error instanceof TokenExpiredError) {
                throw new UnauthorizedException("Refresh token has expired. Please log in again.");
            } else if (error instanceof JsonWebTokenError) {
                throw new UnauthorizedException("Invalid refresh token");
            } else {
                throw new InternalServerErrorException(error.message);
            }
        }
    }

    async generateTokens(
        payload: JwtUserPayload,
        refresh_token?: string,
    ): Promise<{ access_token: string; refresh_token: string; userResponse: JwtUserPayload }> {
        const access_token = await this.signToken(payload);
        if (!refresh_token) {
            refresh_token = await this.signRefreshToken({
                _id: payload._id,
                memberId: payload.memberId,
                companyId: payload.companyId,
            });
        }

        const userResponse = {
            ...payload,
            exp: Date.now() + this.convertToMilliseconds(this.SECRET_EXPIRES),
        };

        return { userResponse, access_token, refresh_token };
    }

    async forgotPassword({ email, domainName }: ForgotPasswordDto): Promise<HttpResponse> {
        try {
            const user = await this.userService.getUserByUsernameOrEmailOrToken(email);
            if (!user) throw new HttpException("User does not exist", HttpStatus.BAD_REQUEST);

            // if its nhr redirect to nhrapp.com else pieceworkpro.com
            const frontendBaseUrl =
                domainName && domainName !== "nhrapp"
                    ? this.configService.get<string>("FR_BASE_URL2")
                    : this.configService.get<string>("FR_BASE_URL");

            const resetToken: string = await this.userService.createPasswordResetToken(user);
            const resetURL = `${frontendBaseUrl}/reset-password/${resetToken}`;
            if (!(await this.mailService.sendResetPasswordMail(user, resetURL)).sent) {
                throw new InternalServerErrorException("Email sent failed");
            }
            return new OkResponse({ message: "Email with token has been sent!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async resetPassword(resetPasswordDto: ResetPasswordDto): Promise<HttpResponse> {
        try {
            const { password, token } = resetPasswordDto;
            const hashedToken = crypto.createHash("sha3-256").update(token).digest("hex");
            const user = await this.userService.getUserByUsernameOrEmailOrToken(hashedToken);
            if (!user) throw new BadRequestException("Invalid token");
            const resetTime: number = +user.passwordResetExpires;
            if (Date.now() >= resetTime) throw new BadRequestException("Token expired");
            await this.userService.updateUserById(user._id, user.email, {
                password,
                passwordResetExpires: null,
                passwordResetToken: null,
            });
            return new OkResponse({
                message: "Password has been changed successfully!",
            });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async signout(): Promise<HttpResponse> {
        try {
            return new OkResponse({ user: null, access_token: null });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async deleteMobileAccount(userId: string): Promise<HttpResponse> {
        try {
            const user = await this.userService.getUserById(userId);
            if (!user) throw new BadRequestException("User not found");
            await this.userService.updateUserById(userId, user.email, { mobileAccess: false });

            return new OkResponse({ message: "Mobile account deactivated successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async signToken(payload: string | object, options?: JwtSignOptions): Promise<string> {
        return this.jwtService.sign(payload, options);
    }

    async signRefreshToken(payload: string | object): Promise<string> {
        return this.jwtService.sign(payload, {
            secret: this.configService.get<string>("JWT_REFRESH_SECRET"),
            expiresIn: this.REFRESH_EXPIRES,
        });
    }

    private convertToMilliseconds(timeStr: string): number {
        const timeUnit = timeStr.slice(-1);
        const timeValue = parseInt(timeStr.slice(0, -1), 10);

        switch (timeUnit) {
            case "m": // minutes
                return timeValue * 60 * 1000;
            case "h": // hours
                return timeValue * 60 * 60 * 1000;
            default:
                throw new Error("Unsupported time format");
        }
    }

    // async verifyToken(token: string): Promise<any> {
    //     return this.jwtService.verify(token);
    // }
}
