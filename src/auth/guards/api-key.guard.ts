import { CanActivate, ExecutionContext, Injectable, UnauthorizedException } from "@nestjs/common";
import { AdminService } from "src/admin/admin.service";

@Injectable()
export class ApiKeyGuard implements CanActivate {
    constructor(private readonly adminService: AdminService) {}

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const request = context.switchToHttp().getRequest();
        const apiKey = request.headers["x-api-key"];

        if (!apiKey) {
            throw new UnauthorizedException("API Key is required");
        }

        try {
            await this.adminService.validateApiKey(apiKey);
            return true;
        } catch (error) {
            throw new UnauthorizedException("Invalid API Key");
        }
    }
}
