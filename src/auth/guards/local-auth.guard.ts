import { Injectable, UnauthorizedException } from "@nestjs/common";
import { AuthGuard } from "@nestjs/passport";
import { TokenExpiredError, JsonWebTokenError } from "jsonwebtoken";

@Injectable()
export class AdminAuthGuard extends AuthGuard("admin-jwt") {
    handleRequest(err, user, info: Error) {
        if (info instanceof TokenExpiredError) {
            throw new UnauthorizedException("Token has expired");
        } else if (info instanceof JsonWebTokenError) {
            throw new UnauthorizedException("Invalid token");
        } else if (err || !user) {
            throw new UnauthorizedException("Unauthorized");
        }
        return user;
    }
}
