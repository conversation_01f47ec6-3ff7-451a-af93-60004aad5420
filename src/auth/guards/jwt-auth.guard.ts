import { Injectable, UnauthorizedException } from "@nestjs/common";
import { AuthGuard } from "@nestjs/passport";
import { TokenExpiredError, JsonWebTokenError } from "jsonwebtoken";

@Injectable()
export class UserAuthGuard extends AuthGuard("user-jwt") {
    handleRequest(err, user, info: Error) {
        if (info instanceof TokenExpiredError) {
            throw new UnauthorizedException("Token has expired");
        } else if (info instanceof JsonWebTokenError) {
            throw new UnauthorizedException("Invalid token");
        } else if (err || !user) {
            throw new UnauthorizedException("Unauthorized");
        }
        return user;
    }
}
