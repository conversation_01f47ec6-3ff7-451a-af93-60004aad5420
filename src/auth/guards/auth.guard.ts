import {
    Injectable,
    CanActivate,
    ExecutionContext,
    UnauthorizedException,
    SetMetadata,
    UseGuards,
    applyDecorators,
    InternalServerErrorException,
    HttpException,
    ForbiddenException,
    BadRequestException,
} from "@nestjs/common";
import { Reflector } from "@nestjs/core";
import { JwtService } from "@nestjs/jwt";
import { CompanyService } from "src/company/company.service";
import { PositionService } from "src/position/position.service";
import { RoleService } from "src/role/role.service";
import { UserRolesEnum } from "src/company/enum/role.enum";
import { PermissionsEnum } from "src/shared/enum/permission.enum";
import { TokenExpiredError, JsonWebTokenError } from "jsonwebtoken";
import { JwtUserPayload } from "../interface/auth.interface";

export const POSITIONS_KEY = "positions";
export const ROLES_KEY = "roles";
export const PLAN_KEY = "plans";

@Injectable()
export class CombinedAuthGuard implements CanActivate {
    constructor(
        private readonly jwtService: JwtService,
        private readonly reflector: Reflector,
        private readonly companyService: CompanyService,
        private readonly positionService: PositionService,
        private readonly roleService: RoleService,
    ) {}

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const request = context.switchToHttp().getRequest();
        const method = request.method;
        const authHeader = request.headers.authorization;

        if (!authHeader) {
            throw new UnauthorizedException("No authorization token provided");
        }

        const token = authHeader.split(" ")[1];

        try {
            const resource = this.reflector.get<string>("resource", context.getHandler());

            // if (!resource) {
            //     throw new HttpException("Resource not defined in @Positions decorator", HttpStatus.INTERNAL_SERVER_ERROR);
            // }

            const action = HttpMethodToActionMap[method];
            if (!action) {
                throw new BadRequestException(`Unsupported HTTP method: ${method}`);
            }

            const payload: JwtUserPayload = this.jwtService.verify(token, {
                secret: process.env.JWT_SECRET,
            });

            if (!payload) {
                throw new UnauthorizedException("Invalid token");
            }

            request.user = payload;
            // request.user.role = "member";

            // Retrieve metadata for roles, positions, and plans
            const requiredRoles = this.reflector.getAllAndOverride<UserRolesEnum[]>(ROLES_KEY, [
                context.getHandler(),
                context.getClass(),
            ]);

            const requiredPositions = this.reflector.getAllAndOverride<string[]>(POSITIONS_KEY, [
                context.getHandler(),
                context.getClass(),
            ]);

            const requiredPlans = this.reflector.getAllAndOverride<string[]>(PLAN_KEY, [
                context.getHandler(),
                context.getClass(),
            ]);

            const companyId = payload.companyId;
            const {
                data: { company },
            } = await this.companyService.getCompanyById(false, companyId);

            if (company.deleted) throw new UnauthorizedException("Company does not exist");

            // Check roles
            if (requiredRoles) {
                const hasRole = await this.roleService.checkRole(payload._id, companyId, requiredRoles);
                if (!hasRole) {
                    throw new ForbiddenException("Insufficient roles");
                }
            }

            // Check positions
            if (requiredPositions) {
                const { isAllowed, teamPermission, positionId, symbol } =
                    await this.positionService.checkPermissionNew(
                        payload._id,
                        companyId,
                        requiredPositions,
                        action,
                    );
                if (!isAllowed) {
                    throw new ForbiddenException("Insufficient permissions");
                }
                request.user.positionId = positionId;
                request.user.teamPermission = teamPermission;
                request.user.symbol = symbol;
            }

            // Check plans
            if (requiredPlans) {
                const { hasPlan, planType } = await this.companyService.checkPlanType(
                    payload._id,
                    companyId,
                    requiredPlans,
                );
                request.user.planType = planType;
                if (!hasPlan) {
                    throw new ForbiddenException("Insufficient plan");
                }
            }
            return true;
        } catch (error) {
            if (error instanceof TokenExpiredError) {
                throw new UnauthorizedException("Token has expired");
            } else if (error instanceof JsonWebTokenError) {
                throw new UnauthorizedException("Invalid token");
            } else if (error instanceof HttpException) {
                throw error;
            } else {
                throw new InternalServerErrorException(error.message);
            }
        }
    }
}

// export const Positions = (...positions: any) => {
//     return SetMetadata(POSITIONS_KEY, positions);
// };

export const Positions = ({ category, name, actions }) => {
    return SetMetadata(POSITIONS_KEY, { category, name, actions });
};

export const Roles = (...roles: UserRolesEnum[]) => SetMetadata(ROLES_KEY, roles);

export const Plans = (...plans: any) => {
    return SetMetadata(PLAN_KEY, plans);
};

const HttpMethodToActionMap = {
    GET: "read",
    POST: "write",
    PATCH: "write",
    PUT: "write",
    DELETE: "write",
};
