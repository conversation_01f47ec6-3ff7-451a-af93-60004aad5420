import { Global, Module } from "@nestjs/common";
import { AuthService } from "./auth.service";
import { AuthController } from "./auth.controller";
import { JwtModule } from "@nestjs/jwt";
import { PassportModule } from "@nestjs/passport";
import { UserModule } from "src/user/user.module";
import { MailModule } from "src/mail/mail.module";
import { ConfigModule, ConfigService } from "@nestjs/config";
import { UserJwtStrategy } from "./strategies/user-jwt.strategy";
import { CompanyModule } from "src/company/company.module";
import { PositionModule } from "src/position/position.module";
import { RoleModule } from "src/role/role.module";
import { CombinedAuthGuard } from "./guards/auth.guard";
import { SubcontractorsModule } from "src/subcontractor/subcontractor.module";
// import { StripeModule } from "src/stripe/stripe.module";
@Global()
@Module({
    imports: [
        PassportModule.register({ defaultStrategy: "user-jwt" }),
        JwtModule.registerAsync({
            imports: [ConfigModule],
            inject: [ConfigService],
            useFactory: (configService: ConfigService) => {
                return {
                    secret: configService.get<string>("JWT_SECRET"),
                    signOptions: {
                        expiresIn: configService.get<string>("JWT_SECRET_EXPIRES"),
                    },
                };
            },
        }),
        JwtModule,
        UserModule,
        MailModule,
        ConfigModule,
        PositionModule,
        RoleModule,
        CompanyModule,
        SubcontractorsModule,
    ],
    providers: [AuthService, UserJwtStrategy, CombinedAuthGuard],
    controllers: [AuthController],
    exports: [AuthService, UserJwtStrategy, JwtModule, CompanyModule, PositionModule, RoleModule],
})
export class AuthModule {}
