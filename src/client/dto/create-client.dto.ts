import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Transform, Type } from "class-transformer";
import {
    IsNotEmpty,
    IsOptional,
    IsUUID,
    IsString,
    IsBoolean,
    IsArray,
    ValidateNested,
} from "class-validator";

export class ContactDto {
    @ApiPropertyOptional({ description: " additional first name" })
    @IsString()
    @Transform(({ value }) => value.trim(), { toClassOnly: true })
    @IsOptional()
    firstName: string;

    @ApiPropertyOptional({ description: "additional last name" })
    @IsString()
    @Transform(({ value }) => value.trim(), { toClassOnly: true })
    @IsOptional()
    lastName: string;

    @ApiPropertyOptional({ description: "additional phone" })
    @IsString()
    @IsOptional()
    phone?: string;

    @ApiPropertyOptional({ description: "additional notes" })
    @IsString()
    @IsOptional()
    notes?: string;

    @ApiPropertyOptional({ description: "additional email" })
    @IsString()
    @IsOptional()
    email?: string;
}
export class CreateClientDto {
    // @ApiProperty({ description: "Company Name", required: true })
    // @IsUUID()
    // @IsNotEmpty()
    // companyId: string;

    @ApiPropertyOptional({ description: "Is business", required: true })
    @IsBoolean()
    @IsNotEmpty()
    isBusiness: boolean;

    @ApiProperty({ description: "first name", required: true })
    @IsString()
    @Transform(({ value }) => value.trim(), { toClassOnly: true })
    @IsNotEmpty()
    firstName: string;

    @ApiPropertyOptional({ description: "last name", required: true })
    @IsString()
    @Transform(({ value }) => value.trim(), { toClassOnly: true })
    @IsOptional()
    lastName?: string;

    @ApiPropertyOptional({ description: "street" })
    @IsString()
    @IsOptional()
    street: string;

    @ApiPropertyOptional({ description: "city" })
    @IsString()
    @IsOptional()
    city: string;

    @ApiPropertyOptional({ description: "state" })
    @IsString()
    @IsOptional()
    state: string;

    @ApiPropertyOptional({ description: "zip" })
    @IsString()
    @IsOptional()
    zip: string;

    @ApiProperty({ description: "Phone", required: true })
    @IsString()
    @IsNotEmpty()
    phone: string;

    @ApiPropertyOptional({ description: "Email" })
    // @IsEmail()
    @Transform(({ value }) => value.trim().toLowerCase(), { toClassOnly: true })
    @IsOptional()
    email: string;

    @ApiProperty({ description: "Lead source", required: true })
    @IsUUID()
    @IsNotEmpty()
    leadSource: string;

    @ApiPropertyOptional({ description: "Campaign id" })
    @IsOptional()
    campaignId?: string;

    @ApiProperty({ description: "Lead Source name", required: true })
    @IsString()
    @IsNotEmpty()
    leadSourceName: string;

    @ApiPropertyOptional({ description: "Notes" })
    @IsString()
    @IsOptional()
    notes?: string;

    @ApiProperty({ description: "Create By", required: true })
    @IsUUID()
    @IsNotEmpty()
    createdBy: string;

    @ApiPropertyOptional({ description: "referred by id" })
    @IsString()
    @IsOptional()
    referredBy?: string;

    @ApiProperty({ description: "extra contacts", required: false, type: [ContactDto] })
    @IsOptional()
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => ContactDto)
    contacts?: ContactDto[];
}
