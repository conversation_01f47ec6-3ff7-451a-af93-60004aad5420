import { ApiPropertyOptional } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsOptional } from "class-validator";
import { PaginationDto } from "src/shared/dto/pagination.dto";

export class GetClientFilterDto extends PaginationDto {
    @ApiPropertyOptional({ description: "serach" })
    @IsOptional()
    @Transform(({ value }) => value.trim().toLowerCase(), { toClassOnly: true })
    search?: string;

    @ApiPropertyOptional({ description: "status value" })
    @IsOptional()
    @Transform(({ value }) => (typeof value === "string" ? value.split(",") : value))
    status?: string[];
}
