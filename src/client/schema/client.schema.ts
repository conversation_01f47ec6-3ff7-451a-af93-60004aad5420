import { <PERSON>hem<PERSON>, SchemaFactory, Prop } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import { Document } from "mongoose";
import { ClientStatusEnum } from "../enum/client-status.enum";
import { UUIDProp } from "src/shared/decorator/uuid.decorator";

export type ClientDocument = Client & Document;

export class Order {
    @Prop({ required: true })
    orderId: number;

    @Prop({ required: true })
    orderValue: number;
}

export class Contact {
    @Prop()
    firstName: string;

    @Prop()
    lastName: string;

    @Prop()
    phone: string;

    @Prop()
    notes: string;

    @Prop()
    email: string;
}

@Schema({ timestamps: true, id: false, strict: true, collection: "Client" })
export class Client {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @UUIDProp()
    companyId: string;

    @Prop({ default: false })
    isBusiness: boolean;

    // @Prop()
    // businessName?: string;

    @Prop({ required: true })
    firstName: string;

    @Prop()
    lastName?: string;

    @Prop()
    street?: string;

    @Prop()
    city?: string;

    @Prop()
    state?: string;

    @Prop()
    zip?: string;

    @Prop({ required: true })
    phone: string;

    @Prop()
    notes?: string;

    @Prop()
    email?: string;

    @Prop({ type: () => [Contact], required: false })
    contacts: Contact[];

    @Prop({ required: true })
    leadSource: string;

    @Prop({ required: false })
    campaignId?: string;

    @Prop({ required: true })
    leadSourceName: string;

    @UUIDProp()
    createdBy: string;

    @Prop({ default: false })
    deleted: boolean;

    @Prop({ default: ClientStatusEnum.PROSPECT, type: String, enum: ClientStatusEnum })
    status?: ClientStatusEnum;

    @Prop({ type: () => [Order], required: false })
    orders: Order[];

    @Prop({ required: false })
    referredBy?: string;

    @Prop()
    createdAt?: Date;

    @Prop()
    modifiedAt?: Date;
}

export const ClientSchema = SchemaFactory.createForClass(Client);
