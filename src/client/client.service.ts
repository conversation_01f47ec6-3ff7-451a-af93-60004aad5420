import {
    BadRequestException,
    ConflictException,
    HttpException,
    Injectable,
    InternalServerErrorException,
} from "@nestjs/common";
import { InjectConnection, InjectModel } from "@nestjs/mongoose";
import { Connection, Model } from "mongoose";
import CreatedResponse from "src/shared/http/response/created.http";
import NoContentResponse from "src/shared/http/response/no-content.http";
import OkResponse from "src/shared/http/response/ok.http";
import { CreateClientDto } from "./dto/create-client.dto";
import { DeleteClientDto } from "./dto/delete-client.dto";
import { RestoreClientDto } from "./dto/restore-client-dto";
import { UpdateClientDto } from "./dto/update-client.dto";
import { ClientDocument } from "./schema/client.schema";
import { ClientStatusEnum } from "./enum/client-status.enum";
import { OpportunityDocument } from "src/opportunity/schema/opportunity.schema";
import { GetClientFilterDto } from "./dto/get-client.dto";

@Injectable()
export class ClientService {
    constructor(
        @InjectConnection() private readonly connection: Connection,
        @InjectModel("Client") private readonly clientModel: Model<ClientDocument>,
        @InjectModel("Opportunity") private readonly opportunityModel: Model<OpportunityDocument>,
    ) {}

    async createClient(companyId: string, createClientDto: CreateClientDto) {
        try {
            // const fullName = createClientDto.firstName + " " + createClientDto?.lastName;
            const createdClient = new this.clientModel({
                companyId,
                status: ClientStatusEnum.PROSPECT,
                ...createClientDto,
                // fullName: fullName.trim(),
            });
            await createdClient.save();
            return new CreatedResponse({ message: "Client created successfully!", data: createdClient });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async deleteClient(userId: string, deleteClientDto: DeleteClientDto) {
        try {
            const activeOrder = await this.clientModel.findOne({
                _id: deleteClientDto.id,
                orders: {
                    $elemMatch: {
                        $exists: true,
                        $not: { $eq: {} },
                    },
                },
            });

            if (activeOrder)
                throw new ConflictException(
                    "The Client you're trying to delete is currently in use and cannot be deleted.",
                );

            await this.clientModel.findOneAndUpdate(
                { _id: deleteClientDto.id, deleted: false },
                {
                    $set: { deleted: true },
                },
                { new: true },
            );
            return new NoContentResponse({ message: "Client deleted successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async permDeleteClient(companyId: string, deleteClientDto: DeleteClientDto) {
        try {
            await this.clientModel.deleteOne({
                _id: deleteClientDto.id,
                companyId,
            });
            return new NoContentResponse({ message: "Client deleted permanently!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async restoreClient(userId: string, restoreClientDto: RestoreClientDto) {
        try {
            await this.clientModel.findOneAndUpdate(
                { _id: restoreClientDto.id, deleted: true },
                {
                    $set: { deleted: false },
                },
                { new: true },
            );
            return new OkResponse({ message: "Client restored successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updateClient(userId: string, updateClientDto: UpdateClientDto) {
        try {
            // const fullName = updateClientDto.firstName + " " + updateClientDto?.lastName;
            await this.clientModel.findOneAndUpdate(
                { _id: updateClientDto.clientId },
                {
                    $set: { ...updateClientDto },
                },
                { new: true },
            );
            return new OkResponse({ message: "Client updated successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getClients(
        userId: any,
        companyId: string,
        deleted: boolean,
        getClientFilterDto: GetClientFilterDto,
        isSalesPerson: boolean,
        memberId: string,
    ) {
        try {
            const { search, status } = getClientFilterDto;
            const limit = getClientFilterDto.limit || 10;
            const offset = limit * (getClientFilterDto.skip || 0);

            const matchStage = {
                companyId,
                deleted,
                ...(status?.length ? { status: { $in: status } } : {}),
            };
            // TODO - search to find seprate keys as well
            if (search) {
                matchStage["$or"] = [
                    { firstName: { $regex: search, $options: "i" } },
                    { lastName: { $regex: search, $options: "i" } },
                    { street: { $regex: search, $options: "i" } },
                    { phone: { $regex: search, $options: "i" } },
                    { email: { $regex: search, $options: "i" } },
                    { "contacts.firstName": { $regex: search, $options: "i" } },
                    { "contacts.lastName": { $regex: search, $options: "i" } },
                    { "contacts.phone": { $regex: search, $options: "i" } },
                    { "contacts.email": { $regex: search, $options: "i" } },
                    {
                        $expr: {
                            $regexMatch: {
                                input: { $concat: ["$firstName", " ", "$lastName"] },
                                regex: search,
                                options: "i",
                            },
                        },
                    },
                    {
                        $expr: {
                            $regexMatch: {
                                input: {
                                    $reduce: {
                                        input: {
                                            $map: {
                                                input: "$contacts",
                                                as: "contact",
                                                in: {
                                                    $concat: [
                                                        "$$contact.firstName",
                                                        " ",
                                                        "$$contact.lastName",
                                                    ],
                                                },
                                            },
                                        },
                                        initialValue: "",
                                        in: {
                                            $cond: [
                                                { $eq: ["$$value", ""] },
                                                "$$this",
                                                { $concat: ["$$value", " ", "$$this"] },
                                            ],
                                        },
                                    },
                                },
                                regex: search,
                                options: "i",
                            },
                        },
                    },
                ];
            }
            if (isSalesPerson) {
                const opportunities = await this.opportunityModel.find(
                    {
                        companyId,
                        salesPerson: memberId,
                        deleted: false,
                    },
                    { clientId: 1 },
                );

                if (opportunities.length) {
                    const clients = [...new Set(opportunities.map((opp) => opp.clientId))];
                    matchStage["_id"] = { $in: clients };
                }
            }

            const client = await this.clientModel.aggregate([
                { $match: matchStage },
                {
                    $addFields: {
                        name: {
                            $cond: {
                                if: { $and: [{ $ifNull: ["$lastName", false] }, { $ne: ["$lastName", ""] }] },
                                then: { $concat: ["$lastName", ", ", "$firstName"] },
                                else: "$firstName",
                            },
                        },
                    },
                },
                { $sort: { name: 1 } },
                { $skip: offset },
                { $limit: limit },
                {
                    $lookup: {
                        from: "LeadSource",
                        localField: "leadSource",
                        foreignField: "_id",
                        pipeline: [{ $project: { name: 1 } }],
                        as: "leadSourceData",
                    },
                },
                {
                    $addFields: { leadSourceName: { $arrayElemAt: ["$leadSourceData.name", 0] } },
                },
                { $unset: "leadSourceData" },
                {
                    $project: {
                        firstName: 1,
                        lastName: 1,
                        name: 1,
                        street: 1,
                        city: 1,
                        state: 1,
                        zip: 1,
                        phone: 1,
                        email: 1,
                        status: 1,
                        leadSourceName: 1,
                        leadSource: 1,
                        isBusiness: 1,
                    },
                },
            ]);

            return new OkResponse({ client });
        } catch (error: any) {
            // console.log(error);

            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getClientById(userId: string, companyId: string, clientId: string, deleted: boolean) {
        try {
            const client = await this.clientModel.findOne({ _id: clientId, companyId, deleted });
            return new OkResponse({ client });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getPrevSalesPerson(userId: string, companyId: string, clientId: string) {
        try {
            const opp = await this.opportunityModel
                .findOne({
                    companyId,
                    clientId,
                    saleDate: { $exists: true },
                    deleted: { $ne: true },
                })
                .sort({ saleDate: -1 })
                .populate("salesPerson", "name", "Member")
                .select("salesPerson");

            const salesPerson = opp?.salesPerson;

            return new OkResponse({ salesPerson });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getClientOpportunities(companyId: string, clientId: string) {
        try {
            const clientOpps = await this.opportunityModel
                .find({ companyId, clientId, deleted: false })
                .select(
                    "_id PO num companyId firstName lastName street city state oppType soldValue realRevValue stage",
                )
                .populate("oppType", "name", "ProjectType")
                .populate("stage", null, "CrmStage")
                .populate("clientId", "firstName lastName", "Client")
                .sort({ createdAt: -1 });

            return new OkResponse({ clientOpps });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async migrateClientAndOpp(companyId: string, fromClient: string, toClient: string) {
        const session = await this.connection.startSession();
        session.startTransaction();

        try {
            // Update opportunities and old client in parallel
            const [oldClient, newClient] = await Promise.all([
                this.clientModel.findOneAndUpdate(
                    { companyId, _id: fromClient },
                    { $set: { deleted: true } },
                    { session },
                ),
                this.clientModel.findOne({ _id: toClient }),
                this.opportunityModel.updateMany(
                    { companyId, clientId: fromClient },
                    { $set: { clientId: toClient } },
                    { session },
                ),
            ]);

            if (!oldClient || !newClient) {
                throw new BadRequestException(oldClient ? "New client not found." : "Old client not found.");
            }

            // Helper function to construct address string
            const constructAddress = (client: any) => {
                const parts = [client.street, client.city, client.state, client.zip].filter(Boolean);
                return parts.join(", ") || undefined;
            };

            // Check if all address fields are empty in newClient
            const isAddressEmpty = !newClient.city && !newClient.state && !newClient.street && !newClient.zip;
            const oldAddress = constructAddress(oldClient);

            const addressUpdate = isAddressEmpty
                ? {
                      street: oldClient.street,
                      city: oldClient.city,
                      state: oldClient.state,
                      zip: oldClient.zip,
                  }
                : {};

            const newNotes = oldClient.notes
                ? !isAddressEmpty && oldAddress
                    ? `${oldClient.notes}, Address - ${oldAddress}`
                    : oldClient.notes
                : undefined;

            oldClient.contacts.push({
                firstName: oldClient.firstName,
                ...(oldClient.lastName && { lastName: oldClient.lastName }),
                ...(oldClient.phone && { phone: oldClient.phone }),
                ...(oldClient.email && { email: oldClient.email }),
                ...(newNotes && { notes: newNotes }),
            });

            // Update new client
            const updateResult = await this.clientModel.updateOne(
                { companyId, _id: toClient },
                {
                    $addToSet: { contacts: { $each: oldClient.contacts || [] } },
                    ...(isAddressEmpty ? { $set: addressUpdate } : {}),
                },
                { session },
            );

            if (updateResult.modifiedCount === 0) {
                throw new BadRequestException("Client update failed.");
            }

            await session.commitTransaction();
            return new OkResponse({ message: "Opportunities migrated successfully!" });
        } catch (error: any) {
            await session.abortTransaction();
            throw error instanceof HttpException ? error : new InternalServerErrorException(error.message);
        } finally {
            session.endSession();
        }
    }
}
