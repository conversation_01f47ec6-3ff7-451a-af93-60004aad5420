import { Body, Controller, Delete, Get, Param, ParseUUIDPipe, Patch, Post, Query } from "@nestjs/common";
import {
    ApiBearerAuth,
    ApiInternalServerErrorResponse,
    ApiNotFoundResponse,
    ApiOperation,
    ApiTags,
    ApiUnauthorizedResponse,
} from "@nestjs/swagger";
import { Positions, Roles } from "src/auth/guards/auth.guard";
import { UserRolesEnum } from "src/company/enum/role.enum";
import HttpResponse from "src/shared/http/response/response.http";
import { ClientService } from "./client.service";
import { CreateClientDto } from "./dto/create-client.dto";
import { DeleteClientDto } from "./dto/delete-client.dto";
import { RestoreClientDto } from "./dto/restore-client-dto";
import { UpdateClientDto } from "./dto/update-client.dto";
import { PermissionsEnum } from "src/shared/enum/permission.enum";
import { PositionService } from "src/position/position.service";
import { Auth, GetUser } from "src/auth/decorator/auth.decorator";
import { JwtUserPayload } from "src/auth/interface/auth.interface";
import { moduleNames } from "src/shared/constants/constant";
import { GetClientFilterDto } from "./dto/get-client.dto";

@ApiTags("Client")
@ApiBearerAuth()
@Auth()
@ApiInternalServerErrorResponse({ description: "Server Error" })
@ApiUnauthorizedResponse({ description: "Unauthorized request" })
@Controller({ path: "client", version: "1" })
export class ClientController {
    constructor(
        private readonly clientService: ClientService,
        private readonly positionService: PositionService,
    ) {}

    /**
     * Creates a new client for a company.
     * @param userId The ID of the user creating the client.
     * @param createClientDto The data transfer object containing the client's information.
     * @returns An HTTP response with the result of the operation.
     * @throws ApiConflictException If a client with the same name already exists.
     * @throws ApiInternalServerErrorException If an unexpected error occurs in the server.
     * @throws ApiUnauthorizedException If the request is not authorized.
     */
    @ApiOperation({ summary: "Create Client" })
    @Positions({
        category: "crm",
        name: moduleNames.crm.clients,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed],
    })
    @Post("create-client")
    async createClient(
        @GetUser() user: JwtUserPayload,
        @Body() createClientDto: CreateClientDto,
    ): Promise<HttpResponse> {
        return this.clientService.createClient(user.companyId, createClientDto);
    }

    /**
    Endpoint to delete a client.
    *@summary Delete Client
    *@param {string} userId The ID of the user making the request.
    *@param {DeleteClientDto} deleteClientDto The DTO containing the details of the client to be deleted.
    *@returns {Promise<HttpResponse>} A promise that resolves to an HTTP response indicating the success or failure of the operation.
    *@throws {ApiNotFoundException} If the specified client is not found.
    *@throws {ApiInternalServerErrorException} If an internal server error occurs.
    *@throws {ApiUnauthorizedException} If the user making the request is not authorized to perform the operation.
    */
    @ApiOperation({ summary: "Delete Client" })
    @ApiNotFoundResponse({ description: "Client not found" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Delete("delete-client")
    async deleteClient(
        @GetUser() user: JwtUserPayload,
        @Body() deleteClientDto: DeleteClientDto,
    ): Promise<HttpResponse> {
        return this.clientService.deleteClient(user._id, deleteClientDto);
    }

    /**
    Endpoint to perm delete a client.
    *@summary Perm Delete Client
    *@param {string} userId The ID of the user making the request.
    *@param {DeleteClientDto} deleteClientDto The DTO containing the details of the client to be deleted.
    *@returns {Promise<HttpResponse>} A promise that resolves to an HTTP response indicating the success or failure of the operation.
    *@throws {ApiNotFoundException} If the specified client is not found.
    *@throws {ApiInternalServerErrorException} If an internal server error occurs.
    *@throws {ApiUnauthorizedException} If the user making the request is not authorized to perform the operation.
    */
    @ApiOperation({ summary: "Permanent Delete Client" })
    @ApiNotFoundResponse({ description: "Client not found" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Delete("perm-delete-client")
    async permDeleteClient(
        @GetUser() user: JwtUserPayload,
        @Body() deleteClientDto: DeleteClientDto,
    ): Promise<HttpResponse> {
        return this.clientService.permDeleteClient(user.companyId, deleteClientDto);
    }

    /**
     *Endpoint to restore a previously deleted client.
     *@summary Restore Client
     *@param {string} userId - The ID of the user making the request.
     *@param {DeleteClientDto} restoreClientDto - The DTO containing the ID of the client to be restored.
     *@returns {Promise<HttpResponse>} - An HTTP response indicating whether the operation was successful.
     *@throws {NotFoundException} - If the specified client is not found.
     *@throws {InternalServerErrorException} - If a server error occurs during the operation.
     *@throws {UnauthorizedException} - If the user making the request is not authorized to perform the operation.
     */
    @ApiOperation({ summary: "Restore Client" })
    @ApiNotFoundResponse({ description: "Client not found" })
    @Positions({
        category: "crm",
        name: moduleNames.crm.clients,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed],
    })
    @Patch("restore-client")
    async restoreClient(
        @GetUser() user: JwtUserPayload,
        @Body() restoreClientDto: RestoreClientDto,
    ): Promise<HttpResponse> {
        return this.clientService.restoreClient(user._id, restoreClientDto);
    }

    /**
     *Endpoint to update a client with the given ID.
     *Requires authentication and authorization as an Admin or Owner.
     *@summary Update Client
     *@param {string} userId - The ID of the user performing the request.
     *@param {UpdateClientDto} updateClientDto - The data to use for updating the client.
     *@returns {Promise<HttpResponse>} An HTTP response containing the updated client or an error message.
     *@throws {NotFoundException} If the client with the given ID is not found.
     *@throws {InternalServerErrorException} If a server error occurs.
     */
    @ApiOperation({ summary: "Update client" })
    @ApiNotFoundResponse({ description: "client not found" })
    @Positions({
        category: "crm",
        name: moduleNames.crm.clients,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed],
    })
    @Patch("update-client")
    async updateClient(
        @GetUser() user: JwtUserPayload,
        @Body() updateClientDto: UpdateClientDto,
    ): Promise<HttpResponse> {
        return this.clientService.updateClient(user._id, updateClientDto);
    }

    /**
     *Endpoint to get company client.
     *@summary Get Client
     *@param {string} userId - The ID of the user performing the request.
     *@param {string} companyId - The ID of the company.
     *@param deleted - Whether to include deleted client in the results.
     *@param GetClientFilterDto - The pagination options for the query.
     *@returns A response containing an array of client.
     *@throws {HttpException} When a client error occurs (e.g. invalid input parameters).
     *@throws {InternalServerErrorException} When a server error occurs.
     */
    @ApiOperation({ summary: "Get clients" })
    @Positions({
        category: "crm",
        name: moduleNames.crm.clients,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed],
    })
    @Get("get-client/deleted/:deleted")
    async getClient(
        @GetUser() user: JwtUserPayload,
        @Param("deleted") deleted: boolean,
        @Query() getClientFilterDto: GetClientFilterDto,
    ): Promise<HttpResponse> {
        const { response: isSalesPerson, memberId } = await this.positionService.checkPositionPermission(
            user._id,
            user.companyId,
            ["SalesPerson"],
        );

        return this.clientService.getClients(
            user._id,
            user.companyId,
            deleted,
            getClientFilterDto,
            isSalesPerson,
            memberId,
        );
    }

    /**
     *Endpoint to get company client by id.
     *@summary Get Client by id
     *@param {string} userId - The ID of the user performing the request.
     *@param {string} companyId - The ID of the company.
     *@param {string} companyId - The ID of the client to be fetched.
     *@param deleted - Whether to include deleted client in the results.
     *@returns A response containing the client.
     *@throws {HttpException} When a client error occurs (e.g. invalid input parameters).
     *@throws {InternalServerErrorException} When a server error occurs.
     */
    @ApiOperation({ summary: "Get client by id" })
    @Positions({
        category: "crm",
        name: moduleNames.crm.clients,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed],
    })
    @Get("get-client-by-id/client/:clientId/deleted/:deleted")
    async getClientById(
        @GetUser() user: JwtUserPayload,
        @Param("clientId", ParseUUIDPipe) clientId: string,
        @Param("deleted") deleted: boolean,
    ): Promise<HttpResponse> {
        return this.clientService.getClientById(user._id, user.companyId, clientId, deleted);
    }

    /**
     *Endpoint to get previous assigned salesperson for a client.
     *@summary Get get previous assigned salesperson for a client
     *@param {string} userId - The ID of the user performing the request.
     *@param {string} companyId - The ID of the company.
     *@param {string} clientId - The ID of the client to be fetched.
     *@returns A response containing the salesperson.
     *@throws {HttpException} When a client error occurs (e.g. invalid input parameters).
     *@throws {InternalServerErrorException} When a server error occurs.
     */
    @ApiOperation({ summary: "Get previous assigned salesperson for client" })
    @Get("previous-client-salesperson/client/:clientId")
    async getPrevSalesPerson(
        @GetUser() user: JwtUserPayload,
        @Param("clientId", ParseUUIDPipe) clientId: string,
    ): Promise<HttpResponse> {
        return this.clientService.getPrevSalesPerson(user._id, user.companyId, clientId);
    }

    /**
     * Retrieves all opportunities for a given client
     * @param {string} companyId - The id of the company the client belongs to
     * @param {string} clientId - The id of the client to retrieve opportunities for
     * @returns {Promise<OkResponse>} - Object containing all opportunities for the given client
     * @throws {HttpException|InternalServerErrorException} - Throws HttpException or InternalServerErrorException
     */
    @ApiOperation({ summary: "Retrieves all opportunities for a given client" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Get("opportunity/clientId/:clientId")
    async getClientOpportunities(
        @GetUser() user: JwtUserPayload,
        @Param("clientId", ParseUUIDPipe) clientId: string,
    ): Promise<HttpResponse> {
        return this.clientService.getClientOpportunities(user.companyId, clientId);
    }

    /**
     * Migrates all Opportunities from one client to another
     * @param companyId The id of the company the Opportunities belong to
     * @param fromClient The id of the client to migrate from
     * @param toClient The id of the client to migrate to
     * @returns A OkResponse with a success message, or an error if there was a problem
     */
    @ApiOperation({ summary: "Migrates all Opportunities from one client to another" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Patch("migrate/fromClient/:fromClient/toClient/:toClient")
    async migrateClientAndOpp(
        @GetUser() user: JwtUserPayload,
        @Param("fromClient", ParseUUIDPipe) fromClient: string,
        @Param("toClient", ParseUUIDPipe) toClient: string,
    ): Promise<HttpResponse> {
        return this.clientService.migrateClientAndOpp(user.companyId, fromClient, toClient);
    }
}
