import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { PositionModule } from "src/position/position.module";
import { RoleModule } from "src/role/role.module";
import { ClientController } from "./client.controller";
import { ClientService } from "./client.service";
import { ClientSchema } from "./schema/client.schema";
import { OpportunitySchema } from "src/opportunity/schema/opportunity.schema";
import { CompensationSchema } from "src/compensation/schema/compensation.schema";

@Module({
    imports: [
        MongooseModule.forFeature([
            { name: "Client", schema: ClientSchema },
            { name: "Opportunity", schema: OpportunitySchema },
            // { name: "Compensation", schema: CompensationSchema },
        ]),
        // RoleModule,
        // PositionModule,
    ],
    providers: [ClientService],
    controllers: [ClientController],
    exports: [ClientService],
})
export class ClientModule {}
