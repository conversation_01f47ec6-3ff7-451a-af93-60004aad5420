import { HttpException, HttpStatus, Injectable, InternalServerErrorException } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";
import CreatedResponse from "src/shared/http/response/created.http";
import OkResponse from "src/shared/http/response/ok.http";
import { CreateCrewMemberDto } from "./dto/create-crew-member.dto";
import { CreateCrewDto } from "./dto/create-crew.dto";
import { UpdateCrewDto } from "./dto/update-crew.dto";
import { UpdateCrewMemberDto } from "./dto/update-crew-member.dto";
import { UpdateCrewOrderDto } from "./dto/update-crew-order.dto";
import { CrewDocument } from "./schema/crew-management.schema";
import { CrewMemberDocument } from "./schema/crew-member.schema";
import { PermissionsEnum } from "src/shared/enum/permission.enum";
import { MemberDocument } from "src/company/schema/member.schema";
import { UserService } from "src/user/user.service";
import { PositionService } from "src/position/position.service";
import NoContentResponse from "src/shared/http/response/no-content.http";
import { WageIntervalEnum } from "src/compensation/enum/wage-interval.enum";
import { PieceWorkDocument } from "src/piece-work/schema/piece-work.schema";
import {
    activeCrewMember,
    averagePitch,
    calcCrewLeadBonus,
    dateLessThan,
    dedupeArray,
    findCrewLeadId,
    findCurrentWage,
    isWeekend,
    roundTo2,
    sumArray,
} from "src/shared/helpers/logics";
import { TimeCardDocument } from "src/time-card/schema/time-card.schema";
import { ReActivateCrewDto } from "./dto/re-activate-crew.dto";
import { DailyLogDocument } from "src/daily-log/schema/daily-log.schema";
import { WorkedEnum } from "src/daily-log/enum/worked.enum";
import { CompanySettingDocument } from "src/company/schema/company-setting.schema";
import { OpportunityDocument } from "src/opportunity/schema/opportunity.schema";
import { OrderDocument } from "src/project/schema/order.schema";
import { PieceWorkSettingDocument } from "src/piece-work/schema/piece-work-setting.schema";
import { MaterialDocument } from "src/project/schema/material.schema";
import { TaskDocument } from "src/project/schema/task.schema";
import { ProjectTypeDocument } from "src/project/schema/project-type.schema";
import { CrewPositionDocument } from "src/project/schema/crew-position.schema";
import { CompensationDocument } from "src/compensation/schema/compensation.schema";
import { WorkTaskDocument } from "src/work-task/schema/work-task.schema";
import { TaskTypeEnum } from "src/time-card/enum/task-type.enum";

@Injectable()
export class CrewService {
    constructor(
        private readonly userService: UserService,
        private readonly positionService: PositionService,
        @InjectModel("Crew") private readonly crewModel: Model<CrewDocument>,
        @InjectModel("Member") private readonly memberModel: Model<MemberDocument>,
        @InjectModel("TimeCard") private readonly timeCardModel: Model<TimeCardDocument>,
        @InjectModel("PieceWork") private readonly pieceWorkModel: Model<PieceWorkDocument>,
        @InjectModel("CrewMember") private readonly crewMemberModel: Model<CrewMemberDocument>,
        @InjectModel("DailyLog") private readonly dailyLogModel: Model<DailyLogDocument>,
        @InjectModel("CompanySetting") private readonly companySettingModel: Model<CompanySettingDocument>,
        @InjectModel("Opportunity") private readonly opportunityModel: Model<OpportunityDocument>,
        @InjectModel("Order") private readonly orderModel: Model<OrderDocument>,
        @InjectModel("PieceWorkSetting")
        private readonly pieceWorkSettingModel: Model<PieceWorkSettingDocument>,
        @InjectModel("Material") private readonly materialModel: Model<MaterialDocument>,
        @InjectModel("Task") private readonly taskModel: Model<TaskDocument>,
        @InjectModel("ProjectType") private readonly projectTypeModel: Model<ProjectTypeDocument>,
        @InjectModel("CrewPosition") private readonly crewPositionModel: Model<CrewPositionDocument>,
        @InjectModel("Compensation") private readonly compensationModel: Model<CompensationDocument>,
        @InjectModel("WorkTask") private readonly workTaskModel: Model<WorkTaskDocument>,
    ) {}

    async createCrew(companyId: string, createCrewDto: CreateCrewDto) {
        try {
            const crew = await this.crewModel
                .exists({
                    companyId,
                    name: createCrewDto.name,
                    deleted: false,
                })
                .exec();
            if (crew) throw new HttpException("Crew already exists", HttpStatus.BAD_REQUEST);
            const companyCrews = await this.crewModel
                .find({
                    companyId,
                    deleted: false,
                })
                .exec();
            const maxOrderValue = companyCrews.reduce((acc, value) => {
                return (acc = acc > value.order ? acc : value.order);
            }, 0);
            const createdCrew = new this.crewModel({
                companyId,
                order: maxOrderValue + 1,
                ...createCrewDto,
            });
            await createdCrew.save();
            return new CreatedResponse({ message: "Crew created successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updateCrew(
        userId: string,
        companyId: string,
        updateCrewDto: UpdateCrewDto,
        crewPermission: number,
    ) {
        try {
            const crew = await this.crewModel
                .findOne({
                    _id: updateCrewDto._id,
                    companyId,
                    deleted: false,
                })
                .exec();
            if (!crew) throw new HttpException("Crew does not exist", HttpStatus.BAD_REQUEST);
            await this.positionService.permissionCheck(
                userId,
                companyId,
                crew._id, //not used in case of crew permission check
                crew.managerId,
                crewPermission,
            );
            const query = updateCrewDto.retired
                ? { ...updateCrewDto, endDate: updateCrewDto.retireDate }
                : { ...updateCrewDto };

            const result = await this.crewModel.updateOne(
                { _id: updateCrewDto._id },
                {
                    $set: query,
                },
            );

            // remove crew member if is retired
            if (updateCrewDto.retired) {
                await this.crewMemberModel.updateMany(
                    {
                        crewId: updateCrewDto._id,
                        companyId,
                        deleted: false,
                    },
                    {
                        $set: {
                            deleted: true,
                            removeDate: updateCrewDto.retireDate,
                        },
                    },
                );
            }
            if (result.modifiedCount === 0) {
                return new OkResponse({ message: "Failed to update changes!" });
            }

            return new OkResponse({ message: "Crew edited successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getCompanyCrews(
        userId: string,
        companyId: string,
        deleted: boolean,
        retired: boolean,
        crewPermission: number,
    ) {
        try {
            const userMember = await this.memberModel.findOne({
                company: companyId,
                user: userId,
                deleted: false,
            });
            const userRes = await this.userService.getUserById(userId);
            const crew: any[] = [];
            let crewResponse = [];
            if (crewPermission === PermissionsEnum.Managed) {
                crewResponse = await this.crewModel.aggregate([
                    {
                        $match: {
                            managerId: userMember._id,
                            companyId,
                            deleted: deleted ? deleted : false,
                            retired: retired ? retired : false,
                        },
                    },
                    {
                        $lookup: {
                            from: "Member",
                            localField: "foremanId",
                            foreignField: "_id",
                            as: "foremanMembers",
                        },
                    },
                    {
                        $lookup: {
                            from: "User",
                            localField: "foremanMembers.user",
                            foreignField: "_id",
                            as: "foremanUser",
                        },
                    },
                    {
                        $unwind: {
                            path: "$foremanUser",
                            preserveNullAndEmptyArrays: true,
                        },
                    },
                ]);
            } else if (crewPermission === PermissionsEnum.Full) {
                crewResponse = await this.crewModel.aggregate([
                    {
                        $match: {
                            companyId,
                            deleted: deleted ? deleted : false,
                            retired: retired ? retired : false,
                        },
                    },
                    {
                        $lookup: {
                            from: "Member",
                            localField: "managerId",
                            foreignField: "_id",
                            as: "managerMembers",
                        },
                    },
                    {
                        $lookup: {
                            from: "Member",
                            localField: "foremanId",
                            foreignField: "_id",
                            as: "foremanMembers",
                        },
                    },
                    {
                        $lookup: {
                            from: "User",
                            localField: "managerMembers.user",
                            foreignField: "_id",
                            as: "managerUser",
                        },
                    },
                    {
                        $lookup: {
                            from: "User",
                            localField: "foremanMembers.user",
                            foreignField: "_id",
                            as: "foremanUser",
                        },
                    },
                    {
                        $unwind: {
                            path: "$managerUser",
                            preserveNullAndEmptyArrays: true,
                        },
                    },
                    {
                        $unwind: {
                            path: "$foremanUser",
                            preserveNullAndEmptyArrays: true,
                        },
                    },
                ]);
            } else if (crewPermission === PermissionsEnum.Self) {
                crewResponse = await this.crewModel.aggregate([
                    {
                        $match: {
                            foremanId: userMember._id,
                            companyId,
                            deleted: deleted ? deleted : false,
                            retired: retired ? retired : false,
                        },
                    },
                    {
                        $lookup: {
                            from: "Member",
                            localField: "foremanId",
                            foreignField: "_id",
                            as: "foremanMembers",
                        },
                    },
                    {
                        $lookup: {
                            from: "User",
                            localField: "foremanMembers.user",
                            foreignField: "_id",
                            as: "foremanUser",
                        },
                    },
                    {
                        $unwind: {
                            path: "$foremanUser",
                            preserveNullAndEmptyArrays: true,
                        },
                    },
                ]);
            }

            crewResponse.length > 0
                ? crewResponse.forEach(function (eachObj) {
                      const obj = {
                          _id: eachObj._id,
                          name: eachObj.name,
                          startDate: eachObj.startDate,
                          workType: eachObj.workType,
                          order: eachObj.order,
                          managerId: eachObj.managerId,
                          managerName: eachObj?.managerUser
                              ? crewPermission === PermissionsEnum.Full
                                  ? eachObj.managerUser.firstName +
                                    (eachObj.managerUser?.lastName ? " " + eachObj.managerUser?.lastName : "")
                                  : userRes.firstName + (userRes?.lastName ? " " + userRes?.lastName : "")
                              : undefined,
                          foremanId: eachObj.foremanId,
                          foremanName: eachObj.foremanUser
                              ? eachObj.foremanUser.firstName +
                                (eachObj.foremanUser?.lastName ? " " + eachObj.foremanUser?.lastName : "")
                              : undefined,
                          retired: eachObj.retired,
                          deleted: eachObj.deleted,
                          retireDate: eachObj.retireDate,
                      };
                      crew.push(obj);
                  })
                : [];
            return new OkResponse({ crew });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getCrewById(userId: string, companyId: string, crewId: string) {
        try {
            const crew = [];
            const crewResponse = await this.crewModel.aggregate([
                {
                    $match: {
                        _id: crewId,
                        companyId,
                    },
                },
                {
                    $lookup: {
                        from: "Member",
                        localField: "managerId",
                        foreignField: "_id",
                        as: "managerMembers",
                    },
                },
                {
                    $lookup: {
                        from: "User",
                        localField: "managerMembers.user",
                        foreignField: "_id",
                        as: "managerUser",
                    },
                },
                {
                    $unwind: {
                        preserveNullAndEmptyArrays: true,
                        path: "$managerUser",
                    },
                },
            ]);
            crewResponse.length > 0
                ? crewResponse.forEach(function (eachObj) {
                      const obj = {
                          _id: eachObj._id,
                          name: eachObj.name,
                          startDate: eachObj.startDate,
                          workType: eachObj.workType,
                          order: eachObj.order,
                          managerId: eachObj?.managerId,
                          managerName: eachObj?.managerUser
                              ? eachObj.managerUser.firstName +
                                (eachObj.managerUser?.lastName ? " " + eachObj.managerUser.lastName : "")
                              : undefined,
                          foremanId: eachObj.foremanId,
                          deleted: eachObj.deleted,
                      };
                      crew.push(obj);
                  })
                : [];
            if (!crew.length) throw new HttpException("Crew does not exist", HttpStatus.BAD_REQUEST);
            return new OkResponse({ crew });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updateCrewOrderNumber(
        userId: string,
        companyId: string,
        updateCrewOrderDto: UpdateCrewOrderDto,
        crewPermission: number,
    ) {
        try {
            const crew = await this.crewModel
                .findOne({
                    _id: updateCrewOrderDto._id,
                    companyId,
                })
                .exec();
            if (!crew) throw new HttpException("Crew does not exist", HttpStatus.BAD_REQUEST);
            await this.positionService.permissionCheck(
                userId,
                companyId,
                crew._id, //not used in case of crew permission check
                crew.managerId,
                crewPermission,
            );
            const companyCrews =
                +updateCrewOrderDto.order < crew.order
                    ? await this.crewModel
                          .find({
                              order: {
                                  $lt: crew.order,
                                  $gte: updateCrewOrderDto.order,
                              },
                              companyId,
                              deleted: false,
                          })
                          .exec()
                    : await this.crewModel
                          .find({
                              order: {
                                  $lte: +updateCrewOrderDto.order,
                                  $gte: crew.order + 1,
                              },
                              companyId,
                              deleted: false,
                          })
                          .exec();
            for (const companyCrew of companyCrews) {
                await this.crewModel.updateOne(
                    { _id: companyCrew._id },
                    {
                        order:
                            +updateCrewOrderDto.order < crew.order
                                ? companyCrew.order + 1
                                : companyCrew.order - 1,
                    },
                );
            }
            await this.crewModel.updateOne(
                { _id: updateCrewOrderDto._id },
                { order: updateCrewOrderDto.order },
            );
            return new OkResponse({ companyCrews });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async createCrewMember(
        userId: string,
        companyId: string,
        createCrewMemberDto: CreateCrewMemberDto,
        crewPermission: number,
    ) {
        try {
            const crew = await this.crewModel
                .findOne({
                    _id: createCrewMemberDto.crewId,
                    companyId,
                    deleted: false,
                })
                .exec();
            if (!crew) throw new HttpException("Crew does not exist", HttpStatus.BAD_REQUEST);
            await this.positionService.permissionCheck(
                userId,
                companyId,
                crew._id, //not used in case of crew permission check
                crew.managerId,
                crewPermission,
            );
            const checkDate = dateLessThan(createCrewMemberDto.startDate, crew.startDate);
            if (checkDate)
                throw new HttpException(
                    "Member join date should be on or after crew start date",
                    HttpStatus.BAD_REQUEST,
                );
            const user = await this.userService.getUserByMemberId(createCrewMemberDto.memberId);
            const memberName =
                user?.preferredName && user?.preferredName !== "" && user?.lastName && user?.lastName !== ""
                    ? `${user.preferredName} ${user?.lastName}`
                    : user.firstName + (user?.lastName ? " " + user?.lastName : "");
            const createdCrewMember = new this.crewMemberModel({
                memberName,
                preferredName: user?.preferredName || "",
                companyId,
                ...createCrewMemberDto,
            });
            await createdCrewMember.save();
            return new CreatedResponse({ message: "Crew Member created successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updateCrewMember(
        userId: string,
        companyId: string,
        updateCrewMemberDto: UpdateCrewMemberDto,
        crewPermission: number,
    ) {
        try {
            const crew = await this.crewModel
                .findOne({
                    _id: updateCrewMemberDto.crewId,
                    companyId,
                    deleted: false,
                })
                .exec();
            if (!crew) throw new HttpException("Crew does not exist", HttpStatus.BAD_REQUEST);
            await this.positionService.permissionCheck(
                userId,
                companyId,
                crew._id, //not used in case of crew permission check
                crew.managerId,
                crewPermission,
            );
            const existingCrewMember = await this.crewMemberModel
                .findOne({
                    _id: updateCrewMemberDto.crewMemberId,
                    crewId: updateCrewMemberDto.crewId,
                    deleted: false,
                })
                .exec();
            if (!existingCrewMember)
                throw new HttpException("Crew Member does not exists", HttpStatus.BAD_REQUEST);
            const checkStartDate = dateLessThan(updateCrewMemberDto.startDate, crew.startDate);
            if (checkStartDate)
                throw new HttpException(
                    "Member join date should be on or after crew start date",
                    HttpStatus.BAD_REQUEST,
                );
            await this.crewMemberModel.findOneAndUpdate(
                { _id: updateCrewMemberDto.crewMemberId },
                {
                    $set: {
                        startDate: updateCrewMemberDto.startDate,
                    },
                },
                { new: true },
            );
            return new OkResponse({ message: "Crew Member edited successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async promoteCrewMember(
        userId: string,
        companyId: string,
        updateCrewMemberDto: UpdateCrewMemberDto,
        crewPermission: number,
    ) {
        try {
            const crew = await this.crewModel
                .findOne({
                    _id: updateCrewMemberDto.crewId,
                    companyId,
                    deleted: false,
                })
                .exec();
            if (!crew) throw new HttpException("Crew does not exist", HttpStatus.BAD_REQUEST);
            await this.positionService.permissionCheck(
                userId,
                companyId,
                crew._id, //not used in case of crew permission check
                crew.managerId,
                crewPermission,
            );
            const existingCrewMember = await this.crewMemberModel
                .findOne({
                    _id: updateCrewMemberDto.crewMemberId,
                    deleted: false,
                })
                .exec();
            if (!existingCrewMember)
                throw new HttpException("Crew Member does not exists", HttpStatus.BAD_REQUEST);
            const checkPromoteDate = dateLessThan(
                updateCrewMemberDto.promoteDate,
                existingCrewMember.startDate,
            );
            if (checkPromoteDate)
                throw new HttpException("Promotion date must be after join date", HttpStatus.BAD_REQUEST);
            await this.crewMemberModel.updateOne(
                {
                    companyId,
                    crewId: updateCrewMemberDto.crewId,
                    deleted: false,
                    promoted: true,
                },
                {
                    $set: {
                        promoted: false,
                    },
                },
            );

            const crewMemb = await this.crewMemberModel.findOneAndUpdate(
                { _id: updateCrewMemberDto.crewMemberId },
                {
                    $set: {
                        promoted: true,
                        promoteDate: updateCrewMemberDto.promoteDate,
                    },
                },
                { new: true },
            );

            await this.crewModel.updateOne(
                { _id: updateCrewMemberDto.crewId },
                {
                    $set: {
                        foremanId: updateCrewMemberDto.memberId,
                        leadDate: updateCrewMemberDto.promoteDate,
                    },
                    $push: {
                        leadHistory: {
                            lead: updateCrewMemberDto.crewMemberId,
                            leadMemberId: crewMemb.memberId,
                            leadName: crewMemb.memberName,
                            promotionDate: updateCrewMemberDto.promoteDate,
                            promotedBy: userId,
                            promotedAt: new Date(),
                        },
                    },
                },
                { new: true },
            );
            return new OkResponse({ message: "Crew Member promoted successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async removeCrewMember(
        userId: string,
        companyId: string,
        updateCrewMemberDto: UpdateCrewMemberDto,
        crewPermission: number,
    ) {
        try {
            const crew = await this.crewModel
                .findOne({
                    _id: updateCrewMemberDto.crewId,
                    companyId,
                    deleted: false,
                })
                .exec();
            if (!crew) throw new HttpException("Crew does not exist", HttpStatus.BAD_REQUEST);
            await this.positionService.permissionCheck(
                userId,
                companyId,
                crew._id, //not used in case of crew permission check
                crew.managerId,
                crewPermission,
            );
            const existingCrewMember = await this.crewMemberModel
                .findOne({
                    _id: updateCrewMemberDto.crewMemberId,
                    crewId: updateCrewMemberDto.crewId,
                    deleted: false,
                })
                .exec();
            if (!existingCrewMember)
                throw new HttpException("Crew Member does not exists", HttpStatus.BAD_REQUEST);
            const checkRemoveDate = dateLessThan(
                updateCrewMemberDto.removeDate,
                existingCrewMember.startDate,
            );
            if (checkRemoveDate)
                throw new HttpException("Cannot leave crew before joining", HttpStatus.BAD_REQUEST);
            await this.crewMemberModel.findOneAndUpdate(
                { _id: updateCrewMemberDto.crewMemberId, crewId: updateCrewMemberDto.crewId },
                {
                    $set: {
                        deleted: true,
                        removeDate: updateCrewMemberDto.removeDate,
                    },
                },
                { new: true },
            );
            return new NoContentResponse({ message: "Crew Member removed successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getCrewMembers(userId: string, companyId: string, crewId: string, crewPermission: number) {
        try {
            const crew = await this.crewModel
                .findOne({
                    _id: crewId,
                    companyId,
                    deleted: false,
                })
                .exec();
            if (!crew) throw new HttpException("Crew does not exist", HttpStatus.BAD_REQUEST);
            await this.positionService.permissionCheck(
                userId,
                companyId,
                crew._id, //not used in case of crew permission check
                crew.managerId,
                crewPermission,
            );
            const crewMembers = [];
            const crewMembersResponse = await this.crewMemberModel.aggregate([
                {
                    $match: {
                        companyId,
                        crewId,
                        deleted: false,
                    },
                },
                {
                    $lookup: {
                        from: "Member",
                        localField: "memberId",
                        foreignField: "_id",
                        as: "member",
                    },
                },
                {
                    $lookup: {
                        from: "User",
                        localField: "member.user",
                        foreignField: "_id",
                        as: "user",
                    },
                },
                {
                    $unwind: {
                        path: "$user",
                    },
                },
            ]);
            crewMembersResponse.length > 0
                ? crewMembersResponse.forEach(function (eachObj) {
                      const obj = {
                          _id: eachObj._id,
                          crewId: eachObj.crewId,
                          memberId: eachObj.memberId,
                          memberName:
                              eachObj.user.firstName +
                              (eachObj.user?.lastName ? " " + eachObj.user?.lastName : ""),
                          startDate: eachObj.startDate,
                          promoted: eachObj.promoted,
                          deleted: eachObj.deleted,
                      };
                      crewMembers.push(obj);
                  })
                : [];
            return new OkResponse({ crewMembers });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getNonCrewMembers(userId: string, companyId: string) {
        try {
            const allTeamMembers = await this.memberModel.aggregate([
                {
                    $match: {
                        company: companyId,
                        deleted: false,
                    },
                },
                {
                    $lookup: {
                        from: "User",
                        localField: "user",
                        foreignField: "_id",
                        as: "user",
                    },
                },
                {
                    $unwind: {
                        path: "$user",
                    },
                },
                {
                    $project: {
                        _id: 1,
                        company: 1,
                        email: 1,
                        firstName: "$user.firstName",
                        lastName: "$user.lastName",
                    },
                },
            ]);
            const existingCrewMembers = await this.crewModel.aggregate([
                {
                    $match: {
                        companyId,
                        deleted: { $ne: true },
                        retired: { $ne: true },
                    },
                },
                {
                    $lookup: {
                        from: "CrewMember",
                        pipeline: [{ $match: { deleted: false } }],
                        localField: "_id",
                        foreignField: "crewId",
                        as: "members",
                    },
                },
                { $unwind: "$members" },
                { $project: { _id: 0, members: 1 } },
            ]);

            const filteredMembers = allTeamMembers.filter((member) =>
                existingCrewMembers.every((crewMember) => !(crewMember.members.memberId === member._id)),
            );

            return new OkResponse({ filteredMembers });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }
    async getCrewMemberById(userId: string, companyId: string, crewId: string, crewPermission: number) {
        try {
            const crew = await this.crewModel
                .findOne({
                    _id: crewId,
                    companyId,
                    deleted: false,
                })
                .exec();
            if (!crew) throw new HttpException("Crew does not exist", HttpStatus.BAD_REQUEST);
            await this.positionService.permissionCheck(
                userId,
                companyId,
                crew._id, //not used in case of crew permission check
                crew.managerId,
                crewPermission,
            );
            const crewMember = await this.crewModel
                .findOne({
                    _id: crewId,
                    companyId,
                })
                .exec();
            if (!crewMember) throw new HttpException("Crew Member does not exist", HttpStatus.BAD_REQUEST);
            return new OkResponse({ crewMember });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getCrewsToApprove(
        logInMemberId: string,
        companyId: string,
        startDate: Date,
        endDate: Date,
        crewPermission,
    ) {
        try {
            const approveStart = new Date(startDate);
            const approveEnd = new Date(endDate);

            let query;
            if (crewPermission === PermissionsEnum.Managed) {
                query = {
                    // managerId: userMember._id,
                    $and: [{ $or: [{ managerId: logInMemberId }, { foremanId: logInMemberId }] }],
                    companyId,
                    startDate: { $lte: approveStart },
                    $or: [{ endDate: { $exists: false } }, { endDate: { $gte: approveStart } }],
                    deleted: { $ne: true },
                    // retired: { $ne: true },
                };
            } else if (crewPermission === PermissionsEnum.Full) {
                query = {
                    companyId,
                    startDate: { $lte: approveStart },
                    $or: [{ endDate: { $exists: false } }, { endDate: { $gte: approveStart } }],
                    deleted: { $ne: true },
                    // retired: { $ne: true },
                };
            }

            const [crews, companySetting, allWorkTaskData, pwSettingAllData] = await Promise.all([
                this.crewModel.aggregate([
                    {
                        $match: query,
                    },
                    {
                        $lookup: {
                            from: "CrewMember",
                            pipeline: [
                                {
                                    $match: {
                                        startDate: { $lte: approveStart },
                                        $or: [
                                            { removeDate: { $exists: false } },
                                            { removeDate: { $gte: approveStart } },
                                        ],
                                    },
                                },
                                {
                                    $sort: { startDate: 1 },
                                },
                            ],
                            localField: "_id",
                            foreignField: "crewId",
                            as: "members",
                        },
                    },
                    {
                        $sort: { order: 1 },
                    },
                ]),

                this.companySettingModel.findOne({
                    companyId,
                }),

                this.workTaskModel.find({ companyId }),

                this.pieceWorkSettingModel.find({
                    companyId: companyId,
                }),
            ]);
            const report = [];
            for (const crew of crews) {
                const crewReport: any = {
                    _id: crew._id,
                    name: crew.name,
                    order: crew.order,
                    dayOff: false,
                    numWorking: 0,
                    timeWorked: 0,
                    paid: 0,
                    leadIndex: 0,
                    members: [],
                };
                const dailyLog = await this.dailyLogModel.findOne({
                    crewId: crew._id,
                    date: { $gte: approveStart, $lt: approveEnd },
                    deleted: { $ne: true },
                });
                const logStats = {
                    _id: dailyLog?._id,
                    dayVol: 0,
                    dayRR: 0,
                    dayBudget: 0,
                    projects: [],
                };
                if (dailyLog) {
                    if (dailyLog.worked == WorkedEnum.None) crewReport.dayOff = true;

                    await Promise.all(
                        dailyLog.projects.map(async (projectLog) => {
                            if (projectLog.oppId === "" || !projectLog.oppId) return;
                            const stats = await this.projectDayCompleted(projectLog, companyId);

                            logStats.dayVol += stats.vol;
                            logStats.dayRR += stats.rr;
                            logStats.dayBudget += stats.budget;
                            logStats.projects.push(stats);
                        }),
                    );
                }

                crewReport.logStats = logStats;
                // finding crew lead
                const crewLeadId = findCrewLeadId(crew, approveStart);

                crewReport.members = [];
                const filtered = !crew.members
                    ? []
                    : crew.members.filter((member) => {
                          return activeCrewMember(member, approveStart, approveEnd);
                      });

                const POListSet = new Set();
                for (const member of filtered) {
                    const memObj: any = await this.memberDayActivity(
                        companyId,
                        member.memberId,
                        approveStart,
                        approveEnd,
                        crewLeadId,
                        pwSettingAllData,
                        allWorkTaskData,
                        companySetting,
                    );

                    memObj.POList.forEach((p) => {
                        if (p !== "companyDefaultPO" && p !== "") {
                            POListSet.add(p);
                        }
                    });
                    memObj.name = member.memberName;
                    memObj.officePerson = false;

                    // Separate out non crew salary workers
                    if (!memObj.pwExtraHourRate && memObj.wageType == "salary") {
                        memObj.officePerson = true;
                    }

                    // // OFF FOR NOW
                    if (!memObj.dayOff && memObj?.cards.length) crewReport.numWorking++;
                    crewReport.timeWorked += memObj.hours;
                    await crewReport.members.push(memObj);
                }
                crewReport.POList = await this.getPOList(POListSet, companyId);

                crewReport.members?.map((member, index) => {
                    if (member.memberId === crewLeadId) {
                        const leadPay = calcCrewLeadBonus(crewReport.members);
                        crewReport.leadIndex = index;
                        member.leadBonus = member.dayOff ? 0 : member?.cards?.length === 0 ? 0 : leadPay;

                        member.totalEarned += member.leadBonus;
                    }
                });

                // Put crew lead in the front
                if (crewReport.leadIndex) {
                    const leadElement = crewReport.members?.splice(crewReport.leadIndex, 1);
                    crewReport.members?.splice(0, 0, leadElement[0]);
                }
                crewReport.paid = sumArray(crewReport.members, "totalEarned");
                crewReport.rrDollar = crewReport.logStats?.dayRR / crewReport.paid;
                crewReport.timeWorked = roundTo2(crewReport.timeWorked);
                crewReport.crewId = crew._id;
                crewReport.name = crew.name;
                // for (const member of crewReport.members) {
                //     const compensation = await this.compensationModel.findOne({ memberId: member.memberId });
                //     if (compensation) {
                //         member.positionId = compensation.positionId; // Add positionId to member
                //     }
                // }

                report.push(crewReport);
            }
            return new OkResponse({ report });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getPOList(POArr: any, companyId: string) {
        try {
            const pipeline: any[] = [
                {
                    $match: {
                        companyId,
                        deleted: { $ne: true },
                        _id: { $in: [...POArr] },
                    },
                },
                {
                    $lookup: {
                        from: "ProjectType",
                        localField: "acceptedType",
                        foreignField: "_id",
                        as: "type",
                    },
                },
                {
                    $project: {
                        _id: 1,
                        PO: 1,
                        num: 1,
                        zip: 1,
                        jobCompletedDate: 1,
                        type: { $arrayElemAt: ["$type", 0] },
                    },
                },
            ];

            const readyOpps = await this.opportunityModel.aggregate(pipeline);

            const mappedProjects = readyOpps.map((opp) => ({
                oppId: opp._id,
                po: opp.PO + "-" + opp.num,
                type: opp.type?.typeReplacement,
                typeName: opp.type?.name,
                zipCode: opp?.zip,
                jobCompletedDate: opp?.jobCompletedDate,
            }));

            return mappedProjects;
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getTimeCardsForQuery(query) {
        try {
            const timeCards = await this.timeCardModel
                .aggregate([
                    {
                        $match: query,
                    },
                    {
                        $lookup: {
                            from: "Opportunity",
                            foreignField: "_id",
                            localField: "projectId",
                            as: "opp",
                            pipeline: [{ $project: { PO: 1, num: 1 } }],
                        },
                    },
                    {
                        $unwind: {
                            path: "$opp",
                            preserveNullAndEmptyArrays: true,
                        },
                    },
                    {
                        $addFields: {
                            projectPO: {
                                $cond: {
                                    // if: { $gt: [{ $size: { $ifNull: [{ $objectToArray: "$opp" }, []] } }, 0] },
                                    if: { $eq: [{ $type: "$opp" }, "object"] },
                                    then: { $concat: ["$opp.PO", "-", "$opp.num"] },
                                    else: "$projectPO",
                                },
                            },
                        },
                    },
                    { $unset: "opp" },
                    {
                        $sort: { timeIn: 1 },
                    },
                ])
                .exec();

            return timeCards;
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     *
     * @param companyId
     * @param memberId
     * @param start - date
     * @param end - date
     * @param pwSettingAllData - pieceWork setting
     * @param allWorkTaskData - work task
     * @param variables - company setting
     * @returns
     */
    async memberDayActivity(
        companyId: string,
        memberId: string,
        start: Date,
        end: Date,
        crewLeadId: string,
        pwSettingAllData,
        allWorkTaskData,
        variables,
    ) {
        try {
            // const start = new Date(date);
            // start.setHours(0, 0, 0, 0);
            // const end = new Date(date);
            // end.setHours(23, 59, 59, 999);
            const report: any = {
                memberId,
                positionId: "",
                date: start,
                day: start.getDay(),
                dayOff: false,
                ptoUsed: false,
                crewLead: crewLeadId === memberId,
                dayBase: 0,
                hours: 0,
                pwSqs: 0, // 'sqsEarnings'
                pwExtras: 0, // 'extrasEarnings'
                pwHourly: 0, // 'hourlyEarnings'
                pwTotal: 0,
                removeFromLead: 0, // $ amount to remove from lead bonus calc
                travel: 0,
                projects: [],
                cards: [],
                POList: [],
            };
            // Find wage type and amount
            const userCompensation = await this.compensationModel.findOne({ memberId });

            const wage = findCurrentWage(userCompensation?.wageHistory, start);
            report.positionId = wage?.positionId;

            if (wage) {
                // assigning lead bonus for calculations
                if (crewLeadId === memberId) report.crewPieceWork = wage?.crewPieceWork || 0;

                // wage type
                if (wage.wageInterval === WageIntervalEnum.Year) {
                    report.wageType = "salary";
                    report.wage = roundTo2(+wage.wageAmount / 52 / 40);
                } else if (wage.wageInterval === WageIntervalEnum.Hour && wage.wageAmount) {
                    report.wageType = "hourly";
                    report.wage = +wage.wageAmount;
                } else {
                    report.wageType = "pieceWork";
                    report.wage = 0;
                }
                report.pwExtraHourRate = +wage.pieceWorkHourlyRate;
            }

            const timeCards = await this.getTimeCardsForQuery({
                memberId,
                timeIn: { $gte: start, $lte: end },
                deleted: { $ne: true },
            });
            // Find piece work
            const cardArray = timeCards.map((card) => card._id);
            const pieceWork = await this.pieceWorkModel
                .find({
                    timeCardId: { $in: cardArray },
                    deleted: { $ne: true },
                })
                .exec();

            // Attach piece work to time card
            timeCards.forEach((card) => {
                pieceWork.forEach((work) => {
                    if (card._id === work.timeCardId) {
                        card.work = work;
                    }
                });
                report.cards.push(card);
            });
            // Find and dedupe project array
            const projectArray = timeCards.map((card) => {
                return card.projectId;
            });
            const allProjects = dedupeArray(projectArray);
            report.POList = allProjects;
            // Compile report

            const pwSettingMap = pwSettingAllData.reduce((map, setting) => {
                map[setting._id] = setting;
                return map;
            }, {});

            report.cards.forEach((card) => {
                const workDone = card?.work?.work?.workDone;
                if (workDone) {
                    workDone.forEach((pieceWork) => {
                        const setting = pwSettingMap[pieceWork.id];
                        if (setting) {
                            // Adding name and unit to pieceWork
                            pieceWork.name = setting.name;
                            pieceWork.unit = setting.unit.split(" (")[0];
                        }
                    });
                }
            });

            await Promise.all(
                allProjects.map(async (projectId) => {
                    const projectReport = {
                        oppId: projectId,
                        hours: 0,
                        pwSqs: 0, // 'sqsEarnings'
                        pwExtras: 0, // 'extrasEarnings'
                        pwHourly: 0, // 'hourlyEarnings'
                        pwTotal: 0,
                        travel: 0,
                        cards: [],
                    };
                    let addTravel = false;
                    report.cards.forEach((card) => {
                        if (card.task === TaskTypeEnum.Day_Off || projectId === "" || !projectId) {
                            report.dayOff = true;
                            report.dayOffTimeCardId = timeCards[0]._id;
                            report.ptoUsed = card?.ptoUsed || false;
                            return;
                        }

                        if (card?.projectId === projectId) {
                            const workTask = allWorkTaskData.find(
                                (workTaskData) => workTaskData._id === card.task,
                            );

                            if (
                                workTask &&
                                workTask.addTravel &&
                                card.work?.work?.extraWorkTime !== card.hrs
                            ) {
                                addTravel = true;
                            }

                            const hours = card.hrs || 0;
                            const pwSqs = card.work?.sqsEarnings || 0;
                            const pwExtras = card.work?.extrasEarnings || 0;
                            const pwHourly = card.work?.hourlyEarnings || 0;
                            const pwTotal = pwSqs + pwExtras + pwHourly;
                            report.hours += hours;
                            report.pwSqs += pwSqs;
                            report.pwExtras += pwExtras;
                            report.pwHourly += pwHourly;
                            report.pwTotal += pwTotal;

                            if (card.work?.removeLeadBonus || (workTask && !workTask.showOnScoreboard)) {
                                report.removeFromLead += pwTotal;
                            }
                            projectReport.hours += hours;
                            projectReport.pwSqs += pwSqs;
                            projectReport.pwExtras += pwExtras;
                            projectReport.pwHourly += pwHourly;
                            projectReport.pwTotal += pwTotal;
                            projectReport.cards.push(card);
                        }
                    });
                    if (projectId === "" || !projectId) {
                        report.dayOff = true;
                        report.dayOffTimeCardId = timeCards[0]._id;
                        return false;
                    }

                    // Calculate Travel Fee
                    if (projectId !== "companyDefaultPO" && addTravel) {
                        const opp = await this.opportunityModel
                            .findOne({ _id: projectId })
                            .select("_id companyId distance");

                        if (opp?.distance >= 20) {
                            const pwWorkerFee = opp?.distance * variables?.travelFee;
                            const salWorkerFee = opp?.distance * (variables?.travelFee / 2);
                            const travelFee = +wage?.wageAmount ? salWorkerFee : pwWorkerFee;
                            report.travel += travelFee;
                            projectReport.travel += travelFee;
                        }
                    }
                    projectReport.hours = projectReport.hours;
                    projectReport.pwSqs = projectReport.pwSqs;
                    projectReport.pwExtras = projectReport.pwExtras;
                    projectReport.pwHourly = projectReport.pwHourly;
                    projectReport.pwTotal = projectReport.pwTotal;
                    report.projects.push(projectReport);
                }),
            );
            if (report.wageType === "hourly") report.dayBase = report.wage * report.hours;

            const weekend = isWeekend(variables.weekEndDays, start);

            if (report.wageType === "salary" && !weekend && !report.dayOff)
                report.dayBase = (report.hours && report.wage * 8) || 0;
            report.totalEarned = report.dayBase + report.pwTotal + report.travel;

            return report;
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getMemberActiveCrewByMemberId(companyId: string, memberId: string) {
        try {
            const [activeCrew] = await this.crewModel.aggregate([
                {
                    $match: {
                        companyId,
                        deleted: { $ne: true },
                        retired: { $ne: true },
                    },
                },
                {
                    $lookup: {
                        from: "CrewMember",
                        pipeline: [{ $match: { deleted: false, memberId } }],
                        localField: "_id",
                        foreignField: "crewId",
                        as: "members",
                    },
                },
                {
                    $unwind: { path: "$members" },
                },
            ]);
            return activeCrew;
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async reActivateCrew(
        userId: string,
        companyId: string,
        reActivateCrewDto: ReActivateCrewDto,
        crewPermission: number,
    ) {
        try {
            const crew = await this.crewModel
                .findOne({
                    _id: reActivateCrewDto.crewId,
                    companyId,
                    deleted: false,
                })
                .exec();
            if (!crew) throw new HttpException("Crew does not exist", HttpStatus.BAD_REQUEST);
            await this.positionService.permissionCheck(
                userId,
                companyId,
                crew._id, //not used in case of crew permission check
                crew.managerId,
                crewPermission,
            );
            await this.crewModel.findOneAndUpdate(
                {
                    _id: reActivateCrewDto.crewId,
                    companyId,
                    deleted: false,
                    retired: true,
                },
                {
                    $unset: {
                        retireDate: 1,
                        endDate: 1,
                    },
                    $set: {
                        retired: false,
                    },
                },
                { new: true },
            );
            return new OkResponse({ message: "Crew reactivated successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async projectDayCompleted(projectLog, companyId: string): Promise<any> {
        try {
            const report: any = {
                oppId: "",
                oppPO: "",
                tearOffDone: 0,
                roofingDone: 0,
                percentDone: 0,
                budget: 0,
                laborBurden: 0,
                rr: 0,
                vol: 0,
            };
            if (!projectLog) return report;
            //TODO: hardcode value to change
            const pieceWorkSettings = await this.pieceWorkSettingModel.find({
                companyId,
                hasLayer: false,
                usesPitch: true,
                workTask: "37efbf6e-5df3-421e-aee8-f2d0a4179e61",
                version: "543f11ea-0333-4c6d-bc66-c0f76c9ae853",
            });

            let hourlyRate, rooferId, tearOffId;
            const roofer = await this.workTaskModel.find({ companyId, deleted: false }).exec(); //crew postion model code: "roofer",
            roofer.forEach((worker) => {
                if (worker.code === "roofer") {
                    hourlyRate = worker.rate;
                    rooferId = worker._id;
                } else if (worker.code === "tearOff") {
                    tearOffId = worker._id;
                }
            });

            const plywood = await this.materialModel
                .findOne({ companyId, code: "osb7/16", deleted: false })
                .exec();

            const order: any = await this.orderModel
                .findOne({ oppId: projectLog.oppId, companyId, deleted: false })
                .populate("projectId", null, "Project")
                .populate("projectPriceId", null, "Price")
                .populate("oppId", null, "Opportunity")
                .exec();

            if (!order) return report;
            const allWorkOrders = [];
            order.projects.forEach(({ workOrder }) => {
                allWorkOrders.push(...workOrder);
            });

            const opp: any = order?.oppId;
            const project: any = order?.projectId;
            const price: any = order?.projectPriceId;

            report.oppId = projectLog.oppId;
            report.oppPO = opp?.PO + "-" + opp?.num;
            report.tearOffDone = projectLog.tearOffDone || projectLog.tearOffSQ || 0;
            report.roofingDone = projectLog.roofingDone || projectLog.roofingSQ || 0;

            if (!order || !project || !price || !opp) return report;
            // throw new HttpException(`Can't find Order of opp ${opp._id}`, HttpStatus.BAD_REQUEST);

            const projectTypes = await this.projectTypeModel.findOne({
                _id: project.projectType,
                companyId,
                deleted: false,
            });
            // Pull project stats
            const projectVol = order.priceTotals.jobTotal + (opp.changeOrderValue || 0);
            const projectRR = order.priceTotals.actRev + (opp.changeOrderRRValue || 0);
            const travelFee = order.priceTotals.travelFee;
            const laborBurden = order.priceTotals.lBurden; //lBurden(laborBurden);
            const pitch = averagePitch(project) || 0; // do we need repair pitch here

            let plyLabor = 0;
            pieceWorkSettings.forEach((pieceWorkSetting) => {
                plyLabor += (
                    pieceWorkSetting.pitch.find((obj) => {
                        return obj.pitchOrder === Math.ceil(pitch);
                    }) || { amount: 0 }
                ).amount;
            });

            if (projectTypes.typeReplacement) {
                let plywoodTotalBudget = 0;
                let instRmvPlywood = 0;
                let tearOffTotalBudget = 0;
                let roofingTotalBudget = 0;
                let plywoodLaborBurden = 0;
                let tearOffLaborBurden = 0;
                let roofingLaborBurden = 0;

                const taskData = await this.taskModel.find({
                    companyId,
                    deleted: false,
                    $or: [{ code: "instPlyTask" }, { code: "rmvPlyTask" }],
                    code: { $exists: true },
                });

                const taskDataId: any = {};

                taskData.forEach((task: any) => {
                    taskDataId[task.code] = task._id;
                });

                allWorkOrders.forEach((task) => {
                    if (task.task_id === taskDataId.instPlyTask || task.task_id === taskDataId.rmvPlyTask) {
                        plywoodTotalBudget += task.cost;
                        instRmvPlywood += task.value * 3.125;
                    } else if (task.worker === tearOffId) {
                        tearOffTotalBudget += task.cost;
                    } else {
                        roofingTotalBudget += task.cost;
                    }
                });

                const totalBudget = tearOffTotalBudget + roofingTotalBudget + plywoodTotalBudget;

                // Calc plywood stats
                const plywoodTotalPercent = plywoodTotalBudget / totalBudget || 0;
                plywoodTotalBudget += travelFee * plywoodTotalPercent;
                plywoodLaborBurden += laborBurden * plywoodTotalPercent;

                // instRmvPlywood holds both remove and install
                report.plywoodTotalSheets = instRmvPlywood / 2;
                const plywoodDayPercent = report.plywoodTotalSheets
                    ? (projectLog.nonBillPly || 0) / report.plywoodTotalSheets
                    : 0;

                const plywoodVol = projectVol * plywoodTotalPercent * plywoodDayPercent;
                const plywoodRR = projectRR * plywoodTotalPercent * plywoodDayPercent;
                const plywoodBudget = plywoodDayPercent * plywoodTotalBudget;

                const plywoodBurden = plywoodDayPercent * plywoodLaborBurden;
                // Calc tear off stats
                const roof = project?.customData?.roofSQ;
                const tearOffTotalPercent = tearOffTotalBudget / totalBudget || 0;
                tearOffTotalBudget += travelFee * tearOffTotalPercent;
                tearOffLaborBurden += laborBurden * tearOffTotalPercent;
                report.toTotalSq = roof.rmvFlat + roof.rmvLow + roof.rmvSteep;
                const tearOffDayPercent =
                    (projectLog.tearOffDone || projectLog.tearOffSQ) / report.toTotalSq || 0;
                const tDayVol = projectVol * tearOffTotalPercent * tearOffDayPercent;
                const tDayRR = projectRR * tearOffTotalPercent * tearOffDayPercent;
                const tDayBudget = tearOffDayPercent * tearOffTotalBudget;
                const tDayBurden = tearOffDayPercent * tearOffLaborBurden;
                // Calc roofing stats
                const roofingTotalPercent = roofingTotalBudget / totalBudget || 0;
                roofingTotalBudget += travelFee * roofingTotalPercent;
                roofingLaborBurden += laborBurden * roofingTotalPercent;
                report.rTotalSq = roof.instFlat + roof.instLow + roof.instSteep;
                const roofingDayPercent =
                    (projectLog.roofingDone || projectLog.roofingSQ) / report.rTotalSq || 0;
                const rDayVol = projectVol * roofingTotalPercent * roofingDayPercent;
                const rDayRR = projectRR * roofingTotalPercent * roofingDayPercent;
                const rDayBudget = roofingDayPercent * roofingTotalBudget;
                const rDayBurden = roofingDayPercent * roofingLaborBurden;
                // Get totals
                report.percentDone = (plywoodBudget + tDayBudget + rDayBudget) / totalBudget || 0;
                report.vol = plywoodVol + tDayVol + rDayVol;
                report.rr = plywoodRR + tDayRR + rDayRR;
                report.budget = plywoodBudget + tDayBudget + rDayBudget;
                report.laborBurden = plywoodBurden + tDayBurden + rDayBurden;
            } else {
                const totalBudget = order.priceTotals.lSubtotal;
                report.percentDone = projectLog.percentDone / 100 || 0;
                report.vol = report.percentDone * projectVol;
                report.rr = report.percentDone * projectRR;
                report.budget = report.percentDone * totalBudget;
                report.laborBurden = report.percentDone * laborBurden;
            }

            // Calc cost of extras
            const extraHourVol =
                (projectLog.manHours || projectLog.addManHours || 0) * price.variables?.manHourRate;
            const extraHourRR =
                (projectLog.manHours || projectLog.addManHours || 0) * price.variables?.manHourRate;
            const extraHourBudget = (projectLog.manHours || projectLog.addManHours || 0) * hourlyRate;
            const extraHourBurden = extraHourBudget * price.variables?.ttlBurden;

            const extraPlyVol =
                (projectLog.plywoodReplaced || projectLog.instSheet || 0) * price.variables?.plywoodRate * 32;
            const extraPlyRR =
                extraPlyVol - (projectLog.plywoodReplaced || projectLog.instSheet || 0) * plywood.cost;
            const extraPlyBudget = (projectLog.plywoodReplaced || projectLog.instSheet || 0) * plyLabor;
            const extraPlyBurden = extraPlyBudget * price.variables?.ttlBurden;

            report.totalExtraHours =
                (projectLog.manHours || projectLog.addManHours || 0) + projectLog.nonBillHours;
            report.totalPlywood =
                (projectLog.plywoodReplaced || projectLog.instSheet || 0) + (projectLog.nonBillPly || 0);

            // Add extras to cost
            report.vol +=
                extraHourVol + extraPlyVol + (projectLog.materialCosts || projectLog.addMaterials || 0);
            report.rr += extraHourRR + extraPlyRR;
            report.budget += extraHourBudget + extraPlyBudget;

            report.laborBurden += extraHourBurden + extraPlyBurden;
            return report;
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getNonCrewsToApprove(
        logInMemberId: string,
        companyId: string,
        start: Date,
        end: Date,
        crewPermission: PermissionsEnum,
    ) {
        try {
            const startDate = new Date(start);
            const endDate = new Date(end);

            const crewMembers = await this.crewMemberModel.find({
                companyId,
                startDate: { $lte: endDate },
                $or: [{ removeDate: { $exists: false } }, { removeDate: { $gte: startDate } }],
            });

            const crewId = [];
            crewMembers.forEach((member) => crewId.push(member.memberId));

            let query;
            if (crewPermission === PermissionsEnum.Managed) {
                query = {
                    _id: { $nin: crewId },
                    company: companyId,
                    managerId: logInMemberId,
                    hireDate: { $lte: endDate },
                    $or: [
                        { deleted: false },
                        {
                            $and: [
                                { terminateDate: { $exists: true } },
                                { terminateDate: { $gte: startDate } },
                            ],
                        },
                    ],
                };
            } else if (crewPermission === PermissionsEnum.Full) {
                query = {
                    _id: { $nin: crewId },
                    company: companyId,
                    hireDate: { $lte: endDate },
                    $or: [
                        { deleted: false },
                        {
                            $and: [
                                { terminateDate: { $exists: true } },
                                { terminateDate: { $gte: startDate } },
                            ],
                        },
                    ],
                };
            } else if (crewPermission === PermissionsEnum.Self) {
                query = {
                    _id: logInMemberId,
                    company: companyId,
                    hireDate: { $lte: endDate },
                    $or: [
                        { deleted: false },
                        {
                            $and: [
                                { terminateDate: { $exists: true } },
                                { terminateDate: { $gte: startDate } },
                            ],
                        },
                    ],
                };
            }

            const [nonCrews, variables, allWorkTaskData, pwSettingAllData] = await Promise.all([
                // removing owner from non crew members
                this.memberModel.aggregate([
                    {
                        $match: query,
                    },
                    {
                        $lookup: {
                            from: "Role",
                            pipeline: [
                                {
                                    $match: {
                                        role: { $ne: 1 },
                                    },
                                },
                            ],
                            foreignField: "_id",
                            localField: "roleId",
                            as: "roleData",
                        },
                    },
                    {
                        $unwind: {
                            path: "$roleData",
                            preserveNullAndEmptyArrays: false,
                        },
                    },
                ]),

                this.companySettingModel.findOne({
                    companyId,
                }),

                this.workTaskModel.find({ companyId }),

                this.pieceWorkSettingModel.find({
                    companyId: companyId,
                }),
            ]);

            const nonCrewReport: any = {
                numWorking: 0,
                timeWorked: 0,
                members: [],
            };

            // nonCrewReport.members = [];
            for (const member of nonCrews) {
                const memObj: any = await this.memberDayActivity(
                    companyId,
                    member._id,
                    startDate,
                    endDate,
                    "", // non crew dont have crewlead
                    pwSettingAllData,
                    allWorkTaskData,
                    variables,
                );
                // memObj.name = member.name;
                memObj.name =
                    member?.preferredName && member?.preferredName !== ""
                        ? `${member?.preferredName} ${member?.name?.split(" ")[1]}`
                        : member.name;

                memObj.preferredName = member?.preferredName;
                memObj.officePerson = false;

                // Separate out non crew salary workers
                if (!memObj.pwExtraHourRate && memObj.wageType == "salary") {
                    memObj.officePerson = true;
                }

                // // OFF FOR NOW
                if (!memObj.dayOff && memObj?.cards.length) nonCrewReport.numWorking++;

                nonCrewReport.timeWorked += +memObj.hours;

                await nonCrewReport.members.push(memObj);
            }
            nonCrewReport.paid = sumArray(nonCrewReport.members, "totalEarned");
            nonCrewReport.rrDollar = nonCrewReport.logStats?.dayRR / nonCrewReport.paid;
            nonCrewReport.timeWorked = roundTo2(nonCrewReport.timeWorked);

            return new OkResponse({ nonCrewReport });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async memberDailyData(
        companyId: string,
        memberId: string,
        start: Date,
        end: Date,
        crewLeadId: string,
        pwSettingAllData: any,
        allWorkTaskData: any,
        companySetting: any,
    ) {
        try {
            const memObj = await this.memberDayActivity(
                companyId,
                memberId,
                start,
                end,
                crewLeadId,
                pwSettingAllData,
                allWorkTaskData,
                companySetting,
            );

            const dateWiseMemberReport = {
                date: start,
                dayOff: false,
                ptoUsed: false,
                earned: 0,
                hours: 0,
                breakdown: {
                    hourly: {
                        hours: 0,
                        earned: 0,
                        regular: { earned: 0, hours: 0 },
                        overtime: { earned: 0, hours: 0 },
                    },
                    travel: memObj?.travel || 0,
                    pieceWorkEarned: 0,
                    pieceWork: [],
                },
                defaultPoTimeCards: [],
                allTimeCards: [],
                poHours: 0,
            };

            const pieceWorkEarning = [];

            memObj.cards.forEach((card) => {
                dateWiseMemberReport.ptoUsed = card?.ptoUsed || false;
                dateWiseMemberReport.dayOff = card.task === TaskTypeEnum.Day_Off;

                const { work } = card;
                if (work && work?.work) {
                    work?.work?.workDone.forEach((pieceWork) => {
                        const { id, amount, earned } = pieceWork;
                        const pieceWorkSettingData = pwSettingAllData.find((pwData) => pwData._id === id);

                        if (pieceWorkSettingData && amount) {
                            const { name, unit } = pieceWorkSettingData;

                            const existingIndex = pieceWorkEarning.findIndex((pw) => pw.pwName === name);
                            if (existingIndex === -1) {
                                pieceWorkEarning.push({
                                    pwName: name,
                                    earned: roundTo2(earned),
                                    amount: Number(amount),
                                    unit: unit.split(" (")[0],
                                });
                            } else {
                                pieceWorkEarning[existingIndex].earned += roundTo2(earned);
                                pieceWorkEarning[existingIndex].amount += roundTo2(Number(amount));
                            }
                        }
                    });
                    if (work?.work?.extraWorkTime) {
                        const existingIndex = pieceWorkEarning.findIndex((pw) => pw.name === "Extra Time");
                        if (existingIndex === -1) {
                            pieceWorkEarning.push({
                                pwName: "Extra Time",
                                earned: roundTo2(work?.hourlyEarnings),
                                amount: roundTo2(work.work.extraWorkTime),
                                unit: "hrs",
                            });
                        } else {
                            pieceWorkEarning[existingIndex].earned += roundTo2(
                                roundTo2(work?.hourlyEarnings),
                            );
                            pieceWorkEarning[existingIndex].amount += roundTo2(work.work.extraWorkTime);
                        }
                    }
                }
            });

            dateWiseMemberReport.breakdown.pieceWork = pieceWorkEarning;
            dateWiseMemberReport.breakdown.pieceWorkEarned = pieceWorkEarning.reduce(
                (total, pieceWork) => total + pieceWork.earned,
                0,
            );
            return { memDayObj: dateWiseMemberReport, memObj };
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }
}
