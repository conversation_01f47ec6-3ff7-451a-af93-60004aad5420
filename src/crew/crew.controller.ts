import {
    Body,
    Controller,
    Delete,
    Get,
    Param,
    ParseBoolPipe,
    ParseUUIDPipe,
    Patch,
    Post,
    Query,
} from "@nestjs/common";
import {
    ApiBearerAuth,
    ApiConflictResponse,
    ApiInternalServerErrorResponse,
    ApiNotFoundResponse,
    ApiOperation,
    ApiTags,
    ApiUnauthorizedResponse,
} from "@nestjs/swagger";
import HttpResponse from "src/shared/http/response/response.http";
import { CrewService } from "./crew.service";
import { CreateCrewDto } from "./dto/create-crew.dto";
import { UpdateCrewDto } from "./dto/update-crew.dto";
import { UpdateCrewOrderDto } from "./dto/update-crew-order.dto";
import { CreateCrewMemberDto } from "./dto/create-crew-member.dto";
import { UpdateCrewMemberDto } from "./dto/update-crew-member.dto";
import { PositionService } from "src/position/position.service";
import { Positions } from "src/auth/guards/auth.guard";
import { PermissionsEnum } from "src/shared/enum/permission.enum";
import { ReActivateCrewDto } from "./dto/re-activate-crew.dto";
import { Auth, GetUser } from "src/auth/decorator/auth.decorator";
import { JwtUserPayload } from "src/auth/interface/auth.interface";
import { moduleNames } from "src/shared/constants/constant";

@ApiTags("Crew")
@Auth()
@ApiBearerAuth()
@Controller({ path: "crew", version: "1" })
export class CrewController {
    constructor(
        private readonly crewService: CrewService,
        private readonly positionService: PositionService,
    ) {}

    /**
     * Creates a new crew
     * @param userId User ID of the user creating the crew
     * @param createCrewDto DTO containing the information for the new crew
     * @returns Promise that resolves to an HTTP response indicating the result of the operation
     */
    @ApiOperation({ summary: "Create Crew Management" })
    @ApiConflictResponse({ description: "Crew already exist" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Positions({
        category: "module",
        name: moduleNames.module.crew,
        actions: [PermissionsEnum.Full],
    })
    @Post("create-crew")
    async createCrew(
        @GetUser() user: JwtUserPayload,
        @Body() createCrewDto: CreateCrewDto,
    ): Promise<HttpResponse> {
        return this.crewService.createCrew(user.companyId, createCrewDto);
    }

    /**
     * Update a crew with the given user ID and crew update data.
     * @param userId The ID of the user making the request.
     * @param updateCrewDto The crew update data.
     * @returns A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Edit Crew Management" })
    @ApiNotFoundResponse({ description: "Crew not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Positions({
        category: "module",
        name: moduleNames.module.crew,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed],
    })
    @Patch("update-crew")
    async updateCrew(
        @GetUser() user: JwtUserPayload,
        @Body() updateCrewDto: UpdateCrewDto,
    ): Promise<HttpResponse> {
        return this.crewService.updateCrew(user._id, user.companyId, updateCrewDto, user.teamPermission);
    }

    /**
     * Get Crews for a Company
     * @param userId User ID of the requester
     * @param companyId Company ID of the Company to get crews for
     * @param deleted Whether to include deleted Marketing Channels in the results
     * @param retired Whether to include retired Marketing Channels in the results
     * @returns List ofCrews for the specified Company
     */
    @ApiOperation({ summary: "Get Company Crews" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Positions({
        category: "module",
        name: moduleNames.module.crew,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed, , PermissionsEnum.Self],
    })
    @Get("get-company-crews")
    async getCompanyCrews(
        @GetUser() user: JwtUserPayload,
        @Query("deleted", ParseBoolPipe) deleted?: boolean,
        @Query("retired", ParseBoolPipe) retired?: boolean,
    ): Promise<HttpResponse> {
        return this.crewService.getCompanyCrews(
            user._id,
            user.companyId,
            deleted,
            retired,
            user.teamPermission,
        );
    }

    /**
     * Get a crew by its ID
     * @param userId - the ID of the user making the request
     * @param companyId - the ID of the company associated with the crew
     * @param crewId - the ID of the crew to retrieve
     * @returns a Promise representing the HTTP response with the requested crew
     */
    @ApiOperation({ summary: "Get Crew By id" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Positions({
        category: "module",
        name: moduleNames.module.crew,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed],
    })
    @Get("get-crew-by-id/crew/:crewId")
    async getCrewById(
        @GetUser() user: JwtUserPayload,
        @Param("crewId", ParseUUIDPipe) crewId: string,
    ): Promise<HttpResponse> {
        return this.crewService.getCrewById(user._id, user.companyId, crewId);
    }

    /**
     * Updates a crew order number
     * @param userId - User ID of the user updating the crew
     * @param updateOrderNumberDto - DTO containing the information for the update in crew
     * @returns A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Update CrewOrder By id" })
    @ApiNotFoundResponse({ description: "Crew not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Positions({
        category: "module",
        name: moduleNames.module.crew,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed],
    })
    @Patch("update-crew-order-number")
    async updateCrewOrderNumber(
        @GetUser() user: JwtUserPayload,
        @Body() updateOrderNumberDto: UpdateCrewOrderDto,
    ): Promise<HttpResponse> {
        return this.crewService.updateCrewOrderNumber(
            user._id,
            user.companyId,
            updateOrderNumberDto,
            user.teamPermission,
        );
    }

    /**
     * Reactivates a crew
     * @param userId - User ID of the requester
     * @param reActivateCrewDto - The crew reactivate data.
     * @returns A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "ReActivate Crew" })
    @ApiNotFoundResponse({ description: "Crew not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Positions({
        category: "module",
        name: moduleNames.module.crew,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed],
    })
    @Patch("re-activate-crew")
    async reActivateCrew(
        @GetUser() user: JwtUserPayload,
        @Body() reActivateCrewDto: ReActivateCrewDto,
    ): Promise<HttpResponse> {
        return this.crewService.reActivateCrew(
            user._id,
            user.companyId,
            reActivateCrewDto,
            user.teamPermission,
        );
    }

    /**
     * Creates a new crew member
     * @param userId - User ID of the user creating the crew member
     * @param createCrewMemberDto - DTO containing the information for the new crew member
     * @returns Promise that resolves to an HTTP response indicating the result of the operation
     */
    @ApiOperation({ summary: "Create Crew Member" })
    @ApiConflictResponse({ description: "Crew Member already exist" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Positions({
        category: "module",
        name: moduleNames.module.crew,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed],
    })
    @Post("create-crew-member")
    async createCrewMember(
        @GetUser() user: JwtUserPayload,
        @Body() createCrewMemberDto: CreateCrewMemberDto,
    ): Promise<HttpResponse> {
        return this.crewService.createCrewMember(
            user._id,
            user.companyId,
            createCrewMemberDto,
            user.teamPermission,
        );
    }

    /**
     * Update a crew member with the given user ID and crew member data.
     * @param userId - The ID of the user making the request.
     * @param updateCrewMemberDto - The crew member update data.
     * @returns A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Edit Crew Member" })
    @ApiNotFoundResponse({ description: "Crew Member not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Positions({
        category: "module",
        name: moduleNames.module.crew,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed],
    })
    @Patch("update-crew-member")
    async updateCrewMember(
        @GetUser() user: JwtUserPayload,
        @Body() updateCrewMemberDto: UpdateCrewMemberDto,
    ): Promise<HttpResponse> {
        return this.crewService.updateCrewMember(
            user._id,
            user.companyId,
            updateCrewMemberDto,
            user.teamPermission,
        );
    }

    /**
     * Get Crew members for a Company
     * @param userId - User ID of the requester
     * @param companyId - Company ID of the Company to get crew members for
     * @param crewId - Crew ID  to get crew members for
     * @returns List of Crew Members for the specified Company
     */
    @ApiOperation({ summary: "Get Crew Members" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Positions({
        category: "module",
        name: moduleNames.module.crew,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed],
    })
    @Get("get-crew-members/crew/:crewId")
    async getCrewMembers(
        @GetUser() user: JwtUserPayload,
        @Param("crewId", ParseUUIDPipe) crewId: string,
    ): Promise<HttpResponse> {
        return this.crewService.getCrewMembers(user._id, user.companyId, crewId, user.teamPermission);
    }

    /**
     * Get Non Crew members for a Company
     * @param userId - User ID of the requester
     * @param companyId - Company ID of the Company to get non crew members for
     * @returns List of Non Crew Members for the specified Company
     */
    @ApiOperation({ summary: "Get Non Crew Members" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Get("get-non-crew-members")
    async getNonCrewMembers(@GetUser() user: JwtUserPayload): Promise<HttpResponse> {
        return this.crewService.getNonCrewMembers(user._id, user.companyId);
    }

    /**
     * Get Crew members by id for a Company
     * @param userId - User ID of the requester
     * @param companyId - Company ID of the Company to get crew members for
     * @param crewId - the ID of the crew to retrieve
     * @returns a Promise representing the HTTP response with the requested crew
     */
    @ApiOperation({ summary: "Get Crew Member By id" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Positions({
        category: "module",
        name: moduleNames.module.crew,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed],
    })
    @Get("get-crew-member-by-id/crew/:crewId")
    async getCrewMemberById(
        @GetUser() user: JwtUserPayload,
        @Param("crewId", ParseUUIDPipe) crewId: string,
    ): Promise<HttpResponse> {
        return this.crewService.getCrewMemberById(user._id, user.companyId, crewId, user.teamPermission);
    }

    /**
     * Promote crew member
     * @param userId - User ID of the requester
     * @param updateCrewMemberDto - The crew member update data.
     * @returns a Promise representing the HTTP response with the requested crew
     */
    @ApiOperation({ summary: "Promote Crew Member" })
    @ApiNotFoundResponse({ description: "Crew Member not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Positions({
        category: "module",
        name: moduleNames.module.crew,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed],
    })
    @Patch("promote-crew-member")
    async promoteCrewMember(
        @GetUser() user: JwtUserPayload,
        @Body() updateCrewMemberDto: UpdateCrewMemberDto,
    ): Promise<HttpResponse> {
        return this.crewService.promoteCrewMember(
            user._id,
            user.companyId,
            updateCrewMemberDto,
            user.teamPermission,
        );
    }

    /**
     * Remove crew member
     * @param userId - User ID of the requester
     * @param updateCrewMemberDto - The crew member update data.
     * @returns - A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Remove Crew Member" })
    @ApiNotFoundResponse({ description: "Crew Member not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Positions({
        category: "module",
        name: moduleNames.module.crew,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed],
    })
    @Delete("remove-crew-member")
    async removeCrewMember(
        @GetUser() user: JwtUserPayload,
        @Body() updateCrewMemberDto: UpdateCrewMemberDto,
    ): Promise<HttpResponse> {
        return this.crewService.removeCrewMember(
            user._id,
            user.companyId,
            updateCrewMemberDto,
            user.teamPermission,
        );
    }

    /**
     * Get Crews approve list
     * @param userId - User ID of the requester
     * @param companyId - Company ID of the Company to get crews for
     * @param startDate - Date to start for
     * @param endDate - Date to end for
     * @returns List of Crews to approve for the specified Company
     */
    @ApiOperation({ summary: "Get Crews Approve List" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Positions({
        category: "module",
        name: moduleNames.module.timeCards,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed],
    })
    @Get("get-crews-to-approve/:startDate/:endDate")
    async getCrewsToApprove(
        @GetUser() user: JwtUserPayload,
        @Param("startDate") startDate: Date,
        @Param("endDate") endDate: Date,
    ) {
        return this.crewService.getCrewsToApprove(
            user.memberId,
            user.companyId,
            startDate,
            endDate,
            user.teamPermission,
        );
    }

    /**
     * Get Non Crews Members approve list
     * @param userId - User ID of the requester
     * @param companyId - Company ID of the Company to get crews for
     * @param date - Date to start for
     * @returns List of Non-Crew Members to approve for the specified Company
     */
    @ApiOperation({ summary: "Get Non-Crew Members Approve List" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Positions({
        category: "module",
        name: moduleNames.module.timeCards,
        actions: [PermissionsEnum.Full],
    })
    @Get("get-non-crews-to-approve/:startDate/:endDate")
    async getNonCrewsToApprove(
        @GetUser() user: JwtUserPayload,
        @Param("startDate") startDate: Date,
        @Param("endDate") endDate: Date,
    ) {
        return this.crewService.getNonCrewsToApprove(
            user.memberId,
            user.companyId,
            startDate,
            endDate,
            user.teamPermission,
        );
    }
}
