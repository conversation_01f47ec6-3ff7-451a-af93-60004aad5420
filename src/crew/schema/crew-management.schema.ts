import { Schema, SchemaFactory, Prop } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import { Document } from "mongoose";
import { WorkTypeEnum } from "../enum/work-type.enum";
import { UUIDProp } from "src/shared/decorator/uuid.decorator";

export type CrewDocument = Crew & Document;

@Schema({ timestamps: true, id: false, strict: false, collection: "Crew" })
export class Crew {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @UUIDProp()
    companyId: string;

    @Prop({ required: true })
    name: string;

    @Prop({ required: true })
    startDate: Date;

    @Prop({ required: true, type: Number, enum: WorkTypeEnum })
    workType: WorkTypeEnum;

    @Prop({ required: true })
    order: number;

    @Prop({ required: true })
    managerId: string;

    @Prop()
    foremanId?: string;

    @Prop({ default: false })
    retired: boolean;

    @Prop({ required: false })
    retireDate?: Date;

    @Prop({ required: false })
    endDate?: Date;

    @Prop({ default: false })
    deleted: boolean;

    @Prop()
    createdAt?: Date;

    @Prop()
    modifiedAt?: Date;
}

export const CrewSchema = SchemaFactory.createForClass(Crew);
