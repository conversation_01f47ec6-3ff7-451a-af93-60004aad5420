import { ApiProperty } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsString, IsNotEmpty, IsOptional, IsDate } from "class-validator";

export class UpdateCrewMemberDto {
    // @ApiProperty({ description: "Company Id", required: true })
    // @IsString()
    // @IsNotEmpty()
    // companyId: string;

    @ApiProperty({ description: "Crew Id", required: true })
    @IsString()
    @IsNotEmpty()
    crewId: string;

    @ApiProperty({ description: "Crew Member Id", required: true })
    @IsString()
    @IsNotEmpty()
    crewMemberId: string;

    @ApiProperty({ description: "Member Id", required: true })
    @IsString()
    @IsNotEmpty()
    memberId: string;

    @ApiProperty({ description: "Start date", required: false })
    @IsDate()
    @Transform(({ value }) => new Date(value))
    @IsOptional()
    startDate?: Date;

    @ApiProperty({ description: "Remove date", required: false })
    @IsDate()
    @Transform(({ value }) => new Date(value))
    @IsOptional()
    removeDate?: Date;

    @ApiProperty({ description: "Promote date", required: false })
    @IsDate()
    @Transform(({ value }) => new Date(value))
    @IsOptional()
    promoteDate?: Date;
}
