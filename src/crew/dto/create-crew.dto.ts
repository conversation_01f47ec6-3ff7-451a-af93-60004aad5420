import { ApiProperty } from "@nestjs/swagger";
import { IsString, IsNotEmpty, IsEnum, IsUUID, IsNumber, IsDate } from "class-validator";
import { WorkTypeEnum } from "../enum/work-type.enum";
import { Transform } from "class-transformer";

export class CreateCrewDto {
    // @ApiProperty({ description: "Company Id", required: true })
    // @IsUUID()
    // @IsNotEmpty()
    // companyId: string;

    @ApiProperty({ description: "Crew name", required: true })
    @IsString()
    @Transform(({ value }) => value.trim(), { toClassOnly: true })
    @IsNotEmpty()
    name: string;

    @ApiProperty({ description: "Start date", required: true })
    @IsDate()
    @Transform(({ value }) => new Date(value))
    @IsNotEmpty()
    startDate: Date;

    @ApiProperty({ description: "Work type", required: true })
    @IsNumber()
    @IsNotEmpty()
    @IsEnum(WorkTypeEnum)
    workType: WorkTypeEnum;

    @ApiProperty({ description: "Manager id", required: true })
    @IsUUID()
    @IsNotEmpty()
    managerId: string;
}
