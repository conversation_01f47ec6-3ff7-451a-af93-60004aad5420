import { ApiProperty } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsDate, IsNotEmpty, IsUUID } from "class-validator";

export class GetCrewsToApprove {
    @ApiProperty({ description: "Date", required: true })
    @IsDate()
    @Transform(({ value }) => new Date(value))
    @IsNotEmpty()
    date: Date;

    // @ApiProperty({ description: "Company Id", required: true })
    // @IsUUID()
    // @IsNotEmpty()
    // companyId: string;

    // @ApiProperty({ description: "Order Number" })
    // @IsNumber()
    // @IsNotEmpty()
    // order: string;
}
