import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsNotEmpty, IsBoolean, IsOptional, IsUUID } from "class-validator";

export class GetCrewMembersDto {
    // @ApiProperty({ description: "Company Id", required: true })
    // @IsUUID()
    // @IsNotEmpty()
    // companyId: string;

    @ApiProperty({ description: "Crew Id", required: true })
    @IsUUID()
    @IsNotEmpty()
    crewId: string;

    @ApiPropertyOptional({ description: "Deleted" })
    @IsBoolean()
    @IsOptional()
    deleted?: boolean;
}
