import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsN<PERSON>ber, IsString } from "class-validator";

export class UpdateCrewOrderDto {
    @ApiProperty({ description: "Crew Id", required: true })
    @IsString()
    @IsNotEmpty()
    _id: string;

    // @ApiProperty({ description: "Company Id", required: true })
    // @IsString()
    // @IsNotEmpty()
    // companyId: string;

    @ApiProperty({ description: "Order Number", required: true })
    @IsNumber()
    @IsNotEmpty()
    order: number;
}
