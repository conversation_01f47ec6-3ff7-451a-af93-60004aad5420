import {
    BadRequestException,
    HttpException,
    HttpStatus,
    Injectable,
    InternalServerErrorException,
} from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";
import { MemberDocument } from "src/company/schema/member.schema";
import { PositionService } from "src/position/position.service";
import CreatedResponse from "src/shared/http/response/created.http";
import NoContentResponse from "src/shared/http/response/no-content.http";
import OkResponse from "src/shared/http/response/ok.http";
import { CreateCompensationDto } from "./dto/create-compensation.dto";
import { DeleteCompensationDto } from "./dto/delete-compensation.dto";
import { CompensationDocument } from "./schema/compensation.schema";
import { findCurrentWage, findNextCurrentWage, startOfDate } from "src/shared/helpers/logics";
import { PayScheduleDocument } from "src/pay-schedule/schema/pay-schedule.schema";

@Injectable()
export class CompensationService {
    constructor(
        private readonly positionService: PositionService,
        @InjectModel("Member") private readonly memberModel: Model<MemberDocument>,
        @InjectModel("Compensation") private readonly compensationModel: Model<CompensationDocument>,
        @InjectModel("PaySchedule") private readonly payScheduleModel: Model<PayScheduleDocument>,
    ) {}

    async createCompensation(
        userId: string,
        companyId: string,
        createCompensationDto: CreateCompensationDto,
        teamPermission: number,
    ) {
        try {
            const {
                memberId,
                positionId,
                createdBy,
                versionId,
                effectivePayPeriod,
                effectivePaySelected,
                payScheduleId,
                pieceWorkHourlyRate,
                ownPieceWork,
                crewPieceWork,
                reason,
                saleCommission,
                wageAmount,
                wageInterval,
                selfLead,
                useBenchmarkBonus,
                payDay,
            } = createCompensationDto;

            const [oldCompensation, member] = await Promise.all([
                this.compensationModel
                    .findOne({
                        companyId,
                        memberId,
                    })
                    .exec(),

                this.memberModel.findOne({ _id: createCompensationDto.memberId }),
            ]);

            const currentComp = findCurrentWage(oldCompensation?.wageHistory, new Date());

            if (currentComp) throw new HttpException("compensation already exists", HttpStatus.BAD_REQUEST);

            if (!member?.hireDate)
                throw new HttpException(
                    "Must enter the Hire Date before adding compensation",
                    HttpStatus.BAD_REQUEST,
                );

            await this.positionService.permissionCheck(
                userId,
                companyId,
                member._id,
                member.managerId,
                teamPermission,
            );

            let compensation;

            if (oldCompensation) {
                compensation = await this.compensationModel.findOneAndUpdate(
                    { _id: oldCompensation._id, memberId },
                    {
                        $set: {
                            positionId,
                        },
                        $push: {
                            wageHistory: {
                                positionId,
                                versionId,
                                wageAmount,
                                wageInterval,
                                payScheduleId,
                                effectivePayPeriod,
                                effectivePaySelected,
                                payDay,
                                saleCommission,
                                selfLead,
                                useBenchmarkBonus,
                                pieceWorkHourlyRate,
                                reason,
                                ownPieceWork,
                                crewPieceWork,
                                createdBy,
                                modifiedAt: new Date(),
                            },
                        },
                    },
                    { new: true },
                );
            } else {
                compensation = await this.compensationModel.create({
                    memberId: memberId,
                    companyId,
                    userId: member.user,
                    positionId,
                    wageHistory: [
                        {
                            positionId,
                            versionId,
                            wageAmount,
                            wageInterval,
                            payScheduleId,
                            effectivePayPeriod,
                            effectivePaySelected,
                            payDay,
                            saleCommission,
                            selfLead,
                            useBenchmarkBonus,
                            pieceWorkHourlyRate,
                            reason,
                            ownPieceWork,
                            crewPieceWork,
                            createdBy,
                            modifiedAt: new Date(),
                        },
                    ],
                });
            }

            return new CreatedResponse({ compensation, message: "Compensation created successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async deleteCompensation(
        userId: string,
        companyId: string,
        { memberId, effectivePayPeriod }: DeleteCompensationDto,
        teamPermission: number,
    ) {
        try {
            if (new Date(effectivePayPeriod) < startOfDate(new Date()))
                throw new HttpException("You can only delete upcoming compensation", HttpStatus.BAD_REQUEST);

            const member = await this.memberModel.findOne({ _id: memberId });
            await this.positionService.permissionCheck(
                userId,
                companyId,
                member._id,
                member.managerId,
                teamPermission,
            );

            const result = await this.compensationModel.findOneAndUpdate(
                {
                    memberId: memberId,
                    companyId,
                    "wageHistory.effectivePayPeriod": new Date(effectivePayPeriod),
                },
                {
                    $pull: {
                        wageHistory: {
                            effectivePayPeriod: new Date(effectivePayPeriod),
                        },
                    },
                },
                { new: true },
            );

            if (!result) {
                throw new BadRequestException("Compensation not found or no matching wage history entry.");
            }

            return new NoContentResponse({ message: "Compensation deleted successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updateCompensation(
        userId: string,
        companyId: string,
        createCompensationDto: CreateCompensationDto,
        teamPermission: number,
    ) {
        try {
            const {
                positionId,
                wageAmount,
                wageInterval,
                payScheduleId,
                effectivePayPeriod,
                effectivePaySelected,
                payDay,
                saleCommission,
                selfLead,
                useBenchmarkBonus,
                pieceWorkHourlyRate,
                reason,
                createdBy,
                memberId,
                versionId,
                ownPieceWork,
                crewPieceWork,
            } = createCompensationDto;
            const compensationData = await this.compensationModel.findOne({ companyId, memberId });
            const currCompensation = findCurrentWage(compensationData?.wageHistory, new Date());

            if (!currCompensation || Object.keys(currCompensation).length === 0)
                throw new HttpException("Compensation does not exists", HttpStatus.BAD_REQUEST);

            if (effectivePayPeriod < currCompensation?.effectivePayPeriod)
                throw new HttpException(
                    "Effective date should be higher than the previous effective date",
                    HttpStatus.BAD_REQUEST,
                );
            const member = await this.memberModel.findOne({ _id: createCompensationDto.memberId });
            await this.positionService.permissionCheck(
                userId,
                companyId,
                member._id,
                member.managerId,
                teamPermission,
            );

            //check for current position
            let currPosition = currCompensation.positionId;
            if (
                positionId !== currCompensation.positionId &&
                effectivePayPeriod === currCompensation?.effectivePayPeriod
            )
                currPosition = positionId;

            // Check if an object with the matching `payDay` exists
            const existingCompensation = await this.compensationModel.findOne({
                memberId: createCompensationDto.memberId,
                companyId,
                "wageHistory.payDay": payDay,
            });
            let updatedCompensation;
            if (existingCompensation) {
                // If an object with the matching `payDay` exists, update it
                updatedCompensation = await this.compensationModel.findOneAndUpdate(
                    {
                        memberId: createCompensationDto.memberId,
                        companyId,
                        "wageHistory.payDay": payDay,
                    },
                    {
                        $set: {
                            positionId: currPosition,
                            "wageHistory.$.positionId": positionId,
                            "wageHistory.$.versionId": versionId,
                            "wageHistory.$.wageAmount": wageAmount,
                            "wageHistory.$.wageInterval": wageInterval,
                            "wageHistory.$.payScheduleId": payScheduleId,
                            "wageHistory.$.effectivePayPeriod": effectivePayPeriod,
                            "wageHistory.$.effectivePaySelected": effectivePaySelected,
                            "wageHistory.$.saleCommission": saleCommission,
                            "wageHistory.$.selfLead": selfLead,
                            "wageHistory.$.useBenchmarkBonus": useBenchmarkBonus,
                            "wageHistory.$.pieceWorkHourlyRate": pieceWorkHourlyRate,
                            "wageHistory.$.reason": reason,
                            "wageHistory.$.ownPieceWork": ownPieceWork,
                            "wageHistory.$.crewPieceWork": crewPieceWork,
                            "wageHistory.$.createdBy": createdBy,
                            "wageHistory.$.modifiedAt": new Date(),
                        },
                    },
                    { new: true },
                );
            } else {
                //If no object with the matching `payDay` exists, push a new object
                updatedCompensation = await this.compensationModel.findOneAndUpdate(
                    {
                        memberId: createCompensationDto.memberId,
                        companyId,
                    },
                    {
                        $set: {
                            positionId: currPosition,
                        },
                        $push: {
                            wageHistory: {
                                positionId,
                                versionId,
                                wageAmount,
                                wageInterval,
                                payScheduleId,
                                effectivePayPeriod,
                                effectivePaySelected,
                                payDay,
                                saleCommission,
                                selfLead,
                                useBenchmarkBonus,
                                pieceWorkHourlyRate,
                                reason,
                                ownPieceWork,
                                crewPieceWork,
                                createdBy,
                                modifiedAt: new Date(),
                            },
                        },
                    },
                    { new: true },
                );
            }

            return new OkResponse({ updatedCompensation, message: "Compensation updated successfully!" });
        } catch (error: any) {
            // console.log(error);

            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getMemberCurrentCompensation(
        userId: string,
        companyId: string,
        memberId: string,
        teamPermission: number,
    ) {
        try {
            const member = await this.memberModel.findOne({ _id: memberId });

            // Check permissions using a separate function
            await this.positionService.permissionCheck(
                userId,
                companyId,
                member._id,
                member.managerId,
                teamPermission,
            );

            const currentDate = new Date();

            // Query to retrieve both current and upcoming compensations
            const compensationData = (
                await this.compensationModel.aggregate([
                    {
                        $match: {
                            companyId,
                            memberId,
                        },
                    },
                    {
                        $lookup: {
                            from: "Position",
                            localField: "positionId",
                            foreignField: "_id",
                            as: "position",
                            pipeline: [{ $project: { position: 1, symbol: 1 } }],
                        },
                    },
                    {
                        $unwind: "$position",
                    },
                ])
            )[0];
            const paySchedules = await this.payScheduleModel.find({ companyId }).select("_id name");

            // check to update current position of member
            if (compensationData) this.updateCurrentPositionOfMember(compensationData);

            const compensation: any = {
                _id: compensationData?._id,
                memberId: compensationData?.memberId,
                companyId: companyId,
                userId: compensationData?.userId,
                position: compensationData?.position,
                ...findCurrentWage(compensationData?.wageHistory, currentDate),
                subContractorId: compensationData?.subContractorId,
            };
            compensation.paySchedule = paySchedules.find((p) => p._id === compensation.payScheduleId);

            const upcomingComp: any = {
                _id: compensationData?._id,
                memberId: compensationData?.memberId,
                companyId: companyId,
                userId: compensationData?.userId,
                position: compensationData?.position,
                ...findNextCurrentWage(compensationData?.wageHistory, currentDate),
            };
            upcomingComp.paySchedule = paySchedules.find((p) => p._id === upcomingComp.payScheduleId);

            return new OkResponse({
                compensation: compensation?.effectivePayPeriod ? compensation : undefined,
                upcomingComp: upcomingComp?.effectivePayPeriod ? upcomingComp : undefined,
            });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getMemberAllCompensation(
        userId: string,
        companyId: string,
        memberId: string,
        teamPermission: number,
    ) {
        try {
            const member = await this.memberModel.findOne({ _id: memberId });

            // Check permissions using a separate function
            await this.positionService.permissionCheck(
                userId,
                companyId,
                member._id,
                member.managerId,
                teamPermission,
            );

            // Query to retrieve both current and upcoming compensations
            const [compensation] = await this.compensationModel.aggregate([
                {
                    $match: { companyId, memberId },
                },
                {
                    $unwind: "$wageHistory",
                },
                {
                    $lookup: {
                        from: "Position",
                        localField: "wageHistory.positionId",
                        foreignField: "_id",
                        as: "wageHistory.position",
                        pipeline: [{ $project: { position: 1 } }],
                    },
                },
                {
                    $unwind: "$wageHistory.position",
                },
                {
                    $set: {
                        "wageHistory.positionName": "$wageHistory.position.position",
                    },
                },
                {
                    $unset: "wageHistory.position", // Remove the original `position` field
                },
                {
                    $group: {
                        _id: "$_id",
                        companyId: { $first: "$companyId" },
                        memberId: { $first: "$memberId" },
                        wageHistory: { $push: "$wageHistory" },
                    },
                },
            ]);

            return new OkResponse({ compensation });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    //this function will update current position of member to outside positionId
    async updateCurrentPositionOfMember(compensationData: any) {
        try {
            if (!compensationData) return false;
            const { wageHistory, companyId, memberId, positionId } = compensationData;
            if (!companyId || !memberId) return false;

            const currentComp = findCurrentWage(wageHistory, new Date());
            if (currentComp && positionId !== currentComp.positionId)
                await this.compensationModel.updateOne(
                    {
                        companyId,
                        memberId,
                    },
                    {
                        $set: {
                            positionId: currentComp.positionId,
                        },
                    },
                );

            return true;
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }
}
