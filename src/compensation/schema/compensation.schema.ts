import { Schema, SchemaFactory, Prop } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import mongoose, { Document } from "mongoose";
import { WageIntervalEnum } from "../enum/wage-interval.enum";
import { UUIDProp } from "src/shared/decorator/uuid.decorator";

export type CompensationDocument = Compensation & Document;
export class Wage {
    @Prop({ required: true })
    positionId: string;

    @Prop({ required: false })
    versionId: string;

    @Prop()
    wageAmount?: number;

    @Prop({ type: Number, enum: WageIntervalEnum })
    wageInterval?: WageIntervalEnum;

    @Prop()
    payScheduleId?: string;

    @Prop({ required: true })
    effectivePayPeriod?: Date;

    @Prop({ required: false })
    effectivePaySelected?: string;

    @Prop()
    saleCommission?: number; // already in decimal no need to divide by 100

    @Prop()
    selfLead?: number; //stored in decimal for percentage eg. 10/100

    @Prop({ default: false })
    useBenchmarkBonus: boolean;

    @Prop()
    pieceWorkHourlyRate?: number;

    @Prop()
    reason?: string;

    @Prop({ required: false })
    ownPieceWork?: number; // already in decimal no need to divide by 100

    @Prop({ required: false })
    crewPieceWork?: number; // already in decimal no need to divide by 100

    @UUIDProp()
    createdBy: string;

    @Prop()
    modifiedAt?: Date;
}

@Schema({ timestamps: true, id: false, collection: "Compensation" })
export class Compensation {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @UUIDProp()
    companyId: string;

    @UUIDProp()
    memberId: string;

    @UUIDProp()
    userId: string;

    @UUIDProp()
    positionId: string;

    @Prop({ type: () => [Wage], required: false })
    wageHistory: Wage[];

    @Prop()
    createdAt?: Date;
}

export const CompensationSchema = SchemaFactory.createForClass(Compensation);
