import {
    Body,
    Controller,
    Delete,
    ForbiddenException,
    Get,
    Param,
    ParseUUI<PERSON>ip<PERSON>,
    Patch,
    Post,
} from "@nestjs/common";
import {
    ApiBearerAuth,
    ApiConflictResponse,
    ApiInternalServerErrorResponse,
    ApiNotFoundResponse,
    ApiOperation,
    ApiTags,
    ApiUnauthorizedResponse,
} from "@nestjs/swagger";
import { Positions } from "src/auth/guards/auth.guard";
import { PositionService } from "src/position/position.service";
import { PermissionsEnum } from "src/shared/enum/permission.enum";
import { CompensationService } from "./compensation.service";
import { CreateCompensationDto } from "./dto/create-compensation.dto";
import { DeleteCompensationDto } from "./dto/delete-compensation.dto";
import HttpResponse from "src/shared/http/response/response.http";
import { Auth, GetUser } from "src/auth/decorator/auth.decorator";
import { JwtUserPayload } from "src/auth/interface/auth.interface";
import { moduleNames } from "src/shared/constants/constant";

@ApiTags("Compensation")
@ApiBearerAuth()
@Auth()
@ApiUnauthorizedResponse({ description: "Unauthorized request" })
@ApiInternalServerErrorResponse({ description: "Server Error" })
@Controller({ path: "compensation", version: "1" })
export class CompensationController {
    constructor(
        private readonly positionService: PositionService,
        private readonly compensationService: CompensationService,
    ) {}

    /**
     * Creates a new compensation for a company.
     * @param userId The ID of the user creating the compensation.
     * @param createCompensationDto The data transfer object containing the compensation's information.
     * @returns An HTTP response with the result of the operation.
     * @throws ApiConflictException If a compensation with the same name already exists.
     * @throws ApiInternalServerErrorException If an unexpected error occurs in the server.
     * @throws ApiUnauthorizedException If the request is not authorized.
     */
    @ApiOperation({ summary: "Create Compensation" })
    @ApiConflictResponse({ description: "Compensation already exist" })
    @Positions({
        category: "module",
        name: moduleNames.module.team,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed],
    })
    @Post("create-compensation")
    async createCompensation(
        @GetUser() user: JwtUserPayload,
        @Body() createCompensationDto: CreateCompensationDto,
    ): Promise<HttpResponse> {
        if (user.symbol !== "Owner" && user.memberId === createCompensationDto.memberId)
            throw new ForbiddenException("Updating your own compensation is not allowed");
        return this.compensationService.createCompensation(
            user._id,
            user.companyId,
            createCompensationDto,
            user.teamPermission,
        );
    }

    /**
     *Endpoint to update a compensation with the given ID.
     *Requires authentication and authorization as an Admin or Owner.
     *@summary Update Compensation
     *@param {string} userId - The ID of the user performing the request.
     *@param {UpdateCompensationDto} updateCompensationDto - The data to use for updating the compensation.
     *@returns {Promise<HttpResponse>} An HTTP response containing the updated compensation or an error message.
     *@throws {NotFoundException} If the compensation with the given ID is not found.
     *@throws {InternalServerErrorException} If a server error occurs.
     */
    @ApiOperation({ summary: "Update Compensation" })
    @ApiNotFoundResponse({ description: "Compensation not found" })
    @Positions({
        category: "module",
        name: moduleNames.module.team,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed],
    })
    @Patch("update-compensation")
    async updateCompensation(
        @GetUser() user: JwtUserPayload,
        @Body() createCompensationDto: CreateCompensationDto,
    ): Promise<HttpResponse> {
        if (user.symbol !== "Owner" && user.memberId === createCompensationDto.memberId)
            throw new ForbiddenException("Updating your own compensation is not allowed");
        return this.compensationService.updateCompensation(
            user._id,
            user.companyId,
            createCompensationDto,
            user.teamPermission,
        );
    }

    /**
    Endpoint to delete a compensation.
    *@summary Delete Compensation
    *@param {string} userId The ID of the user making the request.
    *@param {DeleteCompensationDto} deleteCompensationDto The DTO containing the details of the compensation to be deleted.
    *@returns {Promise<HttpResponse>} A promise that resolves to an HTTP response indicating the success or failure of the operation.
    *@throws {ApiNotFoundException} If the specified compensation is not found.
    *@throws {ApiInternalServerErrorException} If an internal server error occurs.
    *@throws {ApiUnauthorizedException} If the user making the request is not authorized to perform the operation.
    */
    @ApiOperation({ summary: "Delete Compensation" })
    @ApiNotFoundResponse({ description: "Compensation not found" })
    @Positions({
        category: "module",
        name: moduleNames.module.team,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed],
    })
    @Delete("delete-compensation")
    async deleteCompensation(
        @GetUser() user: JwtUserPayload,
        @Body() deleteCompensationDto: DeleteCompensationDto,
    ): Promise<HttpResponse> {
        return this.compensationService.deleteCompensation(
            user._id,
            user.companyId,
            deleteCompensationDto,
            user.teamPermission,
        );
    }

    /**
     *Endpoint to get company member's compensation.
     *@summary Get Compensation
     *@param {string} userId - The ID of the user performing the request.
     *@param {string} companyId - The ID of the company.
     *@param {string} memberId - The ID of the member.
     *@param deleted - Whether to include deleted compensation in the results.
     *@param paginationRequestDto - The pagination options for the query.
     *@returns A response containing an array of compensation.
     *@throws {HttpException} When a client error occurs (e.g. invalid input parameters).
     *@throws {InternalServerErrorException} When a server error occurs.
     */
    @ApiOperation({ summary: "Get Member Compensation" })
    @Positions({
        category: "module",
        name: moduleNames.module.team,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed, PermissionsEnum.Self],
    })
    @Get("get-member-compensation/member/:memberId")
    async getMemberCompensation(
        @GetUser() user: JwtUserPayload,
        @Param("memberId", ParseUUIDPipe) memberId: string,
    ): Promise<HttpResponse> {
        return this.compensationService.getMemberCurrentCompensation(
            user._id,
            user.companyId,
            memberId,
            user.teamPermission,
        );
    }

    /**
     *Endpoint to get company member's compensation history.
     *@summary Get Compensation History
     *@param {string} userId - The ID of the user performing the request.
     *@param {string} companyId - The ID of the company.
     *@param {string} memberId - The ID of the member.
     *@param deleted - Whether to include deleted compensation in the results.
     *@param paginationRequestDto - The pagination options for the query.
     *@returns A response containing an array of compensation.
     *@throws {HttpException} When a client error occurs (e.g. invalid input parameters).
     *@throws {InternalServerErrorException} When a server error occurs.
     */
    @ApiOperation({ summary: "Get Member Compensation History" })
    @Positions({
        category: "module",
        name: moduleNames.module.team,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed, PermissionsEnum.Self],
    })
    @Get("member-compensation-history/member/:memberId")
    async getMemberAllCompensation(
        @GetUser() user: JwtUserPayload,
        @Param("memberId", ParseUUIDPipe) memberId: string,
    ): Promise<HttpResponse> {
        return this.compensationService.getMemberAllCompensation(
            user._id,
            user.companyId,
            memberId,
            user.teamPermission,
        );
    }
}
