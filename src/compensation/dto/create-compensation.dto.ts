import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsString, IsNotEmpty, IsOptional, IsEnum, IsUUID, IsNumber, IsBoolean } from "class-validator";
import { WageIntervalEnum } from "../enum/wage-interval.enum";
import { Transform } from "class-transformer";

export class CreateCompensationDto {
    // @ApiProperty({ description: "Company", required: true })
    // @IsUUID()
    // @IsNotEmpty()
    // companyId: string;

    @ApiProperty({ description: "Member", required: true })
    @IsUUID()
    @IsNotEmpty()
    memberId: string;

    @ApiPropertyOptional({ description: "version", required: false })
    @IsOptional()
    @IsUUID()
    versionId: string;

    @ApiProperty({ description: "Created By", required: true })
    @IsUUID()
    @IsNotEmpty()
    createdBy: string;

    @ApiPropertyOptional({ description: "Position" })
    @IsUUID()
    @IsOptional()
    positionId?: string;

    @ApiPropertyOptional({ description: "Wage Amt" })
    @IsNumber()
    @IsOptional()
    wageAmount?: number;

    @ApiPropertyOptional({ description: "Wage Interval" })
    @IsNumber()
    @IsOptional()
    @IsEnum(WageIntervalEnum)
    wageInterval?: WageIntervalEnum;

    @ApiPropertyOptional({ description: "Pay schedule" })
    @IsUUID()
    @IsOptional()
    payScheduleId?: string;

    @ApiProperty({ description: "Effective Pay Period to save in backend" })
    @Transform(({ value }) => new Date(value))
    @IsNotEmpty()
    effectivePayPeriod: Date;

    @ApiPropertyOptional({ description: "Effective Pay Period selected string" })
    @IsString()
    @IsOptional()
    effectivePaySelected?: string;

    @ApiPropertyOptional({ description: "Pay Day to save in backend" })
    @IsOptional()
    @Transform(({ value }) => new Date(value))
    payDay?: Date;

    @ApiPropertyOptional({ description: "Sale commission" })
    @IsNumber()
    @IsOptional()
    saleCommission?: number;

    @ApiPropertyOptional({ description: "Piece work" })
    @IsNumber()
    @IsOptional()
    pieceWorkHourlyRate?: number;

    @ApiPropertyOptional({ description: "Reason" })
    @IsString()
    @IsOptional()
    reason?: string;

    @ApiPropertyOptional({ description: "pwMod" })
    @IsNumber()
    @IsOptional()
    ownPieceWork?: number;

    @ApiPropertyOptional({ description: "hrMod" })
    @IsNumber()
    @IsOptional()
    crewPieceWork?: number;

    @ApiPropertyOptional({ description: "selfLead" })
    @IsOptional()
    @IsNumber()
    selfLead?: number;

    @ApiPropertyOptional({ description: "the benchmark bonus system is enabled or disabled", default: false })
    @IsOptional()
    @IsBoolean()
    useBenchmarkBonus?: boolean;
}
