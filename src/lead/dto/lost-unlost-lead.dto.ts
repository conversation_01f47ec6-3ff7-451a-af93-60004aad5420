import { ApiProperty } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsNotEmpty, IsString } from "class-validator";

export class LostUnLostLeadDto {
    @ApiProperty({ description: "reason", required: true })
    @IsString()
    @IsNotEmpty()
    reason: string;

    @ApiProperty({ description: "lost date", required: true })
    @Transform(({ value }) => new Date(value))
    @IsNotEmpty()
    date: Date;
}
