import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsUUID } from "class-validator";
import { DeleteRestoreDto } from "src/shared/dto/delete-retore.dto";

export class DeleteLeadCommentDto extends DeleteRestoreDto {
    @ApiProperty({ description: "Lead Id", required: true })
    @IsUUID()
    @IsNotEmpty()
    leadId: string;

    @ApiProperty({ description: "Member Id", required: true })
    @IsUUID()
    @IsNotEmpty()
    memberId: string;
}
