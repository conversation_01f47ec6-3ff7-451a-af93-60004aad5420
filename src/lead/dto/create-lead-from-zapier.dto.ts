import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsOptional, IsString, IsBoolean } from "class-validator";

export class CreateLeadFromZapierDto {
    @ApiPropertyOptional({ description: "Full name of the lead" })
    @IsOptional()
    @IsString()
    name: string;

    @ApiProperty({ description: "First name of the lead" })
    @IsString()
    firstName: string;

    @ApiPropertyOptional({ description: "Last name of the lead" })
    @IsOptional()
    @IsString()
    lastName?: string;

    @ApiPropertyOptional({ description: "Email address of the lead" })
    @IsOptional()
    @IsString()
    email?: string;

    @ApiPropertyOptional({ description: "Lowercase email address of the lead" })
    @IsOptional()
    @IsString()
    emailLowerCase?: string;

    @ApiPropertyOptional({ description: "Timezone of the lead" })
    @IsOptional()
    @IsString()
    timezone?: string;

    @ApiPropertyOptional({ description: "Company name associated with the lead" })
    @IsOptional()
    @IsString()
    companyName?: string;

    @ApiPropertyOptional({ description: "Phone number of the lead" })
    @IsOptional()
    @IsString()
    @Transform(({ value }) => {
        if (value === null || value === undefined) return value;
        return String(value).replace(/\D/g, "");
    })
    phone?: string;

    @ApiPropertyOptional({ description: "Indicates if Do Not Disturb is enabled" })
    @IsOptional()
    @IsBoolean()
    dnd: boolean;

    @ApiPropertyOptional({ description: "Settings for Do Not Disturb" })
    @IsOptional()
    dndSettings?: object;

    @ApiPropertyOptional({ description: "Type of lead" })
    @IsOptional()
    @IsString()
    type?: string;

    @ApiPropertyOptional({ description: "Source of lead registration" })
    @IsOptional()
    @IsString()
    source?: string;

    @ApiPropertyOptional({ description: "Street address of the lead" })
    @IsOptional()
    @IsString()
    address1?: string;

    @ApiPropertyOptional({ description: "State of the lead" })
    @IsOptional()
    @IsString()
    state?: string;

    @ApiPropertyOptional({ description: "City of the lead" })
    @IsOptional()
    @IsString()
    city?: string;

    @ApiPropertyOptional({ description: "Zip code of the lead" })
    @IsOptional()
    @IsString()
    postalCode?: string;

    @ApiPropertyOptional({ description: "The attribution source" })
    @IsOptional()
    attributionSource: object;

    @ApiPropertyOptional({ description: "The last attribution source" })
    @IsOptional()
    lastAttributionSource: object;
}
