import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsBoolean, IsDate, IsNotEmpty, IsOptional, IsString, IsUUID } from "class-validator";

export class UpdateLeadChecklistDto {
    // @ApiProperty({ description: "Company Id" })
    // @IsUUID()
    // @IsNotEmpty()
    // companyId: string;

    @ApiProperty({ description: "Lead Id" })
    @IsUUID()
    @IsNotEmpty()
    leadId: string;

    @ApiProperty({ description: "Stage uuid" })
    @IsString()
    @IsNotEmpty()
    stage: string;

    @ApiProperty({ description: "Current date" })
    @IsDate()
    @Transform(({ value }) => new Date(value))
    @IsNotEmpty()
    currDate: Date;

    @ApiProperty({ description: "uuid of step" })
    @IsString()
    @IsNotEmpty()
    key: string;

    @ApiPropertyOptional({ description: "value" })
    // @IsString()
    @IsOptional()
    value?: any;

    @ApiProperty({ description: "boolean", default: false })
    @IsBoolean()
    @IsNotEmpty()
    boolean: boolean;

    @ApiProperty({ description: "Updated by" })
    @IsUUID()
    @IsNotEmpty()
    updatedBy: string;
}
