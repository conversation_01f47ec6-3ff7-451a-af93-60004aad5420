import { ApiProperty } from "@nestjs/swagger";
import { IsDate, IsNotEmpty, IsString } from "class-validator";
import { Transform } from "class-transformer";
import { DeleteLeadCommentDto } from "./delete-lead-comment.dto";

export class UpdateLeadCommentDto extends DeleteLeadCommentDto {
    @ApiProperty({ description: "Notes", required: true })
    @IsString()
    @IsNotEmpty()
    body: string;

    @ApiProperty({ description: "Current Date", required: true })
    @IsDate()
    @Transform(({ value }) => new Date(value))
    @IsNotEmpty()
    currDate: Date;
}
