import { ApiPropertyOptional } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsUUID, IsOptional, IsEnum } from "class-validator";
import { StageGroupEnum } from "src/crm/enum/stage-group.enum";
import { PaginationDto } from "src/shared/dto/pagination.dto";

export class GetLeadDto extends PaginationDto {
    @ApiPropertyOptional({ description: "csr id" })
    @IsUUID()
    @IsOptional()
    csrId?: string;

    // @ApiPropertyOptional({ description: "project manager" })
    // @IsUUID()
    // @IsOptional()
    // projectManager?: string;

    @ApiPropertyOptional({ enum: StageGroupEnum })
    @IsOptional()
    @IsEnum(StageGroupEnum)
    stageGroup?: StageGroupEnum;

    @ApiPropertyOptional()
    @IsOptional()
    @Transform(({ value }) => value === "true")
    lost?: boolean;

    @ApiPropertyOptional({ description: "for active & inactive opps" })
    @IsOptional()
    status?: string;
}
