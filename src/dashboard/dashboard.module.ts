import { Modu<PERSON> } from "@nestjs/common";
import { DashboardController } from "./dashboard.controller";
import { DashboardService } from "./dashboard.service";
import { MongooseModule } from "@nestjs/mongoose";
import { CompanySettingSchema } from "src/company/schema/company-setting.schema";
import { MemberSchema } from "src/company/schema/member.schema";
import { CrewSchema } from "src/crew/schema/crew-management.schema";
import { CrewMemberSchema } from "src/crew/schema/crew-member.schema";
import { OpportunitySchema } from "src/opportunity/schema/opportunity.schema";
import { PositionModule } from "src/position/position.module";
import { PositionSchema } from "src/position/schema/position.schema";
import { CrewPositionSchema } from "src/project/schema/crew-position.schema";
import { RoleModule } from "src/role/role.module";
import { UserSchema } from "src/user/schema/user.schema";
import { TimeCardSchema } from "src/time-card/schema/time-card.schema";
import { CrewModule } from "src/crew/crew.module";
import { OrderSchema } from "src/project/schema/order.schema";
import { CrmStageSchema } from "src/crm/schema/crm-stage.schema";
import { CompensationSchema } from "src/compensation/schema/compensation.schema";
import { CrmModule } from "src/crm/crm.module";
import { ProjectSchema } from "src/project/schema/project.schema";
import { ProjectTypeSchema } from "src/project/schema/project-type.schema";
import { PieceWorkSchema } from "src/piece-work/schema/piece-work.schema";
import { DailyLogSchema } from "src/daily-log/schema/daily-log.schema";
import { WorkTaskSchema } from "src/work-task/schema/work-task.schema";
import { CompanyPaySchema } from "src/company/schema/company-pay.schema";
import { PieceWorkSettingSchema } from "src/piece-work/schema/piece-work-setting.schema";
import { CommissionModificationSchema } from "src/opportunity/schema/opp-commission.schema";
import { LeadSchema } from "src/lead/schema/lead.schema";
import { ContactSchema } from "src/contacts/schema/contact.schema";

@Module({
    imports: [
        MongooseModule.forFeature([
            { name: "CompanySetting", schema: CompanySettingSchema },
            { name: "Member", schema: MemberSchema },
            { name: "User", schema: UserSchema },
            { name: "Position", schema: PositionSchema },
            { name: "Crew", schema: CrewSchema },
            { name: "CrewMember", schema: CrewMemberSchema },
            { name: "CrewPosition", schema: CrewPositionSchema },
            { name: "Opportunity", schema: OpportunitySchema },
            { name: "PieceWork", schema: PieceWorkSchema },
            { name: "TimeCard", schema: TimeCardSchema },
            { name: "Order", schema: OrderSchema },
            { name: "CrmStage", schema: CrmStageSchema },
            { name: "Compensation", schema: CompensationSchema },
            { name: "CompanyPay", schema: CompanyPaySchema },
            { name: "Project", schema: ProjectSchema },
            { name: "ProjectType", schema: ProjectTypeSchema },
            { name: "DailyLog", schema: DailyLogSchema },
            { name: "WorkTask", schema: WorkTaskSchema },
            { name: "PieceWorkSetting", schema: PieceWorkSettingSchema },
            { name: "CommissionModification", schema: CommissionModificationSchema },
            { name: "Lead", schema: LeadSchema },
            { name: "Contact", schema: ContactSchema },
        ]),
        // RoleModule,
        // PositionModule,
        CrewModule,
        CrmModule,
        PositionModule,
        // WorkTaskModule,
    ],
    controllers: [DashboardController],
    providers: [DashboardService],
})
export class DashboardModule {}
