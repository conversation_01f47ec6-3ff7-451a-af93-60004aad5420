import { HttpException, Injectable, InternalServerErrorException } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";
import { CompanySettingDocument } from "src/company/schema/company-setting.schema";
import { MemberDocument } from "src/company/schema/member.schema";
import { CrewService } from "src/crew/crew.service";
import { CrewDocument } from "src/crew/schema/crew-management.schema";
import { CrmStageDocument } from "src/crm/schema/crm-stage.schema";
import { OpportunityDocument } from "src/opportunity/schema/opportunity.schema";
import { OrderDocument } from "src/project/schema/order.schema";
import {
    activeCrewMember,
    calcCrewLeadBonus,
    dedupeArray,
    findCrewLeadId,
    findCurrentWage,
    formatDate,
    isWeekend,
    roundTo1,
    roundTo2,
    sumArray,
} from "src/shared/helpers/logics";
import { StatusEnum } from "src/time-card/enum/status.enum";
import { TimeCardDocument } from "src/time-card/schema/time-card.schema";
import { UserDocument } from "src/user/schema/user.schema";
import { PaginationRequestDto } from "./dto/pagination.dto";
import { CompanyPayDocument } from "src/company/schema/company-pay.schema";
import { CompensationDocument } from "src/compensation/schema/compensation.schema";
import { CrmService } from "src/crm/crm.service";
import { ProjectTypeDocument } from "src/project/schema/project-type.schema";
import { CrewMemberDocument } from "src/crew/schema/crew-member.schema";
import OkResponse from "src/shared/http/response/ok.http";
import { PieceWorkDocument } from "src/piece-work/schema/piece-work.schema";
import { DailyLogDocument } from "src/daily-log/schema/daily-log.schema";
import { WageIntervalEnum } from "src/compensation/enum/wage-interval.enum";
import { WorkTaskDocument } from "src/work-task/schema/work-task.schema";
import { PieceWorkSettingDocument } from "src/piece-work/schema/piece-work-setting.schema";
import { CommissionModificationDocument } from "src/opportunity/schema/opp-commission.schema";
import { LeadDocument } from "src/lead/schema/lead.schema";
import { StageGroupEnum } from "src/crm/enum/stage-group.enum";
import { PositionService } from "src/position/position.service";
import { OpportunityStatusEnum } from "src/opportunity/enum/opportunityStatus.enum";
import { ContactDocument } from "src/contacts/schema/contact.schema";

@Injectable()
export class DashboardService {
    constructor(
        @InjectModel("Opportunity") private readonly opportunityModel: Model<OpportunityDocument>,
        @InjectModel("Lead") private readonly leadModel: Model<LeadDocument>,
        @InjectModel("CompanySetting") private readonly companySettingModel: Model<CompanySettingDocument>,
        @InjectModel("PieceWork")
        private readonly pieceWorkModel: Model<PieceWorkDocument>,
        @InjectModel("User") private readonly userModel: Model<UserDocument>,
        @InjectModel("Crew") private readonly crewModel: Model<CrewDocument>,
        @InjectModel("Member") private readonly memberModel: Model<MemberDocument>,
        @InjectModel("TimeCard") private readonly timeCardModel: Model<TimeCardDocument>,
        @InjectModel("Order") private readonly orderModel: Model<OrderDocument>,
        @InjectModel("CrmStage") private readonly crmStageModel: Model<CrmStageDocument>,
        @InjectModel("CompanyPay")
        private readonly companyPayModel: Model<CompanyPayDocument>,
        @InjectModel("Compensation") private readonly compensationModel: Model<CompensationDocument>,
        @InjectModel("ProjectType") private readonly projectTypeModel: Model<ProjectTypeDocument>,
        @InjectModel("CrewMember") private readonly crewMemberModel: Model<CrewMemberDocument>,
        @InjectModel("DailyLog") private readonly dailyLogModel: Model<DailyLogDocument>,
        @InjectModel("WorkTask") private readonly workTaskModel: Model<WorkTaskDocument>,
        @InjectModel("PieceWorkSetting")
        private readonly pieceWorkSettingModel: Model<PieceWorkSettingDocument>,
        @InjectModel("CommissionModification")
        private readonly commissionModificationModel: Model<CommissionModificationDocument>,
        @InjectModel("Contact") private readonly contactModel: Model<ContactDocument>,
        private readonly crewService: CrewService,
        private readonly crmService: CrmService,
        private readonly positionService: PositionService,
    ) {}

    async crewCardsAndPieceWork(
        userId: string,
        Roles: string,
        companyId: string,
        startDate: Date,
        endDate: Date,
    ) {
        try {
            let crews;
            let users, cards;
            //NOTE: Permision to be added
            // if (Roles.userIsInRole(userId, [ 'madmin', 'Manager', 'Project Manager' ])) {
            if (Roles === "Manager") {
                crews = await this.crewModel.find({ companyId, deleted: { $ne: true } });
                users = await this.userModel.find({});
                cards = await this.timeCardModel
                    .find({
                        timeIn: { $gte: startDate, $lte: endDate },
                        deleted: { $ne: true },
                    })
                    .sort({ timeIn: -1 });
                // } else if (Roles.userIsInRole(this.userId, [ 'Crew Lead' ])) {
            } else {
                crews = await this.crewModel.find({ lead: userId, deleted: { $ne: true } });
                const ledCrews = crews.fetch();
                const memberArray = [];
                ledCrews &&
                    ledCrews.map((crew) => {
                        crew.members &&
                            crew.members.map((member) => {
                                if (activeCrewMember(member, startDate, endDate))
                                    memberArray.push(member.memberId);
                            });
                    });
                users = await this.memberModel.find(
                    {
                        _id: { $in: memberArray },
                        deleted: false,
                    },
                    // {
                    //     fields: {
                    //         firstName: 1,
                    //         lastName: 1,
                    //         username: 1,
                    //         roles: 1,
                    //         deleted: 1,
                    //         terminated: 1,
                    //         daysOff: 1,
                    //         wage: 1,
                    //     },
                    // },
                );
                cards = await this.timeCardModel
                    .find({
                        companyId,
                        memberId: { $in: memberArray },
                        timeIn: { $gte: startDate, $lte: endDate },
                        deleted: { $ne: true },
                    })
                    .sort({ timeIn: -1 });
            }

            const cardIdArray =
                cards &&
                cards.map((card) => {
                    return card._id;
                });
            const work = await this.pieceWorkModel.find({
                companyId,
                cardId: { $in: cardIdArray },
                deleted: { $ne: true },
            });
            const oppIdArray =
                cards &&
                cards.map((card) => {
                    return card.projectId;
                });
            const opps = await this.opportunityModel.find(
                {
                    _id: { $in: oppIdArray },
                    companyId,
                    deleted: { $ne: true },
                },
                {
                    fields: {
                        PO: 1,
                        num: 1,
                        distance: 1,
                    },
                },
            );
            const variables = await this.companySettingModel.findOne(
                {
                    companyId,
                },
                {
                    fields: {
                        travelFee: 1,
                        weekendBonus: 1,
                    },
                },
            );
            // if (
            //     Roles.userIsInRole(this.userId, [
            //         "madmin",
            //         "Manager",
            //         "Project Manager",
            //         "Sales Manager",
            //         "Crew Lead",
            //     ])
            // ) {

            return [crews, users, cards, work, opps, variables];
            // } else {
            //     return this.ready();
            // }
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    // crew dashboard
    async crewTimeCards(companyId: string, memberId: string, weekStart: Date, startDate: Date) {
        try {
            // collect dates info
            const today = new Date(startDate);
            const weekEnd = new Date(weekStart);

            const [member, crewData, companySetting, allWorkTaskData, pwSettingAllData] = await Promise.all([
                this.memberModel.findOne({ _id: memberId }),
                this.crewMemberModel
                    .findOne({
                        companyId,
                        memberId: memberId,
                        startDate: { $lte: weekStart },
                        $or: [{ removeDate: { $exists: false } }, { removeDate: { $gte: weekStart } }],
                    })
                    .populate("crewId", "_id leadHistory", "Crew"),
                this.companySettingModel.findOne({
                    companyId,
                }),

                this.workTaskModel.find({ companyId }),

                this.pieceWorkSettingModel.find({
                    companyId: companyId,
                }),
            ]);

            weekEnd.setDate(weekEnd.getDate() + 7);
            weekEnd.setMilliseconds(weekEnd.getMilliseconds() - 1);

            const data = [];
            let weekBase = 0;
            let weekPwEarned = 0;
            let weekEarned = 0;
            let weekTravel = 0;
            let weekHours = 0;
            let weekBonus = 0;
            weekEnd.setMilliseconds(weekEnd.getMilliseconds() + 1);
            weekStart.setMilliseconds(weekStart.getMilliseconds() - 1);
            for (let i = weekEnd; i > weekStart; i.setDate(i.getDate() - 1)) {
                const startOfDay = new Date(i);
                const endOfDay = new Date(i);
                endOfDay.setDate(endOfDay.getDate() + 1);
                endOfDay.setMilliseconds(endOfDay.getMilliseconds() - 1);

                if (startOfDay > today) continue;

                //finding crew lead
                const crew: any = crewData?.crewId;
                const crewLeadId = findCrewLeadId(crew, startOfDay);

                const dayObj = await this.crewService.memberDayActivity(
                    companyId,
                    memberId,
                    startOfDay,
                    endOfDay,
                    crewLeadId,
                    pwSettingAllData,
                    allWorkTaskData,
                    companySetting,
                );
                // dayObj.crewLead = memb;

                // Calc for lead bonus
                if (dayObj.crewLead) {
                    // if crew lead then we also need to get bonus so we calculate crew members piecework
                    const leadData = await this.crewLeadCalc(
                        crew?._id,
                        companyId,
                        startOfDay,
                        endOfDay,
                        crewLeadId,
                        dayObj?.wageData?.crewPieceWork || 0,
                    );

                    dayObj.leadBonus = dayObj.dayOff ? 0 : dayObj?.cards?.length === 0 ? 0 : leadData;
                    dayObj.totalEarned += dayObj.leadBonus;
                }

                dayObj.name = member?.name;
                dayObj.officePerson = dayObj.wageType === "salary" && !dayObj.pwExtraHourRate ? true : false;
                weekBase += dayObj.dayBase;
                weekPwEarned += dayObj.pwTotal;
                weekTravel += dayObj.travel;
                weekEarned += dayObj.totalEarned;
                weekHours += dayObj.hours;
                weekBonus += dayObj?.leadBonus || 0;
                data.push(dayObj);
            }

            const weekEarning = data[0]?.officePerson
                ? false
                : { weekBase, weekPwEarned, weekEarned, weekTravel, weekHours, weekBonus };

            return new OkResponse({ data, weekEarning });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    ///////////////////////
    //Crew Lead Dashboard
    async crewLeadCalc(
        crewId: string,
        companyId: string,
        approveStart: Date,
        approveEnd: Date,
        crewLeadId: string,
        crewPieceWork: number,
    ) {
        try {
            const crew: any = (
                await this.crewModel.aggregate([
                    {
                        $match: {
                            _id: crewId,
                            companyId,
                            startDate: { $lte: approveStart },
                            $or: [{ endDate: { $exists: false } }, { endDate: { $gte: approveStart } }],
                            deleted: { $ne: true },
                            // retired: { $ne: true },
                        },
                    },
                    {
                        $lookup: {
                            from: "CrewMember",
                            pipeline: [
                                {
                                    $match: {
                                        startDate: { $lte: approveStart },
                                        $or: [
                                            { removeDate: { $exists: false } },
                                            { removeDate: { $gte: approveStart } },
                                        ],
                                    },
                                },
                            ],
                            localField: "_id",
                            foreignField: "crewId",
                            as: "members",
                        },
                    },
                    {
                        $sort: { order: 1 },
                    },
                ])
            )[0];
            const workTasks = await this.workTaskModel.find();

            const crewReport: any = {};
            crewReport.members = [];
            const filtered = !crew.members
                ? []
                : crew.members.filter((member) => {
                      return activeCrewMember(member, approveStart, approveEnd);
                  });
            for (const member of filtered) {
                const report: any = {
                    date: approveStart,
                    cards: [],
                    pwTotal: 0,
                    crewLead: member.memberId === crewLeadId,
                    wageData: member.memberId === crewLeadId ? { crewPieceWork } : { crewPieceWork: 0 },
                    removeFromLead: 0, // $ amount to remove from lead bonus calc
                };

                const timeCards = await this.timeCardModel
                    .find({
                        memberId: member.memberId,
                        timeIn: { $gte: approveStart, $lte: approveEnd },
                        deleted: { $ne: true },
                    })
                    .sort({ timeIn: 1 })
                    .exec();
                // // Find piece work
                const cardArray = timeCards.map((card) => card._id);
                const pieceWork = await this.pieceWorkModel
                    .find({
                        timeCardId: { $in: cardArray },
                        deleted: { $ne: true },
                    })
                    .exec();

                // Attach piece work to time card
                timeCards.map((card: any) => {
                    let cardObject: any = {};
                    cardObject = { ...card };
                    pieceWork.map((work) => {
                        if (card._id === work.timeCardId) {
                            cardObject = { ...cardObject, work };
                        }
                    });
                    report.cards.push(cardObject);
                });
                // Find and dedupe project array
                const projectArray = timeCards.map((card) => {
                    return card.projectId;
                });
                const allProjects = dedupeArray(projectArray);

                allProjects.forEach((projectId) => {
                    if (projectId === "" || !projectId) return false;

                    // let addTravel = false;
                    for (let i = 0; i < report.cards.length; i++) {
                        const card = report.cards[i];
                        const workTaskData = workTasks.find((w) => w._id === card.task);

                        if (card?.projectId === projectId) {
                            const pwSqs = card?.work?.sqsEarnings || 0;
                            const pwExtras = card?.work?.extrasEarnings || 0;
                            const pwHourly = card?.work?.hourlyEarnings || 0;
                            const pwTotal = pwSqs + pwExtras + pwHourly;

                            report.pwTotal += pwTotal;
                            if (card.work?.removeLeadBonus || !workTaskData.pieceWork) {
                                report.removeFromLead += pwTotal;
                            }
                        }
                    }
                });
                crewReport.members.push(report);
            }
            let leadPay = 0;
            leadPay = calcCrewLeadBonus(crewReport.members);

            return leadPay;
        } catch (e) {
            console.log(e);
        }
    }

    async dayReport(userId: string, companyId: string, start: Date, end: Date) {
        try {
            const dayStart = new Date(start);
            const dayEnd = new Date(end);

            dayEnd.setDate(dayEnd.getDate() + 1);
            dayEnd.setMilliseconds(dayEnd.getMilliseconds() - 1);
            const memberData = await this.memberModel.findOne({
                company: companyId,
                user: userId,
                deleted: false,
            });

            const [crewData, companySetting, allWorkTaskData, pwSettingAllData] = await Promise.all([
                this.crewMemberModel
                    .findOne({
                        companyId,
                        memberId: memberData._id,
                        promoted: true,
                    })
                    .populate("crewId", "_id leadHistory", "Crew"),
                this.companySettingModel.findOne({
                    companyId,
                }),

                this.workTaskModel.find({ companyId }),

                this.pieceWorkSettingModel.find({
                    companyId: companyId,
                }),
            ]);

            let crewMembers;
            const dayObj: any = {};
            if (crewData) {
                //finding crew lead
                const crew: any = crewData.crewId;
                const crewLeadId = findCrewLeadId(crew, dayStart);

                crewMembers = await this.crewMemberModel.find({
                    companyId,
                    crewId: crew?._id,
                    deleted: false,
                });

                const memberArray = [];
                crewMembers.length &&
                    crewMembers.map((member) => {
                        if (activeCrewMember(member, dayStart, dayEnd)) memberArray.push(member.memberId);
                    });
                const members = await this.userModel.find({
                    _id: { $in: memberArray },
                });

                const leadActivity = await this.crewService.memberDayActivity(
                    companyId,
                    crewData._id,
                    dayStart,
                    dayEnd,
                    crewLeadId,
                    pwSettingAllData,
                    allWorkTaskData,
                    companySetting,
                );
                let leadBonus = 0;

                const memberCards = members.map(async (member) => {
                    const memObj = await this.crewService.memberDayActivity(
                        companyId,
                        member._id,
                        dayStart,
                        dayEnd,
                        crewLeadId,
                        pwSettingAllData,
                        allWorkTaskData,
                        companySetting,
                    );
                    const leadPay = leadActivity.dayOff ? 0 : calcCrewLeadBonus([memObj]);
                    memObj.leadPay = leadPay;
                    leadBonus += leadPay;
                    return memObj;
                });
                dayObj.leadActivity = leadActivity;
                dayObj.leadBonus = leadBonus;
                dayObj.earned = roundTo2(leadActivity.totalEarned - leadActivity.pwTotal + dayObj.leadBonus);
                dayObj.members = memberCards;
            }

            return new OkResponse({ dayObj });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async unapprovedCards(
        position: any,
        companyId: string,
        memberId: string,
        d: Date,
        // end: Date
    ) {
        try {
            const date = new Date(d);

            // Set start of period to check (1 months)
            const start = new Date(date);
            start.setMonth(start.getMonth() - 1);
            // Set end of check period
            const end = new Date(date);
            end.setDate(end.getDate() + 1);
            end.setMilliseconds(end.getMilliseconds() - 1);

            let getQuery;
            if (position.symbol === "Owner" || position.symbol === "Admin") {
                getQuery = {
                    companyId,
                    deleted: { $ne: true },
                    startDate: { $lte: start },
                    $or: [{ endDate: { $exists: false } }, { endDate: { $gte: end } }],
                };
            } else {
                getQuery = {
                    companyId,
                    $and: [{ $or: [{ managerId: position.memberId }, { foremanId: position.memberId }] }],
                    deleted: { $ne: true },
                    startDate: { $lte: start },
                    $or: [{ endDate: { $exists: false } }, { endDate: { $gte: start } }],
                };
            }

            const [companyWeekend, allCards, crews] = await Promise.all([
                this.companySettingModel.findOne({ companyId }).select("weekEndDays"),
                this.timeCardModel.find({
                    companyId,
                    timeIn: { $gte: start, $lte: end },
                    deleted: false,
                }),
                this.crewModel.aggregate([
                    {
                        $match: getQuery,
                    },
                    {
                        $lookup: {
                            from: "CrewMember",
                            pipeline: [
                                {
                                    $match: {
                                        startDate: { $lte: start },
                                        $or: [
                                            { removeDate: { $exists: false } },
                                            { removeDate: { $gte: start } },
                                        ],
                                    },
                                },
                                {
                                    $sort: { startDate: 1 },
                                },
                            ],
                            localField: "_id",
                            foreignField: "crewId",
                            as: "members",
                        },
                    },
                    {
                        $sort: { order: 1 },
                    },
                ]),
            ]);

            // Go through each day and look for unapproved or missing cards
            const dates = [];
            for (let i = end; i > start; i.setDate(i.getDate() - 1)) {
                const s = new Date(i);
                const e = new Date(i);
                // set 's' to start of day
                s.setDate(s.getDate() - 1);
                s.setMilliseconds(s.getMilliseconds() + 1);
                // Go through each crew, active members
                crews.map((crew) => {
                    crew.members &&
                        crew.members.map((member) => {
                            // If member active on date
                            if (activeCrewMember(member, s, e)) {
                                // Find member time cards
                                const memberCardsOnDay = allCards.filter((card) => {
                                    return (
                                        card.memberId === member.memberId &&
                                        card.timeIn >= s &&
                                        card.timeIn <= e
                                    );
                                });
                                const unapproved = memberCardsOnDay.filter(
                                    (card) =>
                                        card.status !== StatusEnum.Approved &&
                                        card.status !== StatusEnum.Lead_Approved,
                                );

                                // If any unapproved cards, count it
                                if (unapproved.length > 0) dates.push(formatDate(s));
                                // If no cards on a business day
                                const weekend = isWeekend(companyWeekend.weekEndDays, s);
                                if (!weekend && memberCardsOnDay.length === 0) dates.push(formatDate(s));
                            }
                        });
                });
            }
            const obj: any = {};
            obj.num = dates.length;
            obj.dates = dedupeArray(dates);

            return new OkResponse({ obj });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    ///////////////////////
    //Project Manager Dashboard
    async notClockedIn(position: any, companyId: string, date: Date) {
        try {
            const today = new Date(date);
            let getQuery;
            if (position.symbol === "Owner" || position.symbol === "Admin") {
                getQuery = {
                    companyId,
                    deleted: { $ne: true },
                    startDate: { $lte: today },
                    $or: [{ endDate: { $exists: false } }, { endDate: { $gte: today } }],
                };
            } else {
                getQuery = {
                    companyId,
                    $and: [{ $or: [{ managerId: position.memberId }, { foremanId: position.memberId }] }],
                    deleted: { $ne: true },
                    startDate: { $lte: today },
                    $or: [{ endDate: { $exists: false } }, { endDate: { $gte: today } }],
                };
            }

            const notClockedIn = {
                bool: false,
                num: 0,
                members: [],
            };

            const companyWeekend = await this.companySettingModel
                .findOne({ companyId })
                .select("weekEndDays");

            const weekend = isWeekend(companyWeekend.weekEndDays, today);

            // If weekend, don't show
            if (weekend) return notClockedIn;

            const managedCrews = await this.crewModel.aggregate([
                {
                    $match: getQuery,
                },
                {
                    $lookup: {
                        from: "CrewMember",
                        pipeline: [
                            {
                                $match: {
                                    startDate: { $lte: today },
                                    $or: [
                                        { removeDate: { $exists: false } },
                                        { removeDate: { $gte: today } },
                                    ],
                                },
                            },
                        ],
                        localField: "_id",
                        foreignField: "crewId",
                        as: "members",
                    },
                },
            ]);

            const todayTimeCards = await this.timeCardModel.find({
                companyId,
                timeIn: { $gte: today },
                deleted: { $ne: true },
            });
            managedCrews.length &&
                managedCrews.map((crew) => {
                    crew.members &&
                        crew.members.map((mem) => {
                            // TODO: need to discuss
                            if (!mem.deleted) {
                                let memCards = 0;
                                todayTimeCards &&
                                    todayTimeCards.map((card) => {
                                        if (card.memberId === mem.memberId) {
                                            memCards++;
                                        }
                                    });

                                if (memCards < 1) {
                                    notClockedIn.bool = true;
                                    notClockedIn.num++;
                                    notClockedIn.members.push({ id: mem.memberId, name: mem.memberName });
                                }
                            }
                        });
                });

            return new OkResponse({ notClockedIn });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async membersNotInCrewOrInMany(companyId: string) {
        try {
            const members = await this.memberModel.aggregate([
                {
                    $match: {
                        company: companyId,
                        deleted: false,
                    },
                },
                {
                    $lookup: {
                        from: "Compensation",
                        localField: "_id",
                        foreignField: "memberId",
                        as: "result",
                    },
                },
                {
                    $lookup: {
                        from: "Position",
                        localField: "result.positionId",
                        foreignField: "_id",
                        as: "position",
                    },
                },
                {
                    $unwind: {
                        path: "$result",
                        preserveNullAndEmptyArrays: false,
                    },
                },
                {
                    $unwind: {
                        path: "$position",
                        preserveNullAndEmptyArrays: false,
                    },
                },
                {
                    $match: {
                        "position.symbol": "CrewMember",
                    },
                },
                {
                    $project: {
                        result: 0,
                        position: 0,
                    },
                },
            ]);

            const crews = await this.crewModel.aggregate([
                {
                    $match: {
                        companyId,
                        deleted: false,
                        retired: false,
                    },
                },
                {
                    $lookup: {
                        from: "CrewMember",
                        pipeline: [{ $match: { deleted: false } }],
                        localField: "_id",
                        foreignField: "crewId",
                        as: "members",
                    },
                },
            ]);
            const managedCrews = crews.flatMap((crew) => crew.members);

            const noCrew: any = {};
            noCrew.bool = false;
            noCrew.num = 0;
            noCrew.members = [];
            const manyCrew: any = {};
            manyCrew.bool = false;
            manyCrew.num = 0;
            manyCrew.members = [];

            members.map((member) => {
                let inCrew = 0;
                managedCrews &&
                    managedCrews.map((mem) => {
                        if (
                            // mem.active &&
                            member._id === mem.memberId
                        ) {
                            inCrew++;
                        }
                    });

                if (inCrew < 1) {
                    noCrew.bool = true;
                    noCrew.num++;
                    noCrew.members.push(member);
                }
                if (inCrew > 1) {
                    manyCrew.bool = true;
                    manyCrew.num++;
                    manyCrew.members.push(member);
                }
            });

            return new OkResponse({ noCrew, manyCrew });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    ///////////////////////
    //Sales Person Dashboard
    async backlogCalc(companyId: string) {
        try {
            const date = new Date();
            //NOTE: thses are checkpoint fields that are hardcoded
            const opps: any = await this.opportunityModel
                .find({
                    $and: [
                        { companyId },
                        { saleDate: { $exists: true } },
                        { saleDate: { $lte: date } },
                        {
                            $or: [
                                { jobStartedDate: { $exists: false } },
                                { jobStartedDate: "" },
                                { jobStartedDate: { $gte: date } },
                            ],
                        },
                    ],
                })
                .populate("contactId", "fullName", "Contact");

            const orderIds = opps.map((opp) => opp.orderId);
            const orders = await this.orderModel.find({
                _id: { $in: orderIds },
            });
            orders.map((order) => {
                const client = opps.find((opp) => opp.orderId === order._id);
            });
            let overhead = 0;
            orders.map((order) => {
                overhead += order.priceTotals.overhead;
            });

            const variables = await this.companySettingModel.findOne({ companyId, deleted: false });
            const dailyOH = variables.dailyOH;
            const crews = 2;
            const days = roundTo1((overhead * 1.3) / dailyOH / crews);
            const totalDays = (days / 5) * 7;
            const projectedStart = new Date();
            projectedStart.setDate(projectedStart.getDate() + totalDays);

            const obj = {
                projectedStart,
                rawDays: days,
                crews: 2,
            };

            return new OkResponse({ obj });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async actions(companyId: string, salesPerson: string, dateEnd: Date) {
        try {
            const allStage = await this.crmStageModel.find({
                companyId,
                stageGroup: StageGroupEnum.Sales,
                deleted: { $ne: true },
            });

            const stages = allStage.map((s) => s._id);

            const numOverdue: any = await this.opportunityModel.countDocuments({
                companyId,
                deleted: { $ne: true },
                status: "active",
                salesPerson,
                stage: { $in: stages },
                nextAction: { $exists: true },
                "nextAction.due": { $lte: new Date(dateEnd) },
            });

            const numNoAction = await this.opportunityModel.countDocuments({
                companyId,
                deleted: { $ne: true },
                status: "active",
                salesPerson,
                stage: { $in: stages },
                nextAction: { $exists: false },
            });
            return new OkResponse({ numOverdue, numNoAction });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    //For lead array on selecting salesperson
    async leadActionList(
        companyId: string,
        userId: string,
        memberId: string,
        csrId: string,
        paginationRequestDto: PaginationRequestDto,
        teamPermission: number,
    ) {
        try {
            const member = await this.memberModel.findOne({ _id: memberId });

            // Check permissions using a separate function
            await this.positionService.permissionCheck(
                userId,
                companyId,
                member._id,
                member.managerId,
                teamPermission,
            );

            const limit = paginationRequestDto.limit || 10;
            const offset = limit * (paginationRequestDto.skip || 0);
            const data = await this.crmStageModel.find({
                companyId,
                deleted: false,
                stageGroup: StageGroupEnum.Leads,
            });
            const stages = [];
            data.forEach((stage) => {
                stages.push(stage._id);
            });
            const query = {
                companyId,
                deleted: { $ne: true },
                csrId,
                status: { $ne: true },
                stage: { $in: stages },
                nextAction: { $exists: true },
            };

            const currentOpps: any = await this.leadModel
                .find(query)
                .sort({
                    "nextAction.due": 1,
                })
                .limit(limit)
                .skip(offset)
                .populate("stage", "name", "CrmStage")
                .select("_id stage PO firstName lastName nextAction");

            return new OkResponse({ currentOpps });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async leadNoActionList(
        companyId: string,
        userId: string,
        memberId: string,
        csrId: string,
        paginationRequestDto: PaginationRequestDto,
        teamPermission: number,
    ) {
        try {
            const member = await this.memberModel.findOne({ _id: memberId });

            // Check permissions using a separate function
            await this.positionService.permissionCheck(
                userId,
                companyId,
                member._id,
                member.managerId,
                teamPermission,
            );

            const limit = paginationRequestDto.limit || 10;
            const offset = limit * (paginationRequestDto.skip || 0);
            if (limit > 0) {
                const data = await this.crmStageModel.find({
                    companyId,
                    deleted: false,
                    stageGroup: StageGroupEnum.Leads,
                });
                const stages = [];
                data.forEach((stage) => {
                    stages.push(stage._id);
                });
                const currentOpps = await this.leadModel
                    .find({
                        companyId,
                        csrId,
                        deleted: { $ne: true },
                        lost: { $ne: true },
                        stage: { $in: stages },
                        nextAction: { $exists: false },
                    })
                    .sort({
                        createdAt: -1,
                    })
                    .limit(limit)
                    .skip(offset)
                    .populate("stage", "name", "CrmStage")
                    .select("_id stage stage.stageGroup firstName lastName nextAction");

                return new OkResponse({ currentOpps });
            }
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async leadActions(
        userId: string,
        companyId: string,
        csrId: string,
        memberId: string,
        dateEnd: Date,
        teamPermission: number,
    ) {
        try {
            const member = await this.memberModel.findOne({ _id: memberId });

            // Check permissions using a separate function
            await this.positionService.permissionCheck(
                userId,
                companyId,
                member._id,
                member.managerId,
                teamPermission,
            );

            const allStage = await this.crmStageModel.find({
                companyId,
                deleted: false,
                stageGroup: StageGroupEnum.Leads,
            });

            const stages = allStage.map((s) => s._id);
            const numOverdue = await this.leadModel.countDocuments({
                companyId,
                deleted: { $ne: true },
                lost: { $ne: true },
                status: "active",
                csrId,
                stage: { $in: stages },
                nextAction: { $exists: true },
                "nextAction.due": { $lte: new Date(dateEnd) },
            });

            const numNoAction = await this.leadModel.countDocuments({
                companyId,
                deleted: { $ne: true },
                lost: { $ne: true },
                status: "active",
                csrId,
                stage: { $in: stages },
                nextAction: { $exists: false },
            });

            return new OkResponse({ numOverdue, numNoAction });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    //For opportunity array on selecting salesperson
    async actionList(companyId: string, salesPerson: string, paginationRequestDto: PaginationRequestDto) {
        try {
            const limit = paginationRequestDto.limit || 10;
            const offset = limit * (paginationRequestDto.skip || 0);
            const data = await this.crmStageModel.find({
                companyId,
                deleted: false,
                stageGroup: StageGroupEnum.Sales,
            });
            const stages = [];
            data.forEach((stage) => {
                stages.push(stage._id);
            });
            const currentOpps: any = await this.opportunityModel
                .find({
                    companyId,
                    salesPerson,
                    deleted: { $ne: true },
                    status: OpportunityStatusEnum.Active,
                    stage: { $in: stages },
                    nextAction: { $exists: true },
                })
                .sort({
                    "nextAction.due": 1,
                })
                .limit(limit)
                .skip(offset)
                .populate("stage", "name stageGroup", "CrmStage")
                .populate("contactId", "fullName", "Contact")
                .select("_id stage contactId PO firstName lastName nextAction");

            const contacts = await this.contactModel
                .find(
                    { "nextAction.assignTo": salesPerson },
                    { isBusiness: 1, businessName: 1, fullName: 1, nextAction: 1, type: 1 },
                )
                .sort({
                    "nextAction.due": 1,
                });

            return new OkResponse({ currentOpps, contacts });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async noActionList(companyId: string, salesPerson: string, paginationRequestDto: PaginationRequestDto) {
        try {
            const limit = paginationRequestDto.limit || 10;
            const offset = limit * (paginationRequestDto.skip || 0);
            if (limit > 0) {
                const data = await this.crmStageModel.find({
                    companyId,
                    deleted: false,
                    stageGroup: StageGroupEnum.Sales,
                });
                const stages = [];
                data.forEach((stage) => {
                    stages.push(stage._id);
                });
                const currentOpps = await this.opportunityModel
                    .find({
                        companyId,
                        salesPerson,
                        status: OpportunityStatusEnum.Active,
                        deleted: { $ne: true },
                        stage: { $in: stages },
                        nextAction: { $exists: false },
                    })
                    .sort({
                        createdAt: -1,
                    })
                    .limit(limit)
                    .skip(offset)
                    .populate("stage", "name stageGroup", "CrmStage")
                    .populate("contactId", "fullName", "Contact")
                    .select("_id stage contactId PO firstName lastName nextAction");

                return new OkResponse({ currentOpps });
            }
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    getMonthStart(date) {
        date = new Date(date);
        // end.setDate(end.getDate() + 1);
        // date.setMilliseconds(date.getMilliseconds() - 1); // set time to midnight
        // const days = date.getDate() - 1; //days to start of month
        date.setDate(1); //subtract days to find the 1st
        return date;
    }

    getYearStart(date) {
        date = new Date(date);
        const year = date.getFullYear(date);
        const startOfYear = new Date(`${year}-01-01T00:00`);
        return startOfYear;
    }

    //Stats for branch dashboard - date0=future/now period start, date1=current start
    getBranchReport(opps, prop, date0, date1, date2, yearStart, reportName, salesPersonId) {
        try {
            const report: any = {};
            report.name = reportName;
            report.currNum = 0;
            report.prevNum = 0;
            report.ytdNum = 0;
            report.allNum = 0;
            report.curr = [];
            report.prev = [];
            report.ytd = [];
            report.all = [];
            for (let i = 0; i < opps.length; i++) {
                if (opps[i][prop] >= date1 && opps[i][prop] < date0) {
                    report.currNum++;
                    report.curr.push(opps[i]);
                }
                if (opps[i][prop] >= date2 && opps[i][prop] < date1) {
                    report.prevNum++;
                    report.prev.push(opps[i]);
                }
                if (opps[i][prop] >= yearStart && opps[i][prop] < date0) {
                    report.ytdNum++;
                    report.ytd.push(opps[i]);
                }
                if (opps[i][prop] > 0 && opps[i][prop] < date0) {
                    report.allNum++;
                    report.all.push(opps[i]);
                }
            }
            return report;
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    //it generates sales person report
    async salesPersonReport(userId: string, companyId: string, salesPerson: string, d: Date) {
        try {
            // const date = new Date(d);
            //Find and set date to RIGHT NOW
            const currentDate = new Date(d);
            //Get monthly report dates
            const monthStart = this.getMonthStart(currentDate);
            const mnthRep0 = new Date(monthStart); // start of next month
            mnthRep0.setMonth(mnthRep0.getMonth() + 1);
            const mnthRep1 = new Date(monthStart); // start of this month
            const mnthRep2 = new Date(monthStart); // start of previous month
            mnthRep2.setMonth(mnthRep2.getMonth() - 1);

            const [salesComm, projectTypes, userCompensation, opps, checkpoints, stages, oppModifiedComm] =
                await Promise.all([
                    this.companyPayModel.findOne({ companyId }),
                    this.projectTypeModel.find({ companyId, deleted: false }),
                    this.compensationModel.findOne({ memberId: salesPerson }),
                    this.opportunityModel
                        .find({
                            companyId,
                            salesPerson: { $in: [salesPerson] },
                            deleted: { $ne: true },
                            $or: [
                                {
                                    $and: [
                                        { saleDate: { $gte: mnthRep2 } },
                                        { saleDate: { $lte: mnthRep0 } },
                                    ],
                                },
                                {
                                    $and: [
                                        { jobCompletedDate: { $gte: mnthRep2 } },
                                        { jobCompletedDate: { $lte: mnthRep0 } },
                                    ],
                                },
                                {
                                    $and: [
                                        { jobStartedDate: { $gte: mnthRep2 } },
                                        { jobStartedDate: { $lte: mnthRep0 } },
                                    ],
                                },
                            ],
                        })
                        .populate("contactId", "fullName", "Contact")
                        .populate("acceptedType", "name typeReplacement", "ProjectType")
                        .sort({ createdAt: -1 })
                        .lean(),
                    this.crmService.getCheckpoint(
                        userId,
                        companyId,
                        false,
                        {
                            limit: 100,
                            skip: 0,
                        },
                        [StageGroupEnum.Sales, StageGroupEnum.Operations],
                    ),
                    this.crmService.getStage(userId, companyId, false, {
                        limit: 100,
                        skip: 0,
                        stageGroup: StageGroupEnum.Sales,
                    }),
                    this.commissionModificationModel
                        .aggregate([
                            {
                                $match: {
                                    companyId,
                                    salesPersonId: { $in: [salesPerson] },
                                    $and: [{ date: { $gte: mnthRep2 } }, { date: { $lte: mnthRep0 } }],
                                },
                            },
                            {
                                $lookup: {
                                    from: "Opportunity",
                                    foreignField: "_id",
                                    localField: "oppId",
                                    as: "oppId",
                                    pipeline: [
                                        {
                                            $project: {
                                                _id: 1,
                                                PO: 1,
                                                num: 1,
                                                contactId: 1,
                                                acceptedType: 1,
                                                stage: 1,
                                            },
                                        },
                                    ],
                                },
                            },
                            {
                                $unwind: {
                                    path: "$oppId",
                                    preserveNullAndEmptyArrays: true,
                                },
                            },
                            {
                                $lookup: {
                                    from: "Contact",
                                    pipeline: [
                                        {
                                            $project: {
                                                _id: 1,
                                                fullName: 1,
                                                // lastName: 1,
                                            },
                                        },
                                    ],
                                    foreignField: "_id",
                                    localField: "oppId.contactId",
                                    as: "client",
                                },
                            },
                            {
                                $unwind: {
                                    path: "$client",
                                    preserveNullAndEmptyArrays: true,
                                },
                            },
                            {
                                $lookup: {
                                    from: "ProjectType",
                                    foreignField: "_id",
                                    localField: "oppId.acceptedType",
                                    as: "pType",
                                    pipeline: [{ $project: { _id: 1, name: 1, typeReplacement: 1 } }],
                                },
                            },
                            {
                                $unwind: {
                                    path: "$pType",
                                    preserveNullAndEmptyArrays: true,
                                },
                            },
                            // {
                            //     $lookup: {
                            //         from: "CrmStage",
                            //         foreignField: "_id",
                            //         localField: "oppId.stage",
                            //         as: "stage",
                            //         pipeline: [{ $project: { stageGroup: 1 } }],
                            //     },
                            // },
                            // {
                            //     $unwind: {
                            //         path: "$stage",
                            //         preserveNullAndEmptyArrays: true,
                            //     },
                            // },
                        ])
                        .exec(),
                ]);

            const { benchmarks } = salesComm.sales;

            //Get start of year
            const yearStart = this.getYearStart(currentDate);
            const months = [
                "January",
                "February",
                "March",
                "April",
                "May",
                "June",
                "July",
                "August",
                "September",
                "October",
                "November",
                "December",
            ];
            // Define report array to hold all reports
            const report: any = {};
            const dates: any = {};
            const thisMonth = months[mnthRep1.getMonth()];
            const lastMonth = months[mnthRep2.getMonth()];
            const year = yearStart.getFullYear();
            dates.thisMonth = thisMonth;
            dates.lastMonth = lastMonth;
            dates.year = year;
            report.dates = dates;
            const endCurrentMonth = new Date(mnthRep0);
            endCurrentMonth.setMilliseconds(endCurrentMonth.getMilliseconds() - 1);
            const endLastMonth = new Date(mnthRep1);
            endLastMonth.setMilliseconds(endLastMonth.getMilliseconds() - 1);

            const wageThisMonth = findCurrentWage(userCompensation?.wageHistory, endCurrentMonth);

            const wageLastMonth = findCurrentWage(userCompensation?.wageHistory, endLastMonth);

            const commThisMonth = wageThisMonth?.saleCommission ?? 0; // already in decimal no need to divide by 100

            const thisMonthSalary =
                wageThisMonth?.wageInterval === WageIntervalEnum.Year // "Year"
                    ? roundTo2(wageThisMonth.wageAmount / 12)
                    : wageThisMonth?.wageInterval === WageIntervalEnum.Month
                    ? roundTo2(wageThisMonth.wageAmount)
                    : 0;

            const lastMonthSalary =
                wageLastMonth?.wageInterval === WageIntervalEnum.Year // "Year"
                    ? roundTo2(wageLastMonth.wageAmount / 12)
                    : wageLastMonth?.wageInterval === WageIntervalEnum.Month
                    ? roundTo2(wageLastMonth.wageAmount)
                    : 0;

            //for sales checkpoints
            let leads, sales, completedOpps, startedOpps;
            for (const check of checkpoints.data.checkpoint) {
                if (check.symbol === "oppDate") {
                    leads = this.getBranchReport(
                        opps,
                        check.symbol,
                        mnthRep0,
                        mnthRep1,
                        mnthRep2,
                        yearStart,
                        check.name,
                        salesPerson,
                    );
                } else if (check.symbol === "saleDate") {
                    //for getting last checkpoint
                    sales = this.getBranchReport(
                        opps,
                        check.symbol,
                        mnthRep0,
                        mnthRep1,
                        mnthRep2,
                        yearStart,
                        check.name,
                        salesPerson,
                    );
                } else if (check.symbol === "jobCompletedDate") {
                    //for getting second last checkpoint by subtracting 1
                    completedOpps = this.getBranchReport(
                        opps,
                        check.symbol,
                        mnthRep0,
                        mnthRep1,
                        mnthRep2,
                        yearStart,
                        check.name,
                        salesPerson,
                    );
                } else if (check.symbol === "jobStartedDate") {
                    //for getting second last checkpoint by subtracting 1
                    startedOpps = this.getBranchReport(
                        opps,
                        check.symbol,
                        mnthRep0,
                        mnthRep1,
                        mnthRep2,
                        yearStart,
                        check.name,
                        salesPerson,
                    );
                } else {
                    const name = check.name;
                    const data = this.getBranchReport(
                        opps,
                        check.symbol,
                        mnthRep0,
                        mnthRep1,
                        mnthRep2,
                        yearStart,
                        check.name,
                        salesPerson,
                    );
                    report[name] = data;
                }
            }

            const roofType = projectTypes.find((p) => p.typeReplacement);
            const firstHalf = opps.filter((opp: any) => {
                return (
                    opp?.acceptedType?._id === roofType._id &&
                    opp.saleDate <= currentDate &&
                    (!opp.jobCompletedDate || opp.jobCompletedDate >= currentDate)
                );
            });

            report.firstHalf = {
                num: firstHalf.length,
                opps: firstHalf,
            };

            //Getting all stages
            const stageIds = stages.data.stage.map((stage) => stage._id);

            report.activeOpps = await this.opportunityModel
                .find({
                    companyId,
                    salesPerson,
                    deleted: { $ne: true },
                    stage: { $in: stageIds },
                    status: OpportunityStatusEnum.Active,
                })
                .sort({ createdAt: -1 });

            report.activeOppsNum = report.activeOpps.length;
            report.leads = leads;
            report.sales = sales;

            // sold calculation
            const soldCurr: any = {
                totalVol: 0,
                totalNum: 0,
                totalComm: 0,
                type: [],
                modified: {
                    opp: [],
                    amount: 0,
                },
            };

            // Modified commission calculation
            soldCurr.modified.opp =
                oppModifiedComm.filter(
                    (c) => c.salesPersonId === salesPerson && c.date >= mnthRep1 && c.date < mnthRep0,
                ) || [];
            soldCurr.modified.amount = soldCurr.modified.opp.reduce((sum, c) => sum + (c.amount || 0), 0);
            soldCurr.totalComm += soldCurr.modified.amount;

            // Current Month Report for roof replacement project types
            for (const type of projectTypes) {
                const sold: any = { opps: [], vol: 0, num: 0, commission: 0 };
                const completed: any = { opps: [], vol: 0, num: 0, commission: 0 };
                const started: any = { opps: [], vol: 0, num: 0, commission: 0 };

                // sold calc
                sold.opps =
                    sales?.curr &&
                    sales.curr
                        ?.filter((opp) => {
                            return opp?.acceptedType?._id === type._id;
                        })
                        ?.map((opp) => {
                            // opp.selfGen = opp.referredBy === salesPerson ? true : false;
                            opp.commission = opp?.salesCommission?.sale;
                            return opp;
                        })
                        .sort((a, b) => {
                            return new Date(a.saleDate).getTime() - new Date(b.saleDate).getTime();
                        });
                sold.vol = sumArray(sold.opps, "soldValue");
                sold.num = sold.opps?.length;
                sold.commission = sumArray(sold.opps, "commission");

                // job started calc
                started.opps =
                    startedOpps?.curr &&
                    startedOpps.curr
                        ?.filter((opp) => {
                            return opp?.acceptedType?._id === type._id;
                        })
                        ?.map((opp) => {
                            // opp.selfGen = opp.referredBy === salesPerson ? true : false;
                            opp.commission = opp?.salesCommission?.start;
                            return opp;
                        })
                        .sort((a, b) => {
                            return (
                                new Date(a.jobStartedDate).getTime() - new Date(b.jobStartedDate).getTime()
                            );
                        });
                started.vol = sumArray(started.opps, "soldValue");
                started.num = started.opps?.length;
                started.commission = sumArray(started.opps, "commission");

                // completed calc
                completed.opps =
                    completedOpps?.curr &&
                    completedOpps.curr
                        ?.filter((opp) => {
                            return opp?.acceptedType?._id === type._id;
                        })
                        ?.map((opp) => {
                            // opp.selfGen = opp.referredBy === salesPerson ? true : false;
                            opp.commission = opp?.salesCommission?.completed;
                            return opp;
                        })
                        .sort((a, b) => {
                            return (
                                new Date(a.jobCompletedDate).getTime() -
                                new Date(b.jobCompletedDate).getTime()
                            );
                        });

                completed.vol = sumArray(completed.opps, "soldValue");
                completed.num = completed.opps?.length;
                completed.commission = sumArray(completed.opps, "commission");

                soldCurr.totalVol += sold.vol;
                soldCurr.totalNum += sold.num;
                soldCurr.totalComm += sold.commission + started.commission + completed.commission;

                // pushing in array
                soldCurr.type.push({
                    _id: type._id,
                    name: type.name,
                    typeReplacement: true,
                    sold,
                    started,
                    completed,
                });
            }

            const marks: any = {};
            let i = 0;
            marks.num = 0;
            marks.bonus = 0;
            while (benchmarks[i].goal <= soldCurr.totalVol) {
                marks.num++;
                marks.bonus += wageThisMonth.useBenchmarkBonus ? benchmarks[i].bonus : 0;
                i++;
            }
            marks.next = roundTo2(benchmarks[i].goal - soldCurr.totalVol);
            marks.text = marks.num === 1 ? "benchmark" : "benchmarks";
            const bonus = commThisMonth ? marks.bonus : 0;
            soldCurr.salary = thisMonthSalary || 0;
            soldCurr.totalEarned = roundTo2(soldCurr.totalComm + thisMonthSalary + bonus);
            report.benchmarks = marks;
            report.soldCurr = soldCurr;

            // soldPrev calculation
            // Last Month Report
            const soldPrev: any = {
                totalVol: 0,
                totalNum: 0,
                totalComm: 0,
                type: [],
                modified: {
                    opp: [],
                    amount: 0,
                },
            };

            // Modified commission calculation
            soldPrev.modified.opp =
                oppModifiedComm.filter(
                    (c) => c.salesPersonId === salesPerson && c.date >= mnthRep2 && c.date < mnthRep1,
                ) || [];
            soldPrev.modified.amount = soldPrev.modified.opp.reduce((sum, c) => sum + (c.amount || 0), 0);
            soldPrev.totalComm += soldPrev.modified.amount;

            const commLastMonth = wageLastMonth?.saleCommission ?? 0; // already in decimal no need to divide by 100

            for (const type of projectTypes) {
                const sold = { opps: [], vol: 0, num: 0, commission: 0 };
                const completed = { opps: [], vol: 0, num: 0, commission: 0 };
                const started = { opps: [], vol: 0, num: 0, commission: 0 };

                sold.opps =
                    sales?.prev &&
                    sales.prev
                        ?.filter((opp) => {
                            return opp?.acceptedType?._id === type._id;
                        })
                        ?.map((opp) => {
                            // opp.selfGen = opp.referredBy === salesPerson ? true : false;
                            opp.commission = opp?.salesCommission?.sale;
                            return opp;
                        })
                        .sort((a, b) => {
                            return new Date(a.saleDate).getTime() - new Date(b.saleDate).getTime();
                        });

                sold.vol = sumArray(sold.opps, "soldValue");
                sold.num = sold.opps?.length;
                sold.commission = sumArray(sold.opps, "commission");

                // job started calc
                started.opps =
                    startedOpps?.prev &&
                    startedOpps.prev
                        ?.filter((opp) => {
                            return opp?.acceptedType?._id === type._id;
                        })
                        ?.map((opp) => {
                            // opp.selfGen = opp.referredBy === salesPerson ? true : false;
                            opp.commission = opp?.salesCommission?.start;
                            return opp;
                        })
                        .sort((a, b) => {
                            return (
                                new Date(a.jobStartedDate).getTime() - new Date(b.jobStartedDate).getTime()
                            );
                        });
                started.vol = sumArray(started.opps, "soldValue");
                started.num = started.opps?.length;
                started.commission = sumArray(started.opps, "commission");

                completed.opps =
                    completedOpps?.prev &&
                    completedOpps.prev
                        ?.filter((opp) => {
                            return opp?.acceptedType?._id === type._id;
                        })
                        ?.map((opp) => {
                            // opp.selfGen = opp.referredBy === salesPerson ? true : false;
                            opp.commission = opp?.salesCommission?.completed;
                            return opp;
                        })
                        .sort((a, b) => {
                            return (
                                new Date(a.jobCompletedDate).getTime() -
                                new Date(b.jobCompletedDate).getTime()
                            );
                        });

                completed.vol = sumArray(completed.opps, "soldValue");
                completed.num = completed.opps?.length;
                completed.commission = sumArray(completed.opps, "commission");

                soldPrev.totalVol += sold.vol;
                soldPrev.totalNum += sold.num;
                soldPrev.totalComm += sold.commission + started.commission + completed.commission;
                // pushing in array
                soldPrev.type.push({
                    _id: type._id,
                    name: type.name,
                    typeReplacement: true,
                    sold,
                    started,
                    completed,
                });
            }

            const marksPrev: any = {};
            let j = 0;
            marksPrev.num = 0;
            marksPrev.bonus = 0;
            while (benchmarks[j].goal <= soldPrev.totalVol) {
                marksPrev.num++;
                marksPrev.bonus += wageLastMonth.useBenchmarkBonus ? benchmarks[j].bonus : 0;
                j++;
            }
            marksPrev.next = roundTo2(benchmarks[j].goal - soldPrev.totalVol);
            marksPrev.text = marksPrev.num > 1 ? "benchmarks" : "benchmark";

            const prevBonus = commLastMonth ? marksPrev.bonus : 0;
            soldPrev.salary = lastMonthSalary || 0;
            soldPrev.totalEarned = roundTo2(soldPrev.totalComm + lastMonthSalary + prevBonus);
            report.benchmarksPrev = marksPrev;
            report.soldPrev = soldPrev;

            return new OkResponse({ report });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async csrReport(userId: string, companyId: string, csrId: string, d: Date) {
        try {
            // const date = new Date(d);
            //Find and set date to RIGHT NOW
            const currentDate = new Date(d);
            //Get monthly report dates
            const monthStart = this.getMonthStart(currentDate);
            const mnthRep0 = new Date(monthStart); // start of next month
            mnthRep0.setMonth(mnthRep0.getMonth() + 1);
            const mnthRep1 = new Date(monthStart); // start of this month
            const mnthRep2 = new Date(monthStart); // start of previous month
            mnthRep2.setMonth(mnthRep2.getMonth() - 1);

            const [projectTypes, saleLeads, checkpoints, stages] = await Promise.all([
                this.projectTypeModel.find({ companyId, deleted: false }),

                this.leadModel
                    .find({
                        companyId,
                        csrId,
                        deleted: { $ne: true },
                    })
                    .sort({ createdAt: -1 })
                    .lean(),
                this.crmService.getCheckpoint(
                    userId,
                    companyId,
                    false,
                    {
                        limit: 100,
                        skip: 0,
                    },
                    [StageGroupEnum.Sales, StageGroupEnum.Operations],
                ),
                this.crmService.getStage(userId, companyId, false, {
                    limit: 100,
                    skip: 0,
                    stageGroup: StageGroupEnum.Sales,
                }),
            ]);

            //Get start of year
            const yearStart = this.getYearStart(currentDate);
            const months = [
                "January",
                "February",
                "March",
                "April",
                "May",
                "June",
                "July",
                "August",
                "September",
                "October",
                "November",
                "December",
            ];
            // Define report array to hold all reports
            const report: any = {};
            const dates: any = {};
            const thisMonth = months[mnthRep1.getMonth()];
            const lastMonth = months[mnthRep2.getMonth()];
            const year = yearStart.getFullYear();
            dates.thisMonth = thisMonth;
            dates.lastMonth = lastMonth;
            dates.year = year;
            report.dates = dates;
            const endCurrentMonth = new Date(mnthRep0);
            endCurrentMonth.setMilliseconds(endCurrentMonth.getMilliseconds() - 1);
            const endLastMonth = new Date(mnthRep1);
            endLastMonth.setMilliseconds(endLastMonth.getMilliseconds() - 1);

            //for sales checkpoints
            let leads;
            for (const check of checkpoints.data.checkpoint) {
                if (check.symbol === "oppDate") {
                    leads = this.getBranchReport(
                        saleLeads,
                        check.symbol,
                        mnthRep0,
                        mnthRep1,
                        mnthRep2,
                        yearStart,
                        check.name,
                        csrId,
                    );
                } else {
                    const name = check.name;
                    const data = this.getBranchReport(
                        saleLeads,
                        check.symbol,
                        mnthRep0,
                        mnthRep1,
                        mnthRep2,
                        yearStart,
                        check.name,
                        csrId,
                    );
                    report[name] = data;
                }
            }

            const roofType = projectTypes.find((p) => p.typeReplacement);
            const firstHalf = saleLeads.filter((opp: any) => {
                return (
                    opp?.acceptedType?._id === roofType._id &&
                    opp.saleDate <= currentDate &&
                    (!opp.jobCompletedDate || opp.jobCompletedDate >= currentDate)
                );
            });

            report.firstHalf = {
                num: firstHalf.length,
                opps: firstHalf,
            };

            //Getting all stages
            const stageIds = stages.data.stage.map((stage) => stage._id);

            report.activeOpps = await this.leadModel
                .find({
                    companyId,
                    csrId,
                    deleted: { $ne: true },
                    stage: { $in: stageIds },
                    lost: { $ne: true },
                    status: { $ne: "inactive" },
                })
                .sort({ createdAt: -1 });

            report.activeOppsNum = report.activeOpps.length;
            report.leads = leads;

            return new OkResponse({ report });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async assignProjectToSalesPerson(companyId: string, salesPerson: string, oppIds: string[]) {
        try {
            await this.opportunityModel.updateMany(
                {
                    companyId,
                    _id: { $in: oppIds },
                },
                {
                    $set: {
                        salesPerson,
                    },
                    $addToSet: { salesPersonHistory: salesPerson },
                },
            );

            return new OkResponse({ message: "Sales Person Assigned successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async missingDailyLogs(position: any, companyId: string, start: Date, end: Date) {
        try {
            const allCards = await this.timeCardModel.find({ companyId, timeIn: { $gte: start, $lte: end } });

            let getQuery;
            if (position.symbol === "Owner" || position.symbol === "Admin") {
                getQuery = {
                    companyId,
                    deleted: { $ne: true },
                    retired: { $ne: true },
                };
            } else {
                getQuery = {
                    companyId,
                    $or: [{ managerId: position.memberId }, { foremanId: position.memberId }],
                    deleted: { $ne: true },
                    retired: { $ne: true },
                };
            }

            const managedCrews = await this.crewModel.aggregate([
                {
                    $match: getQuery,
                },
                {
                    $lookup: {
                        from: "CrewMember",
                        pipeline: [{ $match: { deleted: false } }],
                        localField: "_id",
                        foreignField: "crewId",
                        as: "members",
                    },
                },
            ]);

            const crewIdArr = managedCrews.map((crew) => crew._id);

            // const dailyLogs = DailyLogs.find().fetch();
            const dailyLogs = await this.dailyLogModel.find({
                crewId: { $in: crewIdArr },
                date: { $gte: start, $lt: end },
                deleted: { $ne: true },
            });
            // const start = Template.instance().approveStart.get();
            // const end = Template.instance().approveEnd.get();
            const dates = [];
            for (let i = new Date(end); i > start; i.setDate(i.getDate() - 1)) {
                const s = new Date(i);
                const e = new Date(i);
                // set 's' to start of day
                s.setDate(s.getDate() - 1);
                s.setMilliseconds(s.getMilliseconds() + 1);
                // Go through each crew, active members
                managedCrews.map((crew) => {
                    // Skip if not active during that time
                    if (crew.startDate >= s || (crew.endDate && crew.endDate <= e)) return;
                    const crewCards = [];

                    // if no crew members, require one daily log (subcontractors) - except weekends
                    if (!crew.members || crew.members.length === 0) {
                        // if it's a business day
                        if (s.getDay() > 0 && s.getDay() < 6) {
                            // Find daily log for that day
                            const log = dailyLogs.find((log) => {
                                return log.crewId === crew._id && log.date >= s && log.date <= e;
                            });
                            // If no log, record date
                            if (!log) dates.push(formatDate(s));
                        }
                    } else {
                        crew.members.map((member) => {
                            // check active members for time cards
                            if (activeCrewMember(member, s, e)) {
                                const cardsOnDay = allCards.filter((card) => {
                                    return (
                                        card.memberId === member.memberId &&
                                        card.timeIn >= s &&
                                        card.timeOut <= e
                                    );
                                });
                                if (cardsOnDay.length) crewCards.push(...cardsOnDay);
                            }
                        });
                        // collect and dedupe project ids worked on by crew that day
                        const projectList = crewCards.map((card) => {
                            return card.projectId;
                        });
                        const fullProjectArray = dedupeArray(projectList);
                        const projectArray = fullProjectArray.filter((project) => {
                            return project !== "" && project !== "companyDefaultPO";
                        });
                        // If entire crew had day off, or only worked on 'companyDefaultPO', skip daily log
                        if (!projectArray.length) {
                            return;
                        }
                        // Find daily log for that day
                        const log = dailyLogs.find((log) => {
                            return log.crewId === crew._id && log.date >= s && log.date <= e;
                        });

                        // If no log for that day or different length of project array, record date
                        if (!log || projectArray.length !== log.projects.length) {
                            dates.push(formatDate(s));
                            // If same number of projects in each
                        } else {
                            // Create array of id's only
                            const logProjects = log.projects.map((project) => project.oppId);

                            // Sort both arrays
                            projectArray.sort();
                            logProjects.sort();

                            // Compare each value 1 by 1, if any mismatch, record date
                            for (let i = 0; i < projectArray.length; i++) {
                                if (projectArray[i] !== logProjects[i]) dates.push(formatDate(s));
                            }
                        }
                    }
                });
            }

            const missingDates = dedupeArray(dates);
            const obj: any = {};
            obj.num = dates.length;
            obj.dates = missingDates;
            return new OkResponse({ data: obj });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }
}
