import { Modu<PERSON> } from "@nestjs/common";
import { SupportService } from "./support.service";
import { SupportController } from "./support.controller";
import { MailModule } from "src/mail/mail.module";
import { MongooseModule } from "@nestjs/mongoose";
import { SupportSchema } from "./schema/support.schema";

@Module({
    imports: [MongooseModule.forFeature([{ name: "Support", schema: SupportSchema }]), MailModule],
    controllers: [SupportController],
    providers: [SupportService],
    exports: [SupportService],
})
export class SupportModule {}
