import { Controller, Get, Post, Body, Patch, Param, Delete, Query } from "@nestjs/common";
import { SupportService } from "./support.service";
import { SubmitSupportQueryDto } from "./dto/create-support.dto";
import { ReplySupportQureyDto } from "./dto/update-support.dto";
import {
    ApiTags,
    ApiUnauthorizedResponse,
    ApiInternalServerErrorResponse,
    ApiBearerAuth,
    ApiOperation,
    ApiNotFoundResponse,
} from "@nestjs/swagger";
import { Auth, GetUser } from "src/auth/decorator/auth.decorator";
import { Roles } from "src/auth/guards/auth.guard";
import { UserRolesEnum } from "src/company/enum/role.enum";
import { JwtUserPayload } from "src/auth/interface/auth.interface";
import { SupportStatusEnum, SupportTeamEnum } from "./enum/support.enum";

@ApiTags("Support")
@ApiUnauthorizedResponse({ description: "Unauthorized request" })
@ApiInternalServerErrorResponse({ description: "Server Error" })
@Auth()
@ApiBearerAuth()
@Controller({ path: "support", version: "1" })
export class SupportController {
    constructor(private readonly supportService: SupportService) {}

    @ApiOperation({ summary: "Raise a new Query to support team" })
    // @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Post("query")
    async submitQuery(@GetUser() user: JwtUserPayload, @Body() submitSupportQueryDto: SubmitSupportQueryDto) {
        return await this.supportService.submitQuery(user.companyId, submitSupportQueryDto, user.memberId);
    }

    @ApiOperation({ summary: "get all query ticket" })
    @ApiNotFoundResponse({ description: "Checkpoint not found" })
    // @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Get("all-tickets")
    async getAllTickets(@GetUser() user: JwtUserPayload) {
        return await this.supportService.getAllTickets({ companyId: user.companyId });
    }

    @ApiOperation({ summary: "get member all query ticket" })
    @ApiNotFoundResponse({ description: "Checkpoint not found" })
    // @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Get("my-tickets")
    async getUserTickets(@GetUser() user: JwtUserPayload) {
        return await this.supportService.getUserTickets(user.companyId, user.memberId);
    }

    @ApiOperation({ summary: "get a query ticket by id" })
    @ApiNotFoundResponse({ description: "Checkpoint not found" })
    // @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Get("ticket/:ticketId")
    async getTicketById(@GetUser() user: JwtUserPayload, @Param("ticketId") ticketId: string) {
        return await this.supportService.getTicketById(user.companyId, ticketId);
    }

    @ApiOperation({ summary: "Reply to the Query" })
    // @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Patch("reply-query")
    async replyToQuery(
        @GetUser() user: JwtUserPayload,
        @Body() { ticketId, message, date }: ReplySupportQureyDto,
    ) {
        return await this.supportService.replyToQuery(
            user.memberId,
            { companyId: user.companyId, ticketId, message, date },
            SupportTeamEnum.USER,
            SupportStatusEnum.OPEN,
        );
    }

    @ApiOperation({ summary: "Mark ticket as closed" })
    // @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Patch("close-ticket/ticketId/:ticketId")
    async updateQueryStatus(@GetUser() user: JwtUserPayload, @Param("ticketId") ticketId: string) {
        return await this.supportService.updateQueryStatus(
            {
                companyId: user.companyId,
                ticketId,
                memberId: user.memberId,
            },
            SupportTeamEnum.USER,
        );
    }

    @ApiOperation({ summary: "Mark message as seen" })
    // @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Patch("mark-seen/ticket/:ticketId")
    async markAllRepliesAsSeen(@Param("ticketId") ticketId: string) {
        return await this.supportService.markAllRepliesAsSeen(ticketId, SupportTeamEnum.SUPPORT);
    }
}
