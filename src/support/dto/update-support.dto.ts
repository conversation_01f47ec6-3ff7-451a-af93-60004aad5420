import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsDate, IsNotEmpty, IsOptional, IsString, IsUUID } from "class-validator";

export class ReplySupportQureyDto {
    @ApiProperty({ description: "ticketId" })
    @IsUUID()
    ticketId: string;

    @ApiPropertyOptional({ description: "companyId" })
    @IsOptional()
    @IsUUID()
    companyId?: string;

    @ApiProperty({ description: "date" })
    @IsDate()
    @IsNotEmpty()
    @Transform(({ value }) => new Date(value))
    date: Date;

    @ApiProperty({ description: "message" })
    @IsString()
    @IsNotEmpty()
    @Transform(({ value }) => value.trim())
    message: string;
}
