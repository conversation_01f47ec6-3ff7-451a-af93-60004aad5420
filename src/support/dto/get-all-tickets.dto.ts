import { ApiPropertyOptional } from "@nestjs/swagger";
import { IsUUID, IsString, IsOptional, IsArray } from "class-validator";
import { SupportStatusEnum } from "../enum/support.enum";
import { Transform } from "class-transformer";

export class GetTicketsFilterDto {
    @ApiPropertyOptional({ description: "companyId" })
    @IsOptional()
    @IsUUID()
    companyId?: string;

    @ApiPropertyOptional({ description: "memberId" })
    @IsOptional()
    @IsString()
    memberId?: string;

    @ApiPropertyOptional({ description: "status" })
    @IsOptional()
    @Transform(({ value }) => value.split(",").map((element) => element))
    @IsArray()
    // @IsEnum(SupportStatusEnum)
    status?: SupportStatusEnum[];

    @ApiPropertyOptional({ description: "search" })
    @IsOptional()
    @IsString()
    @Transform(({ value }) => value.trim().toLowerCase(), { toClassOnly: true })
    search?: string;
}
