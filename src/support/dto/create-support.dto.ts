import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsNotEmpty, IsOptional, IsString } from "class-validator";

export class SubmitSupportQueryDto {
    @ApiProperty()
    @IsNotEmpty()
    @IsString()
    subject: string;

    @ApiProperty()
    @IsNotEmpty()
    @IsString()
    initialMessage: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    routePath?: string;
}
