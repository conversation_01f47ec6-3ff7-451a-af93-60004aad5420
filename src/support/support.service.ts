import {
    BadRequestException,
    HttpException,
    Injectable,
    InternalServerErrorException,
    NotFoundException,
} from "@nestjs/common";
import { SubmitSupportQueryDto } from "./dto/create-support.dto";
import { ReplySupportQureyDto } from "./dto/update-support.dto";
import { SupportDocument } from "./schema/support.schema";
import { Model } from "mongoose";
import { InjectModel } from "@nestjs/mongoose";
import { MailService } from "src/mail/mail.service";
import { SupportStatusEnum, SupportTeamEnum } from "./enum/support.enum";
import OkResponse from "src/shared/http/response/ok.http";
import { CompanyService } from "src/company/company.service";
import { formatDate } from "src/shared/helpers/logics";
import { CloseTicketDto } from "./dto/close-ticket.dto";
import { GetTicketsFilterDto } from "./dto/get-all-tickets.dto";

@Injectable()
export class SupportService {
    constructor(
        private readonly mailService: MailService,
        private readonly companyService: CompanyService,
        @InjectModel("Support") private readonly supportModel: Model<SupportDocument>,
    ) {}

    async submitQuery(
        companyId: string,
        { subject, initialMessage, routePath }: SubmitSupportQueryDto,
        memberId: string,
    ) {
        try {
            const count = await this.supportModel.countDocuments();
            const requestId = (count + 1).toString().padStart(6, "0");

            const { name, email } = await this.companyService.getMemberByMemberIdInternal(
                memberId,
                companyId,
            );

            const newTicket = await this.supportModel.create({
                companyId,
                email,
                requestId,
                subject,
                initialMessage,
                status: SupportStatusEnum.OPEN,
                createdBy: memberId,
                routePath,
                replies: [],
            });

            this.mailService.supportTicketCreated(
                name,
                email,
                subject,
                requestId,
                formatDate(new Date()),
                initialMessage,
            );

            return new OkResponse({ newTicket });
        } catch (e) {
            if (e instanceof HttpException) {
                throw e;
            }
            throw new InternalServerErrorException(e.message);
        }
    }

    async replyToQuery(
        memberId: string,
        { companyId, ticketId, message, date }: ReplySupportQureyDto,
        replyBy: SupportTeamEnum,
        status: SupportStatusEnum,
    ) {
        try {
            const { replies } = await this.supportModel.findOneAndUpdate(
                {
                    companyId,
                    _id: ticketId,
                },
                {
                    $set: {
                        status,
                    },
                    $push: {
                        replies: {
                            replyBy,
                            message,
                            date,
                            replyById: memberId,
                            isSeen: false,
                        },
                    },
                },
                { new: true },
            );

            return new OkResponse({
                replies: replies || [],
            });
        } catch (e) {
            if (e instanceof HttpException) {
                throw e;
            }
            throw new InternalServerErrorException(e.message);
        }
    }

    async updateQueryStatus(
        { companyId, ticketId, memberId }: CloseTicketDto,
        closedByRole: SupportTeamEnum,
    ) {
        try {
            const data = await this.supportModel.findOneAndUpdate(
                {
                    companyId,
                    _id: ticketId,
                },
                {
                    $set: {
                        closedBy: memberId,
                        status: SupportStatusEnum.CLOSED,
                    },
                },
            );

            const { name, email } = await this.companyService.getMemberByMemberIdInternal(
                data.createdBy,
                companyId,
            );

            const subject = "Issue Resolved";

            await this.mailService.supportTicketClosed(
                name,
                email,
                subject,
                data.requestId,
                formatDate(new Date()),
                closedByRole,
            );
            return new OkResponse({ message: "Status updated" });
        } catch (e) {
            if (e instanceof HttpException) {
                throw e;
            }
            throw new InternalServerErrorException(e.message);
        }
    }

    async getAllTickets(filterDto: GetTicketsFilterDto) {
        try {
            const { companyId, memberId, search, status } = filterDto;

            const filter = {
                ...(companyId && { companyId }),
                ...(memberId && { createdBy: memberId }),
                ...(status && Array.isArray(status) && { status: { $in: status } }),
                // ...(status && !Array.isArray(status) && { status }),
                ...(search && {
                    $or: [
                        { email: { $regex: search, $options: "i" } },
                        { subject: { $regex: search, $options: "i" } },
                        { initialMessage: { $regex: search, $options: "i" } },
                    ],
                }),
            };

            const tickets = await this.supportModel
                .find(filter)
                .sort({ createdAt: -1 })
                .select("_id companyId memberId requestId status subject updatedAt createdBy");
            return new OkResponse({ tickets });
        } catch (e) {
            if (e instanceof HttpException) {
                throw e;
            }
            throw new InternalServerErrorException(e.message);
        }
    }

    async getUserTickets(companyId: string, memberId: string) {
        try {
            const tickets = await this.supportModel.find({ companyId, createdBy: memberId });
            return new OkResponse({ tickets });
        } catch (e) {
            if (e instanceof HttpException) {
                throw e;
            }
            throw new InternalServerErrorException(e.message);
        }
    }

    async getTicketById(companyId: string, ticketId: string) {
        try {
            const ticket = await this.supportModel.findOne({ _id: ticketId, companyId });

            if (!ticket) {
                throw new NotFoundException("Ticket not found or you do not have access");
            }

            return new OkResponse({ ticket });
        } catch (e) {
            if (e instanceof HttpException) {
                throw e;
            }
            throw new InternalServerErrorException(e.message);
        }
    }

    async markAllRepliesAsSeen(ticketId: string, replyBy: SupportTeamEnum) {
        try {
            await this.supportModel.updateOne(
                { _id: ticketId, "replies.replyBy": replyBy, "replies.isSeen": false },
                { $set: { "replies.$[elem].isSeen": true } },
                { arrayFilters: [{ "elem.replyBy": replyBy, "elem.isSeen": false }] },
            );

            return new OkResponse({ success: true });
        } catch (e) {
            if (e instanceof HttpException) {
                throw e;
            }
            throw new InternalServerErrorException(e.message);
        }
    }
}
