import { Schema, SchemaFactory, Prop } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import { Document } from "mongoose";
import { SupportStatusEnum, SupportTeamEnum } from "../enum/support.enum";
import { UUIDProp } from "src/shared/decorator/uuid.decorator";

export type SupportDocument = Support & Document;

class Replies {
    // ex. company name or support team name
    @Prop({ required: true, enum: SupportTeamEnum, default: SupportTeamEnum.USER })
    replyBy: string;

    @Prop({ required: true })
    message: string;

    // ISO date string for reply date
    @Prop({ required: true })
    date: string;

    // Optional ID of who replied (support agent or company user)
    @Prop()
    replyById?: string;

    // If message is seen or not
    @Prop({ default: false })
    isSeen: boolean;
}

@Schema({ timestamps: true, id: false, collection: "Support" })
export class Support {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    // To link the ticket to a company
    @UUIDProp()
    companyId: string;

    // Email of the user who submitted the query
    @Prop({ required: true })
    email: string;

    // Custom request id generated
    @Prop({ required: true })
    requestId: string;

    // Subject of the support ticket
    @Prop({ required: true })
    subject: string;

    // Initial message of the support ticket ex. description of problem
    @Prop({ required: true })
    initialMessage: string;

    // Track status (open, replied, closed)
    @Prop({ enum: SupportStatusEnum, default: SupportStatusEnum.OPEN })
    status: number;

    // Member id who created it
    @Prop()
    createdBy?: string;

    // Member id who closed it
    @Prop()
    closedBy?: string;

    // path of page from where user ask for help
    @Prop()
    routePath?: string;

    // Array of replies, including from support and user
    @Prop({ type: () => [Replies] })
    replies: Replies[];
}

export const SupportSchema = SchemaFactory.createForClass(Support);
