import { Prop } from "@nestjs/mongoose";

const UUID_REGEX = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;

/**
 * Custom UUID property decorator for Mongoose schema with indexing.
 */

export function UUIDProp(
    options: {
        required?: boolean;
        index?: boolean;
        unique?: boolean;
        select?: boolean;
    } = {},
) {
    return Prop({
        type: String,
        // Is required
        required: options.required ?? true,
        validate: {
            validator: (v: string) => UUID_REGEX.test(v),
            message: "Must be a valid UUID",
        },
        // Enables indexing if set to true
        index: options.index ?? false,
        // Enables unique constraint if set to true
        unique: options.unique ?? false,
        // Hides companyId from all queries by default
        select: options.select ?? true,
    });
}
