import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import {
    IsString,
    IsNotEmpty,
    IsOptional,
    IsEmail,
    Min<PERSON>ength,
    <PERSON><PERSON>ength,
    Matches,
    IsDate,
} from "class-validator";

export class UserDto {
    @ApiProperty({ description: "First Name", required: true })
    @MinLength(2)
    @MaxLength(15)
    @IsString()
    @Transform(({ value }) => value.trim(), { toClassOnly: true })
    @IsNotEmpty()
    firstName: string;

    @ApiPropertyOptional({ description: "Last Name", required: true })
    // @MinLength(2)
    @MaxLength(15)
    @IsString()
    @Transform(({ value }) => value.trim(), { toClassOnly: true })
    @IsOptional()
    lastName?: string;

    @ApiPropertyOptional({ description: "PreferredName" })
    @IsOptional()
    @IsString()
    @Transform(({ value }) => value.trim(), { toClassOnly: true })
    preferredName?: string;

    @ApiPropertyOptional({ description: "phone" })
    @IsOptional()
    @IsString()
    phone?: string;

    @ApiPropertyOptional({ description: "Hire date" })
    @IsDate()
    @Transform(({ value }) => new Date(value))
    @IsOptional()
    hireDate?: Date;

    @ApiProperty({ description: "Email", required: true })
    @IsEmail()
    @Transform(({ value }) => value.trim().toLowerCase(), { toClassOnly: true })
    @IsNotEmpty()
    email: string;

    @ApiProperty({ description: "Password", required: true })
    @IsNotEmpty()
    @Transform(({ value }) => value.trim(), { toClassOnly: true })
    @MinLength(8, { message: "Password must contain minimum of 8 characters" })
    @MaxLength(32, { message: "Password must contain maximum of 32 characters" })
    @Matches(/(?:(?=.*\d)|(?=.*\W+))(?![.\n])(?=.*[A-Z])(?=.*[a-z]).*$/, {
        message: "Weak Password",
    })
    @IsNotEmpty()
    password: string;

    @ApiProperty({ description: "Confirm Password", required: true })
    @IsNotEmpty()
    @Transform(({ value }) => value.trim(), { toClassOnly: true })
    @MinLength(8, { message: "Password must contain minimum of 8 characters" })
    @MaxLength(32, { message: "Password must contain maximum of 32 characters" })
    @Matches(/(?:(?=.*\d)|(?=.*\W+))(?![.\n])(?=.*[A-Z])(?=.*[a-z]).*$/, {
        message: "Weak Password",
    })
    @IsNotEmpty()
    confirmPassword: string;

    @ApiPropertyOptional({ description: "Image url", required: false })
    @IsString()
    @Transform(({ value }) => value.trim(), { toClassOnly: true })
    @IsOptional()
    imageUrl?: string;
}
