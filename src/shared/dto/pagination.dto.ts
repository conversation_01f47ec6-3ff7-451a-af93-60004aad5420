import { ApiPropertyOptional } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsInt, IsOptional } from "class-validator";

export class PaginationDto {
    @ApiPropertyOptional({ description: "Skip" })
    @IsInt()
    @Type(() => Number)
    @IsOptional()
    skip?: number;

    @ApiPropertyOptional({ description: "Limit" })
    @IsInt()
    @Type(() => Number)
    @IsOptional()
    limit?: number;
}
