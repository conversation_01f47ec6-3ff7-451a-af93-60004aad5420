import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { PositionModule } from "src/position/position.module";
import { RoleModule } from "src/role/role.module";
import { ProjectSchema } from "src/project/schema/project.schema";
import { CustomProjectSchema } from "./schema/custom-project.schema";
import { CustomProjectController } from "./custom-project.controller";
import { CustomProjectService } from "./custom-project.service";

@Module({
    imports: [
        MongooseModule.forFeature([
            { name: "CustomProject", schema: CustomProjectSchema },
            { name: "Project", schema: ProjectSchema },
        ]),
        // PositionModule,
        // RoleModule,
    ],
    controllers: [CustomProjectController],
    providers: [CustomProjectService],
    exports: [CustomProjectService],
})
export class CustomProjectModule {}
