import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import { UUIDProp } from "src/shared/decorator/uuid.decorator";

export type CustomProjectDocument = CustomProject & Document;

@Schema({ timestamps: true, versionKey: false, id: false, collection: "CustomProject" })
export class CustomProject {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @UUIDProp()
    companyId: string;

    @Prop()
    PO: string;

    @Prop({ required: false })
    num: string;

    @Prop()
    clientName: string;

    @Prop()
    address: string;

    @Prop()
    startDate: Date;

    @Prop()
    endDate: Date;

    @Prop()
    deleted: boolean;

    @Prop()
    createdAt?: Date;

    @Prop()
    modifiedAt?: Date;
}

export const CustomProjectSchema = SchemaFactory.createForClass(CustomProject);
