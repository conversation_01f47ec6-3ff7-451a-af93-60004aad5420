import { Body, Controller, Delete, Get, Param, ParseUUIDPipe, Post, Put, Query } from "@nestjs/common";
import { ApiBearerAuth, ApiInternalServerErrorResponse, ApiOperation, ApiTags } from "@nestjs/swagger";
import { Roles } from "src/auth/guards/auth.guard";
import { UserRolesEnum } from "src/company/enum/role.enum";
import HttpResponse from "src/shared/http/response/response.http";
import { PaginationRequestDto } from "src/company/dto/pagination-request.dto";
import { CustomProjectService } from "./custom-project.service";
import { UpdateCustomProjectDto } from "./dto/update-custom-project.dto";
import { CreateCustomProjectDto } from "./dto/create-custom-project.dto";
import { Auth, GetUser } from "src/auth/decorator/auth.decorator";
import { JwtUserPayload } from "src/auth/interface/auth.interface";

@ApiTags("CustomProject")
@ApiBearerAuth()
@Auth()
@Controller({ path: "custom-project", version: "1" })
export class CustomProjectController {
    constructor(private readonly customProjectService: CustomProjectService) {}

    /**
     * @description Create a new custom project
     * @param createCustomProjectDto - The data needed to create custom project.
     * @returns - An HTTP response indicating success or failure.
     */
    @ApiOperation({ summary: "Create custom projects" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Post("custom-project")
    async createCustomProject(
        @GetUser() user: JwtUserPayload,
        @Body() createCustomProjectDto: CreateCustomProjectDto,
    ): Promise<HttpResponse> {
        return this.customProjectService.createCustomProject(user.companyId, createCustomProjectDto);
    }

    /**
     * @description Get all custom projects
     * @returns - An array of all custom projects.
     */
    @ApiOperation({ summary: "Get all custom projects" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Get("custom-projects")
    async getAllCustomProjects(
        @GetUser() user: JwtUserPayload,
        @Query() paginationRequestDto: PaginationRequestDto,
    ): Promise<HttpResponse> {
        return this.customProjectService.getAllCustomProjects(user.companyId, paginationRequestDto);
    }

    /**
     * @description Get all inactive custom projects
     * @returns - An array of all custom projects.
     */
    @ApiOperation({ summary: "Get all inactive custom projects" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Get("inactive-custom-projects")
    async getInactiveCustomProjects(@GetUser() user: JwtUserPayload): Promise<HttpResponse> {
        return this.customProjectService.getInactiveCustomProjects(user.companyId);
    }

    /**
     * @description Update a custom project
     * @param id - The ID of the custom project to update.
     * @param updateCustomProjectDto - The data needed to update the custom project.
     * @returns - An HTTP response indicating success or failure.
     */
    @ApiOperation({ summary: "Update custom project" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Put("custom-project/:id")
    async updateCustomProject(
        @GetUser() user: JwtUserPayload,
        @Param("id") id: string,
        @Body() updateCustomProjectDto: UpdateCustomProjectDto,
    ): Promise<HttpResponse> {
        return this.customProjectService.updateCustomProject(user.companyId, id, updateCustomProjectDto);
    }

    /**
     * @description Delete a custom project
     * @param id - The ID of the custom project to delete.
     * @returns - An HTTP response indicating success or failure.
     */
    @ApiOperation({ summary: "Delete custom project" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Delete("custom-project/:id")
    async deleteCustomProject(
        @GetUser() user: JwtUserPayload,
        @Param("id") id: string,
    ): Promise<HttpResponse> {
        return this.customProjectService.deleteCustomProject(user.companyId, id);
    }
}
