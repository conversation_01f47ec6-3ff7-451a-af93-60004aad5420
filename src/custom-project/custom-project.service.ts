import { BadRequestException, HttpException, Injectable, InternalServerErrorException } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";
import OkResponse from "src/shared/http/response/ok.http";
import { PaginationRequestDto } from "src/company/dto/pagination-request.dto";
import { CustomProjectDocument } from "./schema/custom-project.schema";
import { CreateCustomProjectDto } from "./dto/create-custom-project.dto";
import { UpdateCustomProjectDto } from "./dto/update-custom-project.dto";
import { startOfDate } from "src/shared/helpers/logics";
import { CompanyAnalyticsService } from "src/company/company-analytics.service";

@Injectable()
export class CustomProjectService {
    constructor(
        private readonly companyAnalyticsService: CompanyAnalyticsService,
        @InjectModel("CustomProject") private readonly customProjectModel: Model<CustomProjectDocument>,
    ) {}

    async createCustomProject(companyId: string, createCustomProjectDto: CreateCustomProjectDto) {
        try {
            const { startDate, endDate } = createCustomProjectDto;
            if (startDate && endDate && startDate > endDate)
                throw new BadRequestException("End Date should be greater than Start Date");

            await this.customProjectModel.create({
                ...createCustomProjectDto,
                PO: createCustomProjectDto.po,
                companyId,
                deleted: false,
            });

            // update company analatyics
            this.companyAnalyticsService.updateCompanyProjectAnalytics(companyId, {
                created: 1,
            });
            return new OkResponse({ message: "Project created successfully" });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getAllCustomProjects(companyId: string, paginationRequestDto: PaginationRequestDto) {
        try {
            const limit = paginationRequestDto.limit || 10;
            const offset = limit * (paginationRequestDto.skip || 0);
            const { search } = paginationRequestDto;
            const filter = {
                companyId,
                deleted: false,
            };

            if (search) {
                const searchRegex = new RegExp(search, "i");
                filter["$or"] = [{ clientName: searchRegex }, { PO: searchRegex }];
            }

            const customProjects = await this.customProjectModel.find(filter).skip(offset).limit(limit);

            return new OkResponse({ customProjects });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getInactiveCustomProjects(companyId: string) {
        try {
            const customProjects = await this.customProjectModel.find({ companyId, deleted: true });
            return new OkResponse({ customProjects });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updateCustomProject(companyId: string, id: string, updateCustomProjectDto: UpdateCustomProjectDto) {
        try {
            const { startDate, endDate } = updateCustomProjectDto;
            if (startDate && endDate && startDate > endDate)
                throw new BadRequestException("End Date should be greater than Start Date");

            await this.customProjectModel.updateOne(
                { _id: id, companyId },
                {
                    $set: {
                        ...updateCustomProjectDto,
                        ...(updateCustomProjectDto?.po ? { PO: updateCustomProjectDto.po } : {}),
                    },
                },
            );
            return new OkResponse({ message: "Project updated successfully" });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async deleteCustomProject(companyId, id) {
        try {
            const customProject = await this.customProjectModel.findOne({ companyId, _id: id });

            if (!customProject.deleted) throw new BadRequestException(`Project should inactive`);

            await this.customProjectModel.deleteOne({ _id: id, companyId });

            // update company analatyics
            this.companyAnalyticsService.updateCompanyProjectAnalytics(companyId, {
                deleted: 1,
            });

            return new OkResponse({ message: "Project deleted successfully" });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getOppList(companyId: string, filter: any) {
        try {
            return await this.customProjectModel.aggregate([
                {
                    $match: {
                        companyId,
                        deleted: false,
                        ...filter,
                    },
                },
                { $sort: { PO: 1 } },
                {
                    $project: {
                        projectTypeId: "$_id",
                        PO: 1,
                        num: 1,
                        oppLat: 1,
                        oppLong: 1,
                        clientName: 1,
                        startDate: 1,
                        endDate: 1,
                        pitches: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20],
                        layers: [1, 2, 3, 4],
                        _id: 1,
                    },
                },
            ]);

            // return new OkResponse({ readyOpps });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }
}
