import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsDate, IsNotEmpty, IsOptional, IsString } from "class-validator";

export class CreateCustomProjectDto {
    @ApiProperty({ description: "PO name", required: true })
    @IsNotEmpty()
    @IsString()
    po: string;

    @ApiPropertyOptional({ description: "PO num" })
    @IsOptional()
    @IsString()
    num?: string;

    @ApiPropertyOptional({ description: "client name" })
    @IsOptional()
    @IsString()
    clientName?: string;

    @ApiPropertyOptional({ description: "address" })
    @IsOptional()
    @IsString()
    address?: string;

    @ApiPropertyOptional({ description: "start date" })
    @IsOptional()
    @Transform(({ value }) => new Date(value))
    @IsDate()
    startDate?: Date;

    @ApiPropertyOptional({ description: "end date" })
    @IsOptional()
    @Transform(({ value }) => new Date(value))
    @IsDate()
    endDate?: Date;
}
