extends base

block content
  // HEADER
  // Set text color and font family ("sans-serif" or "Georgia, serif")
  tr
    td.header(align="center", valign="top", style="\
                        -webkit-font-smoothing: antialiased;\
                        text-size-adjust: 100%;\
                        -ms-text-size-adjust: 100%;\
                        -webkit-text-size-adjust: 100%;\
                        mso-table-lspace: 0pt;\
                        mso-table-rspace: 0pt;\
                        border-spacing: 0;\
                        margin: 0;\
                        padding: 0;\
                        padding-left: 6.25%;\
                        padding-right: 6.25%;\
                        width: 87.5%;\
                        font-size: 24px;\
                        font-weight: bold;\
                        line-height: 130%;\
                        padding-top: 25px;\
                        color: #000000;\
                        font-family: sans-serif;\
                        border-collapse: collapse;\
                    ", width="87.5%")= header
   
  // PARAGRAPH
  // Set text color and font family ("sans-serif" or "Georgia, serif"). Duplicate all text styles in links, including line-height
  tr
    td.paragraph(align="center", valign="top", style="\
                        -webkit-font-smoothing: antialiased;\
                        text-size-adjust: 100%;\
                        -ms-text-size-adjust: 100%;\
                        -webkit-text-size-adjust: 100%;\
                        mso-table-lspace: 0pt;\
                        mso-table-rspace: 0pt;\
                        border-spacing: 0;\
                        margin: 0;\
                        padding: 0;\
                        padding-left: 6.25%;\
                        padding-right: 6.25%;\
                        width: 87.5%;\
                        font-size: 17px;\
                        font-weight: 400;\
                        line-height: 160%;\
                        padding-top: 25px;\
                        color: #000000;\
                        font-family: sans-serif;\
                        border-collapse: collapse;\
                    ", width="87.5%")
        p Hello #{reciverName}, you’ve been invited to #{company} by #{senderName}
        p To join, just click the button below

  // BUTTON
  // Set button background color at TD, link/text color at A and TD, font family ("sans-serif" or "Georgia, serif") at TD. For verification codes add "letter-spacing: 5px;". Link format: http://domain.com/?utm_source={{Campaign-Source}}&utm_medium=email&utm_content={{Button-Name}}&utm_campaign={{Campaign-Name}}

  tr
    td.button(align="center", valign="top", style="\
                        -webkit-font-smoothing: antialiased;\
                        text-size-adjust: 100%;\
                        -ms-text-size-adjust: 100%;\
                        -webkit-text-size-adjust: 100%;\
                        line-height: 100%;\
                        mso-table-lspace: 0pt;\
                        mso-table-rspace: 0pt;\
                        border-spacing: 0;\
                        margin: 0;\
                        padding: 0;\
                        padding-left: 35%;\
                        padding-right: 35%;\
                        width: 87.5%;\
                        padding-top: 25px;\
                        padding-bottom: 5px;\
                        border-collapse: collapse;\
                    ", width="87.5%")
      a(href=`${url}`, target="_blank", style="\
                              -webkit-font-smoothing: antialiased;\
                              text-size-adjust: 100%;\
                              -ms-text-size-adjust: 100%;\
                              -webkit-text-size-adjust: 100%;\
                              line-height: 100%;\
                              color: #add8e6;\
                              text-decoration: none;\
                          ")
        table(border="0", cellpadding="0", cellspacing="0", align="center", style="\
                                -webkit-font-smoothing: antialiased;\
                                text-size-adjust: 100%;\
                                -ms-text-size-adjust: 100%;\
                                -webkit-text-size-adjust: 100%;\
                                line-height: 100%;\
                                mso-table-lspace: 0pt;\
                                mso-table-rspace: 0pt;\
                                max-width: 240px;\
                                min-width: 120px;\
                                border-spacing: 0;\
                                padding: 0;\
                                border-collapse: collapse;\
                            ")
          tr
            td(align="center", valign="middle", style="\
                                        -webkit-font-smoothing: antialiased;\
                                        text-size-adjust: 100%;\
                                        -ms-text-size-adjust: 100%;\
                                        -webkit-text-size-adjust: 100%;\
                                        line-height: 100%;\
                                        mso-table-lspace: 0pt;\
                                        mso-table-rspace: 0pt;\
                                        padding: 12px 24px;\
                                        margin: 0;\
                                        text-decoration: none;\
                                        border-spacing: 0;\
                                        border-radius: 4px;\
                                        -webkit-border-radius: 4px;\
                                        -moz-border-radius: 4px;\
                                        -khtml-border-radius: 4px;\
                                        border-collapse: collapse;\
                                    ", bgcolor="#E9703E")
              a(target="_blank", style="\
                              -webkit-font-smoothing: antialiased;\
                              text-size-adjust: 100%;\
                              -ms-text-size-adjust: 100%;\
                              -webkit-text-size-adjust: 100%;\
                              text-decoration: none;\
                              color: #ffffff;\
                              font-family: sans-serif;\
                              font-size: 17px;\
                              font-weight: 400;\
                              line-height: 120%;\
                          ", href=`${url}`)
                | Click to Join

  tr  
    td.paragraph(align="center", valign="middle", style="\
                        -webkit-font-smoothing: antialiased;\
                        text-size-adjust: 100%;\
                        -ms-text-size-adjust: 100%;\
                        -webkit-text-size-adjust: 100%;\
                        mso-table-lspace: 0pt;\
                        mso-table-rspace: 0pt;\
                        border-spacing: 0;\
                        margin: 0;\
                        padding: 0;\
                        padding-left: 6.25%;\
                        padding-right: 6.25%;\
                        width: 87.5%;\
                        font-size: 10px;\
                        font-weight: 400;\
                        line-height: 160%;\
                        padding-top: 25px;\
                        color: #000000;\
                        font-family: sans-serif;\
                        border-collapse: collapse;\
                        overflow-wrap: break-word;\
                    ", width="87.5%")
      p Disclaimer: This email has been generated automatically. Please do not reply to it.
