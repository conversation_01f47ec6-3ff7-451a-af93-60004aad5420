//- doctype html
//- html(xmlns="http://www.w3.org/1999/xhtml")
//-     head
//-         meta(http-equiv="content-type", content="text/html; charset=utf-8")
//-         meta(name="viewport", content="width=device-width, initial-scale=1.0;")
//-         meta(name="format-detection", content="telephone=no")

//-         // MESSAGE SUBJECT
//-         title= subject
//-         // BODY
//-         body(topmargin="0", rightmargin="0", bottommargin="0", leftmargin="0", marginwidth="0", marginheight="0", width="100%")

//-         block content

doctype html
html(xmlns="http://www.w3.org/1999/xhtml")
  head
    meta(http-equiv="content-type", content="text/html; charset=utf-8")
    meta(name="viewport", content="width=device-width, initial-scale=1.0;")
    meta(name="format-detection", content="telephone=no")
    style.
      @media all and (min-width: 560px) {
        .container {
          border-radius: 8px;
          -webkit-border-radius: 8px;
          -moz-border-radius: 8px;
          -khtml-border-radius: 8px;
        }
      }

      .order-data tr:hover {
        background-color: #61c97d;
      }

      .order-data tr:nth-child(even) {
        background-color: #f2f2f2;
      }
      
    // MESSAGE SUBJECT
    title= subject
  // BODY
  // Set message background color (twice) and text color (twice)
  body(topmargin="0", rightmargin="0", bottommargin="0", leftmargin="0", marginwidth="0", marginheight="0", width="100%", style="\
            min-width: 100%;\
            border-collapse: collapse;\
            border-spacing: 0;\
            margin: 0;\
            padding: 0;\
            -webkit-font-smoothing: antialiased;\
            text-size-adjust: 100%;\
            -ms-text-size-adjust: 100%;\
            -webkit-text-size-adjust: 100%;\
            line-height: 100%;\
            background-color: #f0f0f0;\
            color: #000000;\
            width: 100%;\
            height: 100%;\
        ", bgcolor="#F0F0F0", text="#000000")
    // SECTION / BACKGROUND
    // Set message background color one again
    table.background(width="100%", align="center", border="0", cellpadding="0", cellspacing="0", style="\
                -webkit-font-smoothing: antialiased;\
                text-size-adjust: 100%;\
                -ms-text-size-adjust: 100%;\
                -webkit-text-size-adjust: 100%;\
                line-height: 100%;\
                mso-table-lspace: 0pt;\
                mso-table-rspace: 0pt;\
                border-spacing: 0;\
                margin: 0;\
                padding: 0;\
                width: 100%;\
                border-collapse: collapse;\
            ")
      tr
        td(align="center", valign="top", style="\
                        -webkit-font-smoothing: antialiased;\
                        text-size-adjust: 100%;\
                        -ms-text-size-adjust: 100%;\
                        -webkit-text-size-adjust: 100%;\
                        line-height: 100%;\
                        mso-table-lspace: 0pt;\
                        mso-table-rspace: 0pt;\
                        border-spacing: 0;\
                        margin: 0;\
                        padding: 0;\
                        border-collapse: collapse;\
                    ", bgcolor="#F0F0F0")
          // WRAPPER
          // Set wrapper width (twice)
          table.wrapper(border="0", cellpadding="0", cellspacing="0", align="center", width="560", style="\
                            -webkit-font-smoothing: antialiased;\
                            text-size-adjust: 100%;\
                            -ms-text-size-adjust: 100%;\
                            -webkit-text-size-adjust: 100%;\
                            line-height: 100%;\
                            mso-table-lspace: 0pt;\
                            mso-table-rspace: 0pt;\
                            border-spacing: 0;\
                            padding: 0;\
                            width: inherit;\
                            max-width: 560px;\
                            border-collapse: collapse;\
                        ")
            tr
              td(align="center", valign="top", style="\
                                    -webkit-font-smoothing: antialiased;\
                                    text-size-adjust: 100%;\
                                    -ms-text-size-adjust: 100%;\
                                    -webkit-text-size-adjust: 100%;\
                                    line-height: 100%;\
                                    mso-table-lspace: 0pt;\
                                    mso-table-rspace: 0pt;\
                                    border-spacing: 0;\
                                    margin: 0;\
                                    padding: 0;\
                                    padding-left: 6.25%;\
                                    padding-right: 6.25%;\
                                    width: 87.5%;\
                                    padding-top: 20px;\
                                    padding-bottom: 20px;\
                                    border-collapse: collapse;\
                                ", width="87.5%")
                //NOTE: change LOGO image here
                // Image text color should be opposite to background color. Set your url, image src, alt and title. Alt text should fit the image size. Real image size should be x2. URL format: http://domain.com/?utm_source={{Campaign-Source}}&utm_medium=email&utm_content=logo&utm_campaign={{Campaign-Name}}
              
                img(border="0", vspace="0", hspace="0", src="https://www.newheightsroofing.com/wp-content/uploads/2023/12/New-Heights-Roofing-Logo.png", width="150", height="60", alt="Logo", title="Logo", style="\
                                            line-height: 100%;\
                                            color: #000000;\
                                            font-size: 10px;\
                                            margin: 0;\
                                            padding: 0;\
                                            outline: none;\
                                            text-decoration: none;\
                                            -ms-interpolation-mode: bicubic;\
                                            border: none;\
                                            display: block;\
                                        ")
          // End of WRAPPER



          // WRAPPER / CONTAINER
          // Set container background color
          table.container(border="0", cellpadding="0", cellspacing="0", align="center", bgcolor="#FFFFFF", width="560", style="\
                            -webkit-font-smoothing: antialiased;\
                            text-size-adjust: 100%;\
                            -ms-text-size-adjust: 100%;\
                            -webkit-text-size-adjust: 100%;\
                            line-height: 100%;\
                            mso-table-lspace: 0pt;\
                            mso-table-rspace: 0pt;\
                            border-spacing: 0;\
                            padding: 0;\
                            width: inherit;\
                            max-width: 560px;\
                            border-collapse: collapse;\
                        ")


            // //////////////////////////////////////////////

            // CONTENT
            block content
            
            // //////////////////////////////////////////////

            
            // LINE
            // Set line color
            tr
              td.line(align="center", valign="top", style="\
                                    -webkit-font-smoothing: antialiased;\
                                    text-size-adjust: 100%;\
                                    -ms-text-size-adjust: 100%;\
                                    -webkit-text-size-adjust: 100%;\
                                    line-height: 100%;\
                                    mso-table-lspace: 0pt;\
                                    mso-table-rspace: 0pt;\
                                    border-spacing: 0;\
                                    margin: 0;\
                                    padding: 0;\
                                    padding-left: 6.25%;\
                                    padding-right: 6.25%;\
                                    width: 87.5%;\
                                    padding-top: 25px;\
                                    border-collapse: collapse;\
                                ", width="87.5%")
                hr(color="#E0E0E0", align="center", width="100%", size="1", noshade, style="margin: 0; padding: 0")

            // PARAGRAPH
            // Set text color and font family ("sans-serif" or "Georgia, serif"). Duplicate all text styles in links, including line-height
            tr  
                td.paragraph(align="center", valign="middle", style="\
                                    -webkit-font-smoothing: antialiased;\
                                    text-size-adjust: 100%;\
                                    -ms-text-size-adjust: 100%;\
                                    -webkit-text-size-adjust: 100%;\
                                    mso-table-lspace: 0pt;\
                                    mso-table-rspace: 0pt;\
                                    border-spacing: 0;\
                                    margin: 0;\
                                    padding: 0;\
                                    padding-left: 6.25%;\
                                    padding-right: 6.25%;\
                                    width: 87.5%;\
                                    font-size: 10px;\
                                    font-weight: 400;\
                                    line-height: 160%;\
                                    padding-top: 25px;\
                                    color: #000000;\
                                    font-family: sans-serif;\
                                    border-collapse: collapse;\
                                    overflow-wrap: break-word;\
                                ", width="87.5%")
                            p If you need any assistance, feel free to contact our support team.
                            a(href="mailto:<EMAIL>", target="_blank", style="\
                                                    -webkit-font-smoothing: antialiased;\
                                                    text-size-adjust: 100%;\
                                                    -ms-text-size-adjust: 100%;\
                                                    -webkit-text-size-adjust: 100%;\
                                                    color: #127db3;\
                                                    font-family: sans-serif;\
                                                    font-size: 13px;\
                                                    font-weight: 400;\
                                                    line-height: 160%;\
                                                ")  <EMAIL>
                                                        
          // WRAPPER
          // Set wrapper width (twice)
          table.wrapper(border="0", cellpadding="0", cellspacing="0", align="center", width="560", style="\
                            -webkit-font-smoothing: antialiased;\
                            text-size-adjust: 100%;\
                            -ms-text-size-adjust: 100%;\
                            -webkit-text-size-adjust: 100%;\
                            line-height: 100%;\
                            mso-table-lspace: 0pt;\
                            mso-table-rspace: 0pt;\
                            border-spacing: 0;\
                            padding: 0;\
                            width: inherit;\
                            max-width: 560px;\
                            border-collapse: collapse;\
                        ")
            // SOCIAL NETWORKS
            // Image text color should be opposite to background color. Set your url, image src, alt and title. Alt text should fit the image size. Real image size should be x2
            tr
              td.social-icons(align="center", valign="top", style="\
                                    -webkit-font-smoothing: antialiased;\
                                    text-size-adjust: 100%;\
                                    -ms-text-size-adjust: 100%;\
                                    -webkit-text-size-adjust: 100%;\
                                    line-height: 100%;\
                                    mso-table-lspace: 0pt;\
                                    mso-table-rspace: 0pt;\
                                    border-spacing: 0;\
                                    margin: 0;\
                                    padding: 0;\
                                    padding-left: 6.25%;\
                                    padding-right: 6.25%;\
                                    width: 87.5%;\
                                    padding-top: 25px;\
                                    border-collapse: collapse;\
                                ", width="87.5%")
                table(width="256", border="0", cellpadding="0", cellspacing="0", align="center", style="\
                                        -webkit-font-smoothing: antialiased;\
                                        text-size-adjust: 100%;\
                                        -ms-text-size-adjust: 100%;\
                                        -webkit-text-size-adjust: 100%;\
                                        line-height: 100%;\
                                        mso-table-lspace: 0pt;\
                                        mso-table-rspace: 0pt;\
                                        border-spacing: 0;\
                                        padding: 0;\
                                        border-collapse: collapse;\
                                    ")
                  tr
                    // ICON 1
                    td(align="center", valign="middle", style="\
                                                -webkit-font-smoothing: antialiased;\
                                                text-size-adjust: 100%;\
                                                -ms-text-size-adjust: 100%;\
                                                -webkit-text-size-adjust: 100%;\
                                                line-height: 100%;\
                                                mso-table-lspace: 0pt;\
                                                mso-table-rspace: 0pt;\
                                                margin: 0;\
                                                padding: 0;\
                                                padding-left: 10px;\
                                                padding-right: 10px;\
                                                border-spacing: 0;\
                                                border-collapse: collapse;\
                                            ")
                      a(target="_blank", href="https://raw.githubusercontent.com/konsav/email-templates/", style="\
                                                    -webkit-font-smoothing: antialiased;\
                                                    text-size-adjust: 100%;\
                                                    -ms-text-size-adjust: 100%;\
                                                    -webkit-text-size-adjust: 100%;\
                                                    line-height: 100%;\
                                                    color: #127db3;\
                                                    text-decoration: none;\
                                                ")
                        img(border="0", vspace="0", hspace="0", style="\
                                                        line-height: 100%;\
                                                        padding: 0;\
                                                        margin: 0;\
                                                        outline: none;\
                                                        text-decoration: none;\
                                                        -ms-interpolation-mode: bicubic;\
                                                        border: none;\
                                                        display: inline-block;\
                                                        color: #000000;\
                                                    ", alt="F", title="Facebook", width="44", height="44", src="https://raw.githubusercontent.com/konsav/email-templates/master/images/social-icons/facebook.png")
                    // ICON 2
                    td(align="center", valign="middle", style="\
                                                -webkit-font-smoothing: antialiased;\
                                                text-size-adjust: 100%;\
                                                -ms-text-size-adjust: 100%;\
                                                -webkit-text-size-adjust: 100%;\
                                                line-height: 100%;\
                                                mso-table-lspace: 0pt;\
                                                mso-table-rspace: 0pt;\
                                                margin: 0;\
                                                padding: 0;\
                                                padding-left: 10px;\
                                                padding-right: 10px;\
                                                border-spacing: 0;\
                                                border-collapse: collapse;\
                                            ")
                      a(target="_blank", href="https://raw.githubusercontent.com/konsav/email-templates/", style="\
                                                    -webkit-font-smoothing: antialiased;\
                                                    text-size-adjust: 100%;\
                                                    -ms-text-size-adjust: 100%;\
                                                    -webkit-text-size-adjust: 100%;\
                                                    line-height: 100%;\
                                                    color: #127db3;\
                                                    text-decoration: none;\
                                                ")
                        img(border="0", vspace="0", hspace="0", style="\
                                                        line-height: 100%;\
                                                        padding: 0;\
                                                        margin: 0;\
                                                        outline: none;\
                                                        text-decoration: none;\
                                                        -ms-interpolation-mode: bicubic;\
                                                        border: none;\
                                                        display: inline-block;\
                                                        color: #000000;\
                                                    ", alt="T", title="Twitter", width="44", height="44", src="https://raw.githubusercontent.com/konsav/email-templates/master/images/social-icons/twitter.png")
                    // ICON 3
                    td(align="center", valign="middle", style="\
                                                -webkit-font-smoothing: antialiased;\
                                                text-size-adjust: 100%;\
                                                -ms-text-size-adjust: 100%;\
                                                -webkit-text-size-adjust: 100%;\
                                                line-height: 100%;\
                                                mso-table-lspace: 0pt;\
                                                mso-table-rspace: 0pt;\
                                                margin: 0;\
                                                padding: 0;\
                                                padding-left: 10px;\
                                                padding-right: 10px;\
                                                border-spacing: 0;\
                                                border-collapse: collapse;\
                                            ")
                      a(target="_blank", href="https://raw.githubusercontent.com/konsav/email-templates/", style="\
                                                    -webkit-font-smoothing: antialiased;\
                                                    text-size-adjust: 100%;\
                                                    -ms-text-size-adjust: 100%;\
                                                    -webkit-text-size-adjust: 100%;\
                                                    line-height: 100%;\
                                                    color: #127db3;\
                                                    text-decoration: none;\
                                                ")
                        img(border="0", vspace="0", hspace="0", style="\
                                                        line-height: 100%;\
                                                        padding: 0;\
                                                        margin: 0;\
                                                        outline: none;\
                                                        text-decoration: none;\
                                                        -ms-interpolation-mode: bicubic;\
                                                        border: none;\
                                                        display: inline-block;\
                                                        color: #000000;\
                                                    ", alt="G", title="Google Plus", width="44", height="44", src="https://raw.githubusercontent.com/konsav/email-templates/master/images/social-icons/googleplus.png")
                    // ICON 4
                    td(align="center", valign="middle", style="\
                                                -webkit-font-smoothing: antialiased;\
                                                text-size-adjust: 100%;\
                                                -ms-text-size-adjust: 100%;\
                                                -webkit-text-size-adjust: 100%;\
                                                line-height: 100%;\
                                                mso-table-lspace: 0pt;\
                                                mso-table-rspace: 0pt;\
                                                margin: 0;\
                                                padding: 0;\
                                                padding-left: 10px;\
                                                padding-right: 10px;\
                                                border-spacing: 0;\
                                                border-collapse: collapse;\
                                            ")
                      a(target="_blank", href="https://raw.githubusercontent.com/konsav/email-templates/", style="\
                                                    -webkit-font-smoothing: antialiased;\
                                                    text-size-adjust: 100%;\
                                                    -ms-text-size-adjust: 100%;\
                                                    -webkit-text-size-adjust: 100%;\
                                                    line-height: 100%;\
                                                    color: #127db3;\
                                                    text-decoration: none;\
                                                ")
                        img(border="0", vspace="0", hspace="0", style="\
                                                        line-height: 100%;\
                                                        padding: 0;\
                                                        margin: 0;\
                                                        outline: none;\
                                                        text-decoration: none;\
                                                        -ms-interpolation-mode: bicubic;\
                                                        border: none;\
                                                        display: inline-block;\
                                                        color: #000000;\
                                                    ", alt="I", title="Instagram", width="44", height="44", src="https://raw.githubusercontent.com/konsav/email-templates/master/images/social-icons/instagram.png")
            // FOOTER
            // Set text color and font family ("sans-serif" or "Georgia, serif"). Duplicate all text styles in links, including line-height
            tr
              td.footer(align="center", valign="top", style="\
                                    -webkit-font-smoothing: antialiased;\
                                    text-size-adjust: 100%;\
                                    -ms-text-size-adjust: 100%;\
                                    -webkit-text-size-adjust: 100%;\
                                    mso-table-lspace: 0pt;\
                                    mso-table-rspace: 0pt;\
                                    border-spacing: 0;\
                                    margin: 0;\
                                    padding: 0;\
                                    padding-left: 6.25%;\
                                    padding-right: 6.25%;\
                                    width: 87.5%;\
                                    font-size: 13px;\
                                    font-weight: 400;\
                                    line-height: 150%;\
                                    padding-top: 20px;\
                                    padding-bottom: 20px;\
                                    color: #999999;\
                                    font-family: sans-serif;\
                                    border-collapse: collapse;\
                                ", width="87.5%")
                | Don't want to receive emails, click on
                a(href="https://nhrapp.com", target="_blank", style="\
                                        -webkit-font-smoothing: antialiased;\
                                        text-size-adjust: 100%;\
                                        -ms-text-size-adjust: 100%;\
                                        -webkit-text-size-adjust: 100%;\
                                        text-decoration: underline;\
                                        color: #999999;\
                                        font-family: sans-serif;\
                                        font-size: 13px;\
                                        font-weight: 400;\
                                        line-height: 150%;\
                                    ") 
                                    | Unsubscribe
                //- | anytime.
                
            // End of WRAPPER
          // End of SECTION / BACKGROUND
