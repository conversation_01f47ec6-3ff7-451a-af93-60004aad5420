import { HttpException, HttpStatus, Injectable, InternalServerErrorException } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";
import { PaginationRequestDto } from "src/company/dto/pagination-request.dto";
import CreatedResponse from "src/shared/http/response/created.http";
import NoContentResponse from "src/shared/http/response/no-content.http";
import OkResponse from "src/shared/http/response/ok.http";
import { CreateCityDto } from "./dto/create-city.dto";
import { DeleteCityDto } from "./dto/delete-city.dto";
import { UpdateCityDto } from "./dto/update-city.dto";
import { CityDocument } from "./schema/city.schema";

@Injectable()
export class CityService {
    constructor(@InjectModel("City") private readonly cityModel: Model<CityDocument>) {}

    async createCity(companyId: string, createCityDto: CreateCityDto) {
        try {
            const city = await this.cityModel
                .exists({
                    companyId,
                    city: createCityDto.city,
                    state: createCityDto.state,
                    deleted: false,
                })
                .exec();
            if (city) throw new HttpException("City already exists", HttpStatus.BAD_REQUEST);
            const createdCity = new this.cityModel({
                companyId,
                ...createCityDto,
            });
            await createdCity.save();
            return new CreatedResponse({ message: "City created successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async deleteCity(userId: string, deleteCityDto: DeleteCityDto) {
        try {
            await this.cityModel.findOneAndUpdate(
                { _id: deleteCityDto.id, deleted: false },
                {
                    $set: { deleted: true },
                },
                { new: true },
            );
            return new NoContentResponse({ message: "City deleted successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async restoreCity(userId: string, restoreCityDto: DeleteCityDto) {
        try {
            await this.cityModel.findOneAndUpdate(
                { _id: restoreCityDto.id, deleted: true },
                {
                    $set: { deleted: false },
                },
                { new: true },
            );
            return new OkResponse({ message: "City restored successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updateCity(userId: string, updateCityDto: UpdateCityDto) {
        try {
            await this.cityModel.findOneAndUpdate(
                { _id: updateCityDto.id, deleted: false },
                {
                    $set: { ...updateCityDto },
                },
                { new: true },
            );
            return new OkResponse({ message: "City updated successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getCity(
        userId: string,
        companyId: string,
        deleted: boolean,
        paginationRequestDto: PaginationRequestDto,
    ) {
        try {
            const limit = paginationRequestDto.limit || 10;
            const offset = limit * (paginationRequestDto.skip || 0);
            const city = await this.cityModel.find({ companyId, deleted }).skip(offset).limit(limit);
            return new OkResponse({ city });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async addDefaultCity(companyId: string, createdBy: string) {
        try {
            const defaultCities = [
                {
                    createdBy,
                    city: "Clark Fork",
                    state: "WA",
                    companyId,
                },
                {
                    createdBy,
                    city: "Coeur d'Alene",
                    state: "ID",
                    companyId,
                },
                {
                    createdBy,
                    city: "Dallas",
                    state: "WV1",
                    companyId,
                },
                {
                    createdBy,
                    city: "Hauser",
                    state: "ID",
                    companyId,
                },
                {
                    createdBy,
                    city: "Newman Lake",
                    state: "WA",
                    companyId,
                },
                {
                    createdBy,
                    city: "Post Falls",
                    state: "Id",
                    companyId,
                },
                {
                    createdBy,
                    city: "Post Fallss",
                    state: "SS",
                    companyId,
                },
                {
                    createdBy,
                    city: "Rathdrum",
                    state: "ID",
                    companyId,
                },
                {
                    createdBy,
                    city: "Reno",
                    state: "ALAAM",
                    companyId,
                },
            ];
            await this.cityModel.insertMany(defaultCities);
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }
}
