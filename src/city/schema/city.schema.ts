import { Schema, SchemaFactory, Prop } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import { Document } from "mongoose";
import { UUIDProp } from "src/shared/decorator/uuid.decorator";

export type CityDocument = City & Document;

@Schema({ timestamps: true, id: false, collection: "City" })
export class City {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @UUIDProp()
    companyId: string;

    @Prop({ required: true })
    city: string;

    @Prop({ required: true })
    state: string;

    @UUIDProp()
    createdBy: string;

    @Prop({ default: false })
    deleted: boolean;

    @Prop()
    createdAt?: Date;

    @Prop()
    modifiedAt?: Date;
}

export const CitySchema = SchemaFactory.createForClass(City);
