import { <PERSON>hem<PERSON>, SchemaFactory, Prop } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import { Document } from "mongoose";
import { SubscriptionPlanTypeEnum, SubscriptionStatusEnum } from "src/shared/enum/subscriptions.enum";

export type CompanyDocument = Company & Document;

@Schema({ timestamps: true, id: false, collection: "Company", versionKey: false })
export class Company {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @Prop({ required: true })
    companyName: string;

    // @Prop({ required: true })
    // description: string;

    @Prop({ required: true })
    owner: string;

    @Prop()
    subscribedTeamSize: number; //owner paid for

    @Prop()
    teamSize: number; //company strength entered by owner

    @Prop()
    foundingYear: string;

    @Prop()
    workType: string;

    @Prop({ default: SubscriptionPlanTypeEnum.FREE, type: String, enum: SubscriptionPlanTypeEnum })
    planType: SubscriptionPlanTypeEnum;

    @Prop()
    planId: string;

    @Prop()
    amountPaid: number;

    @Prop()
    subscriptionStartDate: number;

    @Prop()
    subscriptionEndDate: number;

    @Prop()
    nextCycle: number;

    @Prop({ default: SubscriptionStatusEnum.CANCELLED, type: String, enum: SubscriptionStatusEnum })
    status: SubscriptionStatusEnum;

    @Prop({
        type: String,
    })
    interval: string;

    @Prop()
    intervalCount: string;

    @Prop()
    stripeSubscriptionId: string;

    @Prop()
    stripeCustomerId: string;

    @Prop()
    stripeSubscriptionItemId: string;

    @Prop()
    extraStripeSubscriptionItemId: string;

    @Prop({ default: false })
    isUpgraded: boolean;

    @Prop()
    imageUrl: string;

    @Prop()
    cancelAt: number;

    @Prop({ default: false })
    deleted: boolean;

    @Prop()
    deletedAt: Date;
}

export const CompanySchema = SchemaFactory.createForClass(Company);
