import { <PERSON>p, Schema, SchemaFactory } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import { UUIDProp } from "src/shared/decorator/uuid.decorator";

export type ReferrersDocument = Referrers & Document;

@Schema({ timestamps: true, id: false, collection: "Referrers" })
export class Referrers {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @Prop({ required: true })
    name: string;

    @UUIDProp()
    companyId: string;

    @Prop({ default: false })
    deleted: boolean;

    @Prop()
    createdAt?: Date;

    @Prop()
    updatedAt?: Date;
}

export const ReferrersSchema = SchemaFactory.createForClass(Referrers);
