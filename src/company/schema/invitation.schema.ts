import { Schema, SchemaFactory, Prop } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import { Document } from "mongoose";
import { InvitationStatusEnum } from "../enum/invitation-status.enum";
import { Wage } from "src/compensation/schema/compensation.schema";
import { UUIDProp } from "src/shared/decorator/uuid.decorator";

export type InvitationDocument = Invitation & Document;

@Schema({ timestamps: true, id: false, collection: "Invitation" })
export class Invitation {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @Prop({ required: true })
    firstName: string;

    @Prop({ required: false })
    lastName: string;

    @Prop({ required: true })
    email: string;

    @Prop({ required: true })
    senderEmail: string;

    @UUIDProp()
    company: string;

    @Prop({ default: InvitationStatusEnum.Pending, type: Number, enum: InvitationStatusEnum })
    status: InvitationStatusEnum;

    @Prop({ required: false })
    phone?: string;

    @Prop({ required: false })
    hireDate?: Date;

    @Prop({ type: () => Wage, required: false })
    wage?: Wage;

    @Prop({ required: false })
    preferredName?: string;

    @Prop({ required: false })
    managerId?: string;

    @Prop({ required: false })
    departmentId?: string;

    @Prop({ required: false })
    subContractorId?: string;

    @Prop()
    createdAt?: Date;

    @Prop()
    modifiedAt?: Date;
}

export const InvitationSchema = SchemaFactory.createForClass(Invitation);
