import { Schema, SchemaFactory, Prop } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import { Document } from "mongoose";
import { UUIDProp } from "src/shared/decorator/uuid.decorator";

export type CompanyAnalyticsDocument = CompanyAnalytics & Document;

class Payments {
    @Prop()
    date: Date;

    @Prop()
    amount: number;

    @Prop()
    planId: number;
}

class CreditPoints {
    @Prop()
    date: Date;

    @Prop()
    points: number;
}

class ProjectsHistory {
    @Prop()
    date: string;

    // active team size
    @Prop()
    created: number;

    @Prop()
    deleted: number;
}

class TeamSize {
    // date stored in string format ex. '2024-09-12'
    @Prop()
    date: string;

    // active team size
    @Prop()
    count: number;

    @Prop()
    invited: number;

    @Prop()
    joined: number;

    @Prop()
    terminated: number;

    @Prop()
    restored: number;
}

@Schema({ timestamps: true, id: false, collection: "CompanyAnalytics", versionKey: false })
export class CompanyAnalytics {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @UUIDProp()
    companyId: string;

    @Prop()
    subscribedTeamSize: number;

    @Prop()
    totalPayment: number;

    @Prop()
    paymentHistory: Payments[];

    @Prop()
    totalCredits: number;

    @Prop()
    creditPointHistory: CreditPoints[];

    @Prop()
    totalTeamSize: number;

    @Prop()
    teamHistory: TeamSize[];

    @Prop()
    totalProjects: number;

    @Prop()
    projectsHistory: ProjectsHistory[];

    //Subscription start date
    @Prop()
    subStartdate: Date;

    @Prop()
    cancelAt: number;
}

export const CompanyAnalyticsSchema = SchemaFactory.createForClass(CompanyAnalytics);
