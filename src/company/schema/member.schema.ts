import { Schema, SchemaFactory, Prop } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import { Document } from "mongoose";
import { UUIDProp } from "src/shared/decorator/uuid.decorator";

export type MemberDocument = Member & Document;

@Schema({ timestamps: true, id: false, collection: "Member" })
export class Member {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @Prop({ required: true })
    email: string;

    @Prop({ required: false })
    name: string;

    @Prop({ required: false })
    preferredName?: string;

    @Prop()
    notes?: string;

    @UUIDProp()
    company: string;

    @UUIDProp()
    user: string;

    @Prop({ default: false })
    invited: boolean;

    @Prop()
    position: string;

    @Prop()
    hireDate?: Date;

    @Prop()
    managerId?: string;

    @Prop()
    departmentId?: string;

    @Prop()
    roleId?: string;

    @Prop({ required: false })
    message?: string;

    @Prop({ required: false })
    terminateDate?: Date;

    @Prop()
    subContractorId?: string;

    @Prop({ default: false })
    deleted: boolean;

    @Prop()
    createdAt?: Date;

    @Prop()
    modifiedAt?: Date;
}

export const MemberSchema = SchemaFactory.createForClass(Member);
