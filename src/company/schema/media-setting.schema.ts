import { Schema, SchemaFactory, Prop } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import { Document } from "mongoose";
import { UUIDProp } from "src/shared/decorator/uuid.decorator";

export type MediaSettingDocument = MediaSetting & Document;

@Schema({ timestamps: true, id: false, collection: "MediaSetting" })
export class MediaSetting {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @UUIDProp()
    companyId: string;

    @Prop({
        type: [String],
        validate: {
            validator: (tags: string[]) => tags.length <= 100,
            message: "Tags array cannot exceed 100 items",
        },
    })
    tags: string[];

    // max MB image file allowed ex. 5MB
    @Prop({ default: 10 })
    maxImageSizeMB: number;

    // max MB audio file allowed ex. 5MB
    @Prop({ default: 10 })
    maxAudioSizeMB: number;

    // max MB video file allowed ex. 100MB
    @Prop({ default: 100 })
    maxVideoSizeMB: number;

    // max media allowed per opportunity
    @Prop({ default: 100 })
    maxMediaPerOpportunity: number;

    @Prop({ type: [String], default: ["png", "jpg", "jpeg", "mp4", "mp3", "mov", "mpeg", "avi", "pdf"] })
    allowedMediaTypes: string[];

    @Prop()
    createdAt?: Date;

    @Prop()
    updatedAt?: Date;
}

export const MediaSettingSchema = SchemaFactory.createForClass(MediaSetting);
