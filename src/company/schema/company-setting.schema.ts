import { <PERSON>hema, SchemaFactory, Prop } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import { Document } from "mongoose";
import { TimeZoneEnum } from "../enum/time-zome.enum";
import { UUIDProp } from "src/shared/decorator/uuid.decorator";

export type CompanySettingDocument = CompanySetting & Document;

@Schema({ timestamps: true, id: false, collection: "CompanySetting" })
export class CompanySetting {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @UUIDProp()
    companyId: string;

    @Prop({ required: false })
    email: string;

    @Prop({ required: false })
    phone: string;

    @Prop({ required: true })
    defaultPO: string;

    @Prop({ required: false })
    weekStartDay: string;

    @Prop({ required: false })
    weekEndDays: any[];

    @Prop({ default: TimeZoneEnum.PST, type: String, enum: TimeZoneEnum })
    timeZone: TimeZoneEnum;

    @Prop({ required: true })
    dailyOH: number;

    @Prop({ required: true })
    dailyLabor: number;

    @Prop({ required: true })
    dailyProfit: number;

    @Prop({ required: true })
    salesComm: number;

    // @Prop({ required: true })
    // commission: number;

    @Prop({ required: true })
    financeMod: number;

    @Prop({ required: true })
    actRevGoal: number;

    @Prop({ required: true })
    matMarkup: number;

    @Prop({ required: true })
    repairMarkup: number;

    @Prop({ required: true })
    repairMinimum: number;

    @Prop({ required: true })
    travelFee: number;

    @Prop({ required: true })
    travelHrlyRate: number;

    @Prop({ required: true })
    laborWaste: number;

    @Prop({ required: true })
    manHourRate: number;

    @Prop({ required: true })
    plywoodRate: number;

    @Prop()
    plywoodLaborRate: number;

    @Prop({ required: true })
    weekendBonus: number;

    @Prop({ required: true })
    ttlBurden: number;

    @Prop({ required: true })
    insWorkersComp: number;

    @Prop({ required: true })
    insUnemployment: number;

    @Prop({ required: true })
    ssMedicare: number;

    // @Prop({ required: true })
    // salesTaxWA: number;

    // @Prop({ required: true })
    // salesTaxID: number;

    @Prop({ required: true })
    modOH: number;

    @Prop({ required: true })
    modP: number;

    @Prop({ required: true })
    modS: number;

    @Prop({ required: true })
    modTtl: number;

    @UUIDProp()
    createdBy: string;

    @Prop({ default: false })
    deleted: boolean;

    @Prop({ required: false })
    address?: string;

    @Prop({ required: false })
    latitude?: string;

    @Prop({ required: false })
    longitude?: string;

    @Prop({ required: false })
    gpsEnable?: string;

    @Prop({ required: false })
    gpsTimeInterval?: number;

    @Prop({ required: false })
    workingStates: any[];

    @Prop()
    createdAt?: Date;

    @Prop()
    modifiedAt?: Date;
}

export const CompanySettingSchema = SchemaFactory.createForClass(CompanySetting);
