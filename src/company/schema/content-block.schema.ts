import { Schema, SchemaFactory, Prop } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import { Document } from "mongoose";
import { UUIDProp } from "src/shared/decorator/uuid.decorator";
import { ContentBlockTypeEnum } from "../enum/content-type.enum";

export type ContentBlockDocument = ContentBlock & Document;
@Schema({ timestamps: true, id: false, collection: "ContentBlock", strict: true })
export class ContentBlock {
    @Prop({ type: String, default: randomUUID })
    _id: string;

    @Prop({ isRequired: true })
    name: string;

    @UUIDProp()
    companyId: string;

    @Prop({ type: [String] })
    state?: string[];

    @Prop({ type: [String] })
    projectType?: string[];

    @Prop({ type: Number, enum: ContentBlockTypeEnum })
    type: ContentBlockTypeEnum;

    @Prop({ type: String, required: true })
    content: string;

    @Prop({ default: false })
    deleted: boolean;

    @UUIDProp()
    createdBy: string;

    @Prop()
    createdAt: Date;

    @Prop()
    updatedAt: Date;
}

export const ContentBlockSchema = SchemaFactory.createForClass(ContentBlock);
