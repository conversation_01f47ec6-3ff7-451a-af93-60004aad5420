import { Module } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";
import { MongooseModule } from "@nestjs/mongoose";
import { MailModule } from "src/mail/mail.module";
import { UserModule } from "src/user/user.module";
import { UserSchema } from "src/user/schema/user.schema";
import { CompanyController, CompanyOpenController } from "./company.controller";
import { CompanyService } from "./company.service";
import { CompanySchema } from "./schema/company.schema";
import { InvitationSchema } from "./schema/invitation.schema";
import { MemberSchema } from "./schema/member.schema";
import { PositionSchema } from "src/position/schema/position.schema";
import { DepartmentModule } from "src/department/department.module";
import { CompensationModule } from "src/compensation/compensation.module";
import { CompensationSchema } from "src/compensation/schema/compensation.schema";
import { DepartmentSchema } from "src/department/schema/department.schema";
import { PieceWorkModule } from "src/piece-work/piece-work.module";
import { CrewSchema } from "src/crew/schema/crew-management.schema";
import { CrewMemberSchema } from "src/crew/schema/crew-member.schema";
import { CompanySettingSchema } from "./schema/company-setting.schema";
import { WorkTaskModule } from "src/work-task/work-task.module";
import { WorkTaskSchema } from "src/work-task/schema/work-task.schema";
import { CityModule } from "src/city/city.module";
import { CrmModule } from "src/crm/crm.module";
import { ProjectModule } from "src/project/project.module";
import { UnitSchema } from "src/project/schema/unit.schema";
import { InputSchema } from "src/project/schema/input.schema";
import { CategorySchema } from "src/project/schema/category.schema";
import { SubCategorySchema } from "src/project/schema/sub-category.schema";
import { MaterialSchema } from "src/project/schema/material.schema";
import { CrewPositionSchema } from "src/project/schema/crew-position.schema";
import { ProjectTypeSchema } from "src/project/schema/project-type.schema";
import { TaskSchema } from "src/project/schema/task.schema";
import { ProjectSchema } from "src/project/schema/project.schema";
import { OpportunitySchema } from "src/opportunity/schema/opportunity.schema";
import { PriceSchema } from "src/project/schema/price-schema";
import { PackageSchema } from "src/project/schema/package.schema";
import { TaxJurisdictionSchema } from "src/project/schema/tax-jurisdiction.schema";
import { ClientSchema } from "src/client/schema/client.schema";
import { OrderSchema } from "src/project/schema/order.schema";
import { PayScheduleModule } from "src/pay-schedule/pay-schedule.module";
import { PieceWorkSettingSchema } from "src/piece-work/schema/piece-work-setting.schema";
import { ReferrersSchema } from "./schema/referrers.schema";
import { OptionsSchema } from "src/project/schema/options.schema";
import { CompanyPaySchema } from "./schema/company-pay.schema";
import { StripeModule } from "src/stripe/stripe.module";
import { SubscriptionPlanSchema } from "src/subscription/schema/subscription.schema";
import {
    SUBSCRIPTION_PRICE_COLLECTION_NAME,
    SubscriptionPriceSchema,
} from "src/subscription/schema/price.schema";
import { CompanyAnalyticsService } from "./company-analytics.service";
import { CompanyAnalyticsSchema } from "./schema/company-analytics.schema";
import { ContractSchema } from "./schema/contract-schema";
import { CitySchema } from "src/city/schema/city.schema";
import { CrmCheckpointSchema } from "src/crm/schema/crm-checkpoint.schema";
import { CrmStageSchema } from "src/crm/schema/crm-stage.schema";
import { CrmStepSchema } from "src/crm/schema/crm-step.schema";
import { CommissionModificationSchema } from "src/opportunity/schema/opp-commission.schema";
import { ActivityLogSchema } from "src/activity-log/schema/activity-log.schema";
import { SalesActionSchema } from "src/crm/schema/sales-action.schema";
import { CustomProjectSchema } from "src/custom-project/schema/custom-project.schema";
import { DailyLogSchema } from "src/daily-log/schema/daily-log.schema";
import { GpsSchema } from "src/gps/schema/gps.schema";
import { LeadSourceSchema } from "src/marketing-setting/schema/lead-source.schema";
import { LeadSchema } from "src/lead/schema/lead.schema";
import { MarketingChannelSchema } from "src/marketing-setting/schema/channel.schema.dto";
import { PayScheduleSchema } from "src/pay-schedule/schema/pay-schedule.schema";
import { PayRollSchema } from "src/payroll/schema/payroll.schema";
import { PieceWorkSchema } from "src/piece-work/schema/piece-work.schema";
import { RoleSchema } from "src/role/schema/role.schema";
import { SubcontractorSchema } from "src/subcontractor/schema/subcontractor.schema";
import { TimeCardSchema } from "src/time-card/schema/time-card.schema";
import { MediaSettingSchema } from "./schema/media-setting.schema";
import { ContentBlockSchema } from "./schema/content-block.schema";
import { MarketingSettingModule } from "src/marketing-setting/marketing-setting.module";
import { ContactsModule } from "src/contacts/contacts.module";

@Module({
    imports: [
        MongooseModule.forFeature([
            { name: "Company", schema: CompanySchema },
            { name: "CompanyAnalytics", schema: CompanyAnalyticsSchema },
            { name: "Client", schema: ClientSchema },
            { name: "Order", schema: OrderSchema },
            { name: "CompanySetting", schema: CompanySettingSchema },
            { name: "Member", schema: MemberSchema },
            { name: "Invitation", schema: InvitationSchema },
            { name: "User", schema: UserSchema },
            { name: "Position", schema: PositionSchema },
            { name: "Compensation", schema: CompensationSchema },
            { name: "WorkTask", schema: WorkTaskSchema },
            { name: "Department", schema: DepartmentSchema },
            { name: "Crew", schema: CrewSchema },
            { name: "CrewMember", schema: CrewMemberSchema },
            { name: "Unit", schema: UnitSchema },
            { name: "Input", schema: InputSchema },
            { name: "Category", schema: CategorySchema },
            { name: "Material", schema: MaterialSchema },
            { name: "CrewPosition", schema: CrewPositionSchema },
            { name: "ProjectType", schema: ProjectTypeSchema },
            { name: "SubCategory", schema: SubCategorySchema },
            { name: "Task", schema: TaskSchema },
            { name: "Project", schema: ProjectSchema },
            { name: "Opportunity", schema: OpportunitySchema },
            { name: "Price", schema: PriceSchema },
            { name: "Package", schema: PackageSchema },
            { name: "TaxJurisdiction", schema: TaxJurisdictionSchema },
            { name: "PieceWorkSetting", schema: PieceWorkSettingSchema },
            { name: "CompanyPay", schema: CompanyPaySchema },
            { name: "Referrers", schema: ReferrersSchema },
            { name: "Options", schema: OptionsSchema },
            { name: "SubscriptionPlans", schema: SubscriptionPlanSchema },
            { name: "Contracts", schema: ContractSchema },
            { name: SUBSCRIPTION_PRICE_COLLECTION_NAME, schema: SubscriptionPriceSchema },
            { name: "PaySchedule", schema: PayScheduleSchema },
            { name: "Subcontractor", schema: SubcontractorSchema },
            { name: "CrmStage", schema: CrmStageSchema },
            { name: "CrmCheckpoint", schema: CrmCheckpointSchema },
            { name: "CrmStep", schema: CrmStepSchema },
            { name: "ActivityLog", schema: ActivityLogSchema },
            { name: "TimeCard", schema: TimeCardSchema },
            { name: "PieceWork", schema: PieceWorkSchema },
            { name: "DailyLog", schema: DailyLogSchema },
            { name: "City", schema: CitySchema },
            { name: "CommissionModification", schema: CommissionModificationSchema },
            { name: "Lead", schema: LeadSchema },
            { name: "CustomProject", schema: CustomProjectSchema },
            { name: "Departments", schema: DepartmentSchema },
            { name: "Gps", schema: GpsSchema },
            { name: "LeadSource", schema: LeadSourceSchema },
            { name: "Marketing", schema: MarketingChannelSchema },
            { name: "Payroll", schema: PayRollSchema },
            { name: "Role", schema: RoleSchema },
            { name: "SalesAction", schema: SalesActionSchema },
            { name: "MediaSetting", schema: MediaSettingSchema },
            { name: "ContentBlock", schema: ContentBlockSchema },
        ]),
        PayScheduleModule,
        CrmModule,
        UserModule,
        MailModule,
        CityModule,
        ProjectModule,
        WorkTaskModule,
        PieceWorkModule,
        MarketingSettingModule,
        DepartmentModule,
        CompensationModule,
        StripeModule,
        ConfigModule,
        ContactsModule,
    ],
    providers: [CompanyService, CompanyAnalyticsService],
    controllers: [CompanyOpenController, CompanyController],
    exports: [CompanyService, CompanyAnalyticsService],
})
export class CompanyModule {}
