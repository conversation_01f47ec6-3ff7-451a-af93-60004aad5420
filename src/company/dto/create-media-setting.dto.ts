import { ApiPropertyOptional } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsArray, IsString, ArrayNotEmpty, IsOptional, IsNumber } from "class-validator";

export class CreateMediaSettingDto {
    @ApiPropertyOptional({ description: "List of tags", type: [String] })
    @IsOptional()
    @IsArray()
    @ArrayNotEmpty()
    @Transform(({ value }) =>
        Array.isArray(value)
            ? value
                  .filter((tag) => typeof tag === "string")
                  .map((tag) => tag.trim())
                  .filter((tag) => tag !== "")
            : value,
    )
    @IsString({ each: true })
    tags: string[];

    @ApiPropertyOptional({ description: "Max image size allowed in MB" })
    @IsOptional()
    @IsNumber()
    maxImageSizeMB?: number;

    @ApiPropertyOptional({ description: "Max audio size allowed in MB" })
    @IsOptional()
    @IsNumber()
    maxAudioSizeMB?: number;

    @ApiPropertyOptional({ description: "Max video size allowed in MB" })
    @IsOptional()
    @IsNumber()
    maxVideoSizeMB?: number;

    @ApiPropertyOptional({ description: "Max number of media allowed per opportunity" })
    @IsOptional()
    @IsNumber()
    maxMediaPerOpportunity?: number;

    @ApiPropertyOptional({ description: "Allowed media types (e.g., jpg, png, mp4, webp)" })
    @IsOptional()
    @IsArray()
    @ArrayNotEmpty()
    @IsString({ each: true })
    allowedMediaTypes?: string[];
}
