import {
    IsEmail,
    IsNotEmpty,
    Is<PERSON><PERSON>al,
    IsString,
    Matches,
    <PERSON><PERSON>th,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>teNested,
} from "class-validator";
import { Transform, Type } from "class-transformer";
import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { CreateCompanyDto } from "./create-company.dto"; // Assuming you have this class
import { SignupDto } from "src/auth/dto/signup.dto";

// export class UserDto {
//     @ApiProperty({ description: "First Name", required: true })
//     @MinLength(2)
//     @MaxLength(15)
//     @IsString()
//     @Transform(({ value }) => value.trim(), { toClassOnly: true })
//     @IsNotEmpty()
//     firstName: string;

//     @ApiPropertyOptional({ description: "Last Name", required: true })
//     // @MinLength(2)
//     @MaxLength(15)
//     @IsString()
//     @Transform(({ value }) => value.trim(), { toClassOnly: true })
//     @IsOptional()
//     lastName?: string;

//     @ApiPropertyOptional({ description: "PreferredName" })
//     @IsOptional()
//     @IsString()
//     @Transform(({ value }) => value.trim(), { toClassOnly: true })
//     preferredName?: string;

//     @ApiPropertyOptional({ description: "phone" })
//     @IsOptional()
//     @IsString()
//     phone?: string;

//     @ApiProperty({ description: "Email", required: true })
//     @IsEmail()
//     @Transform(({ value }) => value.trim().toLowerCase(), { toClassOnly: true })
//     @IsNotEmpty()
//     email: string;

//     @ApiProperty({ description: "Password", required: true })
//     @IsNotEmpty()
//     @Transform(({ value }) => value.trim(), { toClassOnly: true })
//     @MinLength(8, { message: "Password must contain minimum of 8 characters" })
//     @MaxLength(32, { message: "Password must contain maximum of 32 characters" })
//     @Matches(/(?:(?=.*\d)|(?=.*\W+))(?![.\n])(?=.*[A-Z])(?=.*[a-z]).*$/, {
//         message: "Weak Password",
//     })
//     @IsNotEmpty()
//     password: string;

//     @ApiProperty({ description: "Confirm Password", required: true })
//     @IsNotEmpty()
//     @Transform(({ value }) => value.trim(), { toClassOnly: true })
//     @MinLength(8, { message: "Password must contain minimum of 8 characters" })
//     @MaxLength(32, { message: "Password must contain maximum of 32 characters" })
//     @Matches(/(?:(?=.*\d)|(?=.*\W+))(?![.\n])(?=.*[A-Z])(?=.*[a-z]).*$/, {
//         message: "Weak Password",
//     })
//     @IsNotEmpty()
//     confirmPassword: string;
// }

export class RegisterCompanyDto {
    @ApiProperty({ type: SignupDto })
    @ValidateNested()
    @Type(() => SignupDto)
    signupDto: SignupDto;

    @ApiProperty({ type: CreateCompanyDto })
    @ValidateNested()
    @Type(() => CreateCompanyDto)
    companyDto: CreateCompanyDto;
}
