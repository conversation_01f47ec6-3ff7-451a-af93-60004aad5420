import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsEnum, IsNotEmpty, IsOptional, IsString, IsUUID } from "class-validator";
import { ContentBlockTypeEnum } from "../enum/content-type.enum";
import { Type } from "class-transformer";

export class GetContentDto {
    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    state?: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsUUID()
    projectType?: string;

    @ApiProperty()
    @IsNotEmpty()
    @IsEnum(ContentBlockTypeEnum)
    @Type(() => Number)
    type: ContentBlockTypeEnum;
}
