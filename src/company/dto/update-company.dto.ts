import { ApiPropertyOptional } from "@nestjs/swagger";
import { IsOptional, IsString } from "class-validator";

export class UpdateCompanyDto {
    @ApiPropertyOptional({ description: "Image Url" })
    @IsString()
    @IsOptional()
    imageUrl?: string;

    @ApiPropertyOptional({ description: "Company Name" })
    @IsString()
    @IsOptional()
    companyName?: string;

    @ApiPropertyOptional({ description: "Founding Year" })
    @IsString()
    @IsOptional()
    foundingYear?: string;

    @ApiPropertyOptional({ description: "Work Type" })
    @IsString()
    @IsOptional()
    workType?: string;
}
