import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsEnum, IsNotEmpty, IsString, IsUUID, ValidateNested } from "class-validator";
import { Type } from "class-transformer";
import { ActivityTypeEnum } from "src/crm/enum/activity-type.enum";

class ActionItemDto {
    @ApiPropertyOptional({ description: "Action description" })
    @IsString()
    @IsNotEmpty()
    _id: string;

    @ApiProperty({ description: "Action description" })
    @IsString()
    @IsNotEmpty()
    name: string;

    @ApiPropertyOptional({ description: "Activity type" })
    @IsEnum(ActivityTypeEnum)
    @IsNotEmpty()
    type: ActivityTypeEnum;
}

export class CreateSalesActionCompanyDto {
    @ApiProperty({ description: "Actions list", type: ActionItemDto })
    @ValidateNested({ each: true })
    @Type(() => ActionItemDto)
    @IsNotEmpty()
    action: ActionItemDto;
}
