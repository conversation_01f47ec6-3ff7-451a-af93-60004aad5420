import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsString, IsNotEmpty, MaxLength, <PERSON><PERSON>ength, IsEmail, IsOptional } from "class-validator";
export class InviteMemberDto {
    @ApiProperty({ description: "First Name", required: true })
    @MinLength(1)
    @MaxLength(15)
    @IsString()
    @Transform(({ value }) => value.trim(), { toClassOnly: true })
    firstName: string;

    @ApiPropertyOptional({ description: "Last Name", required: false })
    // @MinLength(1)
    @MaxLength(15)
    @IsString()
    @IsOptional()
    @Transform(({ value }) => value.trim(), { toClassOnly: true })
    lastName?: string;

    @ApiProperty({ description: "Recipient Email", required: true })
    @IsEmail()
    @Transform(({ value }) => value.trim().toLowerCase(), { toClassOnly: true })
    @IsNotEmpty()
    recipientEmail: string;

    @ApiProperty({ description: "Sender Email", required: true })
    @IsEmail()
    @Transform(({ value }) => value.trim().toLowerCase(), { toClassOnly: true })
    @IsNotEmpty()
    senderEmail: string;

    // @ApiProperty({ description: "Company", required: true })
    // @IsUUID()
    // @IsNotEmpty()
    // companyId: string;

    @ApiPropertyOptional({ description: "Phone" })
    @IsString()
    @IsOptional()
    phone?: string;

    @ApiPropertyOptional({ description: "Preferred Name", required: false })
    @IsOptional()
    @IsString()
    @Transform(({ value }) => value.trim(), { toClassOnly: true })
    preferredName?: string;
}
