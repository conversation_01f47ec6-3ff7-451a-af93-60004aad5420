import { ApiProperty, ApiPropertyOptional, OmitType } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsDate, IsNotEmpty, IsOptional, IsUUID } from "class-validator";
import { CreateCompensationDto } from "src/compensation/dto/create-compensation.dto";

export class InvitationWageDto extends OmitType(CreateCompensationDto, ["memberId"] as const) {
    @ApiProperty({ description: "Hire date" })
    @IsDate()
    @Transform(({ value }) => new Date(value))
    @IsNotEmpty()
    hireDate: Date;

    @ApiPropertyOptional({ description: "Manager id" })
    @IsUUID()
    @IsOptional()
    managerId?: string;

    @ApiPropertyOptional({ description: "Member department" })
    @IsUUID()
    @IsOptional()
    departmentId?: string;

    @ApiPropertyOptional({ description: "sub contractors" })
    @IsUUID()
    @IsOptional()
    subContractorId?: string;
}
