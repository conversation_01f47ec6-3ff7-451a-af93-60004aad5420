import { ApiProperty } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsNotEmpty, IsString, IsUUID } from "class-validator";

export class TerminateMemberDto {
    // @ApiProperty({ description: "CompanyId", required: true })
    // @IsUUID()
    // @IsNotEmpty()
    // companyId: string;

    @ApiProperty({ description: "Member Id", required: true })
    @IsUUID()
    @IsNotEmpty()
    memberId: string;

    @ApiProperty({
        description: "Message",
    })
    @Transform(({ value }) => value.trim(), { toClassOnly: true })
    @IsString()
    @IsNotEmpty()
    message: string;

    @ApiProperty({ description: "Terminate date" })
    @Transform(({ value }) => new Date(value))
    @IsNotEmpty()
    terminateDate: Date;
}
