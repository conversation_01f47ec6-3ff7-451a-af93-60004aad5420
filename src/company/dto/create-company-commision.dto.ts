import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import {
    IsUUID,
    IsNumber,
    IsNotEmpty,
    ValidateNested,
    IsArray,
    IsString,
    IsOptional,
    ArrayNotEmpty,
} from "class-validator";

class BenchmarksInputsDto {
    @ApiProperty({ description: "num" })
    @IsNumber()
    @IsNotEmpty()
    num: number;

    @ApiProperty({ description: "goal" })
    @IsNumber()
    @IsNotEmpty()
    goal: number;

    @ApiProperty({ description: "bonus" })
    @IsNumber()
    @IsNotEmpty()
    bonus: number;
}

export class VersionDTO {
    @ApiProperty({ description: "version Id", required: false })
    @IsUUID()
    @IsOptional()
    _id: string;

    @ApiProperty({ description: "name" })
    @IsString()
    @IsOptional()
    name: string;
}

class TypeBonus {
    [key: string]: number;
}

export class CreateCompanyCommisionDto {
    @ApiProperty({ description: "id" })
    @IsUUID()
    @IsNotEmpty()
    _id: string;

    // @ApiProperty({ description: "CompanyId" })
    // @IsUUID()
    // @IsNotEmpty()
    // companyId: string;

    @ApiProperty({ description: "typeBonus" })
    // @IsNumber()
    @IsNotEmpty()
    typeBonus: TypeBonus;

    @ApiProperty({ description: "benchmarks", type: [BenchmarksInputsDto] })
    @IsNotEmpty()
    @ValidateNested({ each: true })
    benchmarks: BenchmarksInputsDto[];

    @ApiProperty({ description: "Created By" })
    @IsUUID()
    @IsNotEmpty()
    createdBy: string;

    @ApiProperty({ description: "sales positions", type: [String] })
    @IsArray()
    @IsNotEmpty()
    @IsUUID("all", { each: true })
    salesPositions: string[];

    @ApiProperty({ description: "piecework position", type: [String] })
    @IsArray()
    @IsNotEmpty()
    @IsUUID("all", { each: true })
    pieceWorkPositions: string[];

    @ApiProperty({ description: "piecework versions", type: [VersionDTO] })
    @IsArray()
    @ArrayNotEmpty()
    @ValidateNested({ each: true })
    @Type(() => VersionDTO)
    versions: VersionDTO[];
}
