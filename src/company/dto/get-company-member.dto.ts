import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsBoolean, IsNotEmpty, IsOptional, IsString, IsUUID } from "class-validator";
import { PaginationRequestDto } from "./pagination-request.dto";
import { Transform } from "class-transformer";

export class GetCompanyMemberDto {
    // @ApiProperty({ description: "CompanyId", required: true })
    // @IsUUID()
    // @IsNotEmpty()
    // companyId: string;

    @ApiProperty({ description: "Member Id", required: true })
    @IsUUID()
    @IsNotEmpty()
    memberId: string;
}

export class GetMembersDto extends PaginationRequestDto {
    @ApiPropertyOptional({ description: "Position Id", required: false })
    @IsOptional()
    @IsString()
    positionId?: string;

    @ApiPropertyOptional({ description: "Department Id", required: false })
    @IsOptional()
    @IsUUID()
    departmentId?: string;

    @ApiPropertyOptional({ description: "Deleted", required: false })
    @IsOptional()
    @IsBoolean()
    @Transform(({ value }) => value === "true" || value === true)
    deleted?: boolean;
}

export class FetchMemberDto extends PaginationRequestDto {
    @ApiPropertyOptional({ description: "Position Id", required: false })
    @IsOptional()
    @IsString()
    positionId?: string;

    @ApiPropertyOptional({ description: "Department Id", required: false })
    @IsOptional()
    @IsUUID()
    departmentId?: string;

    @ApiPropertyOptional({ description: "Deleted", required: false })
    @IsOptional()
    @IsBoolean()
    @Transform(({ value }) => value === "true" || value === true)
    deleted?: boolean;

    @ApiPropertyOptional({ description: "Start date", required: false })
    @IsOptional()
    @Transform(({ value }) => new Date(value))
    startDate?: Date;

    @ApiPropertyOptional({ description: "End date", required: false })
    @IsOptional()
    @Transform(({ value }) => new Date(value))
    endDate?: Date;

    @ApiPropertyOptional({ description: "hasOpportunity", required: false })
    @IsOptional()
    @IsBoolean()
    @Transform(({ value }) => value === "true" || value === true)
    hasOpportunity?: boolean;
}
