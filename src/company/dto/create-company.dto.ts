import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsString, IsNotEmpty, IsOptional, IsNumber, IsPositive } from "class-validator";

export class CreateCompanyDto {
    @ApiProperty({ description: "Company Name", required: true })
    @IsString()
    @Transform(({ value }) => value.trim())
    @IsNotEmpty()
    companyName: string;

    // @ApiProperty({ description: "Company Description", required: true })
    // @IsString()
    // @IsNotEmpty()
    // description: string;

    @ApiProperty({ description: "Work Type", example: "roofing" })
    @IsOptional()
    @IsString()
    @Transform(({ value }) => value.trim())
    workType?: string;

    @ApiProperty({ description: "Company strength", example: 5 })
    @IsOptional()
    @IsNumber()
    @IsPositive()
    teamSize?: number;

    @ApiProperty({ description: "Founding Year" })
    @IsOptional()
    @IsString()
    @Transform(({ value }) => value.trim())
    foundingYear?: string;

    @ApiPropertyOptional({ description: "Image url", required: false })
    @IsString()
    @Transform(({ value }) => value.trim(), { toClassOnly: true })
    @IsOptional()
    imageUrl?: string;
}
