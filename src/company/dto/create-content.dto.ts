import { ApiPropertyOptional } from "@nestjs/swagger";
import { IsArray, IsBoolean, IsEnum, IsOptional, IsString } from "class-validator";
import { ContentBlockTypeEnum } from "../enum/content-type.enum";
import { Transform } from "class-transformer";

export class CreateContentBlockDto {
    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    name?: string;

    @ApiPropertyOptional({ type: [String] })
    @IsOptional()
    @IsArray()
    @IsString({ each: true })
    @Transform(({ value }) =>
        Array.isArray(value) ? value.filter((v) => typeof v === "string" && v.trim() !== "") : [],
    )
    state?: string[];

    @ApiPropertyOptional({ type: [String] })
    @IsOptional()
    @IsArray()
    @IsString({ each: true })
    @Transform(({ value }) =>
        Array.isArray(value) ? value.filter((v) => typeof v === "string" && v.trim() !== "") : [],
    )
    projectType?: string[];

    @ApiPropertyOptional()
    @IsOptional()
    @IsEnum(ContentBlockTypeEnum)
    type?: ContentBlockTypeEnum;

    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    content?: string;
}
