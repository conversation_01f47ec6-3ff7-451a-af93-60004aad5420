import { ApiPropertyOptional } from "@nestjs/swagger";
import { IsString, IsOptional, IsEnum, IsUUID, IsN<PERSON><PERSON> } from "class-validator";
import { InvitationStatusEnum } from "../enum/invitation-status.enum";

export class UserInvitationDto {
    @ApiPropertyOptional({ description: "id" })
    @IsUUID()
    @IsOptional()
    _id?: string;

    @ApiPropertyOptional({ description: "Email" })
    @IsString()
    @IsOptional()
    email?: string;

    // @ApiPropertyOptional({ description: "Company" })
    // @IsUUID()
    // @IsOptional()
    // companyId?: string;

    @ApiPropertyOptional({ description: "Invitation Status" })
    @IsNumber()
    @IsOptional()
    @IsEnum(InvitationStatusEnum)
    status?: InvitationStatusEnum;
}
