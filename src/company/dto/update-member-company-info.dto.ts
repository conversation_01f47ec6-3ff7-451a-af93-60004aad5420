import { ApiPropertyOptional } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsDate, IsOptional, IsString, IsUUID } from "class-validator";
import { MemberDto } from "src/shared/dto/member.dto";

export class UpdateMemberCompanyInfoDto extends MemberDto {
    @ApiPropertyOptional({ description: "Hire date" })
    @IsDate()
    @Transform(({ value }) => new Date(value))
    @IsOptional()
    hireDate?: Date;

    @ApiPropertyOptional({ description: "Manager id" })
    @IsUUID()
    @IsOptional()
    managerId?: string;

    @ApiPropertyOptional({ description: "Member department" })
    @IsUUID()
    @IsOptional()
    departmentId?: string;

    @ApiPropertyOptional({ description: "Member Name" })
    @IsString()
    @IsOptional()
    name?: string;

    @ApiPropertyOptional({ description: "preferredName Name" })
    @IsString()
    @IsOptional()
    preferredName?: string;

    @ApiPropertyOptional({ description: "email" })
    @IsString()
    @IsOptional()
    email?: string;

    @ApiPropertyOptional({ description: "notes" })
    @IsString()
    @IsOptional()
    notes?: string;

    @ApiPropertyOptional({ description: "subContractorId" })
    @IsUUID()
    @IsOptional()
    subContractorId?: string;
}
