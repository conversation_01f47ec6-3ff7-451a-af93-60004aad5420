import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsNotEmpty, IsOptional, IsBoolean, IsEmail, IsUUID, IsString, IsDate } from "class-validator";

export class CreateMemberDto {
    @ApiProperty({ description: "Member Email", required: true })
    @IsEmail()
    @Transform(({ value }) => value.trim().toLowerCase(), { toClassOnly: true })
    @IsNotEmpty()
    email: string;

    // @ApiProperty({ description: "Company Name", required: true })
    // @IsUUID()
    // @IsNotEmpty()
    // company: string;

    @ApiPropertyOptional({ description: "Member Position" })
    @IsUUID()
    @IsOptional()
    position?: string;

    @ApiProperty({ description: "User", required: true })
    @IsUUID()
    @IsNotEmpty()
    user: string;

    @ApiProperty({ description: "Member Name", required: true })
    @IsString()
    @Transform(({ value }) => value.trim(), { toClassOnly: true })
    @IsNotEmpty()
    name: string;

    @ApiPropertyOptional({ description: "Hire date" })
    @IsDate()
    @Transform(({ value }) => new Date(value))
    @IsOptional()
    hireDate?: Date;

    @ApiPropertyOptional({ description: "Preferred Name", required: false })
    @IsOptional()
    @IsString()
    @Transform(({ value }) => value.trim(), { toClassOnly: true })
    preferredName?: string;

    @ApiProperty({ description: "Invited User", required: true })
    @IsBoolean()
    @IsNotEmpty()
    invited: boolean;

    @ApiPropertyOptional({ description: "Manager id" })
    @IsUUID()
    @IsOptional()
    managerId?: string;

    @ApiPropertyOptional({ description: "Member department" })
    @IsUUID()
    @IsOptional()
    departmentId?: string;

    @ApiPropertyOptional({ description: "sub contractor linked to" })
    @IsUUID()
    @IsOptional()
    subContractorId?: string;
}
