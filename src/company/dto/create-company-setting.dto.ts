import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsUUID, IsN<PERSON>ber, IsNotEmpty, IsString, IsOptional, IsEnum, IsArray } from "class-validator";
import { TimeZoneEnum } from "../enum/time-zome.enum";

export class CreateCompanySettingDto {
    @ApiPropertyOptional({ description: "id" })
    @IsUUID()
    @IsOptional()
    _id?: string;

    // @ApiProperty({ description: "CompanyId" })
    // @IsUUID()
    // @IsNotEmpty()
    // companyId: string;

    @ApiPropertyOptional({ description: "email" })
    @IsOptional()
    @IsString()
    email?: string;

    @ApiPropertyOptional({ description: "phone" })
    @IsOptional()
    @IsString()
    phone?: string;

    @ApiProperty({ description: "Default Po" })
    @IsString()
    @IsNotEmpty()
    defaultPO: string;

    @ApiProperty({ description: "weekStartDay" })
    @IsString()
    weekStartDay: string;

    @ApiProperty({ description: "weekEndDays" })
    @IsArray()
    weekEndDays: any[];

    @ApiProperty({ description: "timeZone" })
    @IsString()
    @IsNotEmpty()
    @IsEnum(TimeZoneEnum)
    timeZone: TimeZoneEnum;

    @ApiProperty({ description: "daily OH" })
    @IsNumber()
    @IsNotEmpty()
    dailyOH: number;

    @ApiProperty({ description: "Daily Labor" })
    @IsNumber()
    @IsNotEmpty()
    dailyLabor: number;

    @ApiProperty({ description: "Daily Profit" })
    @IsNumber()
    @IsNotEmpty()
    dailyProfit: number;

    @ApiProperty({ description: "Sales Commission" })
    @IsNumber()
    @IsNotEmpty()
    salesComm: number;

    // @ApiProperty({ description: "Commission" })
    // @IsNumber()
    // @IsNotEmpty()
    // commission: number;

    @ApiProperty({ description: "FInance Mod" })
    @IsNumber()
    @IsNotEmpty()
    financeMod: number;

    @ApiProperty({ description: "Active RevGoal" })
    @IsNumber()
    @IsNotEmpty()
    actRevGoal: number;

    @ApiProperty({ description: "Mat Markup" })
    @IsNumber()
    @IsNotEmpty()
    matMarkup: number;

    @ApiProperty({ description: "Repair Markup" })
    @IsNumber()
    @IsNotEmpty()
    repairMarkup: number;

    @ApiProperty({ description: "Repair Minimum" })
    @IsNumber()
    @IsNotEmpty()
    repairMinimum: number;

    @ApiProperty({ description: "Travel Fee" })
    @IsNumber()
    @IsNotEmpty()
    travelFee: number;

    @ApiProperty({ description: "Travel hourly rate for task price calc" })
    @IsNumber()
    @IsNotEmpty()
    travelHrlyRate: number;

    @ApiProperty({ description: "Labor Waste" })
    @IsNumber()
    @IsNotEmpty()
    laborWaste: number;

    @ApiProperty({ description: "Man Hoyrly Rate" })
    @IsNumber()
    @IsNotEmpty()
    manHourRate: number;

    @ApiProperty({ description: "Plywood Rate" })
    @IsNumber()
    @IsNotEmpty()
    plywoodRate: number;

    @ApiPropertyOptional({ description: "Plywood Sheet Labor Rate" })
    @IsNumber()
    @IsOptional()
    plywoodLaborRate?: number;

    @ApiProperty({ description: "Weekend Bonus" })
    @IsNumber()
    @IsNotEmpty()
    weekendBonus: number;

    // @ApiProperty({ description: "Labor Burden" })
    // @IsNumber()
    // @IsNotEmpty()
    // ttlBurden: number;

    @ApiProperty({ description: "Comp on Labor" })
    @IsNumber()
    @IsNotEmpty()
    insWorkersComp: number;

    @ApiProperty({ description: "Unemployment insurance" })
    @IsNumber()
    @IsNotEmpty()
    insUnemployment: number;

    @ApiProperty({ description: "Social Security & Medicare" })
    @IsNumber()
    @IsNotEmpty()
    ssMedicare: number;

    // @ApiProperty({ description: "WA State Sales Tax" })
    // @IsNumber()
    // @IsNotEmpty()
    // salesTaxWA: number;

    // @ApiProperty({ description: "ID State Sales Tax" })
    // @IsNumber()
    // @IsNotEmpty()
    // salesTaxID: number;

    @ApiProperty({ description: "Created By" })
    @IsUUID()
    @IsNotEmpty()
    createdBy: string;

    @ApiPropertyOptional({ description: "address" })
    @IsString()
    @IsOptional()
    address?: string;

    @ApiPropertyOptional({ description: "latitude" })
    @IsString()
    @IsOptional()
    latitude?: string;

    @ApiPropertyOptional({ description: "longitude" })
    @IsString()
    @IsOptional()
    longitude?: string;

    @ApiPropertyOptional({ description: "gps Enable" })
    @IsString()
    @IsOptional()
    gpsEnable?: string;

    @ApiPropertyOptional({ description: "gps Enable" })
    @IsNumber()
    @IsOptional()
    gpsTimeInterval?: number;
}
