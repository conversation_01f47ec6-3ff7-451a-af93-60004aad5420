import { HttpException, Injectable, InternalServerErrorException } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";
import { CompanyAnalyticsDocument } from "./schema/company-analytics.schema";
import { MemberDocument } from "./schema/member.schema";
import { formatDate } from "src/shared/helpers/logics";
import OkResponse from "src/shared/http/response/ok.http";
import { CompanyDocument } from "./schema/company.schema";

@Injectable()
export class CompanyAnalyticsService {
    constructor(
        @InjectModel("Member") private readonly memberModel: Model<MemberDocument>,
        @InjectModel("Company") private readonly companyModel: Model<CompanyDocument>,
        @InjectModel("CompanyAnalytics")
        private companyAnalyticsModel: Model<CompanyAnalyticsDocument>,
    ) {}

    async createComapnyAnalytics(companyId: string, { subTeamSize }: { subTeamSize?: number }) {
        try {
            const today = formatDate(new Date());
            const memberCount = await this.memberModel.countDocuments({ company: companyId, deleted: false });
            if (!subTeamSize) {
                const { subscribedTeamSize } = await this.companyModel
                    .findOne({ _id: companyId, deleted: false })
                    .select("subscribedTeamSize");

                subTeamSize = subscribedTeamSize;
            }

            await this.companyAnalyticsModel.create({
                companyId,
                subscribedTeamSize: subTeamSize,
                totalPayment: 0,
                paymentHistory: [],
                totalCredits: 0,
                creditPointHistory: 0,
                totalTeamSize: memberCount,
                teamHistory: [
                    {
                        date: today,
                        count: memberCount,
                        invited: 0,
                        joined: 0,
                        terminated: 0,
                        restored: 0,
                    },
                ],
                totalProjects: 0,
                projectsHistory: {
                    date: today,
                    created: 0,
                    deleted: 0,
                },

                cancelAt: null,
            });
            return;
        } catch (e) {
            if (e instanceof HttpException) {
                throw e;
            }
            throw new InternalServerErrorException(e.message);
        }
    }

    async updateCompanyPayAnalytics(
        companyId: string,
        {
            amount,
            planId,
            subscribedTeamSize,
        }: {
            amount?: number;
            planId?: string;
            subscribedTeamSize?: number;
        },
    ) {
        try {
            const today = formatDate(new Date());

            // Build the $inc and $setOnInsert objects
            const incFields: any = {};

            // Only increase if values are provided
            if (amount !== undefined) {
                incFields["paymentHistory.$.amount"] = amount;
            }

            const cmpnyAnalytic = await this.companyAnalyticsModel.findOne({
                companyId,
            });

            // If initial data not present create one
            if (!cmpnyAnalytic) {
                await this.createComapnyAnalytics(companyId, {});
            }

            // If no entry for today, add a new one
            const newHistoryEntry = {
                date: today,
                amount: amount || 0,
                planId,
            };
            await this.companyAnalyticsModel.updateOne(
                { companyId },
                {
                    $set: { subscribedTeamSize },
                    $push: { paymentHistory: newHistoryEntry },
                    $inc: { totalPayment: amount },
                },
            );
            return;
        } catch (e) {
            if (e instanceof HttpException) {
                throw e;
            }
            throw new InternalServerErrorException(e.message);
        }
    }

    async updateCompanyProjectAnalytics(
        companyId: string,
        {
            created,
            deleted,
        }: {
            created?: number;
            deleted?: number;
        },
    ) {
        try {
            const today = formatDate(new Date());

            // Build the $inc and $setOnInsert objects
            const incFields: any = {};
            let totalProjectInc = 0;
            // Only increase if values are provided
            if (created !== undefined) {
                incFields["projectsHistory.$.created"] = created;
                totalProjectInc += created;
            }
            if (deleted !== undefined) {
                incFields["projectsHistory.$.deleted"] = deleted;
                totalProjectInc -= deleted;
            }

            // Combine the totalProjectInc into incFields
            incFields["totalProjects"] = totalProjectInc;

            const cmpnyAnalytic = await this.companyAnalyticsModel.findOne({
                companyId,
            });

            // If initial data not present create one
            if (!cmpnyAnalytic) {
                await this.createComapnyAnalytics(companyId, {});
            }
            // Check if today's record exists in teamHistory
            const todayRecord = await this.companyAnalyticsModel.findOne({
                companyId,
                "teamHistory.date": today,
            });
            if (todayRecord) {
                // Update existing entry
                await this.companyAnalyticsModel.updateOne(
                    { companyId, "teamHistory.date": today },
                    {
                        $inc: incFields,
                    },
                );
            } else {
                // If no entry for today, add a new one
                const newHistoryEntry = {
                    date: today,
                    created: created || 0,
                    deleted: deleted || 0,
                };
                await this.companyAnalyticsModel.updateOne(
                    { companyId },
                    {
                        $push: { projectsHistory: newHistoryEntry },
                        $inc: { totalProjects: totalProjectInc },
                    },
                );
            }

            return;
        } catch (e) {
            if (e instanceof HttpException) {
                throw e;
            }
            throw new InternalServerErrorException(e.message);
        }
    }

    async updateCompanyTeamAnalytics(
        companyId: string,
        {
            invited,
            joined,
            terminated,
            restored,
            count,
        }: {
            invited?: number;
            joined?: number;
            terminated?: number;
            restored?: number;
            count?: number;
        },
    ) {
        try {
            const today = formatDate(new Date());

            // Build the $inc and $setOnInsert objects
            const incFields: any = {};
            let totalTeamSizeInc = 0;

            // Only increase if values are provided
            if (invited !== undefined) {
                incFields["teamHistory.$.invited"] = invited;
            }
            if (joined !== undefined) {
                incFields["teamHistory.$.joined"] = joined;
                totalTeamSizeInc += joined;
                incFields["teamHistory.$.count"] = joined;
            }
            if (terminated !== undefined) {
                incFields["teamHistory.$.terminated"] = terminated;
                totalTeamSizeInc -= terminated;
                incFields["teamHistory.$.count"] = -terminated;
            }
            if (restored !== undefined) {
                incFields["teamHistory.$.restored"] = restored;
                totalTeamSizeInc += restored;
                incFields["teamHistory.$.count"] = restored;
            }

            // Combine the totalTeamSizeInc into incFields
            incFields["totalTeamSize"] = totalTeamSizeInc;

            const cmpnyAnalytic = await this.companyAnalyticsModel.findOne({
                companyId,
            });

            // If initial data not present create one
            if (!cmpnyAnalytic) {
                await this.createComapnyAnalytics(companyId, {});
            }
            // Check if today's record exists in teamHistory
            const todayRecord = await this.companyAnalyticsModel.findOne({
                companyId,
                "teamHistory.date": today,
            });
            if (todayRecord) {
                // Update existing entry
                await this.companyAnalyticsModel.updateOne(
                    { companyId, "teamHistory.date": today },
                    {
                        $inc: incFields,
                    },
                );
            } else {
                // If no entry for today, add a new one
                const newHistoryEntry = {
                    date: today,
                    invited: invited || 0,
                    joined: joined || 0,
                    terminated: terminated || 0,
                    restored: restored || 0,
                    count: cmpnyAnalytic.totalTeamSize + (joined || 0) - (terminated || 0) + (restored || 0),
                };
                await this.companyAnalyticsModel.updateOne(
                    { companyId },
                    {
                        $push: { teamHistory: newHistoryEntry },
                        $inc: { totalTeamSize: totalTeamSizeInc },
                    },
                );
            }
            return;
        } catch (e) {
            if (e instanceof HttpException) {
                throw e;
            }
            throw new InternalServerErrorException(e.message);
        }
    }

    async getAnalyticsByCompanyId(companyId: string) {
        try {
            const analyticsData = await this.companyAnalyticsModel.findOne({
                companyId,
            });
            return new OkResponse({ analyticsData });
        } catch (e) {
            if (e instanceof HttpException) {
                throw e;
            }
            throw new InternalServerErrorException(e.message);
        }
    }

    async getActiveMemberCount(companyId: string) {
        try {
            const memberCount = await this.memberModel.count({ company: companyId, deleted: false });
            const activeMembers = await this.companyAnalyticsModel
                .findOne({ companyId })
                .select("totalTeamSize");
            return { memberCount, activeMembers };
        } catch (e) {
            if (e instanceof HttpException) {
                throw e;
            }
            throw new InternalServerErrorException(e.message);
        }
    }
}
