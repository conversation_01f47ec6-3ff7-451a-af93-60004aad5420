import {
    Controller,
    Delete,
    Get,
    Param,
    UseGuards,
    Post,
    Body,
    BadRequestException,
    Query,
} from "@nestjs/common";
import {
    ApiTags,
    ApiUnauthorizedResponse,
    ApiInternalServerErrorResponse,
    ApiOperation,
    ApiBearerAuth,
    ApiBody,
} from "@nestjs/swagger";
import { GetUser } from "src/auth/decorator/auth.decorator";
import { S3Service } from "./s3.service";
import { JwtUserPayload } from "src/auth/interface/auth.interface";
import { UserAuthGuard } from "src/auth/guards/jwt-auth.guard";
import HttpResponse from "src/shared/http/response/response.http";
import { FilePathTypeEnum } from "./enum/filePath.enum";
import { GeneratePreSignedUrlsDto } from "./dto/create-presigned-url.dto";

@ApiTags("S3")
@UseGuards(UserAuthGuard)
@ApiBearerAuth()
@ApiUnauthorizedResponse({ description: "Unauthorized request" })
@ApiInternalServerErrorResponse({ description: "Server Error" })
@Controller({ path: "s3", version: "1" })
export class S3Controller {
    constructor(private readonly s3Service: S3Service) {}

    // @ApiOperation({ summary: "Update Member Role" })
    // @Get("pre-signed-url/:fileName/:mimetype/:pathType")
    // async preSingedUrl(
    //     @GetUser() user: JwtUserPayload,
    //     @Param("fileName") fileName: string,
    //     @Param("mimetype") mimetype: string,
    //     @Param("pathType") pathType: FilePathTypeEnum,
    // ): Promise<HttpResponse> {
    //     return this.s3Service.preSingedUrl(user.companyId, fileName, mimetype, pathType);
    // }

    // @ApiOperation({ summary: "Delete single media url" })
    // @Delete("image/:imageUrl")
    // async deleteImageS3(
    //     @GetUser() user: JwtUserPayload,
    //     @Param("imageUrl") imageUrl: string,
    // ): Promise<HttpResponse> {
    //     return this.s3Service.deleteImageS3(imageUrl);
    // }

    @ApiOperation({ summary: "Delete multiple images from S3" })
    @Delete("media")
    @ApiBody({
        schema: {
            type: "object",
            properties: { mediaUrl: { type: "array", items: { type: "string" } } },
            required: ["mediaUrl"],
        },
    })
    async deleteImagesS3(@Body("mediaUrl") mediaUrl: string[]): Promise<HttpResponse> {
        if (!Array.isArray(mediaUrl) || mediaUrl.length === 0) {
            throw new BadRequestException("mediaUrl must be a non-empty array of strings.");
        }

        return this.s3Service.deleteMultipleImageS3(mediaUrl);
    }

    @ApiOperation({ summary: "Generate Pre-Signed URLs for Multiple Files" })
    @Post("pre-signed-urls/:pathType")
    async generatePreSignedUrls(
        @GetUser() user: JwtUserPayload,
        @Param("pathType") pathType: FilePathTypeEnum,
        @Body() generatePreSignedUrlsDto: GeneratePreSignedUrlsDto,
    ): Promise<HttpResponse> {
        const { files, opportunityId, memberId } = generatePreSignedUrlsDto;
        return await this.s3Service.generatePreSignedUrls(
            user.companyId,
            files,
            pathType,
            opportunityId,
            memberId,
        );
    }

    @ApiOperation({ summary: "Get Download url" })
    @Get("download-url")
    async getDownloadUrl(
        @Query("imageUrl") imageUrl: string,
        @Query("isEdit") isEdit?: boolean,
    ): Promise<HttpResponse> {
        return await this.s3Service.generateDownloadUrl(imageUrl, isEdit);
    }
}
