import { BadRequestException, HttpException, Injectable, InternalServerErrorException } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import {
    S3Client,
    PutObjectCommand,
    DeleteObjectCommand,
    DeleteObjectsCommand,
    GetObjectCommand,
} from "@aws-sdk/client-s3";
import OkResponse from "src/shared/http/response/ok.http";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import { FilePathTypeEnum } from "./enum/filePath.enum";
import * as https from "https";

@Injectable()
export class S3Service {
    private awsS3BucketName: string;
    private s3Client: S3Client;

    constructor(private readonly configService: ConfigService) {
        this.awsS3BucketName = this.configService.get<string>("AWS_S3_BUCKET_NAME");
        // Initialize S3 client
        this.s3Client = new S3Client({
            credentials: {
                accessKeyId: this.configService.get<string>("AWS_ACCESS_KEY"),
                secretAccessKey: this.configService.get<string>("AWS_SECRET_KEY"),
            },
            region: this.configService.get<string>("AWS_S3_REGION"),
        });
    }

    /**
     * This function will generate a pre-signed URL for S3 bucket to upload image from frontend
     * @param fileName name of the image
     * @param mimetype type of file
     * @returns pre-signed URL
     */
    async preSingedUrl(companyId: string, fileName: string, mimetype: string, pathType: FilePathTypeEnum) {
        try {
            // Allowed mimetype prefixes
            const allowedMimeTypes = ["image/", "video/", "application/pdf", "audio"];

            // Check if mimetype is valid
            if (!allowedMimeTypes.some((type) => mimetype.startsWith(type))) {
                throw new BadRequestException(
                    "Invalid file type. Only images, videos, and PDFs are allowed.",
                );
            }
            const yearMonth = new Date().toISOString().split("T")[0].slice(0, 7).replace(/-/g, "/"); // YYYY/MM
            const fileNameArr = fileName.split(".");
            const originalFileName = fileNameArr[0].replace(/[^a-zA-Z0-9]/g, "_");
            const fileExtension = mimetype;

            // Generate a timestamp for unique file naming
            const timestamp = Date.now(); // Current time in milliseconds

            // Build S3 path based on company and path type (profile-pics, logs, etc.)
            const imageName = `companies/${companyId}/${pathType}/${yearMonth}/${timestamp}-${originalFileName}.${fileExtension}`;

            const command = new PutObjectCommand({
                Bucket: this.awsS3BucketName,
                Key: imageName,
                ContentType: `image/${mimetype}`,
            });

            const uploadUrl = await getSignedUrl(this.s3Client, command, { expiresIn: 60 });

            return new OkResponse({ uploadUrl });
        } catch (err) {
            if (err instanceof HttpException) {
                throw err;
            }
            throw new InternalServerErrorException(err.message);
        }
    }

    /**
     * This function will delete an image from S3
     * @param imageUrl to be deleted
     */
    async deleteImageS3(imageUrl: string) {
        try {
            const key = imageUrl.split("com/")[1];

            const command = new DeleteObjectCommand({
                Bucket: this.awsS3BucketName,
                Key: key,
            });

            await this.s3Client.send(command);

            return new OkResponse({ message: "Image Deleted" });
        } catch (err) {
            throw new InternalServerErrorException("Error while deleting image, Please try again");
        }
    }

    async deleteMultipleImageS3(imageUrls: string[]) {
        try {
            if (!imageUrls || imageUrls.length === 0) {
                return new OkResponse({ message: "No images provided for deletion." });
            }

            const keys = imageUrls.flatMap((url) => [
                { Key: url.split("com/")[1] }, // Original image key
                { Key: this.getThumbnailUrl(url).split("com/")[1] }, // Thumbnail key
            ]);

            const command = new DeleteObjectsCommand({
                Bucket: this.awsS3BucketName,
                Delete: { Objects: keys },
            });

            await this.s3Client.send(command);
            return new OkResponse({ message: "Images deleted successfully" });
        } catch (err) {
            throw new InternalServerErrorException("Error while deleting images, please try again.");
        }
    }

    private getThumbnailUrl(imageUrl: string): string {
        return imageUrl.replace(/(\.[^.]+)$/, "-thumbnail.jpg");
    }

    async generatePreSignedUrls(
        companyId: string,
        files: { fileName: string; mimetype: string }[],
        pathType: FilePathTypeEnum,
        opportunityId?: string,
        memberId?: string,
    ) {
        try {
            const MAX_FILE_SIZE = 100 * 1024 * 1024; // 100MB
            const ALLOWED_FILE_TYPES = ["image/", "video/", "application/pdf", "audio"];

            // Validate all file types before proceeding
            for (const file of files) {
                if (!ALLOWED_FILE_TYPES.some((type) => file.mimetype.startsWith(type))) {
                    throw new BadRequestException(
                        `Invalid file type '${file.mimetype}'. Only images, videos, Audios and PDFs are allowed.`,
                    );
                }
            }

            const yearMonth = new Date().toISOString().split("T")[0].slice(0, 7).replace(/-/g, "/"); // YYYY/MM
            const signedUrls = await Promise.all(
                files.map(async (file) => {
                    const { fileName, mimetype } = file;

                    // Sanitize and format the file name
                    const fileNameArr = fileName.split(".");
                    const originalFileName = fileNameArr[0].replace(/[^a-zA-Z0-9]/g, "_");
                    const fileExtension = mimetype.split("/")[1];
                    const fileType =
                        pathType === FilePathTypeEnum.Project
                            ? `${opportunityId}/${mimetype.split("/")[0]}`
                            : pathType === FilePathTypeEnum.Member
                            ? `${memberId}/${mimetype.split("/")[0]}`
                            : mimetype.split("/")[0];

                    // Generate a timestamp for unique file naming
                    const timestamp = Date.now();

                    // Build the S3 object key (path)
                    const objectKey = `companies/${companyId}/${pathType}/${fileType}/${yearMonth}/${timestamp}-${originalFileName}.${fileExtension}`;

                    // Create the command for the pre-signed URL
                    const command = new PutObjectCommand({
                        Bucket: this.awsS3BucketName,
                        Key: objectKey,
                        ContentType: mimetype,
                    });

                    // Generate the pre-signed URL
                    const signedUrl = await getSignedUrl(this.s3Client, command, { expiresIn: 60 });

                    return {
                        fileName,
                        url: signedUrl,
                    };
                }),
            );

            // Return all pre-signed URLs
            return new OkResponse({ signedUrls });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async generateDownloadUrl(imageUrl: string, isEdit?: boolean) {
        try {
            if (isEdit) {
                const imageData = await new Promise((resolve, reject) => {
                    https
                        .get(imageUrl, (response) => {
                            const chunks = [];

                            response.on("data", (chunk) => {
                                chunks.push(chunk);
                            });

                            response.on("end", () => {
                                const buffer = Buffer.concat(chunks);
                                const contentType = response.headers["content-type"];
                                const base64data = `data:${contentType};base64,${buffer.toString("base64")}`;
                                resolve(base64data);
                            });

                            response.on("error", (err) => {
                                reject(err);
                            });
                        })
                        .on("error", (err) => {
                            reject(err);
                        });
                });

                return new OkResponse({ base64data: imageData });
            } else {
                const originalFileName = imageUrl?.split("/")?.pop()?.split("-")?.[1];
                const command = new GetObjectCommand({
                    Bucket: this.awsS3BucketName,
                    Key: imageUrl,
                    ResponseContentDisposition: `attachment; filename="${originalFileName}"`,
                });
                const signedUrl = await getSignedUrl(this.s3Client, command, {
                    expiresIn: 60 * 10, // 10 minutes
                });
                return new OkResponse({ downloadUrl: signedUrl });
            }
        } catch (error) {
            throw new InternalServerErrorException("Failed to generate download URL");
        }
    }
}
