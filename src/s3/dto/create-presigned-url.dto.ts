import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { <PERSON>Array, IsOptional, IsString, IsUUID, ValidateNested } from "class-validator";
import { Type } from "class-transformer";

class FileItemDto {
    @ApiProperty({ description: "name of file", example: "document.pdf" })
    @IsString()
    fileName: string;

    @ApiProperty({ description: "name of file", example: "application/pdf" })
    @IsString()
    mimetype: string;
}

export class GeneratePreSignedUrlsDto {
    @ApiProperty({ description: "files array", required: true })
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => FileItemDto)
    files: FileItemDto[];

    @ApiPropertyOptional({ description: "opportunity id", required: false })
    @IsUUID()
    @IsOptional()
    opportunityId?: string;

    @ApiPropertyOptional({ description: "Current Member id", required: false })
    @IsUUID()
    @IsOptional()
    memberId?: string;
}
