import {
    IsString,
    IsOptional,
    IsBoolean,
    IsArray,
    IsNotEmpty,
    IsEnum,
    ValidateNested,
    IsUUID,
    IsEmail,
} from "class-validator";
import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Type } from "class-transformer";

// Define the enum for whereIsUse
export enum WhereIsUseEnum {
    ALL = "all",
    OPPORTUNITY = "opportunity",
    PROFILE = "profile",
}

// DTO for whoReceivesCopy
class WhoReceivesCopyDto {
    @IsBoolean()
    @ApiPropertyOptional()
    user: boolean;

    @IsBoolean()
    @ApiPropertyOptional()
    userManager: boolean;

    @IsArray()
    @IsUUID("all", { each: true })
    @ApiPropertyOptional({ type: [String] })
    teamMember: string[];

    @ApiPropertyOptional({ type: [String] })
    @IsArray()
    @IsEmail({}, { each: true })
    @IsOptional()
    otherEmail?: string[];
}

class PermissionsDto {
    @IsArray()
    @IsEnum(WhereIsUseEnum, { each: true })
    @ApiPropertyOptional({ enum: WhereIsUseEnum, isArray: true })
    whereIsUse: WhereIsUseEnum[];

    @IsArray()
    @IsString({ each: true })
    @ApiPropertyOptional({ type: [String] })
    whoCanUse: string[];

    @ValidateNested()
    @Type(() => WhoReceivesCopyDto)
    @ApiPropertyOptional({ type: WhoReceivesCopyDto })
    whoReceivesCopy: WhoReceivesCopyDto;
}

// Main DTO
export class CreateCompanyFormDto {
    @IsString()
    @IsNotEmpty()
    @ApiProperty()
    name: string;

    @IsString()
    @IsOptional()
    @ApiPropertyOptional()
    description?: string;

    @IsString()
    @IsNotEmpty()
    @ApiProperty()
    createdBy: string;

    @IsBoolean()
    @IsOptional()
    @ApiPropertyOptional()
    active?: boolean;

    @IsBoolean()
    @IsOptional()
    @ApiPropertyOptional()
    isPublished?: boolean;

    @IsArray()
    @ApiPropertyOptional({ type: [Object] })
    fields?: Record<string, any>[];

    @IsOptional()
    @ValidateNested()
    @Type(() => PermissionsDto)
    @ApiPropertyOptional({ type: PermissionsDto })
    permissions?: PermissionsDto;
}
