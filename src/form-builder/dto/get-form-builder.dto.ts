import { ApiPropertyOptional } from "@nestjs/swagger";
import { IsOptional, IsString } from "class-validator";

export class GetCompanyFormsDto {
    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    whoCanUse?: string; // Expect comma-separated string, not an array

    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    whereIsUse?: string; // Expect comma-separated string, not an array
}
