import { HttpException, HttpStatus, Injectable, InternalServerErrorException } from "@nestjs/common";
import { InjectConnection, InjectModel } from "@nestjs/mongoose";
import { Connection, Model } from "mongoose";
import OkResponse from "src/shared/http/response/ok.http";
import { FormBuilderDocument } from "./schema/form-builder.schema";
import { CreateCompanyFormDto } from "./dto/create-form-builder.dto";
import { UpdateCompanyFormDto } from "./dto/update-form-builder.dto";
import { JwtUserPayload } from "src/auth/interface/auth.interface";

@Injectable()
export class FormService {
    constructor(@InjectModel("FormBuilder") private readonly formBuilderModel: Model<FormBuilderDocument>) {}

    async createCompanyForm(user: JwtUserPayload, createDto: CreateCompanyFormDto) {
        try {
            const newForm = new this.formBuilderModel({
                companyId: user.companyId,
                ...createDto,
            });
            await newForm.save();
            return new OkResponse({ newForm });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updateCompanyForm(_id: string, updateCompanyFormDto: UpdateCompanyFormDto) {
        try {
            const existingForm = await this.formBuilderModel.exists({ _id });
            if (!existingForm) {
                throw new HttpException("Opportunity Form not found!", HttpStatus.NOT_FOUND);
            }
            const updatedForm = await this.formBuilderModel.findByIdAndUpdate(_id, updateCompanyFormDto, {
                new: true,
            });

            if (!updatedForm) {
                throw new HttpException("Failed to update changes!", HttpStatus.NOT_MODIFIED);
            }
            return new OkResponse({ updatedForm });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getCompanyForms(companyId: string, whoCanUse?: string, whereIsUse?: string) {
        try {
            const query: any = { companyId, deleted: false };

            const whoCanUseArray = whoCanUse ? whoCanUse.split(",") : [];
            const whereIsUseArray = whereIsUse ? whereIsUse.split(",") : [];

            if (whereIsUseArray.length > 0 && !whereIsUseArray.includes("all")) {
                query["permissions.whereIsUse"] = { $in: whereIsUseArray };
            }

            if (whereIsUseArray.includes("all")) {
                delete query["permissions.whereIsUse"];
            } else if (whereIsUseArray.length === 0) {
                query["permissions.whereIsUse"] = { $ne: "opportunity" };
            }

            if (whoCanUseArray.length > 0) {
                query["permissions.whoCanUse"] = { $in: whoCanUseArray };
            }

            // Fetch full documents because we may want full data later
            const forms = await this.formBuilderModel
            .find(query)
            .select("name _id companyId active deleted createdBy createdAt isPublished")
            .lean(); // convert mongoose docs to plain JS

            return new OkResponse({ forms });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getCompanyForm(_id: string) {
        try {
            const form = await this.formBuilderModel.findOne({ _id });

            if (!form) {
                throw new HttpException("Company form not found!", HttpStatus.NOT_FOUND);
            }

            return new OkResponse({ form });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async deleteCompanyForm(_id: string) {
        try {
            const { modifiedCount } = await this.formBuilderModel.updateOne(
                { _id },
                { $set: { deleted: true } },
            );

            if (!modifiedCount) {
                throw new HttpException("Failed to delete found!", HttpStatus.BAD_REQUEST);
            }

            return new OkResponse({ message: "Company form deleted successfully!" });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }
}
