import { Body, Controller, Delete, Get, Param, Patch, Post, Query } from "@nestjs/common";
import {
    ApiBearerAuth,
    ApiInternalServerErrorResponse,
    ApiOperation,
    ApiResponse,
    ApiTags,
    ApiUnauthorizedResponse,
} from "@nestjs/swagger";
import { Auth, GetUser } from "src/auth/decorator/auth.decorator";
import { FormService } from "./form-builder.service";
import { CreateCompanyFormDto } from "./dto/create-form-builder.dto";
import HttpResponse from "src/shared/http/response/response.http";
import { UpdateCompanyFormDto } from "./dto/update-form-builder.dto";
import { JwtUserPayload } from "src/auth/interface/auth.interface";
import { GetCompanyFormsDto } from "./dto/get-form-builder.dto";

@ApiTags("form-builder")
@ApiBearerAuth()
@Auth()
@Controller({ path: "form-builder", version: "1" })
@ApiInternalServerErrorResponse({ description: "Server Error" })
@ApiUnauthorizedResponse({ description: "Unauthorized request" })
export class FormSettingController {
    constructor(private readonly formService: FormService) {}

    @ApiOperation({ summary: "Add New Form of a company" })
    @Post()
    async createCompanyForm(
        @GetUser() user: JwtUserPayload,
        @Body() createCompanyFormDto: CreateCompanyFormDto,
    ): Promise<HttpResponse> {
        return this.formService.createCompanyForm(user, createCompanyFormDto);
    }

    @Patch("id/:_id")
    @ApiOperation({ summary: "Update a Company Form" })
    @ApiResponse({ status: 200, description: "Company Form updated" })
    async updateCompanyForm(
        @Param("_id") _id: string,
        @Body() updateCompanyFormDto: UpdateCompanyFormDto,
    ): Promise<HttpResponse> {
        return this.formService.updateCompanyForm(_id, updateCompanyFormDto);
    }

    @Get("all")
    @ApiOperation({ summary: "Get all forms of a company" })
    @ApiResponse({ status: 200, description: "List of forms retrieved successfully" })
    async getCompanyForms(
        @GetUser() user: JwtUserPayload,
        @Query() query: GetCompanyFormsDto,
    ): Promise<HttpResponse> {
        return this.formService.getCompanyForms(user.companyId, query.whoCanUse, query.whereIsUse);
    }

    @Get("id/:_id")
    @ApiOperation({ summary: "Get a specific form of a company by _id" })
    @ApiResponse({ status: 200, description: "Company form retrieved successfully" })
    async getCompanyForm(@Param("_id") _id: string): Promise<HttpResponse> {
        return this.formService.getCompanyForm(_id);
    }

    @Delete("id/:_id")
    @ApiOperation({ summary: "Delete a company form" })
    @ApiResponse({ status: 200, description: "Company form deleted successfully" })
    @ApiResponse({ status: 404, description: "Company form not found" })
    async deleteCompanyForm(@Param("_id") _id: string): Promise<HttpResponse> {
        return this.formService.deleteCompanyForm(_id);
    }
}
