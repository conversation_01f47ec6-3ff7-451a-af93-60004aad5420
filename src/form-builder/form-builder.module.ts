import { Modu<PERSON> } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { FormSettingController } from "./form-builder.controller";
import { FormService } from "./form-builder.service";
import { FormBuilderSchema } from "./schema/form-builder.schema";

@Module({
    imports: [MongooseModule.forFeature([{ name: "FormBuilder", schema: FormBuilderSchema }])],
    controllers: [FormSettingController],
    providers: [FormService],
    exports: [FormService, MongooseModule],
})
export class FormBuilderModule {}
