import { Schema, SchemaFactory, Prop } from "@nestjs/mongoose";
import { Document } from "mongoose";
import { UUIDProp } from "src/shared/decorator/uuid.decorator";
import { randomUUID } from "crypto";

export enum WhereIsUseEnum {
    ALL = "all",
    OPPORTUNITY = "opportunity",
    PROFILE = "profile",
}

export type FormBuilderDocument = FormBuilder & Document;

@Schema({ timestamps: true, collection: "FormBuilder", strict: false })
export class FormBuilder {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @UUIDProp()
    companyId: string;

    @Prop({ type: String, required: true })
    name: string;

    @Prop({ type: String })
    description?: string;

    @UUIDProp()
    createdBy: string;

    @Prop({ type: Boolean, default: true })
    active: boolean;

    @Prop({ type: Boolean, default: false })
    deleted: boolean;

    @Prop({ type: Boolean, required: false })
    isPublished?: boolean;

    @Prop({ type: Array, default: [] })
    fields: any[];

    @Prop({
        type: {
            whereIsUse: { type: [String], enum: Object.values(WhereIsUseEnum), default: [] },
            whoCanUse: { type: [String], default: [] },
            whoReceivesCopy: {
                type: {
                    user: { type: Boolean, default: false },
                    userManager: { type: Boolean, default: false },
                    teamMember: { type: [String], default: [] },
                    otherEmail: { type: [String], default: [], required: false },
                },
                _id: false,
            },
        },
        _id: false,
    })
    permissions: {
        whereIsUse: WhereIsUseEnum[];
        whoCanUse: string[];
        whoReceivesCopy: {
            user: boolean;
            userManager: boolean;
            teamMember: string[];
            otherEmail?: string[];
        };
    };

    @Prop()
    createdAt?: Date;

    @Prop()
    modifiedAt?: Date;
}

export const FormBuilderSchema = SchemaFactory.createForClass(FormBuilder);

// Indexes for performance
FormBuilderSchema.index({ companyId: 1, name: 1, deleted: 1 });
