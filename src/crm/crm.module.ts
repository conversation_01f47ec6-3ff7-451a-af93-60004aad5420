import { Modu<PERSON> } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { CrmController } from "./crm.controller";
import { CrmService } from "./crm.service";
import { CrmCheckpointSchema } from "./schema/crm-checkpoint.schema";
import { CrmStageSchema } from "./schema/crm-stage.schema";
import { CrmStepSchema } from "./schema/crm-step.schema";
import { OpportunitySchema } from "../opportunity/schema/opportunity.schema";
import { SalesActionSchema } from "./schema/sales-action.schema";

@Module({
    imports: [
        MongooseModule.forFeature([
            { name: "CrmStep", schema: CrmStepSchema },
            { name: "CrmStage", schema: CrmStageSchema },
            { name: "CrmCheckpoint", schema: CrmCheckpointSchema },
            { name: "Opportunity", schema: OpportunitySchema },
            { name: "SalesAction", schema: SalesActionSchema },
        ]),
        // PositionModule,
        // RoleModule,
    ],
    providers: [CrmService],
    controllers: [CrmController],
    exports: [CrmService],
})
export class CrmModule {}
