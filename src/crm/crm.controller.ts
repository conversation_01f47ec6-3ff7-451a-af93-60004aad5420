import {
    Body,
    Controller,
    Delete,
    Get,
    Param,
    ParseEnumPipe,
    ParseUUIDPipe,
    Patch,
    Post,
    Query,
} from "@nestjs/common";
import {
    ApiTags,
    ApiOperation,
    ApiInternalServerErrorResponse,
    ApiNotFoundResponse,
    ApiUnauthorizedResponse,
    ApiBearerAuth,
    ApiQuery,
} from "@nestjs/swagger";
import { Roles } from "src/auth/guards/auth.guard";
import { PaginationRequestDto } from "src/company/dto/pagination-request.dto";
import { UserRolesEnum } from "src/company/enum/role.enum";
import { PositionService } from "src/position/position.service";
import HttpResponse from "src/shared/http/response/response.http";
import { CrmService } from "./crm.service";
import { CreateCheckpointDto } from "./dto/create-checkpoint.dto";
import { CreateSalesActionDto } from "./dto/create-sales-action.dto";
import { CreateStageDto } from "./dto/create-stage.dto";
import { CreateStepDto } from "./dto/create-step.dto";
import { DeleteCheckpointDto } from "./dto/delete-checkpoint.dto";
import { DeleteSalesActionDto } from "./dto/delete-sales-action.dto";
import { DeleteStageDto } from "./dto/delete-stage.dto";
import { DeleteStepDto } from "./dto/delete-step.dto";
import { UpdateCheckpointDto } from "../opportunity/dto/update-checkpoint.dto";
import { UpdateSalesActionDto } from "./dto/update-sales-action.dto";
import { UpdateStageDto } from "./dto/update-stage.dto";
import { UpdateStepDto } from "./dto/update-step.dto";
import { UpdateSequenceDto } from "./dto/updateSequence.dto";
import { StageRequestDto } from "./dto/get-stage.dto";
import { StepRequestDto } from "./dto/get-step.dto";
import { Auth, GetUser } from "src/auth/decorator/auth.decorator";
import { JwtUserPayload } from "src/auth/interface/auth.interface";
import { StageGroupEnum } from "./enum/stage-group.enum";

@ApiTags("Crm")
@ApiBearerAuth()
@Auth()
@Controller({ path: "crm", version: "1" })
export class CrmController {
    constructor(private readonly crmService: CrmService, private readonly positionService: PositionService) {}

    /**
     * Creates a new stage
     * @param userId - User ID of the user creating the stage
     * @param createStageDto - DTO containing the information for the new stage
     * @returns - Promise that resolves to an HTTP response indicating the result of the operation
     */
    @ApiOperation({ summary: "Create Stage" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Post("create-stage")
    async createStage(
        @GetUser() user: JwtUserPayload,
        @Body() createStageDto: CreateStageDto,
    ): Promise<HttpResponse> {
        return this.crmService.createStage(user.companyId, createStageDto);
    }

    /**
     * Delete a stage
     * @param userId - User ID of the user deleting the stage
     * @param deleteStageDto - The DTO containing the stage ID.
     * @returns The HTTP response.
     */
    @ApiOperation({ summary: "Delete Stage" })
    @ApiNotFoundResponse({ description: "Stage not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Delete("delete-stage")
    async deleteStage(
        @GetUser() user: JwtUserPayload,
        @Body() deleteStageDto: DeleteStageDto,
    ): Promise<HttpResponse> {
        return this.crmService.deleteStage(user.companyId, deleteStageDto);
    }

    @ApiOperation({ summary: "opportunity list of Stage" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Get("stage-opportunity-list/:stageId")
    async stageOppList(
        @GetUser() user: JwtUserPayload,
        @Param("stageId", ParseUUIDPipe) stageId: string,
    ): Promise<HttpResponse> {
        return this.crmService.stageOppList(user.companyId, stageId);
    }

    @ApiOperation({ summary: "opportunity list of Stage" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Get("migrate-stage-opportunity/:oldStageId/:newStageId")
    async migrateOppStage(
        @GetUser() user: JwtUserPayload,
        @Param("oldStageId", ParseUUIDPipe) oldStageId: string,
        @Param("newStageId", ParseUUIDPipe) newStageId: string,
    ): Promise<HttpResponse> {
        return this.crmService.migrateOppStage(user.companyId, oldStageId, newStageId);
    }

    /**
     * Update a stage with the given user ID and stage update data.
     * @param userId - The ID of the user making the request.
     * @param updateStageDto - The stage update data.
     * @returns A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Update stage" })
    @ApiNotFoundResponse({ description: "stage not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Patch("update-stage")
    async updateStage(
        @GetUser() user: JwtUserPayload,
        @Body() updateStageDto: UpdateStageDto,
    ): Promise<HttpResponse> {
        return this.crmService.updateStage(user.companyId, updateStageDto);
    }

    /**
     * Update stage sequence
     * @param userId - The ID of the user making the request.
     * @param updateStageSequenceDto - The stage sequence update data.
     * @returns A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Update stage sequence" })
    @ApiNotFoundResponse({ description: "stage not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Patch("update-stage-sequence")
    async updateStageSequence(
        @GetUser() user: JwtUserPayload,
        @Body() updateStageSequenceDto: UpdateSequenceDto,
    ): Promise<HttpResponse> {
        return this.crmService.updateStageSequence(user._id, user.companyId, updateStageSequenceDto);
    }

    // get company stage
    /**
     * Get stage for a company
     * @param userId - User ID of the requester
     * @param companyId - Company ID of the Company to get stage for
     * @param deleted - Whether to include deleted stage in the results
     * @param stageRequestDto - Pagination request details (offset, limit, etc.)
     * @returns List of stage for the specified Company
     */
    @ApiOperation({ summary: "Get stages" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Get("get-stage/deleted/:deleted")
    async getStage(
        @GetUser() user: JwtUserPayload,
        @Param("deleted") deleted: boolean,
        @Query() stageRequestDto: StageRequestDto,
    ): Promise<HttpResponse> {
        return this.crmService.getStage(user._id, user.companyId, deleted, stageRequestDto);
    }

    /**
     * Get stage for a company by stage id
     * @param userId User ID of the requester
     * @param companyId Company ID of the Company to get stage for
     * @param stageId the ID of the stage to retrieve
     * @param deleted whether to include deleted stage in the results
     * @returns  A Promise representing the HTTP response with the requested stage
     */
    @ApiOperation({ summary: "Get stage by id" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Get("get-stage-by-id/stage/:stageId/deleted/:deleted")
    async getStageById(
        @GetUser() user: JwtUserPayload,
        @Param("stageId", ParseUUIDPipe) stageId: string,
        @Param("deleted") deleted: boolean,
    ): Promise<HttpResponse> {
        return this.crmService.getStageById(user._id, user.companyId, stageId, deleted);
    }

    /**
     * Create a new step
     * @param userId - User ID of the user creating the step
     * @param createStepDto - DTO containing the information for the new step
     * @returns Promise that resolves to an HTTP response indicating the result of the operation
     */
    @ApiOperation({ summary: "Create Step" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Post("create-step")
    async createStep(
        @GetUser() user: JwtUserPayload,
        @Body() createStepDto: CreateStepDto,
    ): Promise<HttpResponse> {
        return this.crmService.createStep(user.companyId, createStepDto);
    }

    /**
     * Delete a step
     * @param userId - User ID of the user deleting the step
     * @param deleteStepDto - The DTO containing the step ID.
     * @returns - The HTTP response.
     */
    @ApiOperation({ summary: "Delete Step" })
    @ApiNotFoundResponse({ description: "Step not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Delete("delete-step")
    async deleteStep(
        @GetUser() user: JwtUserPayload,
        @Body() deleteStepDto: DeleteStepDto,
    ): Promise<HttpResponse> {
        return this.crmService.deleteStep(user._id, deleteStepDto);
    }

    /**
     * Update a step with the given user ID and step update data.
     * @param userId - The ID of the user making the request.
     * @param updateStepDto - The step update data.
     * @returns - A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Update step" })
    @ApiNotFoundResponse({ description: "step not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Patch("update-step")
    async updateStep(
        @GetUser() user: JwtUserPayload,
        @Body() updateStepDto: UpdateStepDto,
    ): Promise<HttpResponse> {
        return this.crmService.updateStep(user._id, updateStepDto);
    }

    /**
     * Update step sequence
     * @param userId - The ID of the user making the request.
     * @param updateStageSequenceDto - The step sequence update data.
     * @returns A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Update step sequence" })
    @ApiNotFoundResponse({ description: "step not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Patch("update-step-sequence")
    async updateStepSequence(
        @GetUser() user: JwtUserPayload,
        @Body() updateStepSequenceDto: UpdateSequenceDto,
    ): Promise<HttpResponse> {
        return this.crmService.updateStepSequence(user._id, user.companyId, updateStepSequenceDto);
    }

    // get company step
    /**
     * Get step for a company
     * @param userId - User ID of the requester
     * @param companyId - Company ID of the Company to get step for
     * @param stageId - Stage id of the step
     * @param deleted -  Whether to include deleted step in the results
     * @param paginationRequestDto - Pagination request details (offset, limit, etc.)
     * @returns - List of step for the specified Company
     */
    @ApiOperation({ summary: "Get steps" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Get("get-step/stage/:stageId/deleted/:deleted")
    async getStep(
        @GetUser() user: JwtUserPayload,
        @Param("stageId", ParseUUIDPipe) stageId: string,
        @Param("deleted") deleted: boolean,
        @Query() stepRequestDto: StepRequestDto,
    ): Promise<HttpResponse> {
        return this.crmService.getStep(user._id, user.companyId, stageId, deleted, stepRequestDto);
    }

    /**
     *  Get step for a company by step id
     * @param userId - User ID of the requester
     * @param companyId - Company ID of the Company to get step for
     * @param stepId - he ID of the step to retrieve
     * @param deleted - whether to include deleted step in the results
     * @returns - a Promise representing the HTTP response with the requested step
     */
    @ApiOperation({ summary: "Get step by id" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Get("get-step-by-id/step/:stepId/deleted/:deleted")
    async getStepById(
        @GetUser() user: JwtUserPayload,
        @Param("stepId", ParseUUIDPipe) stepId: string,
        @Param("deleted") deleted: boolean,
    ): Promise<HttpResponse> {
        return this.crmService.getStepById(user._id, user.companyId, stepId, deleted);
    }

    /**
     * Update opp commission
     * @param userId - The ID of the user making the request.
     * @param UpdateOppCommissionDto - The DTO containing information to update opp commission
     * @returns - A promise that resolves to an HTTP response.
     */
    // @ApiOperation({ summary: "Update Opportunity commission" })
    // @ApiNotFoundResponse({ description: "Opportunity not found" })
    // @ApiInternalServerErrorResponse({ description: "Server Error" })
    // @Positions({
    //     name: moduleNames.module.sales,
    //     actions: [PermissionsEnum.Full, PermissionsEnum.Managed, PermissionsEnum.Self],
    // })
    // @Patch("update-opportunity-commission")
    // async updateOppCommission(
    //     @GetUser() user: JwtUserPayload,
    //     @Body() updateOppCommissionDto: UpdateOppCommissionDto,
    // ): Promise<HttpResponse> {
    //     //TODO need to update this logic for position check
    //     const { response: positionCheck } = await this.positionService.checkPositionPermission(
    //         user._id,
    //         user.companyId,
    //         ["Owner", "Admin", "GeneralManager", "SalesManager"],
    //     );

    //     return this.crmService.updateOppCommission(updateOppCommissionDto, positionCheck);
    // }

    //checkpoint
    /**
     * Create a new checkpoint
     * @param userId - The ID of the user making the request.
     * @param createCheckpointDto - The DTO containing information to create opp checkpoint
     * @returns - A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Create Checkpoint" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Post("create-checkpoint")
    async createCheckpoint(
        @GetUser() user: JwtUserPayload,
        @Body() createCheckpointDto: CreateCheckpointDto,
    ): Promise<HttpResponse> {
        return this.crmService.createCheckpoint(user.companyId, createCheckpointDto);
    }

    /**
     * Delete checkpoint
     * @param userId - The ID of the user making the request.
     * @param deleteCheckpointDto - The DTO containing information to delete opp checkpoint
     * @returns - A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Delete Checkpoint" })
    @ApiNotFoundResponse({ description: "Checkpoint not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Delete("delete-checkpoint")
    async deleteCheckpoint(
        @GetUser() user: JwtUserPayload,
        @Body() deleteCheckpointDto: DeleteCheckpointDto,
    ): Promise<HttpResponse> {
        return this.crmService.deleteCheckpoint(user._id, deleteCheckpointDto);
    }

    /**
     * Permanent Delete checkpoint
     * @param userId - The ID of the user making the request.
     * @param deleteCheckpointDto - The DTO containing information to delete opp checkpoint
     * @returns - A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Permanent Delete Checkpoint" })
    @ApiNotFoundResponse({ description: "Checkpoint not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Delete("perm-delete-checkpoint")
    async premDeleteCheckpoint(
        @GetUser() user: JwtUserPayload,
        @Body() deleteCheckpointDto: DeleteCheckpointDto,
    ): Promise<HttpResponse> {
        return this.crmService.premDeleteCheckpoint(user.companyId, deleteCheckpointDto);
    }

    /**
     * Permanent Delete Step
     * @param userId - The ID of the user making the request.
     * @param deleteStepDto - The DTO containing information to delete opp Step
     * @returns - A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Permanent Delete Step" })
    @ApiNotFoundResponse({ description: "Step not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Delete("perm-delete-step")
    async premDeleteStep(
        @GetUser() user: JwtUserPayload,
        @Body() deleteStepDto: DeleteStepDto,
    ): Promise<HttpResponse> {
        return this.crmService.premDeleteStep(user.companyId, deleteStepDto);
    }

    /**
     * Restore checkpoint
     * @param userId - The ID of the user making the request.
     * @param deleteCheckpointDto - The DTO containing information to delete opp checkpoint
     * @returns - A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Restore Checkpoint" })
    @ApiNotFoundResponse({ description: "Checkpoint not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Patch("restore-checkpoint")
    async restoreCheckpoint(
        @GetUser() user: JwtUserPayload,
        @Body() deleteCheckpointDto: DeleteCheckpointDto,
    ): Promise<HttpResponse> {
        return this.crmService.restoreCheckpoint(user._id, deleteCheckpointDto);
    }

    /**
     * Restore Stage
     * @param userId - The ID of the user making the request.
     * @param deleteStageDto - The DTO containing information to delete opp Stage
     * @returns - A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Restore Stage" })
    @ApiNotFoundResponse({ description: "Stage not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Patch("restore-stage")
    async restoreStage(
        @GetUser() user: JwtUserPayload,
        @Body() deleteStageDto: DeleteStageDto,
    ): Promise<HttpResponse> {
        return this.crmService.restoreStage(user._id, deleteStageDto);
    }

    /**
     * Restore Step
     * @param userId - The ID of the user making the request.
     * @param deleteStepDto - The DTO containing information to delete opp Step
     * @returns - A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Restore Step" })
    @ApiNotFoundResponse({ description: "Step not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Patch("restore-step")
    async restoreStep(
        @GetUser() user: JwtUserPayload,
        @Body() deleteStepDto: DeleteStepDto,
    ): Promise<HttpResponse> {
        return this.crmService.restoreStep(user._id, deleteStepDto);
    }

    /**
     * Update checkpoint
     * @param userId - The ID of the user making the request.
     * @param updateCheckpointDto - The DTO containing information to update opp checkpoint
     * @returns - A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Update Checkpoint" })
    @ApiNotFoundResponse({ description: "Checkpoint not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Patch("update-checkpoint")
    async updateCheckpoint(
        @GetUser() user: JwtUserPayload,
        @Body() updateCheckpointDto: UpdateCheckpointDto,
    ): Promise<HttpResponse> {
        return this.crmService.updateCheckpoint(user._id, updateCheckpointDto);
    }

    /**
     * Update checkpoint sequence
     * @param userId - The ID of the user making the request.
     * @param UpdateSequenceDto - The DTO containing information to update opp checkpoint sequence
     * @returns - A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Update checkpoint sequence" })
    @ApiNotFoundResponse({ description: "checkpoint not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Patch("update-checkpoint-sequence")
    async updateCheckpointSequence(
        @GetUser() user: JwtUserPayload,
        @Body() updateCheckpointSequenceDto: UpdateSequenceDto,
    ): Promise<HttpResponse> {
        return this.crmService.updateCheckpointSequence(
            user._id,
            user.companyId,
            updateCheckpointSequenceDto,
        );
    }

    /**
     * Get checkpoint
     * @param userId - The ID of the user making the request.
     * @param companyId - Company ID of the Company to get checkpoint for
     * @param deleted - whether to include deleted checkpoint in the results
     * @param operations - whether to include operations checkpoint in the results
     * @param paginationRequestDto - Pagination request details (offset, limit, etc.)
     * @returns List of opp checkpoint for the specified Company
     */
    @ApiOperation({ summary: "Get Checkpoint" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @ApiQuery({
        name: "stagegroup",
        required: false,
        enum: StageGroupEnum,
    })
    @Get("get-checkpoint/deleted/:deleted")
    async getCheckpoint(
        @GetUser() user: JwtUserPayload,
        @Param("deleted") deleted: boolean,
        @Query("stagegroup") stageGroup?: StageGroupEnum,
        @Query() paginationRequestDto?: PaginationRequestDto,
    ): Promise<HttpResponse> {
        return this.crmService.getCheckpoint(
            user._id,
            user.companyId,
            deleted,
            paginationRequestDto,
            stageGroup ? [stageGroup] : undefined,
        );
    }

    /**
     * Get checkpoint by id
     * @param userId - The ID of the user making the request.
     * @param companyId - Company ID of the Company to get step for
     * @param checkpointId - Checkpoint ID to retrieve
     * @param deleted - whether to include deleted checkpoint in the results
     * @returns a Promise representing the HTTP response with the requested checkpoint
     */
    @ApiOperation({ summary: "Get Checkpoint by id" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Get("get-checkpoint-by-id/checkpoint/:checkpointId/deleted/:deleted")
    async getCheckpointById(
        @GetUser() user: JwtUserPayload,
        @Param("checkpointId", ParseUUIDPipe) checkpointId: string,
        @Param("deleted") deleted: boolean,
    ): Promise<HttpResponse> {
        return this.crmService.getCheckpointById(user.companyId, checkpointId, deleted);
    }

    /**
     * Create sales action
     * @param userId - The ID of the user making the request.
     * @param createSalesActionDto - The DTO containing information to create sales action
     * @returns - A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Create Sales Action" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    // @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Post("sales-action")
    async createSalesAction(
        @GetUser() user: JwtUserPayload,
        @Body() createSalesActionDto: CreateSalesActionDto,
    ): Promise<HttpResponse> {
        return this.crmService.createSalesAction(user.companyId, createSalesActionDto);
    }

    /**
     * Delete sales action
     * @param userId - The ID of the user making the request.
     * @param deleteSalesActionDto - The DTO containing information to delete sales action
     * @returns - A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Delete Sales Action" })
    @ApiNotFoundResponse({ description: "Sales Action not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    // @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Delete("sales-action")
    async deleteSalesAction(
        @GetUser() user: JwtUserPayload,
        @Body() deleteSalesActionDto: DeleteSalesActionDto,
    ): Promise<HttpResponse> {
        return this.crmService.deleteSalesAction(user.companyId, deleteSalesActionDto);
    }

    /**
     * Updates sales action
     * @param userId - The ID of the user making the request.
     * @param updateSalesActionDto - The DTO containing information to update sales action
     * @returns - A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Update Sales Action" })
    @ApiNotFoundResponse({ description: "Sales Action not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    // @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Patch("sales-action")
    async updateSalesAction(
        @GetUser() user: JwtUserPayload,
        @Body() updateSalesActionDto: UpdateSalesActionDto,
    ): Promise<HttpResponse> {
        return this.crmService.updateSalesAction(user.companyId, updateSalesActionDto);
    }

    /**
     * Get sales action of a company
     * @param userId - User ID of the requester
     * @param companyId - Company ID of the Company to get sales action for
     * @param deleted - Whether to include deleted sales action in the results
     * @param paginationRequestDto - Pagination request details (offset, limit, etc.)
     * @returns List of sales action for the specified Company
     */
    @ApiOperation({ summary: "Get Sales Actions" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Get("sales-actions-all-members/deleted/:deleted")
    async getSalesActions(
        @GetUser() user: JwtUserPayload,
        @Param("deleted") deleted: boolean,
    ): Promise<HttpResponse> {
        return this.crmService.getSalesAction(user._id, user.companyId, deleted);
    }

    /**
     * Get sales action for a company by sales action id
     * @param userId - User ID of the requester
     * @param companyId - Company ID of the Company to get sales action for
     * @param salesActionId - the ID of the sales action to retrieve
     * @param deleted -  whether to include deleted sales action in the results
     * @returns - A Promise representing the HTTP response with the requested sales action
     */
    @ApiOperation({ summary: "Get Sales Action by id" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Get("sales-action-by-memberId")
    async getSalesActionById(
        @GetUser() user: JwtUserPayload,
        @Query("memberId", ParseUUIDPipe) memberId: string,
        @Query("deleted") deleted: boolean,
    ): Promise<HttpResponse> {
        return this.crmService.getSalesActionById(user._id, user.companyId, memberId, deleted);
    }
}
