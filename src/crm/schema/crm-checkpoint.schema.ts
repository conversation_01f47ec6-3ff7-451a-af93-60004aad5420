import { Schema, SchemaFactory, Prop } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import { Document } from "mongoose";
import { StageGroupEnum } from "../enum/stage-group.enum";
import { UUIDProp } from "src/shared/decorator/uuid.decorator";

export type CrmCheckpointDocument = CrmCheckpoint & Document;

@Schema({ timestamps: true, id: false, collection: "CrmCheckpoint" })
export class CrmCheckpoint {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @UUIDProp()
    companyId: string;

    @Prop({ required: true })
    name: string;

    @Prop({ required: true })
    symbol: string;

    @Prop({ required: false })
    sequence: number;

    @UUIDProp()
    createdBy: string;

    @Prop({ default: true })
    editable: boolean;

    @Prop()
    stageDisplay?: string[];

    @Prop()
    stageEditable?: string[];

    @Prop()
    stageSet?: string[];

    @Prop({ enum: StageGroupEnum, default: StageGroupEnum.Sales })
    stageGroup: StageGroupEnum;

    @Prop({ default: false })
    isDisplay: boolean;

    @Prop()
    createdAt?: Date;

    @Prop()
    modifiedAt?: Date;

    @Prop({ default: false })
    deleted: boolean;

    @Prop()
    requiredStage?: string;
}

export const CrmCheckpointSchema = SchemaFactory.createForClass(CrmCheckpoint);
