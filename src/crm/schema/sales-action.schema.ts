import { Schema, SchemaFactory, Prop } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import { Document } from "mongoose";
import { ActivityTypeEnum } from "../enum/activity-type.enum";
import { UUIDProp } from "src/shared/decorator/uuid.decorator";

export type SalesActionDocument = SalesAction & Document;

@Schema({ timestamps: true, id: false, collection: "SalesAction" })
export class SalesAction {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @UUIDProp()
    companyId: string;

    @Prop({
        type: [
            {
                _id: { type: String, default: () => randomUUID() },
                name: { type: String, required: true },
                type: { type: String, enum: Object.values(ActivityTypeEnum), required: true },
            },
        ],
        _id: true,
        default: [],
        validate: [(val: any[]) => val.length <= 100, "Max 100 actions allowed"],
    })
    actions: {
        _id: string;
        name: string;
        type: ActivityTypeEnum;
    }[];

    @Prop({ default: false })
    deleted: boolean;

    @Prop()
    createdAt?: Date;

    @Prop()
    modifiedAt?: Date;

    @Prop()
    memberId: string;
}

export const SalesActionSchema = SchemaFactory.createForClass(SalesAction);

SalesActionSchema.index({ companyId: 1 });
SalesActionSchema.index({ memberId: 1 });
SalesActionSchema.index({ "actions.action": 1 });
