import { Schema, SchemaFactory, Prop } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import { Document } from "mongoose";
import { StageGroupEnum } from "../enum/stage-group.enum";
import { UUIDProp } from "src/shared/decorator/uuid.decorator";

export type CrmStageDocument = CrmStage & Document;

@Schema({ timestamps: true, id: false, collection: "CrmStage" })
export class CrmStage {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @UUIDProp()
    companyId: string;

    @Prop({ required: true })
    name: string;

    @Prop({ required: false })
    sequence: number;

    @Prop({ default: false })
    deleted: boolean;

    @UUIDProp()
    createdBy: string;

    @Prop({ required: true, enum: StageGroupEnum, default: StageGroupEnum.Sales })
    stageGroup: StageGroupEnum;

    @Prop({ default: true })
    editable: boolean;

    @Prop({ required: false, default: false })
    PMRequired: boolean;

    @Prop({ required: false, default: false })
    projectRequired: boolean;

    @Prop({ required: false, default: false })
    orderRequired: boolean;

    @Prop({ required: false })
    defaultCsrId: string;

    @Prop({ type: Object, required: false })
    sortingField: object;

    @Prop({ required: false })
    code: string;

    @Prop()
    createdAt?: Date;

    @Prop()
    modifiedAt?: Date;

    @Prop()
    description?: string;

    @Prop()
    aging?: boolean;

    @Prop()
    agingCheckpointId?: string;
}

export const CrmStageSchema = SchemaFactory.createForClass(CrmStage);
