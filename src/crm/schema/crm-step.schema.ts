import { Schema, SchemaFactory, Prop } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import { Document } from "mongoose";
import { ActivityTypeEnum } from "../enum/activity-type.enum";
import { FieldTypeEnum } from "../enum/field-type.enum";
import { UUIDProp } from "src/shared/decorator/uuid.decorator";

export type CrmStepDocument = CrmStep & Document;

@Schema({ timestamps: true, id: false, collection: "CrmStep" })
export class CrmStep {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @UUIDProp()
    companyId: string;

    @UUIDProp()
    stageId: string;

    @Prop({ required: true })
    name: string;

    @Prop({ required: false })
    description: string;

    @Prop({ required: false })
    lable?: string;

    @Prop({ required: true, type: String, enum: FieldTypeEnum })
    fieldType: FieldTypeEnum;

    @Prop({ required: false, type: Array })
    dropDownOptions?: any;

    @Prop({ required: true, default: true })
    checkbox: boolean;

    @Prop({ default: false })
    isDisplay: boolean;

    @Prop({ required: false })
    parent?: string;

    @Prop({ required: false })
    isParentRequired?: boolean;

    @Prop({ required: false, type: String, enum: ActivityTypeEnum, default: ActivityTypeEnum.Task })
    activityType?: ActivityTypeEnum;

    @Prop({ required: true })
    sequence: number;

    @Prop({ required: true, default: true })
    isRequire: boolean;

    @Prop({ required: false })
    projectTypeId: string[];

    @Prop({ required: false })
    location: string[];

    @Prop({ default: false })
    deleted: boolean;

    @UUIDProp()
    createdBy: string;

    @Prop()
    createdAt?: Date;

    @Prop()
    modifiedAt?: Date;

    @Prop()
    formType?: string;
}

export const CrmStepSchema = SchemaFactory.createForClass(CrmStep);
