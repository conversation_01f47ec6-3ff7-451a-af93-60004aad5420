import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsUUID } from "class-validator";

export class CreateDefaultCrmDto {
    // @ApiProperty({ description: "Company Id", required: true })
    // @IsUUID()
    // @IsNotEmpty()
    // companyId: string;

    @ApiProperty({ description: "Created By", required: true })
    @IsUUID()
    @IsNotEmpty()
    createdBy: string;

    @ApiProperty({ description: "project type", required: true })
    @IsNotEmpty()
    projectType: {
        replacementType: string;
        repairType: string;
    };
}
