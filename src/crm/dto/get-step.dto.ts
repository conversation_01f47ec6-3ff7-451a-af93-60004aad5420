import { ApiPropertyOptional } from "@nestjs/swagger";
import { IsOptional } from "class-validator";
import { PaginationDto } from "src/shared/dto/pagination.dto";

export class StepRequestDto extends PaginationDto {
    @ApiPropertyOptional({ description: "project Type", type: "any" })
    @IsOptional()
    projectTypeId?: any;

    @ApiPropertyOptional({ description: "location", type: "any" })
    @IsOptional()
    location?: any;
}
