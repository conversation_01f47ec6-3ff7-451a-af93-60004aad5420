import { PartialType, ApiPropertyOptional } from "@nestjs/swagger";
import { IsArray, IsBoolean, IsOptional, IsString } from "class-validator";
import { CreateStageDto } from "./create-stage.dto";

export class CreateCheckpointDto extends PartialType(CreateStageDto) {
    @ApiPropertyOptional({ description: "editable" })
    @IsBoolean()
    @IsOptional()
    editable?: boolean;

    @ApiPropertyOptional({ description: "changes to stage" })
    @IsArray()
    @IsOptional()
    stageEditable?: string[];

    @ApiPropertyOptional({ description: "visible to stages" })
    @IsArray()
    @IsOptional()
    stageDisplay?: string[];

    @ApiPropertyOptional({ description: "set to stages" })
    @IsArray()
    @IsOptional()
    stageSet?: string[];

    @ApiPropertyOptional({ description: "require stage id" })
    @IsOptional()
    @IsString()
    requiredStage: string;

    @ApiPropertyOptional({ description: "is display" })
    @IsOptional()
    @IsBoolean()
    isDisplay?: boolean;
}
