import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import {
    IsArray,
    IsBoolean,
    IsEnum,
    IsNotEmpty,
    IsNumber,
    IsOptional,
    IsString,
    IsUUID,
} from "class-validator";
import { ActivityTypeEnum } from "../enum/activity-type.enum";
import { FieldTypeEnum } from "../enum/field-type.enum";
import { Transform } from "class-transformer";

export class CreateStepDto {
    // @ApiProperty({ description: "Company Id", required: true })
    // @IsUUID()
    // @IsNotEmpty()
    // companyId: string;

    @ApiProperty({ description: "Company Id", required: true })
    @IsUUID()
    @IsNotEmpty()
    stageId: string;

    @ApiProperty({ description: "name", required: true })
    @IsString()
    @Transform(({ value }) => value.trim(), { toClassOnly: true })
    @IsNotEmpty()
    name: string;

    @ApiPropertyOptional({ description: "description", required: false })
    @IsOptional()
    @IsString()
    @Transform(({ value }) => value.trim(), { toClassOnly: true })
    description?: string;

    @ApiPropertyOptional({ description: "lable", required: true })
    @IsString()
    @Transform(({ value }) => value.trim(), { toClassOnly: true })
    @IsOptional()
    lable?: string;

    @ApiProperty({ description: "Sequence", required: true })
    @IsNumber()
    @IsNotEmpty()
    sequence: number;

    @ApiProperty({ description: "field type", required: true })
    @IsEnum(FieldTypeEnum)
    @IsNotEmpty()
    fieldType: FieldTypeEnum;

    @ApiPropertyOptional({ description: "field type" })
    @IsArray()
    @IsOptional()
    dropDownOptions?: any[];

    @ApiPropertyOptional({ description: "To show checkbox or not" })
    @IsBoolean()
    @IsOptional()
    checkbox?: boolean;

    @ApiPropertyOptional({ description: "is display" })
    @IsBoolean()
    @IsOptional()
    isDisplay?: boolean;

    @ApiPropertyOptional({ description: "parent" })
    @IsString()
    @IsOptional()
    parent?: string;

    @ApiPropertyOptional({ description: "is Parent Required" })
    @IsBoolean()
    @IsOptional()
    isParentRequired?: boolean;

    @ApiPropertyOptional({ description: "is Required" })
    @IsBoolean()
    @IsOptional()
    isRequire?: boolean;

    @ApiPropertyOptional({ description: "Activity type" })
    @IsEnum(ActivityTypeEnum)
    @IsOptional()
    activityType?: ActivityTypeEnum;

    @ApiProperty({ description: "Created By", required: true })
    @IsUUID()
    @IsNotEmpty()
    createdBy: string;

    @ApiPropertyOptional({ description: "project Type" })
    @IsArray()
    @IsOptional()
    projectTypeId?: string[];

    @ApiPropertyOptional({ description: "location" })
    @IsArray()
    @IsOptional()
    location?: string[];
}
