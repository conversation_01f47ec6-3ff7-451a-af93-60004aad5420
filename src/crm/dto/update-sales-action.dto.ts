import { ApiProperty } from "@nestjs/swagger";
import { IsEnum, IsNotEmpty, IsString, IsUUID } from "class-validator";
import { ActivityTypeEnum } from "../enum/activity-type.enum";

export class UpdateSalesActionDto {
    @ApiProperty({ description: "Action Id", required: true })
    @IsUUID()
    @IsNotEmpty()
    actionId: string;

    @ApiProperty({ description: "Action Id", required: true })
    @IsUUID()
    @IsNotEmpty()
    memberId: string;

    @ApiProperty({ description: "Updated Action Name", required: true })
    @IsString()
    @IsNotEmpty()
    name: string;

    @ApiProperty({ description: "Updated Action Type", required: true })
    @IsString()
    @IsEnum(ActivityTypeEnum)
    @IsNotEmpty()
    type: ActivityTypeEnum;
}
