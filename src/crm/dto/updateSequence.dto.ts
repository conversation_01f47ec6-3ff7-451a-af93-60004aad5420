import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsArray, IsInt, IsNotEmpty, IsPositive, IsUUID, ValidateNested } from "class-validator";

class SequenceItem {
    @ApiProperty({ description: "Unique identifier for the task", required: true })
    @IsUUID()
    @IsNotEmpty()
    _id: string;

    @ApiProperty({ description: "Sequence number", required: true, example: 1 })
    @IsInt({ message: "Sequence must be an integer" })
    @IsPositive({ message: "Sequence must be a positive number" })
    @IsNotEmpty()
    sequence: number;
}

export class UpdateSequenceDto {
    // @ApiProperty({ description: "Company Id", required: true })
    // @IsUUID()
    // @IsNotEmpty()
    // companyId: string;

    @ApiProperty({
        description: "Array of tasks with _id and sequence",
        type: [SequenceItem],
        required: true,
    })
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => SequenceItem)
    @IsNotEmpty({ message: "Data array should not be empty" })
    data: SequenceItem[];
}
