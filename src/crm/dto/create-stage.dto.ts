import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsBoolean, IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString, IsUUID } from "class-validator";
import { StageGroupEnum } from "../enum/stage-group.enum";

export class CreateStageDto {
    // @ApiProperty({ description: "Company Id", required: true })
    // @IsUUID()
    // @IsNotEmpty()
    // companyId: string;

    @ApiProperty({ description: "name", required: true })
    @IsString()
    @Transform(({ value }) => value.trim(), { toClassOnly: true })
    @IsNotEmpty()
    name: string;

    @ApiProperty({ description: "Sequence", required: true })
    @IsNumber()
    @IsNotEmpty()
    sequence: number;

    @ApiProperty({ description: "Created By", required: true })
    @IsUUID()
    @IsNotEmpty()
    createdBy: string;

    @ApiPropertyOptional({ enum: StageGroupEnum })
    @IsNotEmpty()
    @IsEnum(StageGroupEnum)
    stageGroup?: StageGroupEnum;

    @ApiPropertyOptional({ description: "Project Manager Required", required: false, default: false })
    @IsBoolean()
    @IsOptional()
    PMRequired?: boolean;

    @ApiPropertyOptional({ description: "Project Required", required: false, default: false })
    @IsBoolean()
    @IsOptional()
    projectRequired?: boolean;

    @ApiPropertyOptional({ description: "Order Required", required: false, default: false })
    @IsBoolean()
    @IsOptional()
    orderRequired?: boolean;

    @ApiPropertyOptional({ description: "Sorting field", required: false })
    @IsOptional()
    sortingField?: object;

    @ApiPropertyOptional({ description: "Default CSR person Id", required: true })
    @IsUUID()
    @IsOptional()
    defaultCsrId?: string;

    @ApiPropertyOptional({ description: "Description of CRM", required: false })
    @IsOptional()
    description?: string;

    @ApiPropertyOptional({ description: "Aging field", required: false })
    @IsOptional()
    aging?: boolean;

    @ApiPropertyOptional({ description: "agingCheckpointId", required: false })
    @IsOptional()
    agingCheckpointId?: string;
}
