import { ApiProperty, PartialType } from "@nestjs/swagger";
import { IsNotEmpty, IsUUID, IsOptional } from "class-validator";
import { CreateStepDto } from "./create-step.dto";

export class UpdateStepDto extends PartialType(CreateStepDto) {
    @ApiProperty({ description: "Step Id", required: true })
    @IsUUID()
    @IsNotEmpty()
    stepId: string;

    @ApiProperty({ description: "formType ", required: false })
    @IsOptional()
    @IsUUID()
    formType?: string | null;
}
