import { ValidationPipe, VersioningType } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { NestFactory } from "@nestjs/core";
import { DocumentBuilder, SwaggerModule } from "@nestjs/swagger";
import { AppModule } from "./app.module";
import helmet from "helmet";
import * as hpp from "hpp";
import * as csurf from "csurf";
import * as compression from "compression";
import * as cookieParser from "cookie-parser";
import rawBodyMiddleware from "./middlewares/rawbodymiddleware/rawBody.middleware";

async function bootstrap() {
    const app = await NestFactory.create(AppModule);
    const configService = app.get(ConfigService);
    app.setGlobalPrefix("api");
    app.enableVersioning({
        type: VersioningType.URI,
    });
    app.useGlobalPipes(new ValidationPipe({ transform: true }));

    const isProd = ["prod", "production"].includes(configService.get<string>("NODE_ENV")?.toLowerCase());
    const allowedOrigins = isProd
        ? [process.env.FR_BASE_URL, process.env.FR_BASE_URL2, process.env.FR_BASE_URL3]
        : ["*"];

    app.enableCors({
        origin: (origin, callback) => {
            if (!isProd || !origin || allowedOrigins.includes(origin)) {
                callback(null, true);
            } else {
                console.error(`Blocked by CORS: ${origin}`);
                callback(new Error("Not allowed by CORS"));
            }
        },
        methods: ["GET", "HEAD", "PUT", "PATCH", "POST", "DELETE", "OPTIONS"],
        allowedHeaders: ["Content-Type", "Authorization"],
        exposedHeaders: ["Content-Length", "X-Kuma-Revision"],
        credentials: true,
        preflightContinue: false,
        optionsSuccessStatus: 204,
    });

    app.use(
        helmet({
            hsts: {
                includeSubDomains: true,
                preload: true,
                maxAge: 63072000,
            },
            contentSecurityPolicy: {
                useDefaults: true,
                directives: {
                    defaultSrc: isProd
                        ? [
                              "'self'",
                              "https://polyfill.io", // Needed if you're using polyfills
                              "https://*.cloudflare.com", // Cloudflare resources, if any
                              ...allowedOrigins,
                          ]
                        : ["'self'", "*"], // More relaxed for development
                    scriptSrc: isProd
                        ? [
                              "'self'",
                              "https://polyfill.io", // If you're using polyfills
                              "https://*.cloudflare.com", // Cloudflare scripts
                              ...allowedOrigins,
                          ]
                        : ["'self'", "*"], // More relaxed for development
                    styleSrc: [
                        "'self'",
                        "https:",
                        "'unsafe-inline'", // Needed if inline styles are used
                    ],
                    imgSrc: ["'self'", "data:"],
                    fontSrc: ["'self'", "https:", "data:"],
                    childSrc: ["'self'"],
                    frameSrc: ["'self'"],
                },
            },
            xssFilter: true,
            noSniff: true,
            frameguard: { action: "sameorigin" },
            hidePoweredBy: true,
        }),
    );
    app.use(cookieParser());
    app.use(
        csurf({
            cookie: { secure: isProd },
            ignoreMethods: ["GET", "HEAD", "OPTIONS", "POST", "PUT", "DELETE", "PATCH"],
        }),
    );
    app.use(hpp());
    app.use(compression({ threshold: 100000 })); // set the threshold to 100 kb
    app.use(rawBodyMiddleware());

    // NOTE: Setup Swagger docs
    if (!["prod", "production"].includes(configService.get<string>("NODE_ENV").toLowerCase())) {
        const config = new DocumentBuilder()
            .addBearerAuth()
            .addOAuth2()
            .setTitle("New Heights Roofing")
            .setDescription("New Heights Roofing is a platform to track companies people and success")
            .setVersion("1.0")
            .build();
        const document = SwaggerModule.createDocument(app, config, {
            ignoreGlobalPrefix: false,
        });
        const options = {
            swaggerOptions: {
                tagsSorter: "alpha",
            },
        };
        SwaggerModule.setup("api", app, document, options);
    }
    const port = configService.get<string>("PORT");
    await app.listen(port);
}
bootstrap();
