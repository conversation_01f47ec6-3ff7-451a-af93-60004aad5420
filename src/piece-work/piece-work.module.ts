import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { PositionModule } from "src/position/position.module";
import { RoleModule } from "src/role/role.module";
import { PieceWorkController } from "./piece-work.controller";
import { PieceWorkService } from "./piece-work.service";
import { PieceWorkSchema } from "./schema/piece-work.schema";
import { PieceWorkSettingSchema } from "./schema/piece-work-setting.schema";
import { CompanySettingSchema } from "src/company/schema/company-setting.schema";
import { WorkTaskSchema } from "src/work-task/schema/work-task.schema";
import { CompanyPaySchema } from "src/company/schema/company-pay.schema";
import { CompensationSchema } from "src/compensation/schema/compensation.schema";

@Module({
    imports: [
        MongooseModule.forFeature([
            { name: "PieceWorkSetting", schema: PieceWorkSettingSchema },
            { name: "PieceWork", schema: PieceWorkSchema },
            { name: "CompanySetting", schema: CompanySettingSchema },
            { name: "Compensation", schema: CompensationSchema },
            { name: "WorkTask", schema: WorkTaskSchema },
            { name: "CompanyPay", schema: CompanyPaySchema },
        ]),
        // PositionModule,
        // RoleModule,
    ],
    providers: [PieceWorkService],
    controllers: [PieceWorkController],
    exports: [PieceWorkService],
})
export class PieceWorkModule {}
