import { Schema, SchemaFactory, Prop } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import { Document } from "mongoose";
import { UUIDProp } from "src/shared/decorator/uuid.decorator";

export type PieceWorkSettingDocument = PieceWorkSetting & Document;

export class Layer {
    @Prop({
        required: false,
        type: {
            value: { type: Number, required: false, default: null },
            isActive: { type: Boolean, required: false, default: false },
        },
    })
    fixed: { value: number | null; isActive: boolean };

    @Prop({
        required: false,
        type: {
            value: { type: Number, required: false, default: null },
            isActive: { type: Boolean, required: false, default: false },
        },
    })
    percent: { value: number | null; isActive: boolean };
}

export class Pitch {
    @Prop({ required: true })
    pitchOrder: number;

    @Prop({ required: true })
    amount: number;
}

@Schema({ timestamps: true, id: false, collection: "PieceWorkSetting", versionKey: false })
export class PieceWorkSetting {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @UUIDProp()
    companyId: string;

    @Prop({ required: true })
    name: string;

    @Prop({ required: false })
    description?: string;

    @Prop({ required: true, ref: "workTask" })
    workTask: string;

    @Prop({ required: true })
    version: string;

    @Prop({ required: true })
    usesPitch: boolean;

    @Prop({ type: () => [Pitch], required: false })
    pitch: Pitch[];

    @Prop({ required: true, default: false })
    hasLayer: boolean;

    @Prop({ type: Layer, required: false, default: {} })
    layer: Layer;

    @Prop({ required: true })
    isExtra: boolean;

    @Prop({ required: true })
    amount: number;

    @Prop({ required: true, type: String })
    unit: string;

    @Prop({ required: true })
    unitId: string;

    @UUIDProp()
    createdBy: string;

    @Prop({ default: false })
    deleted: boolean;

    @Prop({ required: false })
    sequence: number;

    @Prop()
    createdAt?: Date;

    @Prop()
    modifiedAt?: Date;
}

export const PieceWorkSettingSchema = SchemaFactory.createForClass(PieceWorkSetting);
