import {
    Body,
    Controller,
    Delete,
    Get,
    Param,
    ParseUUIDPipe,
    Patch,
    Post,
    Put,
    Query,
    UseGuards,
} from "@nestjs/common";
import {
    ApiBearerAuth,
    ApiConflictResponse,
    ApiInternalServerErrorResponse,
    ApiOperation,
    ApiTags,
    ApiUnauthorizedResponse,
} from "@nestjs/swagger";
import { Positions, Roles } from "src/auth/guards/auth.guard";
import HttpResponse from "src/shared/http/response/response.http";
import { CreatePieceWorkDto } from "./dto/create-piece-work.dto";
import {
    CreatePieceWorkSettingDto,
    FetchPieceWorkSettingByTaskIdDto,
    FetchPieceWorkSettingDto,
    FetchWorkTaskPieceWorkSettingsDto,
    NewCreatePieceWorkSettingDto,
    NewUpdatePieceWorkSettingDto,
    UpdatePieceWorkSettingAmountsDto,
} from "./dto/create-piece-work-setting.dto";
import { PieceWorkService } from "./piece-work.service";
import { UserRolesEnum } from "src/company/enum/role.enum";
import { UserAuthGuard } from "src/auth/guards/jwt-auth.guard";
import { AddPositionsDTO, CreateVersionDTO, UpdateVersionDTO } from "./dto/version.dto";
import { UpdateSequenceDto } from "src/crm/dto/updateSequence.dto";
import { Auth, GetUser } from "src/auth/decorator/auth.decorator";
import { JwtUserPayload } from "src/auth/interface/auth.interface";
import { PermissionsEnum } from "src/shared/enum/permission.enum";

@ApiBearerAuth()
@ApiTags("PieceWork")
@Auth()
@Controller({ path: "piece-work", version: "1" })
export class PieceWorkController {
    constructor(private readonly pieceWorkService: PieceWorkService) {}

    /**
     * Creates a new piece work setting or updates existing one
     * @param userId The ID of the user creating the piece work
     * @param createPieceWorkSettingDto The DTO containing information about the piece work setting to create.
     * @returns An HTTP response with the result of the operation.
     */
    @ApiOperation({ summary: "Create PieceWorkSetting" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Post("upsert-piece-work-setting")
    async upsertPieceWorkSetting(
        @GetUser() user: JwtUserPayload,
        @Body() createPieceWorkSettingDto: CreatePieceWorkSettingDto[],
    ): Promise<HttpResponse> {
        return this.pieceWorkService.upsertPieceWorkSetting(user.companyId, createPieceWorkSettingDto);
    }

    /**
     * Get company piecework setting
     * @param userId - ID of the user making the request
     * @param companyId - ID of the company whose piece works setting are being retrieved
     * @returns HttpResponse containing the piece work settings for the specified company
     */
    @ApiOperation({ summary: "Get PieceWorkSetting" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Get("get-piece-work-setting")
    async getPieceWorkSetting(@GetUser() user: JwtUserPayload): Promise<HttpResponse> {
        return this.pieceWorkService.getPieceWorkSetting(user._id, user.companyId);
    }

    /**
     * Creates a new piece work
     * @param userId The ID of the user creating the piece work
     * @param createPieceWorkDto The DTO containing information about the piece work to create.
     * @returns An HTTP response with the result of the operation.
     */
    @ApiOperation({ summary: "Create PieceWork" })
    @ApiConflictResponse({ description: "PieceWork already exist" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    // @UseGuards(UserAuthGuard)
    @Post("create-piece-work")
    async createPieceWork(
        @GetUser() user: JwtUserPayload,
        @Body() createPieceWorkDto: CreatePieceWorkDto,
    ): Promise<HttpResponse> {
        return this.pieceWorkService.upsertPieceWork(user.companyId, user.memberId, createPieceWorkDto);
    }

    @ApiOperation({ summary: "Create PieceWorkSetting" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Post("create-piece-work-setting")
    async createPieceWorkSetting(
        @GetUser() user: JwtUserPayload,
        @Body() createPieceWorkSettingDto: NewCreatePieceWorkSettingDto,
    ): Promise<HttpResponse> {
        return this.pieceWorkService.createPieceWorkSetting(user.companyId, createPieceWorkSettingDto);
    }

    @ApiOperation({ summary: "Update PieceWorkSetting" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Put("update-piece-work-setting/:id")
    async updatePieceWorkSetting(
        @GetUser() user: JwtUserPayload,
        @Param("id", ParseUUIDPipe) id: string,
        @Body() createPieceWorkSettingDto: NewUpdatePieceWorkSettingDto,
    ): Promise<HttpResponse> {
        return this.pieceWorkService.updatePieceWorkSetting(id, user.companyId, createPieceWorkSettingDto);
    }

    @ApiOperation({ summary: "Update PieceWorkSetting Amounts" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Patch("update-piece-work-setting-amounts")
    async updatePieceWorkSettingAmounts(
        @GetUser() user: JwtUserPayload,
        @Body() updateAmountDto: UpdatePieceWorkSettingAmountsDto,
    ): Promise<HttpResponse> {
        return this.pieceWorkService.updatePieceWorkSettingAmounts(user.companyId, updateAmountDto);
    }

    @ApiOperation({ summary: "fetch PieceWorkSetting" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Get("fetch-piece-work-settings")
    async fetchPieceWorkSetting(
        @GetUser() user: JwtUserPayload,
        @Query() fetchPieceWorkSettingDto: FetchPieceWorkSettingDto,
    ): Promise<HttpResponse> {
        return this.pieceWorkService.fetchPieceWorkSetting(user.companyId, fetchPieceWorkSettingDto);
    }

    @ApiOperation({ summary: "fetch PieceWorkSetting by id" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner, UserRolesEnum.Member)
    @Get("fetch-piece-work-setting-by-id/:id")
    async fetchPieceWorkSettingById(
        @GetUser() user: JwtUserPayload,
        @Param("id", ParseUUIDPipe) id: string,
    ): Promise<HttpResponse> {
        return this.pieceWorkService.fetchPieceWorkSettingById(id, user.companyId);
    }

    @ApiOperation({ summary: "fetch PieceWorkSetting by task id" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner, UserRolesEnum.Member)
    @Get("fetch-piece-work-setting-by-task-id")
    async fetchPieceWorkSettingByTaskId(
        @GetUser() user: JwtUserPayload,
        @Query() { taskId, memberId, date }: FetchPieceWorkSettingByTaskIdDto,
    ): Promise<HttpResponse> {
        return this.pieceWorkService.fetchPieceWorkSettingByTaskId(memberId, date, taskId);
    }

    @ApiOperation({ summary: "fetch PieceWorkSetting grouped by workTask" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner, UserRolesEnum.Member)
    @Get("fetch-worktask-piece-work-settings")
    async fetchWorktaskPieceWorkSettingByTaskId(
        @GetUser() user: JwtUserPayload,
        @Query() { memberId, date }: FetchWorkTaskPieceWorkSettingsDto,
    ): Promise<HttpResponse> {
        return this.pieceWorkService.fetchWorkTaskPieceWorkSettings(memberId, date);
    }

    @ApiOperation({ summary: "to create version" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Post("add-version")
    async addVersion(
        @GetUser() user: JwtUserPayload,
        @Body() createVersionDto: CreateVersionDTO,
    ): Promise<HttpResponse> {
        return this.pieceWorkService.addVersion(user.companyId, createVersionDto);
    }

    @ApiOperation({ summary: "to create piecework positions" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Post("add-piecework-positions")
    async addPieceworkPositions(
        @GetUser() user: JwtUserPayload,
        @Body() createVersionDto: AddPositionsDTO,
    ): Promise<HttpResponse> {
        return this.pieceWorkService.addPieceworkPositions(user.companyId, createVersionDto);
    }

    @ApiOperation({ summary: "to update version" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Put("update-version/:versionId")
    async updateVersion(
        @GetUser() user: JwtUserPayload,
        @Param("versionId", ParseUUIDPipe) versionId: string,
        @Body() updateVersion: UpdateVersionDTO,
    ): Promise<HttpResponse> {
        return this.pieceWorkService.updateVersion(user.companyId, versionId, updateVersion);
    }

    @ApiOperation({ summary: "to fetch version and positon" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    // @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    // @Positions({
    //     name: "settings",
    //     actions: [PermissionsEnum.Full],
    // })
    @Get("fetch-company-pay-data")
    async fetchCompanyPayData(@GetUser() user: JwtUserPayload): Promise<HttpResponse> {
        return this.pieceWorkService.fetchCompanyPayData(user.companyId);
    }

    @ApiOperation({ summary: "delete Version" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Delete("delete-version/:versionId")
    async deleteVersion(
        @GetUser() user: JwtUserPayload,
        @Param("versionId", ParseUUIDPipe) versionId: string,
    ): Promise<HttpResponse> {
        return this.pieceWorkService.deleteVersion(user.companyId, versionId);
    }

    @ApiOperation({ summary: "add positions" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Post("add-sales-positions")
    async addSalesPositions(
        @GetUser() user: JwtUserPayload,
        @Body() { positions }: AddPositionsDTO,
    ): Promise<HttpResponse> {
        return this.pieceWorkService.addSalesPositions(user.companyId, positions);
    }

    @ApiOperation({ summary: "Update pieceworkSetting sequence" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Patch("update-pw-setting-sequence")
    async updateStageSequence(
        @GetUser() user: JwtUserPayload,
        @Body() updateStageSequenceDto: UpdateSequenceDto,
    ): Promise<HttpResponse> {
        return this.pieceWorkService.updatePWSettingSequence(user.companyId, updateStageSequenceDto);
    }

    @ApiOperation({ summary: "fetch PieceWorkSetting" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Get("fetch-inactive-piece-work-settings")
    async fetchInactivePieceWorkSetting(@GetUser() user: JwtUserPayload): Promise<HttpResponse> {
        return this.pieceWorkService.fetchInactivePieceWorkSetting(user.companyId);
    }

    @ApiOperation({ summary: "fetch PieceWorkSetting" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Delete("piece-work-settings/:id")
    async deletePieceWorkSetting(
        @GetUser() user: JwtUserPayload,
        @Param("id", ParseUUIDPipe) id: string,
    ): Promise<HttpResponse> {
        return this.pieceWorkService.deletePieceWorkSetting(user.companyId, id);
    }

    @ApiOperation({ summary: "restore PieceWorkSetting" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Patch("restore-piece-work-setting/:id")
    async restorePieceWorkSetting(
        @GetUser() user: JwtUserPayload,
        @Param("id", ParseUUIDPipe) id: string,
    ): Promise<HttpResponse> {
        return this.pieceWorkService.restorePieceWorkSetting(user.companyId, id);
    }
}
