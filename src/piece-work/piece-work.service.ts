import {
    BadRequestException,
    ConflictException,
    HttpException,
    Injectable,
    InternalServerErrorException,
    NotFoundException,
} from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import { Model } from "mongoose";
import { buildInPieceWorksSetting } from "src/shared/constants";
import CreatedResponse from "src/shared/http/response/created.http";
import OkResponse from "src/shared/http/response/ok.http";
import { CreatePieceWorkDto } from "./dto/create-piece-work.dto";
import {
    CreatePieceWorkSettingDto,
    FetchPieceWorkSettingDto,
    NewCreatePieceWorkSettingDto,
    NewUpdatePieceWorkSettingDto,
    UpdatePieceWorkSettingAmountsDto,
} from "./dto/create-piece-work-setting.dto";
import { PieceWorkDocument } from "./schema/piece-work.schema";
import { PieceWorkSettingDocument } from "./schema/piece-work-setting.schema";
import { roundTo2, isWeekend, findCurrentWage } from "src/shared/helpers/logics";
import { CompanySettingDocument } from "src/company/schema/company-setting.schema";
import { WageIntervalEnum } from "src/compensation/enum/wage-interval.enum";
import { CompensationDocument } from "src/compensation/schema/compensation.schema";
import { WorkTaskDocument } from "src/work-task/schema/work-task.schema";
import { CompanyPayDocument } from "src/company/schema/company-pay.schema";
import { AddPositionsDTO, CreateVersionDTO, UpdateVersionDTO } from "./dto/version.dto";
import { UpdateSequenceDto } from "src/crm/dto/updateSequence.dto";

@Injectable()
export class PieceWorkService {
    constructor(
        @InjectModel("PieceWork") private readonly pieceWorkModel: Model<PieceWorkDocument>,
        @InjectModel("PieceWorkSetting")
        private readonly pieceWorkSettingModel: Model<PieceWorkSettingDocument>,
        @InjectModel("CompanySetting") private readonly companySettingModel: Model<CompanySettingDocument>,
        @InjectModel("Compensation") private readonly compensationModel: Model<CompensationDocument>,
        @InjectModel("WorkTask") private readonly workTaskModel: Model<WorkTaskDocument>,
        @InjectModel("CompanyPay") private readonly companyPayModel: Model<CompanyPayDocument>,
    ) {}

    async upsertPieceWorkSetting(companyId: string, createPieceWorkSettingDto: CreatePieceWorkSettingDto[]) {
        try {
            for (let i = 0; i < createPieceWorkSettingDto.length; i++) {
                const _id = createPieceWorkSettingDto[i]._id
                    ? createPieceWorkSettingDto[i]._id
                    : randomUUID();
                await this.pieceWorkSettingModel.updateOne(
                    {
                        _id,
                        companyId,
                        name: createPieceWorkSettingDto[i].name,
                        type: createPieceWorkSettingDto[i].type,
                        subType: createPieceWorkSettingDto[i].subType,
                        order: createPieceWorkSettingDto[i].order,
                        deleted: false,
                    },
                    {
                        $set: { companyId, ...createPieceWorkSettingDto[i] },
                    },
                    { upsert: true },
                );
            }
            return new CreatedResponse({ message: "Piece Work Setting upserted successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getPieceWorkSetting(userId: string, companyId: string) {
        try {
            const pieceWorkSetting = await this.pieceWorkSettingModel.aggregate([
                {
                    $match: {
                        companyId,
                        deleted: false,
                    },
                },
                {
                    $group: {
                        _id: { type: "$type", subType: "$subType", workerType: "$workerType" },
                        data: {
                            $push: {
                                _id: "$_id",
                                name: "$name",
                                amount: "$amount",
                                unit: "$unit",
                                type: "$type",
                                subType: "$subType",
                                order: "$order",
                                description: "$description",
                                createdBy: "$createdBy",
                            },
                        },
                    },
                },
                { $sort: { "_id.subType": 1, "_id.order": 1 } },
            ]);
            return new OkResponse({ pieceWorkSetting });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async addDefaultPieceWorksSetting(
        companyId: string,
        memberId: string,
        versionIdUUID,
        unitUUID,
        workTaskUUID,
    ) {
        try {
            const data = buildInPieceWorksSetting(workTaskUUID, versionIdUUID, unitUUID).map((value) => ({
                ...value,
                companyId,
                createdBy: memberId,
            }));
            await this.pieceWorkSettingModel.insertMany(data);
            return new CreatedResponse({ message: "Piece Work Setting upserted successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async upsertPieceWork(companyId: string, logInmemberId: string, createPieceWorkDto: CreatePieceWorkDto) {
        try {
            if (!createPieceWorkDto.taskName) {
                const workTask = await this.workTaskModel.findOne({ _id: createPieceWorkDto.task });
                createPieceWorkDto["taskName"] = workTask.name;
            }

            //For extraTime calculation for Piece Work
            const extraTime = createPieceWorkDto?.work?.extraTime;
            if (extraTime) {
                const hrs = extraTime?.extraHrs ? Number(extraTime.extraHrs) : 0;
                const min = extraTime?.extraMin ? Number(extraTime.extraMin) : 0;
                createPieceWorkDto.work["extraWorkTime"] = roundTo2(hrs + min / 60);
            }

            // Find wage type and amount
            const userCompensation = await this.compensationModel.findOne({
                memberId: createPieceWorkDto.memberId,
            });

            const wage = findCurrentWage(userCompensation?.wageHistory, createPieceWorkDto.date);

            const { work, earned, sqsEarnings, extrasEarnings, hourlyEarnings, hourlyWages } =
                await this.calculatePieceWorkEarned(
                    companyId,
                    wage,
                    createPieceWorkDto.task,
                    createPieceWorkDto.work,
                    createPieceWorkDto.timeIn,
                    createPieceWorkDto.timeOut,
                    createPieceWorkDto.allHourly,
                    createPieceWorkDto.foreman,
                );

            // deleteing work and saving work from above function
            delete createPieceWorkDto.work;

            await this.pieceWorkModel.findOneAndUpdate(
                { timeCardId: createPieceWorkDto.timeCardId, deleted: false },
                {
                    $set: {
                        companyId,
                        ...createPieceWorkDto,
                        earned,
                        sqsEarnings,
                        extrasEarnings,
                        hourlyEarnings,
                        hourlyWages,
                        work,
                    },
                    $push: {
                        auditLog: {
                            editedBy: logInmemberId,
                            editedAt: new Date(),
                            work,
                        },
                    },
                },
                { upsert: true },
            );

            return new CreatedResponse({ message: "Piece work created successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async calculatePieceWorkEarned(
        companyId: string,
        wage,
        task: string,
        workData,
        timeInDate: Date,
        timeOutDate: Date,
        allHourlyInput: boolean,
        foreman: boolean,
    ) {
        try {
            const timeIn = new Date(timeInDate);
            const timeOut = new Date(timeOutDate);
            const msec = timeOut.getTime() - timeIn.getTime();
            const hrs = Math.round((msec / (1000 * 60 * 60)) * 100) / 100;

            const workTask = await this.workTaskModel.findOne({ _id: task });

            // const allHourly = task === TaskTypeEnum.Meetings ? false : allHourlyInput;

            const coVar = await this.companySettingModel.findOne({ companyId, deleted: false });

            const hourlyWage = wage?.wageInterval === WageIntervalEnum.Hour ? wage?.wageAmount : 0;
            const extraHrWage = wage?.pieceWorkHourlyRate || 0;
            const salaried = wage?.wageInterval === WageIntervalEnum.Year ? true : false;
            const weekend = isWeekend(coVar.weekEndDays, timeIn);
            const weekendMod = coVar.weekendBonus;
            /*
                for foreman check its own piecework %
                as pwTotal already added we will remove extra ownPieceWork amount ex. 30% ownPieceWork
                hen subtract 70% of already added pwTotal
            */
            const ownPieceWork = foreman && wage?.ownPieceWork ? wage.ownPieceWork : 1;

            // Add piece work record
            let work: any = {
                workDone: [],
                extraWorkTime: 0,
                extraTime: { extraHrs: 0, extraMin: 0 },
            };
            let earned = 0;
            let sqsEarnings = 0;
            let extrasEarnings = 0;
            // For extra hours worked
            let hourlyEarnings = 0;
            // For hourly + piece work people
            const hourlyWages = hrs * hourlyWage;
            if (allHourlyInput) {
                const workDone = [];
                // const extra: any = {};
                // extra.extraTime = hrs;
                hourlyEarnings += roundTo2(hrs * extraHrWage);
                earned += hourlyEarnings;
                const hourPart = Math.floor(hrs);
                const minutePart = Math.round((hrs - hourPart) * 60);

                const extraTime = {
                    extraHrs: hourPart,
                    extraMin: minutePart,
                };

                work = {
                    workDone,
                    extraTime,
                    extraWorkTime: hrs,
                };
            } else if (workTask.pieceWork && workData) {
                work = {
                    workDone: [],
                    extraTime: workData.extraTime,
                    extraWorkTime: workData?.extraWorkTime,
                };
                const pieceWorkSettingAllData = await this.pieceWorkSettingModel.find({
                    companyId,
                    _id: {
                        $in: workData?.workDone.map((wd) => wd.id),
                    },
                });
                for (const pieceWork of workData?.workDone) {
                    const { id, amount, pitch, layers } = pieceWork;

                    const pieceWorkSettingData = pieceWorkSettingAllData.find((pwData) => pwData._id === id);

                    if (pieceWorkSettingData && amount) {
                        const matchingPitch =
                            pitch !== null && pitch !== undefined && pitch !== ""
                                ? pieceWorkSettingData.pitch.find(
                                      (obj) => obj.pitchOrder === Math.ceil(pitch),
                                  )
                                : null;
                        const pitchMultiplier = matchingPitch
                            ? matchingPitch.amount
                            : pieceWorkSettingData.amount;

                        let earningsToAdd = pitchMultiplier * amount;
                        if (layers && pieceWorkSettingData.hasLayer) {
                            if (pieceWorkSettingData.layer.fixed.isActive) {
                                earningsToAdd += pieceWorkSettingData.layer.fixed.value * (layers - 1);
                            } else {
                                earningsToAdd +=
                                    (amount *
                                        (layers - 1) *
                                        (pitchMultiplier * pieceWorkSettingData.layer.percent.value)) /
                                    100;
                            }
                        }
                        // for foreman its his sef bonus & for rest its 100%
                        earningsToAdd = earningsToAdd * ownPieceWork;

                        if (pieceWorkSettingData?.isExtra) {
                            extrasEarnings += roundTo2(earningsToAdd);
                        } else {
                            sqsEarnings += roundTo2(earningsToAdd);
                        }

                        // adding data in work with earnings for each piece work
                        work.workDone.push({ ...pieceWork, earned: earningsToAdd });
                    }
                }

                hourlyEarnings += roundTo2(workData?.extraWorkTime * extraHrWage);
                earned += roundTo2(sqsEarnings + extrasEarnings + hourlyEarnings);
            }

            if (salaried && weekend) {
                sqsEarnings = sqsEarnings * weekendMod;
                extrasEarnings = extrasEarnings * weekendMod;
                hourlyEarnings = hourlyEarnings * weekendMod;
                earned = earned * weekendMod;
            }

            return { work, earned, sqsEarnings, extrasEarnings, hourlyEarnings, hourlyWages };
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async createPieceWorkSetting(companyId: string, createPieceWorkSettingDto: NewCreatePieceWorkSettingDto) {
        try {
            const {
                name,
                amount,
                unit,
                unitId,
                workTask,
                description,
                isExtra,
                hasLayer,
                layer,
                usesPitch,
                createdBy,
                deleted,
                version,
            } = createPieceWorkSettingDto;

            const workTaskDetails = await this.workTaskModel.findOne({ _id: workTask });
            if (!workTaskDetails) {
                throw new NotFoundException(`Work Task with ID ${workTask} not found`);
            }

            const _id = randomUUID();
            const pitch = usesPitch
                ? Array.from({ length: 21 }, (_, pitchOrder) => ({ pitchOrder, amount }))
                : undefined;

            const allPieceWorkSettinData = await this.pieceWorkSettingModel
                .find({
                    companyId,
                    workTask,
                    usesPitch,
                    version,
                })
                .select("sequence");
            const lastSequence = Math.max(...allPieceWorkSettinData.map((pwSetting) => pwSetting?.sequence));

            const pieceWorkSettingData = {
                companyId,
                name,
                workTask,
                description,
                usesPitch,
                ...(usesPitch && { pitch }),
                amount,
                isExtra,
                hasLayer,
                layer,
                unit,
                unitId,
                createdBy,
                deleted,
                version,
                sequence: lastSequence + 1,
                _id,
            };

            await this.pieceWorkSettingModel.create(pieceWorkSettingData);

            return new CreatedResponse({ message: "Piece Work Setting created successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updatePieceWorkSetting(
        id: string,
        companyId: string,
        updatePieceWorkSettingDto: NewUpdatePieceWorkSettingDto,
    ) {
        try {
            const allPWSettings = await this.pieceWorkSettingModel.find({ version: { $exists: true } });

            const existingSetting = allPWSettings.find((pwSetting) => pwSetting._id === id);

            if (!existingSetting) {
                throw new NotFoundException(`Piece Work Setting with ID ${id} not found`);
            }

            const { workTask, usesPitch, pitch, amount, version } = updatePieceWorkSettingDto;
            if (!workTask) {
                const workTaskDetails = await this.workTaskModel.findOne({ _id: workTask });
                if (!workTaskDetails) throw new NotFoundException(`Work Task with ID ${workTask} not found`);
            }

            if (!existingSetting.usesPitch && usesPitch && (!pitch || pitch?.length === 0)) {
                updatePieceWorkSettingDto["pitch"] = Array.from({ length: 21 }, (_, pitchOrder) => ({
                    pitchOrder,
                    amount: amount || existingSetting?.amount,
                }));
            }

            if (usesPitch !== existingSetting?.usesPitch) {
                const filterCriteria1 = { usesPitch, workTask, version };
                const filterCriteria2 = {
                    usesPitch: existingSetting?.usesPitch,
                    workTask: existingSetting?.workTask,
                    version: existingSetting?.version,
                };

                const allPieceWorkSettinData1 = allPWSettings
                    .filter(
                        ({ usesPitch, workTask, version }) =>
                            usesPitch === filterCriteria1.usesPitch &&
                            workTask === filterCriteria1.workTask &&
                            version === filterCriteria1.version,
                    )
                    .map(({ _id, sequence }) => ({ _id, sequence }));

                const lastSequence = Math.max(...allPieceWorkSettinData1.map(({ sequence }) => sequence)) + 1;

                const allPieceWorkSettinData2 = allPWSettings
                    .filter(
                        ({ usesPitch, workTask, version }) =>
                            usesPitch === filterCriteria2.usesPitch &&
                            workTask === filterCriteria2.workTask &&
                            version === filterCriteria2.version,
                    )
                    .map(({ _id, sequence }) => ({ _id, sequence }));
                const filteredPieceWorkSettinData2 = allPieceWorkSettinData2.filter(
                    ({ sequence }) => sequence > existingSetting?.sequence,
                );

                if (lastSequence) {
                    updatePieceWorkSettingDto["sequence"] = lastSequence;
                }

                const data = filteredPieceWorkSettinData2.map(({ _id, sequence }) => ({
                    _id,
                    sequence: sequence - 1,
                }));
                const updatePwSequenceData = { companyId, data };

                if (data.length) await this.updatePWSettingSequence(companyId, updatePwSequenceData);
            }

            await this.pieceWorkSettingModel.findOneAndUpdate({ _id: id }, updatePieceWorkSettingDto);

            return new CreatedResponse({ message: "Piece Work Setting updated successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updatePieceWorkSettingAmounts(
        companyId: string,
        updatePieceWorkSettingDto: UpdatePieceWorkSettingAmountsDto,
    ) {
        const { updateInputAmounts } = updatePieceWorkSettingDto;

        if (!updateInputAmounts || updateInputAmounts.length === 0) {
            return new CreatedResponse({ message: "No Piece Work Settings to update." });
        }

        try {
            const bulkPWSettings = updateInputAmounts.map((pwSettingInput) => ({
                updateOne: {
                    filter: {
                        companyId,
                        _id: pwSettingInput.id,
                    },
                    update: {
                        $set: {
                            amount: pwSettingInput?.amount,
                            pitch: pwSettingInput?.pitch,
                        },
                    },
                },
            }));

            await this.pieceWorkSettingModel.bulkWrite(bulkPWSettings);

            return new CreatedResponse({ message: "Piece Work Setting amounts updated successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException("An error occurred while updating piece work settings.");
        }
    }

    async fetchPieceWorkSetting(companyId: string, { version }: FetchPieceWorkSettingDto) {
        try {
            const pieceWorkSettings = await this.pieceWorkSettingModel
                .aggregate([
                    {
                        $match: { companyId, version, deleted: false },
                    },
                    {
                        $group: {
                            _id: "$workTask",
                            pieceWorkSettings: { $push: "$$ROOT" },
                        },
                    },
                    {
                        $unwind: "$pieceWorkSettings",
                    },
                    {
                        $sort: {
                            "pieceWorkSettings.usesPitch": -1,
                            "pieceWorkSettings.isExtra": 1,
                            "pieceWorkSettings.sequence": 1,
                        },
                    },
                    {
                        $group: {
                            _id: "$_id",
                            pieceWorkSettings: { $push: "$pieceWorkSettings" },
                        },
                    },
                ])
                .exec();

            return new OkResponse({ pieceWorkSettings });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async fetchPieceWorkSettingById(id: string, companyId: string) {
        try {
            const pieceWorkSetting = await this.pieceWorkSettingModel.findOne({ _id: id, companyId });

            return new OkResponse({ pieceWorkSetting });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async fetchPieceWorkSettingByTaskId(memberId: string, date, workTask: string) {
        try {
            // Find wage type and amount
            const userCompensation = await this.compensationModel.findOne({ memberId });

            const wage = findCurrentWage(userCompensation?.wageHistory, date);
            // const hourlyWage = wage?.wageInterval === WageIntervalEnum.Hour ? wage.wageAmount : 0;
            // const salaried = wage?.wageInterval === WageIntervalEnum.Year;

            // // If hybrid model - piece work + hourly/salary
            // const hybrid = salaried ? true : hourlyWage ? true : false;
            const version = wage.versionId;
            //  hybrid ? VersionEnum.Salaried : VersionEnum.Default;

            const filter = {
                version,
                deleted: false,
            };
            if (workTask) {
                filter["workTask"] = workTask;
            }

            const pieceWorkSettings = await this.pieceWorkSettingModel
                .find(filter)
                .sort({ usesPitch: -1, isExtra: 1, sequence: 1 });

            return new OkResponse({ pieceWorkSettings });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async fetchWorkTaskPieceWorkSettings(memberId: string, date) {
        try {
            // Find wage type and amount
            const userCompensation = await this.compensationModel.findOne({ memberId });

            const wage = findCurrentWage(userCompensation?.wageHistory, date);

            // const hourlyWage = wage?.wageInterval === WageIntervalEnum.Hour ? wage.wageAmount : 0;
            // const salaried = wage?.wageInterval === WageIntervalEnum.Year;

            // // If hybrid model - piece work + hourly/salary
            // const hybrid = salaried ? true : hourlyWage ? true : false;
            const version = wage.versionId; // hybrid ? VersionEnum.Salaried : VersionEnum.Default;

            const aggregatedData = await this.pieceWorkSettingModel.aggregate([
                {
                    $match: {
                        version,
                        deleted: false,
                    },
                },
                {
                    $sort: {
                        usesPitch: -1,
                        isExtra: 1,
                        sequence: 1,
                    },
                },
                {
                    $group: {
                        _id: "$workTask",
                        settings: { $push: "$$ROOT" }, // Push the entire document into an array
                    },
                },
            ]);

            return new OkResponse({ pieceWorkSettings: aggregatedData });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async addVersion(companyId: string, { name, versionId }: CreateVersionDTO) {
        try {
            const companyPayData = await this.companyPayModel.findOne({ companyId });
            const newVersionId = randomUUID();
            const { _id, pieceWork } = companyPayData;
            const { versions } = pieceWork;

            versions.forEach((version) => {
                if (version.name === name)
                    throw new ConflictException(`version with name ${name} already exist`);
            });
            if (versionId) {
                let versionExistsFlag = false;

                companyPayData.pieceWork.versions.forEach((version) => {
                    if (version._id === versionId) {
                        versionExistsFlag = true;
                        if (name) {
                            version.name = name;
                        }
                    }
                });

                if (!versionExistsFlag) {
                    throw new NotFoundException(`Version does not exist`);
                }

                await this.createNewVersionPieceWorkSetting(versionId, newVersionId);
            }

            await this.companyPayModel.updateOne(
                { _id },
                {
                    $push: {
                        "pieceWork.versions": {
                            _id: newVersionId,
                            name,
                        },
                    },
                },
            );

            return new CreatedResponse({ message: "Version added successfully" });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async addPieceworkPositions(companyId: string, { positions }: AddPositionsDTO) {
        try {
            await this.companyPayModel.updateOne(
                { companyId },
                {
                    $set: {
                        "pieceWork.positions": positions,
                    },
                },
            );

            return new CreatedResponse({ message: "Piecework Position added successfully" });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async createNewVersionPieceWorkSetting(versionId: string, newVersionId: string) {
        const pieceWorkSettings = await this.pieceWorkSettingModel.find({
            version: versionId,
        });

        // Transform existing documents and add new _id
        const newPieceWorkSettings = pieceWorkSettings.map((setting) => {
            const { createdAt, ...rest } = setting;
            return {
                ...rest,
                _id: randomUUID(),
                version: newVersionId,
            };
        });

        // Insert new documents
        await this.pieceWorkSettingModel.insertMany(newPieceWorkSettings);
    }

    async updateVersion(companyId: string, versionId: string, { name }: UpdateVersionDTO) {
        try {
            const companyPayData = await this.companyPayModel.findOne({ companyId });

            let versionExistsFlag = false;

            companyPayData.pieceWork.versions.forEach((version) => {
                if (version._id === versionId) {
                    versionExistsFlag = true;
                    if (name) {
                        version.name = name;
                    }
                }
            });

            if (!versionExistsFlag) {
                throw new NotFoundException(`Version not exist`);
            }

            // Update the specific version within the document
            await this.companyPayModel.updateOne(
                { companyId },
                { $set: { "pieceWork.versions": companyPayData.pieceWork.versions } },
            );

            return new OkResponse({ message: "Version updated successfully" });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async fetchCompanyPayData(companyId: string) {
        try {
            const companyPayData = await this.companyPayModel
                .findOne({ companyId })
                .select({ versions: 1, sales: 1, pieceWork: 1 });
            const { sales, pieceWork } = companyPayData;

            return new OkResponse({
                versions: pieceWork.versions || [],
                salesPositions: sales.positions || [],
                pieceWorkPositions: pieceWork.positions,
            });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async deleteVersion(companyId: string, versionId: string) {
        try {
            const companyPayData = await this.companyPayModel.findOne({ companyId });

            let versionExistsFlag = false;

            companyPayData.pieceWork.versions.forEach((version, index) => {
                if (version._id === versionId) {
                    versionExistsFlag = true;
                    companyPayData.pieceWork.versions.splice(index, 1);
                }
            });

            if (!versionExistsFlag) {
                throw new NotFoundException(`Version not exist`);
            }

            // Update the specific version within the document
            await this.companyPayModel.updateOne(
                { companyId },
                { $set: { "pieceWork.versions": companyPayData.pieceWork.versions } },
            );

            return new OkResponse({ message: "Version updated successfully" });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async addSalesPositions(companyId: string, positions: string[]) {
        try {
            const companyPayData = await this.companyPayModel.findOne({ companyId });

            await this.companyPayModel.updateOne(
                { _id: companyPayData._id },
                { $set: { "sales.positions": positions } },
            );

            return new CreatedResponse({ message: "Positions added successfully" });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updatePWSettingSequence(companyId: string, updateStageSequenceDto: UpdateSequenceDto) {
        const { data } = updateStageSequenceDto;

        if (!data?.length) {
            return new OkResponse({ message: "Nothing to updates!" });
        }

        try {
            const bulkOperations = data.map(({ _id, sequence }) => ({
                updateOne: {
                    filter: { _id, companyId },
                    update: { $set: { sequence } },
                },
            }));

            const result = await this.pieceWorkSettingModel.bulkWrite(bulkOperations, { ordered: false });

            if (result?.modifiedCount > 0) {
                return new OkResponse({ message: "Piecework Setting sequence updated successfully!" });
            }

            return new OkResponse({ message: "No sequences were updated!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async fetchInactivePieceWorkSetting(companyId) {
        try {
            const pieceWorkSettings = await this.pieceWorkSettingModel
                .find({ companyId, deleted: true }, { name: 1, workTask: 1 })
                .populate("workTask", "name", "WorkTask");

            return new OkResponse({ pieceWorkSettings });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async deletePieceWorkSetting(companyId, id) {
        try {
            const pieceWorkSetting = await this.pieceWorkSettingModel.findOne({
                _id: id,
                companyId,
            });

            if (!pieceWorkSetting) {
                throw new NotFoundException("Piecework setting not found");
            }

            if (!pieceWorkSetting.deleted) {
                throw new BadRequestException(`Piecework Setting should be inactive`);
            }

            const pieceWork = await this.pieceWorkModel.find({
                "work.workDone": {
                    $elemMatch: {
                        id,
                    },
                },
                deleted: false,
            });

            if (pieceWork.length) {
                throw new ConflictException(`Piecework setting is in use`);
            }

            await this.pieceWorkSettingModel.deleteOne({
                _id: id,
                companyId,
                deleted: true,
            });

            return new OkResponse({ message: "Piecework Setting deleted successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async restorePieceWorkSetting(companyId, id) {
        try {
            const pieceWorkSetting = await this.pieceWorkSettingModel.findOne({
                _id: id,
                companyId,
            });

            if (!pieceWorkSetting) {
                throw new NotFoundException("Piecework setting not found");
            }

            if (!pieceWorkSetting.deleted) {
                throw new BadRequestException(`Piecework Setting should be inactive`);
            }

            await this.pieceWorkSettingModel.updateOne(
                {
                    _id: id,
                    companyId,
                },
                {
                    $set: {
                        deleted: false,
                    },
                },
            );

            return new OkResponse({ message: "Piecework Setting restored successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }
}
