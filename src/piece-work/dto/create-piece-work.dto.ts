import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsString, IsNotEmpty, IsOptional, IsEnum, IsUUID, IsBoolean } from "class-validator";

export class CreatePieceWorkDto {
    // @ApiProperty({ description: "Company", required: true })
    // @IsUUID()
    // @IsNotEmpty()
    // companyId: string;

    @ApiProperty({ description: "Time Card Id", required: true })
    @IsUUID()
    @IsNotEmpty()
    timeCardId: string;

    @ApiProperty({ description: "Member Id", required: true })
    @IsUUID()
    @IsNotEmpty()
    memberId: string;

    @ApiProperty({ description: "Date", required: true })
    @Transform(({ value }) => new Date(value))
    @IsNotEmpty()
    date: Date;

    @ApiProperty({ description: "Task Type", required: true })
    @IsString()
    @IsNotEmpty()
    task: string;

    @IsOptional()
    taskName?: string;

    @ApiProperty({ description: "Project Id", required: true })
    @IsString()
    @IsNotEmpty()
    projectId: string;

    @ApiPropertyOptional({ description: "Work" })
    work: any;

    @ApiProperty({ description: "TimeIn" })
    @Transform(({ value }) => new Date(value))
    timeIn: Date;

    @ApiProperty({ description: "TimeOut" })
    @Transform(({ value }) => new Date(value))
    timeOut: Date;

    @ApiProperty({ description: "Created By", required: true })
    @IsUUID()
    @IsNotEmpty()
    createdBy: string;

    @ApiPropertyOptional({ description: "removeLeadBonus" })
    @IsBoolean()
    @IsOptional()
    removeLeadBonus?: boolean;

    @ApiPropertyOptional({ description: "allHourly" })
    @IsBoolean()
    @IsOptional()
    allHourly?: boolean;

    @ApiPropertyOptional({ description: "Is the member a foreman", default: false })
    @IsBoolean()
    @IsOptional()
    foreman?: boolean;
}
