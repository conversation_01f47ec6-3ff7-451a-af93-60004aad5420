import { ApiProperty } from "@nestjs/swagger";
import { IsString, IsNotEmpty, IsUUID, IsArray, IsOptional } from "class-validator";

export class CreateVersionDTO {
    @ApiProperty({ description: "name" })
    @IsString()
    @IsNotEmpty()
    name: string;

    @ApiProperty({ description: "version Id", required: false })
    @IsUUID()
    @IsOptional()
    versionId: string;
}

export class UpdateVersionDTO {
    @ApiProperty({ description: "name" })
    @IsString()
    @IsOptional()
    name: string;
}

export class AddPositionsDTO {
    @ApiProperty({ description: "positions", type: [String] })
    @IsOptional()
    @IsArray()
    @IsUUID("all", { each: true })
    positions: string[];
}
