import {
    Controller,
    UseInterceptors,
    ClassSerializerInterceptor,
    Post,
    Body,
    UseGuards,
    Get,
    Param,
    ParseUUIDPipe,
    Delete,
    Put,
    Patch,
    Query,
    BadRequestException,
} from "@nestjs/common";
import {
    ApiTags,
    ApiOperation,
    ApiUnauthorizedResponse,
    ApiInternalServerErrorResponse,
    ApiBody,
    ApiBearerAuth,
    ApiConflictResponse,
    ApiResponse,
    ApiNotFoundResponse,
} from "@nestjs/swagger";
import { TransformInterceptor } from "src/shared/interceptors/transform.interceptor";
import { AdminService } from "./admin.service";
import { SigninDto } from "src/auth/dto/signin.dto";
import { CompanyService } from "src/company/company.service";
import { AdminAuthGuard } from "src/auth/guards/local-auth.guard";
import { SubscriptionService } from "src/subscription/subscription.service";
import { CreatePlanDto, UpdatePlanDto } from "src/subscription/dto/plan.dto";
import { CompanyAnalyticsService } from "src/company/company-analytics.service";
import { UpdateExtraUserAmount } from "./dto/update-admin.dto";
import { SupportService } from "src/support/support.service";
import { CloseTicketDto } from "src/support/dto/close-ticket.dto";
import { GetTicketsFilterDto } from "src/support/dto/get-all-tickets.dto";
import { SupportStatusEnum, SupportTeamEnum } from "src/support/enum/support.enum";
import { ReplySupportQureyDto } from "src/support/dto/update-support.dto";
import { CreateAdminDto } from "./dto/create-admin.dto";
import { AdminRoleEnum } from "src/company/enum/role.enum";
import { GetUser } from "src/auth/decorator/auth.decorator";
import { JwtAdminPayload } from "src/auth/interface/auth.interface";

@ApiTags("Admin")
@Controller({ path: "admin", version: "1" })
@UseInterceptors(ClassSerializerInterceptor)
export class AdminOpenController {
    constructor(
        private readonly adminService: AdminService,
        private readonly subscriptionService: SubscriptionService,
    ) {}

    /**
     * Post API - "/login" - used for user login and get authentication token to access other protected APIs.
     * it requires the LoginUserDto object in request body.
     * @returns newly logged in user object, token for authentication and response status.
     * @throws UnauthorizedException with message in case admin is not logged in.
     * @throws InternalServerError with message in case server error.
     */
    @Post("login")
    @UseInterceptors(TransformInterceptor)
    @ApiOperation({
        description: "Api to login admin.",
        summary: "Api to login admin",
    })
    @ApiUnauthorizedResponse({ description: "Invalid credentials" })
    @ApiInternalServerErrorResponse({ description: "In case Server Error" })
    @ApiBody({ required: true, type: SigninDto })
    async adminLogin(@Body() loginAdminDto: SigninDto) {
        return this.adminService.adminLogin(loginAdminDto);
    }
}

@ApiTags("Admin")
@Controller({ path: "admin", version: "1" })
// @UseInterceptors(ClassSerializerInterceptor)
@UseGuards(AdminAuthGuard)
// @UseGuards(JwtAuthGuard, RoleGuard([UserRoles.ADMIN]))
@ApiBearerAuth()
@ApiUnauthorizedResponse({ description: "In case admin is not logged-in" })
@ApiInternalServerErrorResponse({ description: "In case Server Error" })
export class AdminController {
    constructor(
        private readonly adminService: AdminService,
        private readonly companyService: CompanyService,
        private readonly companyAnalyticsService: CompanyAnalyticsService,
        private readonly subscriptionService: SubscriptionService,
        private readonly supportService: SupportService,
    ) {}

    /**
     *Logs the admin out by invalidating their token
     *@returns A success response with a message indicating that the admin was logged out successfully!
     *@throws HttpException if the token is invalid or if there is an internal server error
     */
    @ApiOperation({ summary: "Logout" })
    @ApiConflictResponse({ description: "Invalid token" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Get("logout")
    async signout() {
        return this.adminService.signout();
    }

    @Get("by-id/:id")
    @ApiOperation({ summary: "Get admin by id" })
    @ApiResponse({ status: 201, description: "Admin fetched successfully" })
    @ApiResponse({ status: 400, description: "SuperAdmin access required" })
    async getAdminById(@Param("id", ParseUUIDPipe) id: string) {
        return this.adminService.getAdminById(id);
    }

    @Post("add-admin")
    @ApiOperation({ summary: "Add a new admin" })
    @ApiResponse({ status: 201, description: "Admin added successfully" })
    @ApiResponse({ status: 400, description: "SuperAdmin access required" })
    async addAdmin(@Body() adminDto: CreateAdminDto, @GetUser() user: JwtAdminPayload) {
        if (user.role !== AdminRoleEnum.SuperAdmin)
            throw new BadRequestException("You should have superadmin access");
        return this.adminService.createAdmin({ ...adminDto, role: AdminRoleEnum.Admin });
    }

    @Get("admin-list")
    @ApiOperation({ summary: "List all admins" })
    @ApiResponse({ status: 200, description: "List of admins" })
    @ApiResponse({ status: 400, description: "SuperAdmin access required" })
    async adminList(@GetUser() user: JwtAdminPayload) {
        if (user.role !== AdminRoleEnum.SuperAdmin)
            throw new BadRequestException("You should have superadmin access");
        return this.adminService.adminList();
    }

    @Patch("restore-access/:adminId")
    @ApiOperation({ summary: "Restore access for a deleted admin" })
    @ApiResponse({ status: 200, description: "Admin restored successfully" })
    @ApiResponse({ status: 400, description: "SuperAdmin access required" })
    async delegateAccess(@Param("adminId", ParseUUIDPipe) adminId: string, @GetUser() user: JwtAdminPayload) {
        if (user.role !== AdminRoleEnum.SuperAdmin)
            throw new BadRequestException("You should have superadmin access");
        return this.adminService.restoreAdmin(adminId);
    }

    @Delete("delete-admin/:adminId")
    @ApiOperation({ summary: "Soft delete an admin" })
    @ApiResponse({ status: 200, description: "Admin deleted successfully" })
    @ApiResponse({ status: 400, description: "SuperAdmin access required" })
    async deleteAdmin(@Param("adminId", ParseUUIDPipe) adminId: string, @GetUser() user: JwtAdminPayload) {
        if (user.role !== AdminRoleEnum.SuperAdmin)
            throw new BadRequestException("You should have superadmin access");
        return this.adminService.deleteAdmin(adminId);
    }

    @Delete("perm-delete/:adminId")
    @ApiOperation({ summary: "Permanently delete an admin" })
    @ApiResponse({ status: 200, description: "Admin permanently deleted" })
    @ApiResponse({ status: 400, description: "SuperAdmin access required" })
    async permanentDelete(
        @Param("adminId", ParseUUIDPipe) adminId: string,
        @GetUser() user: JwtAdminPayload,
    ) {
        if (user.role !== AdminRoleEnum.SuperAdmin)
            throw new BadRequestException("You should have superadmin access");
        return this.adminService.permanentDelete(adminId);
    }

    /**
     * Generates a new API key for the SuperAdmin by hashing the provided user secret with the email and a hash key.
     * The new API key is then stored in the database and returned.
     * @param userSecret The user secret that should be used to generate the new API key.
     * @returns The newly generated API key.
     * @throws {NotFoundException} If the SuperAdmin is not found.
     * @throws {InternalServerErrorException} If there is an error while creating the new API key.
     */
    @Patch("generate-api-key/:userSecret")
    @ApiOperation({ summary: "Generates & update a new API key" })
    @ApiResponse({ status: 200, description: "API key generated" })
    @ApiResponse({ status: 400, description: "SuperAdmin access required" })
    async genrateAndUpdateApiKey(@Param("userSecret") userSecret: string, @GetUser() user: JwtAdminPayload) {
        if (user.role !== AdminRoleEnum.SuperAdmin)
            throw new BadRequestException("You should have superadmin access");
        return this.adminService.genrateAndUpdateApiKey(userSecret);
    }

    //Company API's
    /**
     * GET API - "/all-companies/deleted/:deleted" - to get all companies in database.
     * @param deleted boolean - true to get all deleted companies, false to get all active companies
     * @returns all companies
     * @throws InternalServerError with message in case server error.
     */
    @Get("all-companies/deleted/:deleted")
    @ApiOperation({
        description: "Api to get all Companies",
    })
    async getCompanies(@Param("deleted") deleted: boolean) {
        return await this.companyService.getCompanies(deleted);
    }

    /**
     * GET API - "/all-companies" - to get all compnies in database
     * it requires authentication.
     * @returns all companies
     * @throws UnauthorizedException with message in case admin is not logged in.
     * @throws InternalServerError with message in case server error.
     */
    @Get("company-by-id/companyId/:companyId")
    @ApiOperation({
        description: "Api to get a Company by its _id",
    })
    async getCompanyById(@Param("companyId", ParseUUIDPipe) companyId: string) {
        return await this.companyService.getCompanyById(true, companyId);
    }

    /**
     * RESTORE API - "/restore-company/:companyId" - to restore a deleted company.
     * @param companyId string - the id of the company to be restored
     * @returns the restored company
     * @throws InternalServerError with message in case server error
     * @throws NotFoundException with message in case company does not exist
     * @throws BadRequestException with message in case company is already active
     */
    @Patch("restore-company/:companyId")
    @ApiOperation({ summary: "Restore a deleted company" })
    @ApiResponse({ status: 200, description: "The company was successfully restored." })
    @ApiResponse({ status: 400, description: "Bad Request" })
    async restoreCompany(@Param("companyId", ParseUUIDPipe) companyId: string) {
        return this.companyService.restoreCompany(companyId);
    }

    @Delete("company/:companyId")
    @ApiOperation({ summary: "Soft Delete an existing company" })
    @ApiResponse({ status: 200, description: "The company was successfully marked as deleted." })
    @ApiResponse({ status: 400, description: "Bad Request" })
    async softDeleteCompany(@Param("companyId", ParseUUIDPipe) companyId: string) {
        return this.companyService.softDeleteCompany(companyId);
    }

    @Delete("company-permanently")
    @ApiOperation({ summary: "Permanently Delete an existing company" })
    @ApiResponse({ status: 200, description: "The Company & its data has been successfully deleted." })
    @ApiResponse({ status: 400, description: "Bad Request" })
    @ApiBody({
        schema: {
            type: "object",
            properties: { companyIds: { type: "array", items: { type: "string" } } },
            required: ["companyIds"],
        },
    })
    async permDeleteCompany(@Body("companyIds") companyIds: string[]) {
        if (!companyIds.length) {
            throw new BadRequestException("Company IDs cannot be empty");
        }
        return this.companyService.permDeleteCompany(companyIds);
    }

    //Subscription PLAN API's
    @Post("create-plan")
    @ApiOperation({ summary: "Create a new plan" })
    @ApiBody({ type: CreatePlanDto })
    @ApiResponse({ status: 201, description: "The plan has been successfully created." })
    @ApiResponse({ status: 400, description: "Bad Request" })
    async createPlan(@Body() createPlanData: CreatePlanDto) {
        return this.subscriptionService.createPlan(createPlanData);
    }

    @Put("updatePlan/:planId")
    @ApiOperation({ summary: "Update an existing plan" })
    @ApiBody({ type: UpdatePlanDto })
    @ApiResponse({ status: 200, description: "The plan has been successfully updated." })
    @ApiResponse({ status: 400, description: "Bad Request" })
    async updatePlan(@Param("planId", ParseUUIDPipe) planId: string, @Body() updatePlanData: UpdatePlanDto) {
        return this.subscriptionService.updatePlan(planId, updatePlanData);
    }

    @Delete("delete-plan/:planId")
    @ApiOperation({ summary: "Delete an existing plan" })
    @ApiResponse({ status: 200, description: "The plan has been successfully deleted." })
    @ApiResponse({ status: 400, description: "Bad Request" })
    async deletePlan(@Param("planId", ParseUUIDPipe) planId: string) {
        return this.subscriptionService.deletePlan(planId);
    }

    @Get("fetch-plans")
    @ApiOperation({ summary: "Fetch all plans" })
    @ApiResponse({ status: 200, description: "All plans have been successfully fetched." })
    @ApiResponse({ status: 400, description: "Bad Request" })
    async fetchAllPlans() {
        return this.subscriptionService.fetchAllPlans();
    }

    @Get("fetch-plan/:planId")
    @ApiOperation({ summary: "Fetch plan by id" })
    @ApiResponse({ status: 200, description: "All plans have been successfully fetched." })
    @ApiResponse({ status: 400, description: "Bad Request" })
    async fetchPlan(@Param("planId", ParseUUIDPipe) planId: string) {
        return this.subscriptionService.fetchPlan(planId);
    }

    @Get("company-analytics/companyId/:companyId")
    @ApiOperation({ summary: "Fetch company analytics data by id" })
    @ApiResponse({ status: 200, description: "Company data has been successfully fetched." })
    @ApiResponse({ status: 400, description: "Bad Request" })
    async getAnalyticsByCompanyId(@Param("companyId", ParseUUIDPipe) companyId: string) {
        return this.companyAnalyticsService.getAnalyticsByCompanyId(companyId);
    }

    @Get("extra-user-amount")
    @ApiOperation({ summary: "Fetch extra amount charged per user" })
    @ApiResponse({ status: 200, description: "Data successfully fetched." })
    @ApiResponse({ status: 400, description: "Bad Request" })
    async getExtraUserAmount() {
        return this.adminService.getExtraUserAmount();
    }

    @Put("extra-user-amount")
    @ApiOperation({ summary: "Fetch company analytics data by id" })
    @ApiResponse({ status: 200, description: "Data successfully updated." })
    @ApiResponse({ status: 400, description: "Bad Request" })
    async updateExtraUserAmount(@Body() updateExtraUserAmount: UpdateExtraUserAmount) {
        return this.adminService.updateExtraUserAmount(updateExtraUserAmount);
    }

    @Get("company-invoices/:companyId")
    @UseInterceptors(TransformInterceptor)
    @ApiOperation({
        description: "Api to fetch company invoiceslogin admin.",
    })
    @ApiUnauthorizedResponse({ description: "Invalid credentials" })
    @ApiInternalServerErrorResponse({ description: "In case Server Error" })
    async companyInvoices(@Param("companyId") companyId: string) {
        return this.subscriptionService.getCustomerDetails(companyId);
    }

    // Support Team
    @ApiOperation({ summary: "get all query ticket" })
    @ApiNotFoundResponse({ description: "Checkpoint not found" })
    @Get("support/all-tickets")
    async getAllTickets(@Query() getTicketsFilterDto: GetTicketsFilterDto) {
        return await this.supportService.getAllTickets(getTicketsFilterDto);
    }

    @ApiOperation({ summary: "get a query ticket by id" })
    @ApiNotFoundResponse({ description: "Checkpoint not found" })
    @Get("support/ticket-by-id/:companyId/:ticketId")
    async getTicketById(@Param("companyId") companyId: string, @Param("ticketId") ticketId: string) {
        return await this.supportService.getTicketById(companyId, ticketId);
    }

    @ApiOperation({ summary: "Reply to the Query" })
    @Patch("support/reply-query")
    async replyToQuery(@Body() replySupportQureyDto: ReplySupportQureyDto) {
        return await this.supportService.replyToQuery(
            "support",
            replySupportQureyDto,
            SupportTeamEnum.SUPPORT,
            SupportStatusEnum.REPLIED,
        );
    }

    @ApiOperation({ summary: "Mark ticket as closed" })
    @Patch("support/close-ticket")
    async updateQueryStatus(@Param() closeTicketDto: CloseTicketDto) {
        return await this.supportService.updateQueryStatus(closeTicketDto, SupportTeamEnum.SUPPORT);
    }

    @ApiOperation({ summary: "Mark message as seen for admin" })
    @Patch("support/mark-seen/ticket/:ticketId")
    async markAllRepliesAsSeen(@Param("ticketId") ticketId: string) {
        return await this.supportService.markAllRepliesAsSeen(ticketId, SupportTeamEnum.USER);
    }
}
