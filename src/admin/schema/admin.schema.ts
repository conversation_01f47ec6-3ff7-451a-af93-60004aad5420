import { Schema, SchemaFactory, Prop } from "@nestjs/mongoose";
import { Document } from "mongoose";
import { Exclude } from "class-transformer";
import { randomUUID } from "crypto";
export type AdminDocument = Admin & Document;

@Schema({ timestamps: true, id: false, collection: "Admin", versionKey: false })
export class Admin {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @Prop({ required: true, unique: true })
    email: string;

    @Prop({ default: "admin", description: "Role of Admin" })
    role: string;

    @Exclude({ toPlainOnly: true })
    @Prop({ required: true })
    password: string;

    @Prop()
    extraUserChargeMonthly: number;

    @Prop()
    extraUserChargeYearly: number;

    @Prop()
    baseUserCount: number;

    @Prop()
    apiKey: string;

    @Prop({ default: false })
    deleted: boolean;

    @Prop()
    createdAt?: Date;

    @Prop()
    modifiedAt?: Date;
}

export const AdminSchema = SchemaFactory.createForClass(Admin);
