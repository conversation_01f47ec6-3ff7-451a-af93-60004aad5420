import { <PERSON>du<PERSON> } from "@nestjs/common";
import { AdminService } from "./admin.service";
import { AdminController, AdminOpenController } from "./admin.controller";
import { AdminSchema } from "./schema/admin.schema";
import { MongooseModule } from "@nestjs/mongoose";
import { ConfigModule, ConfigService } from "@nestjs/config";
import { JwtModule } from "@nestjs/jwt";
import { PassportModule } from "@nestjs/passport";
import { AdminJwtStrategy } from "src/auth/strategies/admin-jwt.strategy";
import { SubscriptionModule } from "src/subscription/subscription.module";
import { StripeModule } from "src/stripe/stripe.module";
import { SupportModule } from "src/support/support.module";

@Module({
    imports: [
        PassportModule.register({ defaultStrategy: "admin-jwt" }),
        JwtModule.registerAsync({
            imports: [ConfigModule],
            inject: [ConfigService],
            useFactory: (configService: ConfigService) => {
                return {
                    secret: configService.get<string>("JWT_SECRET_ADMIN"),
                    signOptions: {
                        expiresIn: configService.get<string>("JWT_SECRET_EXPIRES_ADMIN"),
                    },
                };
            },
        }),
        MongooseModule.forFeature([{ name: "Admin", schema: AdminSchema }]),
        // CompanyModule,
        // AuthModule,
        SubscriptionModule,
        ConfigModule,
        StripeModule,
        SupportModule,
    ],
    controllers: [AdminController, AdminOpenController],
    providers: [AdminService, AdminJwtStrategy],
    exports: [AdminService],
})
export class AdminModule {}
