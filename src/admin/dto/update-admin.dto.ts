import { ApiPropertyOptional, PartialType } from "@nestjs/swagger";
import { CreateAdminDto } from "./create-admin.dto";
import { IsNumber, IsOptional } from "class-validator";

export class UpdateAdminDto extends PartialType(CreateAdminDto) {}

export class UpdateExtraUserAmount {
    @ApiPropertyOptional()
    @IsOptional()
    @IsNumber()
    extraUserChargeMonthly?: number;

    @ApiPropertyOptional()
    @IsOptional()
    @IsNumber()
    extraUserChargeYearly?: number;

    @ApiPropertyOptional()
    @IsOptional()
    @IsNumber()
    baseUserCount?: number;
}
