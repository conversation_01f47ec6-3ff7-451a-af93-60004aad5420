import { Module } from "@nestjs/common";
import { CronService } from "./cron.service";
import { ScheduleModule } from "@nestjs/schedule";
import { MongooseModule } from "@nestjs/mongoose";
import { CompanySchema } from "src/company/schema/company.schema";
import { StripeModule } from "src/stripe/stripe.module";
import { UserSchema } from "src/user/schema/user.schema";
import {
    SUBSCRIPTION_PLAN_COLLECTION_NAME,
    SubscriptionPlanSchema,
} from "src/subscription/schema/subscription.schema";
import {
    SUBSCRIPTION_PRICE_COLLECTION_NAME,
    SubscriptionPriceSchema,
} from "src/subscription/schema/price.schema";
import { AdminSchema } from "src/admin/schema/admin.schema";

@Module({
    imports: [
        ScheduleModule.forRoot(),
        MongooseModule.forFeature([
            { name: "Company", schema: CompanySchema },
            { name: "User", schema: UserSchema },
            { name: SUBSCRIPTION_PLAN_COLLECTION_NAME, schema: SubscriptionPlanSchema },
            { name: SUBSCRIPTION_PRICE_COLLECTION_NAME, schema: SubscriptionPriceSchema },
            { name: "Admin", schema: AdminSchema },
        ]),

        StripeModule,
    ],
    providers: [CronService],
})
export class CronModule {}
