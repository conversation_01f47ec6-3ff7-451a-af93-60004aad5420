import { Injectable, Logger } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { <PERSON>ron } from "@nestjs/schedule";
import { Model } from "mongoose";
import { AdminDocument } from "src/admin/schema/admin.schema";
import { CompanyAnalyticsService } from "src/company/company-analytics.service";
import { AdminRoleEnum, UserRolesEnum } from "src/company/enum/role.enum";
import { CompanyDocument } from "src/company/schema/company.schema";
import {
    SubscriptionItem,
    SubscriptionRenewalPeriod,
    SubscriptionStatusEnum,
} from "src/shared/enum/subscriptions.enum";
import { endOfDate, startOfDate } from "src/shared/helpers/logics";
import { StripeService } from "src/stripe/stripe.service";
import {
    SUBSCRIPTION_PRICE_COLLECTION_NAME,
    SubscriptionPriceDocument,
} from "src/subscription/schema/price.schema";
import {
    SUBSCRIPTION_PLAN_COLLECTION_NAME,
    SubscriptionPlanDocument,
} from "src/subscription/schema/subscription.schema";

@Injectable()
export class CronService {
    constructor(
        @InjectModel("Company")
        private readonly companyModel: Model<CompanyDocument>,
        @InjectModel(SUBSCRIPTION_PLAN_COLLECTION_NAME)
        private readonly subscriptionPlanModel: Model<SubscriptionPlanDocument>,
        @InjectModel(SUBSCRIPTION_PRICE_COLLECTION_NAME)
        private readonly subscriptionPriceModel: Model<SubscriptionPriceDocument>,
        @InjectModel("Admin")
        private adminModel: Model<AdminDocument>,
        private readonly stripeService: StripeService,
        private readonly companyAnalyticsService: CompanyAnalyticsService,
    ) {}

    // async onModuleInit() {
    //     await this.updateCompanyCredits();
    // }

    /* Cron time
    * * * * * *
    | | | | | |
    | | | | | day of week
    | | | | months
    | | | day of month
    | | hours
    | minutes
    seconds (optional)
    */

    // @Cron("1/30 * * * * *")
    // handleCron() {
    //     console.log("Called when the current second is 15", new Date());
    //     this.updateCompanyCredits();
    // }

    async updateCompanyCredits() {
        const date = new Date();
        const today = new Date().getUTCDate();
        const currentMonth = date.getUTCMonth() + 1; // Months are zero-indexed in JS, so add 1
        const currentYear = date.getUTCFullYear();

        // Get the number of days in the current month
        const daysInCurrentMonth = new Date(currentYear, currentMonth, 0).getUTCDate();

        const startOfDay = startOfDate(date);
        const endOfDay = endOfDate(date);
        console.log({ today, date, startOfDay, endOfDay, currentMonth, currentYear, daysInCurrentMonth });

        // const allCmpny = await this.companyModel.find({
        //     deleted: { $ne: true },
        //     status: SubscriptionStatusEnum.ACTIVE,
        //     $or: [
        //         // Match for the current day
        //         { $expr: { $eq: [{ $dayOfMonth: "$subscriptionStartDate" }, today] } },

        //         // Handle cases where today is the 30th and the month has no 31st
        //         ...(today === 30 && daysInCurrentMonth === 30
        //             ? [
        //                   {
        //                       $expr: { $eq: [{ $dayOfMonth: "$subscriptionStartDate" }, 31] },
        //                   },
        //               ]
        //             : []),

        //         // Handle cases where today is February 28th, and it should include 29th (leap year) or 30th or 31st
        //         ...(today === 28 && currentMonth === 2
        //             ? [
        //                   {
        //                       $expr: { $in: [{ $dayOfMonth: "$subscriptionStartDate" }, [29, 30, 31]] },
        //                   },
        //               ]
        //             : []),
        //     ],
        // });

        const allCmpny = await this.companyModel.aggregate([
            {
                $match: {
                    deleted: { $ne: true },
                    status: SubscriptionStatusEnum.ACTIVE,
                    interval: SubscriptionRenewalPeriod.MONTH,
                    $or: [{ cancelAt: { $exists: false } }, { cancelAt: { $eq: null } }],
                },
            },
            {
                $addFields: {
                    currentDate: { $toDate: "$$NOW" },
                    endDateInMillis: { $toDate: { $multiply: ["$subscriptionEndDate", 1000] } },
                },
            },
            {
                $project: {
                    _id: 1,
                    endDateDiff: {
                        $dateDiff: {
                            startDate: "$currentDate",
                            endDate: "$endDateInMillis",
                            unit: "day",
                        },
                    },
                    // Include all other fields from the original document
                    originalFields: "$$ROOT",
                },
            },
            {
                $replaceRoot: {
                    newRoot: {
                        $mergeObjects: ["$originalFields", { endDateDiff: "$endDateDiff" }],
                    },
                },
            },
            {
                $match: {
                    endDateDiff: 1,
                },
            },
        ]);

        await this.updateMonthlySubscriptionForNearbyCycle(allCmpny);
    }

    async updateMonthlySubscriptionForNearbyCycle(allCmpny) {
        try {
            const {
                baseUserCount: defaultUserCount,
                extraUserChargeMonthly: extraMemberChargeForMonthlySub,
            } = await this.adminModel.findOne({ role: AdminRoleEnum.SuperAdmin });

            const updatePromises = allCmpny.map(async (company) => {
                const { stripeSubscriptionId, extraStripeSubscriptionItemId, planId, subscribedTeamSize } =
                    company;

                const { memberCount: thisMonthActiceMembers } =
                    await this.companyAnalyticsService.getActiveMemberCount(company._id);
                const thisMonthExtraActiveMembers = Math.max(thisMonthActiceMembers - defaultUserCount, 0);

                // Fetch plan data
                const planData = await this.subscriptionPlanModel
                    .findOne({ _id: planId })
                    .populate("defaultPrice", "", this.subscriptionPriceModel);

                // Proceed only if there is a change in subscribed team size
                if (thisMonthActiceMembers !== subscribedTeamSize) {
                    const amountToCharge = thisMonthExtraActiveMembers * extraMemberChargeForMonthlySub;

                    // Create new Stripe price
                    const newStripePrice = await this.stripeService.createNewPrice(
                        amountToCharge,
                        planData.stripeProductId,
                        SubscriptionRenewalPeriod.MONTH,
                        SubscriptionItem.EXTRA,
                    );

                    // Update subscription price on Stripe
                    await this.stripeService.updateMonthlySubscriptionPrice({
                        userSubscriptionId: stripeSubscriptionId,
                        extraSubscriptionItemId: extraStripeSubscriptionItemId,
                        newPriceId: newStripePrice,
                        subscribedTeamSize: thisMonthActiceMembers,
                    });
                }
            });

            // Await all updates to complete
            await Promise.all(updatePromises);
        } catch (error) {
            new Logger("Error while updating new active user for monthly plan", error);
        }
    }
}
