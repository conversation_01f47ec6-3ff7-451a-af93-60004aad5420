import { Controller, Get, Post, Body, Patch, Param, Delete, ParseUUIDPipe, Query } from "@nestjs/common";
import { CreateCampaignDto } from "./dto/create-campaign.dto";
import { UpdateCampaignDto } from "./dto/update-campaign.dto";
import { CampaignService } from "./campaign.service";
import { ApiBearerAuth, ApiTags, ApiOperation, ApiInternalServerErrorResponse } from "@nestjs/swagger";
import { Auth, GetUser } from "src/auth/decorator/auth.decorator";
import { Roles } from "src/auth/guards/auth.guard";
import { JwtUserPayload } from "src/auth/interface/auth.interface";
import { UserRolesEnum } from "src/company/enum/role.enum";
import { GetCampaignDto } from "./dto/fetch-campaign.dto";

@ApiBearerAuth()
@ApiTags("Campaign")
@Auth()
@Controller({ path: "campaign", version: "1" })
@ApiInternalServerErrorResponse({ description: "Server Error" })
export class CampaignController {
    constructor(private readonly campaignService: CampaignService) {}

    @ApiOperation({ summary: "Create campaign" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Post()
    create(@GetUser() user: JwtUserPayload, @Body() createCampaignDto: CreateCampaignDto) {
        return this.campaignService.create(user.companyId, user.memberId, createCampaignDto);
    }

    @ApiOperation({ summary: "Get all campaign" })
    @Get("all/deleted/:deleted")
    findAll(
        @GetUser() user: JwtUserPayload,
        @Param("deleted") deleted: boolean,
        @Query() getCampaignDto: GetCampaignDto,
    ) {
        return this.campaignService.findAll(user.companyId, deleted, getCampaignDto);
    }

    @ApiOperation({ summary: "Get campaign by id" })
    @Get(":id")
    findOne(@GetUser() user: JwtUserPayload, @Param("id", ParseUUIDPipe) id: string) {
        return this.campaignService.findOne(id, user.companyId);
    }

    @ApiOperation({ summary: "Update campaign by id" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Patch(":id")
    update(
        @GetUser() user: JwtUserPayload,
        @Param("id", ParseUUIDPipe) id: string,
        @Body() updateMarketingCampaignDto: UpdateCampaignDto,
    ) {
        return this.campaignService.update(id, user.companyId, updateMarketingCampaignDto);
    }

    @ApiOperation({ summary: "Delete campaign by id" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Delete(":id")
    remove(@GetUser() user: JwtUserPayload, @Param("id", ParseUUIDPipe) id: string) {
        return this.campaignService.remove(id, user.companyId);
    }

    @ApiOperation({ summary: "Restore campaign by id" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Patch("restore/:id")
    restore(@GetUser() user: JwtUserPayload, @Param("id", ParseUUIDPipe) id: string) {
        return this.campaignService.restore(id, user.companyId);
    }
}
