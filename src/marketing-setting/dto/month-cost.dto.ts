import { ApiProperty } from "@nestjs/swagger";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from "class-validator";

export class MonthCostDto {
    @ApiProperty({ description: "Month (1-12)", minimum: 1, maximum: 12 })
    @IsNumber()
    @IsNotEmpty()
    @Min(1)
    @Max(12)
    month: number;

    @ApiProperty({ description: "Year" })
    @IsNumber()
    @IsNotEmpty()
    year: number;

    @ApiProperty({ description: "Cost for this month/year" })
    @IsNumber()
    @IsNotEmpty()
    cost: number;
}
