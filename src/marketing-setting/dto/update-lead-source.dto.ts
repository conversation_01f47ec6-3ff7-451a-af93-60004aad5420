import { ApiProperty, ApiPropertyOptional, PartialType } from "@nestjs/swagger";
import { IsArray, IsNotEmpty, IsOptional, IsUUID, ValidateNested } from "class-validator";
import { CreateLeadSourceDto } from "./create-lead-source.dto";
import { Type } from "class-transformer";
import { MonthCostDto } from "./month-cost.dto";

export class UpdateLeadSourceDto extends PartialType(CreateLeadSourceDto) {
    @ApiProperty({ description: "Lead Source Id", required: true })
    @IsUUID()
    @IsNotEmpty()
    leadSourceId: string;

    @ApiPropertyOptional({
        description: "Monthly actual cost",
        type: MonthCostDto,
    })
    @IsOptional()
    @ValidateNested()
    @Type(() => MonthCostDto)
    actualCost?: MonthCostDto;
}
