import { ApiPropertyOptional } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsBoolean, IsOptional, IsString } from "class-validator";

export class GetCampaignDto {
    @ApiPropertyOptional({ description: "search using name" })
    @IsOptional()
    @IsString()
    search?: string;

    @ApiPropertyOptional({ description: "active campaign", default: true })
    @IsOptional()
    @IsBoolean()
    @Transform(({ value }) => value === "true")
    active?: boolean;
}
