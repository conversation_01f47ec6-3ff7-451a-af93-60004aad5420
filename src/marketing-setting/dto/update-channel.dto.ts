import { ApiProperty, PartialType } from "@nestjs/swagger";
import { IsNotEmpty, IsUUID } from "class-validator";
import { CreateMarketingChannelDto } from "./create-channel.dto";

export class UpdateMarketingChannelDto extends PartialType(CreateMarketingChannelDto) {
    @ApiProperty({ description: "Marketing Channel Id", required: true })
    @IsUUID()
    @IsNotEmpty()
    marketingChannelId: string;
}
