import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsNotEmpty, IsOptional, IsUUID, IsString, IsBoolean, <PERSON><PERSON><PERSON>ber, <PERSON>, <PERSON> } from "class-validator";

export class CreateLeadSourceDto {
    @ApiProperty({ description: "name", required: true })
    @IsString()
    @Transform(({ value }) => value.trim(), { toClassOnly: true })
    @IsNotEmpty()
    name: string;

    @ApiPropertyOptional({ description: "description" })
    @IsString()
    @IsOptional()
    description: string;

    @ApiProperty({ description: "channel id", required: true })
    @IsUUID()
    @IsNotEmpty()
    channelId: string;

    @ApiPropertyOptional({ description: "Start Month of campaign" })
    @IsNumber()
    @Max(12)
    @Min(1)
    @IsOptional()
    startMonth?: number;

    @ApiPropertyOptional({ description: "Start Year of campaign" })
    @IsNumber()
    @IsOptional()
    startYear?: number;

    @ApiPropertyOptional({ description: "End Month of campaign" })
    @IsNumber()
    @Max(12)
    @Min(1)
    @IsOptional()
    endMonth?: number;

    @ApiPropertyOptional({ description: "End Year of campaign" })
    @IsNumber()
    @IsOptional()
    endYear?: number;

    @ApiPropertyOptional({ description: "cost of campaign" })
    @IsNumber()
    @IsOptional()
    cost?: number;

    @ApiPropertyOptional({ description: "running monthly", default: false })
    @IsBoolean()
    @IsOptional()
    isMonthly?: boolean;
}
