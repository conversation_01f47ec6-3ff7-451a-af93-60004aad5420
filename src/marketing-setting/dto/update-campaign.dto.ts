import { ApiPropertyOptional, PartialType } from "@nestjs/swagger";
import { CreateCampaignDto } from "./create-campaign.dto";
import { Type } from "class-transformer";
import { IsOptional, ValidateNested } from "class-validator";
import { MonthCostDto } from "./month-cost.dto";

export class UpdateCampaignDto extends PartialType(CreateCampaignDto) {
    @ApiPropertyOptional({
        description: "Monthly actual cost",
        type: MonthCostDto,
    })
    @IsOptional()
    @ValidateNested()
    @Type(() => MonthCostDto)
    actualCost?: MonthCostDto;
}
