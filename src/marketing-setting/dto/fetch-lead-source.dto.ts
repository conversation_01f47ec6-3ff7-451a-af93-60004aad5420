import { ApiPropertyOptional } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsOptional, IsString, IsBoolean } from "class-validator";
import { PaginationDto } from "src/shared/dto/pagination.dto";

export class GetLeadSourceDto extends PaginationDto {
    @ApiPropertyOptional({ description: "search using name" })
    @IsOptional()
    @IsString()
    search?: string;

    @ApiPropertyOptional({ description: "active campaign", default: true })
    @IsOptional()
    @IsBoolean()
    @Transform(({ value }) => value === "true")
    active?: boolean;
}
