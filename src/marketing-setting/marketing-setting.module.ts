import { Module } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { PositionSchema } from "src/position/schema/position.schema";
import { LeadSourceController } from "./lead-source.controller";
import { LeadSourceService } from "./lead-source.service";
import { LeadSourceSchema } from "./schema/lead-source.schema";
import { CampaignService } from "./campaign.service";
import { CampaignController } from "./campaign.controller";
import { CampaignSchema } from "./schema/campaign.schema";
import { MarketingChannelSchema } from "./schema/channel.schema.dto";
import { ChannelController } from "./channel.controller";
import { ChannelService } from "./channel.service";
import { TrackingRuleController } from "./tracking-rule.controller";
import { TrackingRuleService } from "./tracking-rule.service";
import { TrackingRuleSchema } from "./schema/tracking-rule.schema";

@Module({
    imports: [
        MongooseModule.forFeature([
            { name: "LeadSource", schema: LeadSourceSchema },
            { name: "MarketingChannel", schema: MarketingChannelSchema },
            { name: "Position", schema: PositionSchema },
            { name: "Campaign", schema: CampaignSchema },
            { name: "TrackingRule", schema: TrackingRuleSchema },
        ]),
    ],
    providers: [LeadSourceService, CampaignService, ChannelService, TrackingRuleService],
    controllers: [LeadSourceController, CampaignController, ChannelController, TrackingRuleController],
    exports: [LeadSourceService, CampaignService, ChannelService, TrackingRuleService],
})
export class MarketingSettingModule {}
