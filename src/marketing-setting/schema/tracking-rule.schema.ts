import { Schema, SchemaFactory, Prop } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import { Document } from "mongoose";
import { UUIDProp } from "src/shared/decorator/uuid.decorator";

export type TrackingRuleDocument = TrackingRule & Document;

@Schema({ timestamps: true, id: false, collection: "TrackingRule", strict: true })
export class TrackingRule {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @UUIDProp()
    companyId: string;

    @Prop({
        type: [
            {
                field: String,
                value: String,
                _id: false,
            },
        ],
        required: true,
    })
    conditions: Array<{
        field: string;
        value: string | string[] | number | boolean;
    }>;

    @UUIDProp({ required: true })
    leadSourceId: string;

    @UUIDProp({ required: false })
    campaignId?: string;

    @Prop({ type: Boolean, default: true })
    isActive: boolean;

    @UUIDProp()
    createdBy: string;

    @Prop({ default: false })
    deleted: boolean;

    @Prop()
    createdAt?: Date;

    @Prop()
    updatedAt?: Date;
}

export const TrackingRuleSchema = SchemaFactory.createForClass(TrackingRule);

// Indexes for performance
// LeadSourceRuleSchema.index({ companyId: 1, deleted: 1, isActive: 1 });
// LeadSourceRuleSchema.index({ companyId: 1, leadSourceId: 1, deleted: 1 });
