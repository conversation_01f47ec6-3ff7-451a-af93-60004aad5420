import { Schema, SchemaFactory, Prop } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import { Document } from "mongoose";

export type MarketingChannelDocument = MarketingChannel & Document;

@Schema({ timestamps: true, id: false, collection: "MarketingChannel" })
export class MarketingChannel {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @Prop({ required: true })
    companyId: string;

    @Prop({ required: true })
    name: string;

    @Prop()
    description?: string;

    @Prop()
    order?: number;

    @Prop({ required: true })
    createdBy: string;

    @Prop({ default: false })
    deleted: boolean;

    @Prop()
    createdAt?: Date;

    @Prop()
    modifiedAt?: Date;
}

export const MarketingChannelSchema = SchemaFactory.createForClass(MarketingChannel);
