import { Schema, SchemaFactory, Prop } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import { Document } from "mongoose";
import { UUIDProp } from "src/shared/decorator/uuid.decorator";
import { MonthCost } from "../interfaces/month-cost.interface";

export type LeadSourceDocument = LeadSource & Document;

@Schema({ timestamps: true, id: false, collection: "LeadSource" })
export class LeadSource {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @UUIDProp()
    companyId: string;

    @Prop({ required: true })
    name: string;

    @Prop()
    description?: string;

    @Prop()
    code?: string;

    @Prop({ required: true })
    channelId: string;

    @Prop({
        type: Number,
        min: 1,
        max: 12,
        description: "Campaign start data for this month (1-12)",
    })
    startMonth?: number;

    @Prop({
        type: Number,
        description: "Campaign start data for this year",
    })
    startYear?: number;

    @Prop({
        type: Number,
        min: 1,
        max: 12,
        description: "Campaign end data for this month (1-12)",
    })
    endMonth?: number;

    @Prop({
        type: Number,
        description: "Campaign end data for this year",
    })
    endYear?: number;

    @Prop()
    cost?: number;

    @Prop({ type: [{ month: Number, year: Number, cost: Number, _id: false }] })
    actualCost?: MonthCost[];

    @Prop({ type: Boolean, default: false })
    isMonthly: boolean;

    @UUIDProp()
    createdBy: string;

    @Prop({ default: false })
    deleted: boolean;

    @Prop()
    createdAt?: Date;

    @Prop()
    modifiedAt?: Date;
}

export const LeadSourceSchema = SchemaFactory.createForClass(LeadSource);
