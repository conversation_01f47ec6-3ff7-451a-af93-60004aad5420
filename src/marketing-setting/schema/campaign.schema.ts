import { <PERSON>hem<PERSON>, SchemaFactory, Prop } from "@nestjs/mongoose";
import { Document } from "mongoose";
import { UUIDProp } from "src/shared/decorator/uuid.decorator";
import { randomUUID } from "crypto";
import { MonthCost } from "../interfaces/month-cost.interface";

export type CampaignDocument = Campaign & Document;

@Schema({ timestamps: true, collection: "Campaign", strict: true, id: false })
export class Campaign {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @UUIDProp()
    companyId: string;

    @Prop({ type: String, required: true })
    name: string;

    @Prop({ type: String })
    description?: string;

    @UUIDProp()
    leadSourceId: string;

    @Prop({
        type: Number,
        min: 1,
        max: 12,
        description: "Campaign start data for this month (1-12)",
    })
    startMonth?: number;

    @Prop({
        type: Number,
        description: "Campaign start data for this year",
    })
    startYear?: number;

    @Prop({
        type: Number,
        min: 1,
        max: 12,
        description: "Campaign end data for this month (1-12)",
    })
    endMonth?: number;

    @Prop({
        type: Number,
        description: "Campaign end data for this year",
    })
    endYear?: number;

    @Prop()
    cost?: number;

    @Prop({ type: [{ month: Number, year: Number, cost: Number, _id: false }] })
    actualCost?: MonthCost[];

    @Prop({ type: Boolean, default: false })
    isMonthly: boolean;

    @Prop({ type: Boolean, default: false })
    deleted: boolean;

    @UUIDProp()
    createdBy: string;

    @Prop()
    createdAt?: Date;

    @Prop()
    updatedAt?: Date;
}

export const CampaignSchema = SchemaFactory.createForClass(Campaign);

// Indexes for performance
CampaignSchema.index({ companyId: 1, name: 1, deleted: 1 });
CampaignSchema.index({ companyId: 1, leadSourceId: 1, deleted: 1 });
