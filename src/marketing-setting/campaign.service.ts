import {
    HttpException,
    HttpStatus,
    Injectable,
    InternalServerErrorException,
    NotFoundException,
} from "@nestjs/common";
import { CreateCampaignDto } from "./dto/create-campaign.dto";
import { UpdateCampaignDto } from "./dto/update-campaign.dto";
import { CampaignDocument } from "./schema/campaign.schema";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";
import CreatedResponse from "src/shared/http/response/created.http";
import OkResponse from "src/shared/http/response/ok.http";
import { GetCampaignDto } from "./dto/fetch-campaign.dto";

@Injectable()
export class CampaignService {
    constructor(@InjectModel("Campaign") private readonly campaignModel: Model<CampaignDocument>) {}
    async create(companyId: string, createdBy: string, createCampaignDto: CreateCampaignDto) {
        try {
            const campaign = await this.campaignModel.exists({
                companyId,
                name: createCampaignDto.name,
                deleted: false,
            });
            if (campaign) throw new HttpException("Campaign already exists", HttpStatus.BAD_REQUEST);

            const createdcampaign = new this.campaignModel({
                companyId,
                createdBy,
                ...createCampaignDto,
            });
            await createdcampaign.save();

            return new CreatedResponse({ message: "Campaign created successfully!" });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async findAll(companyId: string, deleted: boolean, getCampaignDto: GetCampaignDto) {
        try {
            const { active, search } = getCampaignDto;
            // const endOfMonth = getEndOfMonth(new Date());
            const now = new Date();
            const currentMonth = now.getMonth() + 1; // 1–12
            const currentYear = now.getFullYear();

            const query: any = {
                companyId,
                deleted,
                ...(search && { name: { $regex: search, $options: "i" } }),
            };

            // by defalt fetch active campaigns
            if (active === true) {
                query.$or = [
                    { endYear: null, endMonth: null },
                    { endMonth: null },
                    { endYear: { $gt: currentYear } },
                    {
                        endYear: currentYear,
                        endMonth: { $gte: currentMonth },
                    },
                ];
            }

            const campaigns = await this.campaignModel.aggregate([
                {
                    $match: query,
                },
                {
                    $lookup: {
                        from: "LeadSource",
                        foreignField: "_id",
                        localField: "leadSourceId",
                        as: "leadSource",
                        pipeline: [{ $project: { name: 1 } }],
                    },
                },
                {
                    $unwind: {
                        path: "$leadSource",
                        preserveNullAndEmptyArrays: true,
                    },
                },
                {
                    $project: {
                        name: 1,
                        description: 1,
                        leadSource: "$leadSource.name",
                        isMonthly: 1,
                        startDate: 1,
                        endDate: 1,
                        cost: 1,
                    },
                },
            ]);
            return new OkResponse({ campaigns });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async findOne(_id: string, companyId: string) {
        try {
            const campaign = await this.campaignModel
                .findOne({ _id, companyId })
                .populate("leadSourceId", "name", "LeadSource");
            if (!campaign) throw new NotFoundException("Campaign not found");

            return new OkResponse({ campaign });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     * api to check if campaign exists
     * @param _id of campaign
     * @param companyId
     */
    async exists(_id: string, companyId: string) {
        try {
            const campaign = await this.campaignModel.exists({ _id, companyId });
            if (!campaign) throw new NotFoundException("Campaign not found");
            return campaign;
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async update(_id: string, companyId: string, updateCampaignDto: UpdateCampaignDto) {
        try {
            await this.exists(_id, companyId);

            // Create a copy of the DTO to avoid modifying the original
            const updateData: any = { ...updateCampaignDto };

            // Handle the case where we're updating with a single month cost via actualCost field
            if (updateCampaignDto.actualCost) {
                // Get the current campaign to check existing monthlyActualCosts
                const currentCampaign = await this.campaignModel.findOne({ _id, companyId });

                if (currentCampaign) {
                    // Initialize the array if it doesn't exist
                    const existingMonthlyCosts = currentCampaign.actualCost || [];

                    // Create a new array to avoid modifying the original
                    const updatedMonthlyCosts = [...existingMonthlyCosts];

                    // Check if there's an existing entry for this month/year
                    const existingIndex = updatedMonthlyCosts.findIndex(
                        (cost) =>
                            cost.month === updateCampaignDto.actualCost.month &&
                            cost.year === updateCampaignDto.actualCost.year,
                    );

                    if (existingIndex >= 0) {
                        // Update existing entry
                        updatedMonthlyCosts[existingIndex] = {
                            month: updateCampaignDto.actualCost.month,
                            year: updateCampaignDto.actualCost.year,
                            cost: updateCampaignDto.actualCost.cost,
                        };
                    } else {
                        // Add new entry
                        updatedMonthlyCosts.push({
                            month: updateCampaignDto.actualCost.month,
                            year: updateCampaignDto.actualCost.year,
                            cost: updateCampaignDto.actualCost.cost,
                        });
                    }

                    // Set the monthlyActualCosts in the update data
                    updateData.actualCost = updatedMonthlyCosts;
                }
            }

            const { modifiedCount } = await this.campaignModel.updateOne(
                {
                    _id,
                    companyId,
                },
                { $set: updateData },
            );

            if (modifiedCount === 0)
                throw new HttpException("Failed to update changes!", HttpStatus.NOT_MODIFIED);

            return new CreatedResponse({ message: "Campaign updated successfully!" });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async remove(_id: string, companyId: string) {
        try {
            await this.exists(_id, companyId);
            const { modifiedCount } = await this.campaignModel.updateOne(
                {
                    _id,
                    companyId,
                    deleted: false,
                },
                { $set: { deleted: true } },
            );

            if (modifiedCount === 0)
                throw new HttpException("Failed to update changes!", HttpStatus.NOT_MODIFIED);

            return new CreatedResponse({ message: "Campaign deleted successfully!" });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async restore(_id: string, companyId: string) {
        try {
            await this.exists(_id, companyId);
            const { modifiedCount } = await this.campaignModel.updateOne(
                {
                    _id,
                    companyId,
                    deleted: true,
                },
                { $set: { deleted: false } },
            );

            if (modifiedCount === 0)
                throw new HttpException("Failed to update changes!", HttpStatus.NOT_MODIFIED);

            return new CreatedResponse({ message: "Campaign restored successfully!" });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }
}
