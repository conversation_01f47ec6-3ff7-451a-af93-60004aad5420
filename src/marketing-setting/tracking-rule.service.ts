import { HttpException, HttpStatus, Injectable, InternalServerErrorException } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";
import { PaginationRequestDto } from "src/company/dto/pagination-request.dto";
import CreatedResponse from "src/shared/http/response/created.http";
import NoContentResponse from "src/shared/http/response/no-content.http";
import OkResponse from "src/shared/http/response/ok.http";
import { CreateTrackingRuleDto } from "./dto/create-tracking-rule.dto";
import { UpdateTrackingRuleDto } from "./dto/update-tracking-rule.dto";
import { GetTrackingRuleDto } from "./dto/fetch-tracking-rule.dto";
import { TrackingRuleDocument } from "./schema/tracking-rule.schema";
import { LeadSourceDocument } from "./schema/lead-source.schema";
import { CampaignDocument } from "./schema/campaign.schema";

@Injectable()
export class TrackingRuleService {
    constructor(
        @InjectModel("TrackingRule") private readonly trackingRuleModel: Model<TrackingRuleDocument>,
        @InjectModel("LeadSource") private readonly leadSourceModel: Model<LeadSourceDocument>,
        @InjectModel("Campaign") private readonly campaignModel: Model<CampaignDocument>,
    ) {}

    /**
     * Creates a new lead source rule
     * @param companyId - Company ID
     * @param memberId - Member ID creating the rule
     * @param createTrackingRuleDto - Rule data
     * @returns Created response
     */
    async createLeadSourceRule(
        companyId: string,
        memberId: string,
        createTrackingRuleDto: CreateTrackingRuleDto,
    ) {
        try {
            const { leadSourceId, campaignId, conditions } = createTrackingRuleDto;

            if (conditions.length === 0) {
                throw new HttpException("Can not create rule with no conditions", HttpStatus.BAD_REQUEST);
            }

            // Verify lead source exists
            if (leadSourceId) {
                const leadSource = await this.leadSourceModel.exists({
                    _id: createTrackingRuleDto.leadSourceId,
                    companyId,
                    deleted: false,
                });

                if (!leadSource) {
                    throw new HttpException("Lead source not found", HttpStatus.NOT_FOUND);
                }
            }
            if (campaignId) {
                const campaign = await this.campaignModel.exists({
                    _id: createTrackingRuleDto.campaignId,
                    companyId,
                    deleted: false,
                });

                if (!campaign) {
                    throw new HttpException("Campaign not found", HttpStatus.NOT_FOUND);
                }
            }

            // Check for existing rule with same lead source or campaign
            // const existingRule = await this.trackingRuleModel.exists({
            //     companyId,
            //     deleted: false,
            //     ...(leadSourceId !== undefined && leadSourceId !== null && { leadSourceId: leadSourceId }),
            //     ...(campaignId !== undefined && campaignId !== null && { campaignId: campaignId }),
            // });

            // if (existingRule) {
            //     throw new HttpException(
            //         "Rule already exists for this leadsource or campaign",
            //         HttpStatus.BAD_REQUEST,
            //     );
            // }

            // Check if ALL conditions match an existing rule's ALL conditions
            if (conditions && conditions.length > 0) {
                const existingRules = await this.trackingRuleModel
                    .find({
                        companyId,
                        deleted: false,
                        conditions: { $exists: true, $ne: [] },
                    })
                    .select("conditions");

                const duplicateRule = existingRules.find((existingRule) => {
                    // Check if both rules have the same number of conditions
                    if (existingRule.conditions.length !== conditions.length) {
                        return false;
                    }

                    // Check if ALL conditions in the new rule match ALL conditions in the existing rule
                    return (
                        conditions.every((newCondition) => {
                            return existingRule.conditions.some((existingCondition) => {
                                return (
                                    existingCondition.field.toLowerCase() ===
                                        newCondition.field.toLowerCase() &&
                                    String(existingCondition.value).toLowerCase() ===
                                        String(newCondition.value).toLowerCase()
                                );
                            });
                        }) &&
                        existingRule.conditions.every((existingCondition) => {
                            return conditions.some((newCondition) => {
                                return (
                                    existingCondition.field.toLowerCase() ===
                                        newCondition.field.toLowerCase() &&
                                    String(existingCondition.value).toLowerCase() ===
                                        String(newCondition.value).toLowerCase()
                                );
                            });
                        })
                    );
                });

                if (duplicateRule) {
                    throw new HttpException(
                        "A tracking rule with identical conditions already exists",
                        HttpStatus.BAD_REQUEST,
                    );
                }
            }

            createTrackingRuleDto = this.sanitizeData(createTrackingRuleDto);

            // No need to check for duplicate names since the schema doesn't have a name field
            const newRule = new this.trackingRuleModel({
                ...createTrackingRuleDto,
                companyId,
                createdBy: memberId,
            });

            await newRule.save();

            return new CreatedResponse({
                message: "Lead source rule created successfully",
                data: newRule,
            });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    private sanitizeData(data: any): any {
        // Handle null/undefined input
        if (data === null || data === undefined) {
            return undefined;
        }

        // Handle arrays
        if (Array.isArray(data)) {
            const filteredArray = data
                .map((item) => this.sanitizeData(item))
                .filter((item) => item !== undefined && item !== null && item !== "");
            return filteredArray.length > 0 ? filteredArray : undefined;
        }

        // Handle objects
        if (typeof data === "object") {
            const sanitizedObj = {};
            for (const [key, value] of Object.entries(data)) {
                const sanitizedValue = this.sanitizeData(value);
                if (sanitizedValue !== undefined && sanitizedValue !== null && sanitizedValue !== "") {
                    sanitizedObj[key] = sanitizedValue;
                }
            }
            return Object.keys(sanitizedObj).length > 0 ? sanitizedObj : undefined;
        }

        // Handle primitive values
        return data === "" ? undefined : data;
    }

    /**
     * Get all lead source rules with pagination and filtering
     * @param companyId - Company ID
     * @param paginationDto - Pagination parameters
     * @param filterDto - Filter parameters
     * @returns Paginated rules
     */
    async getLeadSourceRules(
        companyId: string,
        paginationDto: PaginationRequestDto,
        filterDto: GetTrackingRuleDto,
    ) {
        try {
            const { skip = 1, limit = 10 } = paginationDto;
            const { leadSourceId, isActive, includeDeleted = false } = filterDto;

            const filter: any = { companyId };

            if (!includeDeleted) {
                filter.deleted = false;
            }

            if (leadSourceId) {
                filter.leadSourceId = leadSourceId;
            }

            if (isActive !== undefined) {
                filter.isActive = isActive;
            }

            const skips = (skip - 1) * limit;

            const [rules, total] = await Promise.all([
                this.trackingRuleModel
                    .find(filter)
                    .populate("leadSourceId", "name description")
                    .sort({ createdAt: -1 })
                    .skip(skips)
                    .limit(limit)
                    .lean(),
                this.trackingRuleModel.countDocuments(filter),
            ]);

            return new OkResponse({
                message: "Lead source rules retrieved successfully",
                data: {
                    rules,
                    pagination: {
                        page: skip,
                        limit,
                        total,
                        totalPages: Math.ceil(total / limit),
                    },
                },
            });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     * Get a single lead source rule by ID
     * @param companyId - Company ID
     * @param ruleId - Rule ID
     * @returns Rule data
     */
    async getLeadSourceRuleById(companyId: string, ruleId: string) {
        try {
            const rule = await this.trackingRuleModel
                .findOne({ _id: ruleId, companyId, deleted: false })
                .populate("leadSourceId", "name description");

            if (!rule) {
                throw new HttpException("Tracking rule has been deleted", HttpStatus.NOT_FOUND);
            }

            return new OkResponse({
                message: "Lead source rule retrieved successfully",
                data: rule,
            });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     * Update a lead source rule
     * @param companyId - Company ID
     * @param ruleId - Rule ID
     * @param updateTrackingRuleDto - Update data
     * @returns Updated rule
     */
    async updateLeadSourceRule(
        companyId: string,
        ruleId: string,
        updateTrackingRuleDto: UpdateTrackingRuleDto,
    ) {
        try {
            const { leadSourceId, campaignId, conditions } = updateTrackingRuleDto;

            // Verify rule exists
            const rule = await this.trackingRuleModel.findOne({
                _id: ruleId,
                companyId,
                deleted: false,
            });

            if (!rule) {
                throw new HttpException("Lead source rule not found", HttpStatus.NOT_FOUND);
            }

            // Verify if the tracking rule with same lead source and campaign exists
            // const ruleWithLeadSource = await this.trackingRuleModel.exists({
            //     _id: { $ne: ruleId },
            //     companyId,
            //     ...(leadSourceId !== undefined && leadSourceId !== null && { leadSourceId: leadSourceId }),
            //     ...(campaignId !== undefined && campaignId !== null && { campaignId: campaignId }),
            //     deleted: false,
            // });

            // if (ruleWithLeadSource) {
            //     throw new HttpException(
            //         "Rule already exists for this leadsource or campaign",
            //         HttpStatus.BAD_REQUEST,
            //     );
            // }

            // Check if ALL conditions match an existing rule's ALL conditions (excluding current rule)
            if (conditions && conditions.length > 0) {
                const existingRules = await this.trackingRuleModel
                    .find({
                        _id: { $ne: ruleId },
                        companyId,
                        deleted: false,
                        conditions: { $exists: true, $ne: [] },
                    })
                    .select("conditions");

                const duplicateRule = existingRules.find((existingRule) => {
                    // Check if both rules have the same number of conditions
                    if (existingRule.conditions.length !== conditions.length) {
                        return false;
                    }

                    // Check if ALL conditions in the updated rule match ALL conditions in the existing rule
                    return (
                        conditions.every((newCondition) => {
                            return existingRule.conditions.some((existingCondition) => {
                                return (
                                    existingCondition.field.toLowerCase() ===
                                        newCondition.field.toLowerCase() &&
                                    String(existingCondition.value).toLowerCase() ===
                                        String(newCondition.value).toLowerCase()
                                );
                            });
                        }) &&
                        existingRule.conditions.every((existingCondition) => {
                            return conditions.some((newCondition) => {
                                return (
                                    existingCondition.field.toLowerCase() ===
                                        newCondition.field.toLowerCase() &&
                                    String(existingCondition.value).toLowerCase() ===
                                        String(newCondition.value).toLowerCase()
                                );
                            });
                        })
                    );
                });

                if (duplicateRule) {
                    throw new HttpException(
                        "A tracking rule with identical conditions already exists",
                        HttpStatus.BAD_REQUEST,
                    );
                }
            }

            // Update only provided fields
            const updateData: any = {};
            Object.keys(updateTrackingRuleDto).forEach((key) => {
                if (updateTrackingRuleDto[key] !== undefined) {
                    updateData[key] = updateTrackingRuleDto[key];
                }
            });

            const updatedRule = await this.trackingRuleModel.findByIdAndUpdate(ruleId, updateData, {
                new: true,
            });

            return new OkResponse({
                message: "Lead source rule updated successfully",
                data: updatedRule,
            });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     * Delete a lead source rule (soft delete)
     * @param companyId - Company ID
     * @param deleteLeadSourceRuleDto - Delete data
     * @returns No content response
     */
    async deleteLeadSourceRule(companyId: string, ruleId: string) {
        try {
            const rule = await this.trackingRuleModel.findOne({
                _id: ruleId,
                companyId,
                deleted: false,
            });

            if (!rule) {
                throw new HttpException("Lead source rule not found", HttpStatus.NOT_FOUND);
            }

            await this.trackingRuleModel.findByIdAndUpdate(ruleId, {
                deleted: true,
            });

            return new NoContentResponse({
                message: "Lead source rule deleted successfully",
            });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     * Restore a deleted lead source rule
     * @param companyId - Company ID
     * @param restoreLeadSourceRuleDto - Restore data
     * @returns Restored rule
     */
    async restoreLeadSourceRule(companyId: string, ruleId: string) {
        try {
            const rule = await this.trackingRuleModel.findOne({
                _id: ruleId,
                companyId,
                deleted: true,
            });

            if (!rule) {
                throw new HttpException("Deleted lead source rule not found", HttpStatus.NOT_FOUND);
            }

            if (!rule) {
                throw new HttpException("Deleted trackingrule not found", HttpStatus.NOT_FOUND);
            }

            // Verify if the tracking rule with same lead source and campaign exists
            // if (rule.leadSourceId) {
            //     const ruleWithLeadSourceAndCampaign = await this.trackingRuleModel.exists({
            //         _id: { $ne: ruleId },
            //         companyId,
            //         deleted: false,
            //         leadSourceId: rule.leadSourceId,
            //         ...(rule?.campaignId && { campaignId: rule.campaignId }),
            //     });

            //     if (ruleWithLeadSourceAndCampaign) {
            //         throw new HttpException(
            //             "Rule already exists for this leadsource and campaign",
            //             HttpStatus.BAD_REQUEST,
            //         );
            //     }
            // }

            // Check if conditions are already used in other rules
            // Check if ALL conditions match an existing rule's ALL conditions
            if (rule?.conditions && rule.conditions.length > 0) {
                const existingRules = await this.trackingRuleModel
                    .find({
                        companyId,
                        deleted: false,
                        conditions: { $exists: true, $ne: [] },
                    })
                    .select("conditions");

                const duplicateRule = existingRules.find((existingRule) => {
                    // Check if both rules have the same number of conditions
                    if (existingRule.conditions.length !== rule.conditions.length) {
                        return false;
                    }

                    // Check if ALL conditions in the new rule match ALL conditions in the existing rule
                    return (
                        rule.conditions.every((newCondition) => {
                            return existingRule.conditions.some((existingCondition) => {
                                return (
                                    existingCondition.field.toLowerCase() ===
                                        newCondition.field.toLowerCase() &&
                                    String(existingCondition.value).toLowerCase() ===
                                        String(newCondition.value).toLowerCase()
                                );
                            });
                        }) &&
                        existingRule.conditions.every((existingCondition) => {
                            return rule.conditions.some((newCondition) => {
                                return (
                                    existingCondition.field.toLowerCase() ===
                                        newCondition.field.toLowerCase() &&
                                    String(existingCondition.value).toLowerCase() ===
                                        String(newCondition.value).toLowerCase()
                                );
                            });
                        })
                    );
                });

                if (duplicateRule) {
                    throw new HttpException(
                        "A tracking rule with identical conditions already exists",
                        HttpStatus.BAD_REQUEST,
                    );
                }
            }

            if (rule?.leadSourceId) {
                const leadSource = await this.leadSourceModel.exists({
                    _id: rule.leadSourceId,
                    companyId,
                    deleted: false,
                });

                if (!leadSource) {
                    throw new HttpException("Lead source not found", HttpStatus.NOT_FOUND);
                }
            }
            if (rule?.campaignId) {
                const campaign = await this.campaignModel.exists({
                    _id: rule.campaignId,
                    companyId,
                    deleted: false,
                });

                if (!campaign) {
                    throw new HttpException("Campaign not found", HttpStatus.NOT_FOUND);
                }
            }

            const restoredRule = await this.trackingRuleModel
                .findByIdAndUpdate(ruleId, { deleted: false }, { new: true })
                .populate("leadSourceId", "name description")
                .lean();

            return new OkResponse({
                message: "Lead source rule restored successfully",
                data: restoredRule,
            });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     * Find matching lead source for a contact based on rules
     * @param companyId - Company ID
     * @param trackingData - Contact tracking data to match against
     * @returns Lead source ID if match found, null otherwise
     */
    async findMatchingLeadSource(companyId: string, tracking: any) {
        try {
            // Get all active rules sorted by creation date (newest first)
            const rules = await this.trackingRuleModel
                .find({
                    companyId,
                    deleted: false,
                    isActive: true,
                    conditions: { $exists: true, $ne: [] },
                })
                .select("conditions leadSourceId campaignId");

            const matchingRule = rules.find((rule) => {
                // Check if conditions array exists and is not empty
                if (!rule.conditions || rule.conditions.length === 0) {
                    return false;
                }

                // ALL conditions must match for the rule to be selected
                const allConditionsMatch = rule.conditions.every((condition) => {
                    const matches = this.matchesCondition(tracking, condition);
                    return matches;
                });
                return allConditionsMatch;
            });

            if (matchingRule && Object.keys(matchingRule).length !== 0) {
                return {
                    trackingRuleId: matchingRule._id,
                    leadSourceId: matchingRule?.leadSourceId,
                    campaignId: matchingRule?.campaignId,
                };
            }

            return null;
        } catch (error: any) {
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     * Check if a tracking field matches a condition
     * @param tracking - Tracking data object
     * @param condition - Condition to match against
     * @returns True if condition matches, false otherwise
     */
    private matchesCondition(
        tracking: any,
        condition: { field: string; value: string | string[] | number | boolean },
    ): boolean {
        const trackingValue = tracking[condition.field];
        const conditionValue = condition.value;

        // Handle null/undefined values
        if (trackingValue == null && conditionValue == null) {
            return true;
        }
        if (trackingValue == null || conditionValue == null) {
            return false;
        }

        // Handle empty string cases
        if (trackingValue === "" && conditionValue === "") {
            return true;
        }
        if (trackingValue === "" || conditionValue === "") {
            return false;
        }

        // Convert both values to strings for comparison to handle type differences
        const trackingStr = String(trackingValue).trim();
        const conditionStr = String(conditionValue).trim();

        // Perform case-insensitive comparison
        return trackingStr.toLowerCase() === conditionStr.toLowerCase();
    }

    /**
     * Auto-assign lead source for Zapier leads
     * This method is specifically designed to be called from createLeadFromZapier
     * @param companyId - Company ID
     * @param tracking - Contact tracking data information
     * @returns Lead source ID & campaign ID if match found, null otherwise
     */
    async autoAssignLeadSourceAndCampaignForZapier(companyId: string, tracking: any): Promise<any> {
        try {
            // Find matching lead source using existing logic
            const matchingRule = await this.findMatchingLeadSource(companyId, tracking);

            if (matchingRule) {
                return matchingRule;
            } else {
                return null;
            }
        } catch (error: any) {
            console.error(`Error in auto-assign lead source: ${error.message}`);
            // Don't throw error to avoid breaking the lead creation process
            return null;
        }
    }
}
