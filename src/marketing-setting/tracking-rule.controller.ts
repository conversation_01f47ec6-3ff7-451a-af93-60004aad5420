import { Body, Controller, Delete, Get, Param, ParseUUIDPipe, Patch, Post, Query } from "@nestjs/common";
import {
    ApiBearerAuth,
    ApiInternalServerErrorResponse,
    ApiNotFoundResponse,
    ApiOperation,
    ApiTags,
} from "@nestjs/swagger";
import { Roles } from "src/auth/guards/auth.guard";
import { PaginationRequestDto } from "src/company/dto/pagination-request.dto";
import { UserRolesEnum } from "src/company/enum/role.enum";
import HttpResponse from "src/shared/http/response/response.http";
import { CreateTrackingRuleDto } from "./dto/create-tracking-rule.dto";
import { UpdateTrackingRuleDto } from "./dto/update-tracking-rule.dto";
import { GetTrackingRuleDto } from "./dto/fetch-tracking-rule.dto";
import { TrackingRuleService } from "./tracking-rule.service";
import { Auth, GetUser } from "src/auth/decorator/auth.decorator";
import { JwtUserPayload } from "src/auth/interface/auth.interface";

@ApiBearerAuth()
@ApiTags("TrackingRule")
@Auth()
@Controller({ path: "tracking-rule", version: "1" })
export class TrackingRuleController {
    constructor(private readonly trackingRuleService: TrackingRuleService) {}

    /**
     * Creates a new lead source rule
     * @param user - The authenticated user
     * @param createLeadSourceRuleDto - The DTO containing the data for the new rule
     * @returns A Promise that resolves to an HTTP response
     */
    @ApiOperation({ summary: "Create Lead Source Rule" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Post("create")
    async createLeadSourceRule(
        @GetUser() user: JwtUserPayload,
        @Body() createLeadSourceRuleDto: CreateTrackingRuleDto,
    ): Promise<HttpResponse> {
        return this.trackingRuleService.createLeadSourceRule(
            user.companyId,
            user.memberId,
            createLeadSourceRuleDto,
        );
    }

    /**
     * Get all lead source rules with pagination and filtering
     * @param user - The authenticated user
     * @param paginationDto - Pagination parameters
     * @param filterDto - Filter parameters
     * @returns A Promise that resolves to an HTTP response
     */
    @ApiOperation({ summary: "Get Lead Source Rules" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Get("list")
    async getLeadSourceRules(
        @GetUser() user: JwtUserPayload,
        @Query() paginationDto: PaginationRequestDto,
        @Query() filterDto: GetTrackingRuleDto,
    ): Promise<HttpResponse> {
        return this.trackingRuleService.getLeadSourceRules(user.companyId, paginationDto, filterDto);
    }

    /**
     * Get a single lead source rule by ID
     * @param user - The authenticated user
     * @param ruleId - Rule ID
     * @returns A Promise that resolves to an HTTP response
     */
    @ApiOperation({ summary: "Get Lead Source Rule by ID" })
    @ApiNotFoundResponse({ description: "Lead source rule not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Get("id/:ruleId")
    async getLeadSourceRuleById(
        @GetUser() user: JwtUserPayload,
        @Param("ruleId", ParseUUIDPipe) ruleId: string,
    ): Promise<HttpResponse> {
        return this.trackingRuleService.getLeadSourceRuleById(user.companyId, ruleId);
    }

    /**
     * Update a lead source rule
     * @param user - The authenticated user
     * @param ruleId - Rule ID
     * @param updateLeadSourceRuleDto - Update data
     * @returns A Promise that resolves to an HTTP response
     */
    @ApiOperation({ summary: "Update Lead Source Rule" })
    @ApiNotFoundResponse({ description: "Lead source rule not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Patch("id/:ruleId")
    async updateLeadSourceRule(
        @GetUser() user: JwtUserPayload,
        @Param("ruleId", ParseUUIDPipe) ruleId: string,
        @Body() updateLeadSourceRuleDto: UpdateTrackingRuleDto,
    ): Promise<HttpResponse> {
        return this.trackingRuleService.updateLeadSourceRule(user.companyId, ruleId, updateLeadSourceRuleDto);
    }

    /**
     * Delete a lead source rule (soft delete)
     * @param user - The authenticated user
     * @param deleteLeadSourceRuleDto - Delete data
     * @returns A Promise that resolves to an HTTP response
     */
    @ApiOperation({ summary: "Delete Lead Source Rule" })
    @ApiNotFoundResponse({ description: "Lead source rule not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Delete("id/:ruleId")
    async deleteLeadSourceRule(
        @GetUser() user: JwtUserPayload,
        @Param("ruleId", ParseUUIDPipe) ruleId: string,
    ): Promise<HttpResponse> {
        return this.trackingRuleService.deleteLeadSourceRule(user.companyId, ruleId);
    }

    /**
     * Restore a deleted lead source rule
     * @param user - The authenticated user
     * @param restoreLeadSourceRuleDto - Restore data
     * @returns A Promise that resolves to an HTTP response
     */
    @ApiOperation({ summary: "Restore Lead Source Rule" })
    @ApiNotFoundResponse({ description: "Deleted lead source rule not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Patch("restore/id/:ruleId")
    async restoreLeadSourceRule(
        @GetUser() user: JwtUserPayload,
        @Param("ruleId", ParseUUIDPipe) ruleId: string,
    ): Promise<HttpResponse> {
        return this.trackingRuleService.restoreLeadSourceRule(user.companyId, ruleId);
    }
}
