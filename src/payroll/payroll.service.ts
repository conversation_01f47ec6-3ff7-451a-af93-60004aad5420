import { Injectable, HttpException, HttpStatus, InternalServerErrorException } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import { Model } from "mongoose";
import CreatedResponse from "src/shared/http/response/created.http";
import { PayRollDocument } from "./schema/payroll.schema";
import { CreatePayRollDto } from "./dto/create-payroll.dto";
import OkResponse from "src/shared/http/response/ok.http";
import { UpdatePayRollDto } from "./dto/update-payroll.dto";

@Injectable()
export class PayRollService {
    constructor(@InjectModel("PayRoll") private readonly payRollModel: Model<PayRollDocument>) {}

    async createPayRoll(companyId: string, createPayRollDto: CreatePayRollDto) {
        try {
            const id = randomUUID();
            const createdPayRoll = new this.payRollModel({
                _id: id,
                companyId,
                ...createPayRollDto,
            });
            await createdPayRoll.save();
            return new CreatedResponse({ message: "PayRoll created successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getAllPayRoll(userId: string, companyId: string) {
        try {
            const payrolls = await this.payRollModel.find({ companyId });
            return new OkResponse({ payrolls });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getPayRollById(userId: string, companyId: string, _id: string) {
        try {
            const payroll = await this.payRollModel.findOne({ companyId, _id });
            return new OkResponse({ payroll });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updatePayRoll(userId: string, companyId: string, updatePayRollDto: UpdatePayRollDto) {
        try {
            const payroll = await this.payRollModel.updateOne(
                { companyId, _id: updatePayRollDto.id },
                {
                    $set: {
                        ...updatePayRollDto,
                    },
                },
            );
            return new OkResponse({ payroll });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }
}
