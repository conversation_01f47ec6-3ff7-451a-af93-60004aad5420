import { Modu<PERSON> } from "@nestjs/common";
import { PayRollSchema } from "./schema/payroll.schema";
import { PayRollService } from "./payroll.service";
import { PayRollController } from "./payroll.controller";
import { MongooseModule } from "@nestjs/mongoose";
import { PositionModule } from "src/position/position.module";
import { RoleModule } from "src/role/role.module";

@Module({
    imports: [
        MongooseModule.forFeature([{ name: "PayRoll", schema: PayRollSchema }]),
        // PositionModule,
        // RoleModule,
    ],
    providers: [PayRollService],
    controllers: [PayRollController],
    exports: [PayRollService],
})
export class PayrollModule {}
