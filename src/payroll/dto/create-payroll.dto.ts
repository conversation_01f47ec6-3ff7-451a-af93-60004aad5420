import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsNotEmpty, IsUUID, IsN<PERSON><PERSON> } from "class-validator";
import { Transform } from "class-transformer";

export class CreatePayRollDto {
    // @ApiProperty({ description: "Company", required: true })
    // @IsUUID()
    // @IsNotEmpty()
    // companyId: string;

    @ApiProperty({ description: "payScheduleId", required: true })
    @IsUUID()
    @IsNotEmpty()
    payScheduleId: string;

    @ApiProperty({ description: "Start Pay date", required: true })
    @Transform(({ value }) => new Date(value))
    @IsNotEmpty()
    start: Date;

    @ApiProperty({ description: "End Pay date", required: true })
    @Transform(({ value }) => new Date(value))
    @IsNotEmpty()
    end: Date;

    @ApiProperty({ description: "Pay date", required: true })
    @Transform(({ value }) => new Date(value))
    @IsNotEmpty()
    payDate: Date;

    @ApiPropertyOptional({ description: "netPay" })
    @IsNotEmpty()
    netPay: any;

    @ApiPropertyOptional({ description: "subcontractors" })
    @IsNumber()
    @IsNotEmpty()
    subcontractors: number;

    @ApiProperty({ description: "Created By", required: true })
    @IsUUID()
    @IsNotEmpty()
    createdBy: string;
}
