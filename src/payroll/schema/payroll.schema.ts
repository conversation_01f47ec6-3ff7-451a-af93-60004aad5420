import { Schema, SchemaFactory, Prop } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import mongoose, { Document } from "mongoose";
import { UUIDProp } from "src/shared/decorator/uuid.decorator";

export type PayRollDocument = PayRoll & Document;

@Schema({ timestamps: true, id: false, collection: "PayRoll" })
export class PayRoll {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @UUIDProp()
    companyId: string;

    @UUIDProp()
    payScheduleId: string;

    @Prop({ required: true })
    start: Date;

    @Prop({ required: true })
    end: Date;

    @Prop({ required: true })
    payDate: Date;

    @Prop({ required: true, type: mongoose.Schema.Types.Mixed })
    netPay: any;

    @Prop({ required: true })
    subcontractors: number;

    @UUIDProp()
    createdBy: string;

    @Prop()
    createdAt?: Date;

    @Prop()
    modifiedAt?: Date;
}

export const PayRollSchema = SchemaFactory.createForClass(PayRoll);
