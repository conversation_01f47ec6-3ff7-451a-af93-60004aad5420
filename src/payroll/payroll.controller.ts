import { Controller, Post, Body, Param, Get, Patch, ParseUUIDPipe } from "@nestjs/common";
import {
    ApiBearerAuth,
    ApiTags,
    ApiOperation,
    ApiConflictResponse,
    ApiInternalServerErrorResponse,
} from "@nestjs/swagger";
import { Roles } from "src/auth/guards/auth.guard";
import { UserRolesEnum } from "src/company/enum/role.enum";
import HttpResponse from "src/shared/http/response/response.http";
import { PayRollService } from "./payroll.service";
import { CreatePayRollDto } from "./dto/create-payroll.dto";
import { UpdatePayRollDto } from "./dto/update-payroll.dto";
import { Auth, GetUser } from "src/auth/decorator/auth.decorator";
import { JwtUserPayload } from "src/auth/interface/auth.interface";

@ApiBearerAuth()
@ApiTags("PayRoll")
@Auth()
@ApiInternalServerErrorResponse({ description: "Server Error" })
@Controller({ path: "payroll", version: "1" })
export class PayRollController {
    constructor(private readonly payRollService: PayRollService) {}

    /**
     *Create a new pay Roll for a user.
     *@param userId The ID of the user creating the pay Roll.
     *@param createPayRollDto The DTO containing information about the pay Roll to create.
     *@returns A Promise that resolves to an HttpResponse.
     */
    @ApiOperation({ summary: "Create PayRoll" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Post("create-payroll")
    async createPayRoll(
        @GetUser() user: JwtUserPayload,
        @Body() createPayRollDto: CreatePayRollDto,
    ): Promise<HttpResponse> {
        return this.payRollService.createPayRoll(user.companyId, createPayRollDto);
    }

    /**
     *get all pay Roll.
     *@param userId The ID of the user creating the pay Roll.
     *@Param companyId The ID of the company.
     *@returns A Promise that resolves to an HttpResponse.
     */
    @ApiOperation({ summary: "get PayRoll" })
    @ApiConflictResponse({ description: "PayRoll already exist" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Get("all-payroll")
    async getAllPayRoll(@GetUser() user: JwtUserPayload): Promise<HttpResponse> {
        return this.payRollService.getAllPayRoll(user._id, user.companyId);
    }

    /**
     *get pay Roll by id.
     *@param userId The ID of the user creating the pay Roll.
     *@Param companyId The ID of the company.
     *@Param payrollId The ID of the payroll.
     *@returns A Promise that resolves to an HttpResponse.
     */
    @ApiOperation({ summary: "get PayRoll by id" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Get("payroll-by-id/payroll/:payrollId")
    async getPayRollById(
        @GetUser() user: JwtUserPayload,
        @Param("payrollId", ParseUUIDPipe) payrollId: string,
    ): Promise<HttpResponse> {
        return this.payRollService.getPayRollById(user._id, user.companyId, payrollId);
    }

    /**
     *Update pay Roll by id.
     *@param userId The ID of the user creating the pay Roll.
     *@Param companyId The ID of the company.
     *@Body UpdatePayRollDto.
     *@returns A Promise that resolves to an HttpResponse.
     */
    @ApiOperation({ summary: "Update PayRoll by id" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Patch("update-payroll")
    async updatePayRoll(
        @GetUser() user: JwtUserPayload,
        @Body() updatePayRollDto: UpdatePayRollDto,
    ): Promise<HttpResponse> {
        return this.payRollService.updatePayRoll(user._id, user.companyId, updatePayRollDto);
    }
}
