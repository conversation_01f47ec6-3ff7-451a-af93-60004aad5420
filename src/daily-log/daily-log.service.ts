import { Model } from "mongoose";
import { InjectModel } from "@nestjs/mongoose";
import OkResponse from "src/shared/http/response/ok.http";
import { CreateDailyLogDto } from "./dto/create-daily-log.dto";
import { UpdateDailyLogDto } from "./dto/update-daily-log.dto";
import { DailyLogDocument } from "./schema/daily-log.schema";
import CreatedResponse from "src/shared/http/response/created.http";
import { HttpException, HttpStatus, Injectable, InternalServerErrorException } from "@nestjs/common";
import { OpportunityDocument } from "src/opportunity/schema/opportunity.schema";
import { roundTo2 } from "src/shared/helpers/logics";

@Injectable()
export class DailyLogService {
    constructor(
        @InjectModel("DailyLog") private readonly dailyLogModel: Model<DailyLogDocument>,
        @InjectModel("Opportunity") private readonly opportunityModel: Model<OpportunityDocument>,
    ) {}

    async createDailyLog(companyId: string, createDailyLogDto: CreateDailyLogDto) {
        try {
            const dailyLog = await this.dailyLogModel
                .exists({
                    companyId,
                    crewId: createDailyLogDto.crewId,
                    date: createDailyLogDto.date,
                    deleted: false,
                })
                .exec();
            if (dailyLog) throw new HttpException("DailyLog already exists", HttpStatus.BAD_REQUEST);
            const createdDailyLog = new this.dailyLogModel({
                companyId,
                ...createDailyLogDto,
            });
            await createdDailyLog.save();
            return new CreatedResponse({ message: "Daily Log created successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updateDailyLog(companyId: string, updateDailyLogDto: UpdateDailyLogDto) {
        try {
            //get the daily log to check its date
            const existingLog = await this.dailyLogModel.findOne(
                {
                    _id: updateDailyLogDto.dailyLogId,
                    companyId,
                    deleted: false,
                },
                { date: 1 },
            );

            if (!existingLog) {
                throw new HttpException("DailyLog does not exist", HttpStatus.BAD_REQUEST);
            }

            // Check if the log is more than 30 days old
            const logDate = new Date(existingLog.date);
            const thirtyDaysAgo = new Date();
            thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

            if (logDate < thirtyDaysAgo) {
                throw new HttpException(
                    "Cannot edit daily logs that are more than 30 days old",
                    HttpStatus.BAD_REQUEST,
                );
            }

            const { auditLog, ...updateData } = updateDailyLogDto;
            const dailyLog = await this.dailyLogModel.findOneAndUpdate(
                { _id: updateDailyLogDto.dailyLogId, companyId },
                {
                    $set: {
                        ...updateData,
                    },
                    $push: {
                        auditLog,
                    },
                },
                { new: true },
            );

            return new OkResponse({ message: "Daily Log updated successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getDailyLog(userId: string, companyId: string, crewId: string) {
        try {
            const dailyLog = await this.dailyLogModel.find({ companyId, crewId, deleted: false }).exec();
            if (!dailyLog) throw new HttpException("Daily Log does not exist", HttpStatus.BAD_REQUEST);
            return new OkResponse({ dailyLog });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getDailyLogById(userId: string, companyId: string, _id: string) {
        try {
            const dailyLog = (
                await this.dailyLogModel
                    .aggregate([
                        {
                            $match: {
                                _id,
                                companyId,
                                deleted: { $ne: true },
                            },
                        },
                        { $unwind: "$projects" }, // Unwind projects array
                        {
                            $lookup: {
                                from: "Opportunity",
                                localField: "projects.oppId",
                                foreignField: "_id",
                                as: "opp",
                                pipeline: [{ $project: { PO: 1, num: 1 } }],
                            },
                        },
                        {
                            $unwind: {
                                path: "$opp",
                                preserveNullAndEmptyArrays: true,
                            },
                        },
                        {
                            $addFields: {
                                "projects.oppPO": {
                                    $cond: {
                                        if: { $eq: [{ $type: "$opp" }, "object"] },
                                        then: { $concat: ["$opp.PO", "-", "$opp.num"] },
                                        else: "$projects.oppPO",
                                    },
                                },
                            },
                        },
                        { $unset: "opp" },
                        {
                            $group: {
                                _id: "$_id",
                                projects: { $push: "$projects" },
                                otherFields: { $first: "$$ROOT" }, // Preserve all other fields
                            },
                        },
                        {
                            $replaceRoot: {
                                newRoot: {
                                    $mergeObjects: ["$otherFields", { projects: "$projects" }], // Merge all fields including projects
                                },
                            },
                        },
                    ])
                    .exec()
            )[0];
            if (!dailyLog) throw new HttpException("Daily Log does not exist", HttpStatus.BAD_REQUEST);
            return new OkResponse({ dailyLog });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    //Pull PO list for dropdown in daily log
    async getPOList(userId: string, companyId: string, showAll: boolean, search?: string) {
        try {
            const pipeline: any[] = [
                {
                    $match: {
                        companyId,
                        deleted: { $ne: true },
                        jobStartedDate: { $nin: [null, ""], $exists: true },
                    },
                },
                {
                    $sort: { jobStartedDate: -1 },
                },
                {
                    $lookup: {
                        from: "ProjectType",
                        localField: "acceptedType",
                        foreignField: "_id",
                        as: "type",
                    },
                },
                {
                    $project: {
                        _id: 1,
                        PO: 1,
                        num: 1,
                        zip: 1,
                        jobCompletedDate: 1,
                        type: { $arrayElemAt: ["$type", 0] },
                    },
                },
            ];

            // Add additional $match stage if showAll is false to get only last 1 month completed PO
            if (!showAll) {
                const today = new Date();
                today.setMonth(today.getMonth() - 1);
                pipeline[0].$match.$and = [
                    {
                        $or: [
                            { jobCompletedDate: { $gte: today } },
                            { jobCompletedDate: { $exists: false } },
                        ],
                    },
                ];
            }

            // Add search filter
            if (search) {
                pipeline[0].$match.$or = [
                    { PO: { $regex: search, $options: "i" } },
                    { num: { $regex: search, $options: "i" } },
                    {
                        $expr: {
                            $regexMatch: {
                                input: { $concat: ["$PO", " ", "$num"] },
                                regex: search,
                                options: "i",
                            },
                        },
                    },
                    {
                        $expr: {
                            $regexMatch: {
                                input: { $concat: ["$PO", "-", "$num"] },
                                regex: search,
                                options: "i",
                            },
                        },
                    },
                ];
            }

            const readyOpps = await this.opportunityModel.aggregate(pipeline);

            const mappedProjects = readyOpps.map((opp) => ({
                oppId: opp._id,
                po: opp.PO + "-" + opp.num,
                type: opp.type?.typeReplacement,
                typeName: opp.type?.name,
                zipCode: opp?.zip,
                jobCompletedDate: opp?.jobCompletedDate,
            }));

            return new OkResponse({ projects: mappedProjects });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async projectTotalSQ(userId: string, companyId: string, oppId: string, projectType: boolean) {
        try {
            const opps = (
                await this.opportunityModel.aggregate([
                    {
                        $match: {
                            _id: oppId,
                            companyId,
                            deleted: { $ne: true },
                        },
                    },
                    {
                        $lookup: {
                            from: "Order",
                            localField: "orderId",
                            foreignField: "_id",
                            as: "order",
                        },
                    },
                    {
                        $unwind: {
                            path: "$order",
                            preserveNullAndEmptyArrays: true,
                        },
                    },
                    {
                        $unwind: {
                            path: "$order.projects",
                            preserveNullAndEmptyArrays: true,
                        },
                    },
                    {
                        $lookup: {
                            from: "Project",
                            localField: "order.projects.projectId",
                            foreignField: "_id",
                            as: "projectDetails",
                        },
                    },
                    {
                        $group: {
                            _id: "$_id",
                            PO: { $first: "$PO" },
                            projects: { $push: "$projectDetails" },
                        },
                    },
                    {
                        $project: {
                            _id: 1,
                            PO: 1,
                            projects: {
                                $reduce: {
                                    input: "$projects",
                                    initialValue: [],
                                    in: { $concatArrays: ["$$value", "$$this"] },
                                },
                            },
                        },
                    },
                ])
            )[0];

            //getting all logs where oppId is present
            const logs = await this.dailyLogModel.find({
                "projects.oppId": oppId,
                deleted: { $ne: true },
            });

            const SQ: any = {
                doneTearSQ: 0,
                doneRoofSQ: 0,
                totalTearSQ: 0,
                totalRoofSQ: 0,
                percentDone: 0,
                plywoodReplaced: 0,
                nonBillPly: 0,
                manHours: 0,
                nonBillHours: 0,
                materialCosts: 0,

                //for subcontractor
                instSheet: 0,
                rmvSheet: 0,
                tExtraFelt: 0,
                tCutRV: 0,
                tCutCan: 0,
                tVentPlug: 0,
                tBaffle: 0,
                install50Yr: 0,
                chimCF: 0,
                replaceSkylight: 0,
                eyebrows: 0,
                bayWindows: 0,
                addManHours: 0,
                addMaterials: 0,
                instFascia: 0,
                rmvFascia: 0,
                handLoadSQ: 0,
                highRoof: 0,
            };

            logs.forEach((log) => {
                log.projects.forEach((project) => {
                    if (project.oppId === oppId) {
                        SQ.plywoodReplaced += Number(project.plywoodReplaced) || 0;
                        SQ.nonBillPly += Number(project.nonBillPly) || 0;
                        SQ.manHours += Number(project.manHours) || 0;
                        SQ.nonBillHours += Number(project.nonBillHours) || 0;
                        SQ.materialCosts += Number(project.materialCosts) || 0;

                        SQ.instSheet += Number(project?.instSheet) || 0;
                        SQ.rmvSheet += Number(project?.rmvSheet) || 0;
                        SQ.tExtraFelt += Number(project?.tExtraFelt) || 0;
                        SQ.tCutRV += Number(project?.tCutRV) || 0;
                        SQ.tCutCan += Number(project?.tCutCan) || 0;
                        SQ.tVentPlug += Number(project?.tVentPlug) || 0;
                        SQ.tBaffle += Number(project?.tBaffle) || 0;
                        SQ.install50Yr += Number(project?.install50Yr) || 0;
                        SQ.chimCF += Number(project?.chimCF) || 0;
                        SQ.replaceSkylight += Number(project?.replaceSkylight) || 0;
                        SQ.eyebrows += Number(project?.eyebrows) || 0;
                        SQ.bayWindows += Number(project?.bayWindows) || 0;
                        SQ.addManHours += Number(project?.addManHours) || 0;
                        SQ.addMaterials += Number(project?.addMaterials) || 0;
                        SQ.instFascia += Number(project?.instFascia) || 0;
                        SQ.rmvFascia += Number(project?.rmvFascia) || 0;
                        SQ.handLoadSQ += Number(project?.handLoadSQ) || 0;
                        SQ.highRoof += Number(project?.highRoof) || 0;

                        if (projectType) {
                            SQ.doneTearSQ += project.tearOffSQ || project.tearOffDone || 0;
                            SQ.doneRoofSQ += project.roofingSQ || project.roofingDone || 0;
                        } else {
                            SQ.percentDone += Number(project.percentDone) || 0;
                        }
                        SQ.doneTearSQ = roundTo2(SQ.doneTearSQ);
                        SQ.doneRoofSQ = roundTo2(SQ.doneRoofSQ);
                        SQ.percentDone = roundTo2(SQ.percentDone);
                    }
                });
            });

            // For TotalSQ calculation
            // if (projectType) {
            opps?.projects?.forEach((project) => {
                const area = project?.customData?.roofSQ;
                SQ.totalRoofSQ += roundTo2(
                    (area?.instFlat || 0) + (area?.instLow || 0) + (area?.instSteep || 0),
                );
                SQ.totalTearSQ += roundTo2(
                    (area?.rmvFlat || 0) + (area?.rmvLow || 0) + (area?.rmvSteep || 0),
                );
            });
            // }

            return new OkResponse({ SQ });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async dailylogExtraWorkForOpp(companyId: string, oppId: string) {
        try {
            const [[opp], logs] = await Promise.all([
                this.opportunityModel.aggregate([
                    {
                        $match: {
                            deleted: false,
                            companyId,
                            _id: oppId,
                        },
                    },

                    {
                        $lookup: {
                            from: "Order",
                            foreignField: "_id",
                            localField: "orderId",
                            pipeline: [{ $project: { projectPriceId: 1 } }],
                            as: "order",
                        },
                    },
                    {
                        $unwind: {
                            path: "$order",
                            preserveNullAndEmptyArrays: true,
                        },
                    },
                    {
                        $lookup: {
                            from: "Price",
                            foreignField: "_id",
                            localField: "order.projectPriceId",
                            as: "projectPrice",
                        },
                    },
                    {
                        $unwind: {
                            path: "$projectPrice",
                            preserveNullAndEmptyArrays: true,
                        },
                    },
                    {
                        $project: {
                            projectPrice: 1,
                            changeOrderValue: 1,
                        },
                    },
                ]),
                this.dailyLogModel.aggregate([
                    {
                        $match: {
                            companyId,
                            deleted: false,
                            projects: { $elemMatch: { oppId } },
                        },
                    },
                    {
                        $sort: {
                            date: -1,
                        },
                    },
                ]),
            ]);

            const price = opp.projectPrice;

            const hourCost = price.variables?.manHourRate;
            const plyCost = price.variables?.plywoodRate;
            const matMarkup = price.variables?.matMarkup * 100;

            let hours = opp.extraWork?.hours || 0;
            let nonBillHours = opp.extraWork?.nonBillHours || 0;
            let ply = opp.extraWork?.ply || 0;
            let nonBillPly = opp.extraWork?.nonBillPly || 0;
            let mats = opp.extraWork?.mats || 0;
            const changeOrderValue = opp?.changeOrderValue || 0;

            const dailyLogs = logs.map((log) => {
                const projectLog = log.projects.find((project) => {
                    return project.oppId === opp._id;
                });
                projectLog.crewId = log.crewId;
                projectLog.logDate = log.date;
                projectLog.logId = log._id;
                projectLog.roofingDone = projectLog?.roofingDone || projectLog?.roofingSQ || 0;
                projectLog.tearOffDone = projectLog?.tearOffDone || projectLog?.tearOffSQ || 0;
                projectLog.manHours = projectLog?.manHours || projectLog?.addManHours || 0;
                projectLog.plywoodReplaced = projectLog?.plywoodReplaced || projectLog?.instSheet || 0;
                projectLog.materialCosts = projectLog?.materialCosts || projectLog?.addMaterials || 0;
                projectLog.nonBillPly = projectLog?.nonBillPly || 0;
                hours += projectLog.manHours || 0;
                nonBillHours += projectLog.nonBillHours || 0;
                ply += projectLog?.plywoodReplaced || 0;
                nonBillPly += projectLog.nonBillPly || 0;
                mats += projectLog.materialCosts || 0;
                return projectLog;
            });
            const totalHoursCost = roundTo2(hours * hourCost);
            const totalNonBillHoursCost = roundTo2(nonBillHours * hourCost);
            const totalPlyCost = roundTo2(ply * 32 * plyCost);
            const totalNonBillPlyCost = roundTo2(nonBillPly * 32 * plyCost);
            const totalMatCost = roundTo2(mats * (1 + matMarkup / 100));
            const extras = {
                hours,
                hourCost,
                totalHoursCost,
                nonBillHours,
                totalNonBillHoursCost,
                ply,
                plyCost,
                totalPlyCost,
                totalNonBillPlyCost,
                mats,
                matMarkup,
                totalMatCost,
                nonBillPly,
                billTotal: roundTo2(totalHoursCost + totalPlyCost + totalMatCost + changeOrderValue),
                nonBillTotal: roundTo2(totalNonBillHoursCost + totalNonBillPlyCost),
                changeOrderValue,
            };

            return new OkResponse({ dailyLogs, extras });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }
}
