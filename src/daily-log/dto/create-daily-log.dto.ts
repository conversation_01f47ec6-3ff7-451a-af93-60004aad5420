import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { WorkedEnum } from "../enum/worked.enum";
import { IsDate, IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString, IsUUID } from "class-validator";
import { Transform } from "class-transformer";

export class CreateDailyLogDto {
    // @ApiProperty({ description: "Company Id", required: true })
    // @IsUUID()
    // @IsNotEmpty()
    // companyId: string;

    @ApiProperty({ description: "Crew Id", required: true })
    @IsUUID()
    @IsNotEmpty()
    crewId: string;

    @ApiProperty({ description: "Date", required: true })
    @IsDate()
    @Transform(({ value }) => new Date(value))
    @IsNotEmpty()
    date: string;

    @ApiPropertyOptional({ description: "Worked" })
    @IsString()
    @IsOptional()
    @IsEnum(WorkedEnum)
    worked?: WorkedEnum;

    @ApiPropertyOptional({ description: "High Temp" })
    @IsNumber()
    @IsOptional()
    highTemp?: number;

    @ApiPropertyOptional({ description: "Low Temp" })
    @IsNumber()
    @IsOptional()
    lowTemp?: number;

    @ApiProperty({ description: "Projects", required: true })
    @IsNotEmpty()
    projects: any;

    @ApiProperty({ description: "Created By", required: true })
    @IsUUID()
    @IsNotEmpty()
    createdBy: string;

    @ApiProperty({ description: "AuditLog", required: true })
    @IsNotEmpty()
    auditLog: any;

    @ApiPropertyOptional({ description: "max wind speed" })
    @IsNumber()
    @IsOptional()
    maxwind_mph?: number;

    @ApiPropertyOptional({ description: "Precipitation" })
    @IsNumber()
    @IsOptional()
    totalprecip_in?: number;
}
