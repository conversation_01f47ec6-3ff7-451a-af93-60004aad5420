import {
    Api<PERSON><PERSON>ation,
    ApiConflictResponse,
    ApiInternalServerErrorResponse,
    ApiTags,
    ApiNotFoundResponse,
    ApiUnauthorizedResponse,
    ApiBearerAuth,
    ApiQuery,
} from "@nestjs/swagger";
import { Body, Controller, Get, Param, ParseUUIDPipe, Patch, Post, Query, UseGuards } from "@nestjs/common";
import HttpResponse from "src/shared/http/response/response.http";
import { DailyLogService } from "./daily-log.service";
import { CreateDailyLogDto } from "./dto/create-daily-log.dto";
import { UpdateDailyLogDto } from "./dto/update-daily-log.dto";
import { UserAuthGuard } from "src/auth/guards/jwt-auth.guard";
import { Auth, GetUser } from "src/auth/decorator/auth.decorator";
import { JwtUserPayload } from "src/auth/interface/auth.interface";

@ApiTags("DailyLog")
@ApiBearerAuth()
@UseGuards(UserAuthGuard)
@Controller({ path: "daily-log", version: "1" })
@ApiInternalServerErrorResponse({ description: "Server Error" })
export class DailyLogController {
    constructor(private readonly dailyLogService: DailyLogService) {}

    @ApiOperation({ summary: "Create Daily log" })
    @ApiConflictResponse({ description: "Daily log already exist" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Post("create-daily-log")
    async createDailyLog(
        @GetUser() user: JwtUserPayload,
        @Body() createDailyLogDto: CreateDailyLogDto,
    ): Promise<HttpResponse> {
        return this.dailyLogService.createDailyLog(user.companyId, createDailyLogDto);
    }

    @ApiOperation({ summary: "Update Daily Log" })
    @ApiNotFoundResponse({ description: "DailyLog not found" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Patch("update-daily-log")
    async updateDailyLog(
        @GetUser() user: JwtUserPayload,
        @Body() updateDailyLogDto: UpdateDailyLogDto,
    ): Promise<HttpResponse> {
        return this.dailyLogService.updateDailyLog(user.companyId, updateDailyLogDto);
    }

    @ApiOperation({ summary: "Get Daily Log" })
    @Get("get-daily-log/crew/:crewId")
    async getDailylog(
        @GetUser() user: JwtUserPayload,
        @Param("crewId", ParseUUIDPipe) crewId: string,
    ): Promise<HttpResponse> {
        return this.dailyLogService.getDailyLog(user._id, user.companyId, crewId);
    }

    @ApiOperation({ summary: "Get Daily Log by Id" })
    @Get("daily-log-by-id/dailylog/:dailylogId")
    async getDailyLogById(
        @GetUser() user: JwtUserPayload,
        @Param("dailylogId", ParseUUIDPipe) dailylogId: string,
    ): Promise<HttpResponse> {
        return this.dailyLogService.getDailyLogById(user._id, user.companyId, dailylogId);
    }

    @ApiOperation({ summary: "Get PO list for Daily Log" })
    @ApiQuery({
        name: "search",
        type: String,
        description: "search keyword",
        required: false,
    })
    @Get("po-list")
    async getPOList(
        @GetUser() user: JwtUserPayload,
        @Query("showAll") showAll: boolean,
        @Query("search") search?: string,
    ): Promise<HttpResponse> {
        return this.dailyLogService.getPOList(user._id, user.companyId, showAll, search);
    }

    @ApiOperation({ summary: "Get data to show project details in Daily Log" })
    @Get("project-data/oppId/:oppId/projectType/:projectType")
    async projectTotalSQ(
        @GetUser() user: JwtUserPayload,
        @Param("oppId", ParseUUIDPipe) oppId: string,
        @Param("projectType") projectType: boolean,
    ): Promise<HttpResponse> {
        return this.dailyLogService.projectTotalSQ(user._id, user.companyId, oppId, projectType);
    }

    @ApiOperation({ summary: "Get dailylog & extra work for a given opportunity" })
    @Get("opportunity-work/oppId/:oppId")
    async dailylogExtraWorkForOpp(
        @GetUser() user: JwtUserPayload,
        @Param("oppId", ParseUUIDPipe) oppId: string,
    ): Promise<HttpResponse> {
        return this.dailyLogService.dailylogExtraWorkForOpp(user.companyId, oppId);
    }
}
