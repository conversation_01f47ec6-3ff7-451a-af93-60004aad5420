import { Modu<PERSON> } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { DailyLogController } from "./daily-log.controller";
import { DailyLogService } from "./daily-log.service";
import { DailyLogSchema } from "./schema/daily-log.schema";
import { OpportunitySchema } from "src/opportunity/schema/opportunity.schema";

@Module({
    imports: [
        MongooseModule.forFeature([
            { name: "DailyLog", schema: DailyLogSchema },
            { name: "Opportunity", schema: OpportunitySchema },
        ]),
    ],
    providers: [DailyLogService],
    controllers: [DailyLogController],
    exports: [DailyLogService],
})
export class DailyLogModule {}
