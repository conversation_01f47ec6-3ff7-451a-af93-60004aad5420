import { Schema, SchemaFactory, Prop } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import mongoose, { Document } from "mongoose";
import { WorkedEnum } from "../enum/worked.enum";
import { UUIDProp } from "src/shared/decorator/uuid.decorator";

export type DailyLogDocument = DailyLog & Document;

@Schema({ timestamps: true, id: false, collection: "DailyLog" })
export class DailyLog {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @UUIDProp()
    companyId: string;

    @UUIDProp()
    crewId: string;

    @Prop({ required: true })
    date: Date;

    @Prop({ required: false, type: String, enum: WorkedEnum })
    worked: WorkedEnum;

    @Prop({ required: false })
    highTemp: number;

    @Prop({ required: false })
    lowTemp: number;

    @Prop({ required: false, default: 0 })
    dayRealRev: number;

    @Prop({ required: false, default: 0 })
    dayEarned: number;

    @Prop({ required: true, type: [mongoose.Schema.Types.Mixed] })
    projects: any;

    @Prop({ required: false, type: [mongoose.Schema.Types.Mixed] })
    auditLog: any;

    @UUIDProp()
    createdBy: string;

    @Prop({ required: false })
    maxwind_mph?: number;

    @Prop({ required: false })
    totalprecip_in?: number;

    @Prop({ default: false })
    deleted: boolean;

    @Prop()
    createdAt?: Date;

    @Prop()
    modifiedAt?: Date;
}

export const DailyLogSchema = SchemaFactory.createForClass(DailyLog);
