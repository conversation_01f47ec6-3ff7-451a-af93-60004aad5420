import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { PositionSchema } from "src/position/schema/position.schema";
import { PositionService } from "./position.service";
import { PositionController } from "./position.controller";
import { CompensationSchema } from "src/compensation/schema/compensation.schema";
import { MemberSchema } from "src/company/schema/member.schema";
import { CrewSchema } from "src/crew/schema/crew-management.schema";

@Module({
    imports: [
        MongooseModule.forFeature([
            { name: "Position", schema: PositionSchema },
            { name: "Compensation", schema: CompensationSchema },
            { name: "Member", schema: MemberSchema },
            { name: "Crew", schema: CrewSchema },
        ]),
    ],
    providers: [PositionService],
    controllers: [PositionController],
    exports: [PositionService],
})
export class PositionModule {}
