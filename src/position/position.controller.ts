import {
    Body,
    Controller,
    Delete,
    Get,
    Param,
    ParseUUIDPipe,
    Patch,
    Post,
    Query,
    UseGuards,
} from "@nestjs/common";
import {
    ApiBearerAuth,
    ApiConflictResponse,
    ApiInternalServerErrorResponse,
    ApiNotFoundResponse,
    ApiOperation,
    ApiTags,
    ApiUnauthorizedResponse,
} from "@nestjs/swagger";
import { Roles } from "src/auth/guards/auth.guard";
import { UserAuthGuard } from "src/auth/guards/jwt-auth.guard";
import { PaginationRequestDto } from "src/company/dto/pagination-request.dto";
import { UserRolesEnum } from "src/company/enum/role.enum";
import HttpResponse from "src/shared/http/response/response.http";
import { CreatePositionDto } from "./dto/create-position.dto";
import { DeletePositionDto } from "./dto/delete-position.dto";
import { UpdatePositionDto } from "./dto/update-position.dto";
import { PositionService } from "./position.service";
import { Auth, GetUser } from "src/auth/decorator/auth.decorator";
import { JwtUserPayload } from "src/auth/interface/auth.interface";

@ApiBearerAuth()
@ApiTags("Position")
@UseGuards(UserAuthGuard)
@ApiUnauthorizedResponse({ description: "Unauthorized request" })
@ApiInternalServerErrorResponse({ description: "Server Error" })
@Controller({ path: "position", version: "1" })
export class PositionController {
    constructor(private readonly positionService: PositionService) {}

    /**
     * Creates a new position for the company
     * @param userId The ID of the user creating the position
     * @param createPositionDto The DTO containing info about the position to create
     * @returns An HTTP response with the result of the operation
     */
    @ApiOperation({ summary: "Create Position" })
    @ApiConflictResponse({ description: "Position already exist" })
    // @Auth()
    // @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Post("create-position")
    async createPosition(
        @GetUser() user: JwtUserPayload,
        @Body() createPositionDto: CreatePositionDto,
    ): Promise<HttpResponse> {
        return this.positionService.createPosition(user.companyId, createPositionDto);
    }

    /**
     * Updates position of the company
     * @param userId The ID of the user updating the position
     * @param updatePositionDto The DTO containing info about the position to update
     * @returns An HTTP response with the result of the operation
     */
    @ApiOperation({ summary: "Update Position" })
    @ApiNotFoundResponse({ description: "Position not found" })
    // @Auth()
    // @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Patch("update-position")
    async updatePosition(
        @GetUser() user: JwtUserPayload,
        @Body() updatePositionDto: UpdatePositionDto,
    ): Promise<HttpResponse> {
        return this.positionService.updatePosition(user.companyId, updatePositionDto);
    }

    /**
     * Deletes position of the company
     * @param userId The ID of the user updating the position
     * @param deletePositionDto The DTO containing info about the position to delete
     * @returns An HTTP response with the result of the operation
     */
    @ApiOperation({ summary: "Delete Position" })
    @ApiNotFoundResponse({ description: "Position not found" })
    // @Auth()
    // @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Delete("delete-position")
    async deletePosition(
        @GetUser() user: JwtUserPayload,
        @Body() deletePositionDto: DeletePositionDto,
    ): Promise<HttpResponse> {
        return this.positionService.deletePosition(user.companyId, deletePositionDto);
    }

    /**
     * Restores position of the company
     * @param userId The ID of the user restoring the position
     * @param deletePositionDto The DTO containing info about the position to restore
     * @returns An HTTP response with the result of the operation
     */
    @ApiOperation({ summary: "Restore Position" })
    @ApiNotFoundResponse({ description: "Position not found" })
    // @Auth()
    // @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Patch("restore-position")
    async restorePosition(
        @GetUser() user: JwtUserPayload,
        @Body() deletePositionDto: DeletePositionDto,
    ): Promise<HttpResponse> {
        return this.positionService.restorePosition(user.companyId, deletePositionDto);
    }

    /**
     * Get company position
     * @param userId ID of the user making the request
     * @param companyId ID of the company whose position are being retrieved
     * @param deleted Whether to include deleted positions in the results
     * @param paginationRequestDto Pagination request details (offset, limit, etc.)
     * @returns List of Positions for the specified Company
     */
    @ApiOperation({ summary: "Get Company Position" })
    @Get("get-position/deleted/:deleted")
    async getCompanyPosition(
        @GetUser() user: JwtUserPayload,
        @Param("deleted")
        deleted: boolean,
        @Query() paginationRequestDto: PaginationRequestDto,
    ): Promise<HttpResponse> {
        return this.positionService.getCompanyPosition(
            user._id,
            user.companyId,
            deleted,
            paginationRequestDto,
        );
    }

    /**
     * Get member position
     * @param userId User ID of the requester
     * @param companyId Company ID of the Company to get Positions for
     * @returns a Promise representing the HTTP response with the requested position
     */
    @ApiOperation({ summary: "Get Member Position" })
    @Get("get-member-position")
    async getMemberPosition(@GetUser() user: JwtUserPayload) {
        return this.positionService.getMemberPosition(user._id, user.companyId);
    }
}
