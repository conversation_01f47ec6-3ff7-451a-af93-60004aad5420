import { <PERSON>hem<PERSON>, SchemaFactory, Prop } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import { Document } from "mongoose";
import { PermissionsEnum } from "../../shared/enum/permission.enum";
import { UUIDProp } from "src/shared/decorator/uuid.decorator";

export type PositionDocument = Position & Document;

@Schema({ timestamps: true, id: false, collection: "Position", strict: true })
export class Position {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @UUIDProp()
    companyId: string;

    @Prop({ required: true })
    position: string;

    @Prop({ required: false })
    description: string;

    @Prop({
        type: [
            {
                category: { type: String, required: true },
                resources: [
                    {
                        name: { type: String, required: true },
                        permissions: { type: Number, enum: PermissionsEnum, required: true },
                        crud: {
                            type: {
                                read: { type: Boolean, required: true },
                                write: { type: Boolean, required: true },
                                // update: { type: <PERSON><PERSON><PERSON>, required: true },
                                // delete: { type: <PERSON>olean, required: true },
                            },
                            required: true,
                            _id: false, // Prevent _id from being added to `crud`
                        },
                        _id: false,
                    },
                ],
                _id: false,
            },
        ],
        required: true,
        _id: false,
    })
    permissions: {
        category: string;
        resources: {
            name: string;
            permissions: PermissionsEnum;
            crud: {
                read: boolean;
                write: boolean;
                // update: boolean;
                // delete: boolean;
            };
        }[];
    }[];

    @Prop({ default: false })
    deleted: boolean;

    @Prop({ required: false })
    symbol: string;

    @Prop({ default: true })
    deletable: boolean;

    @UUIDProp()
    createdBy: string;

    @Prop()
    createdAt?: Date;

    @Prop()
    modifiedAt?: Date;
}

export const PositionSchema = SchemaFactory.createForClass(Position);
