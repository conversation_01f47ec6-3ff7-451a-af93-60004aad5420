import {
    BadRequestException,
    HttpException,
    HttpStatus,
    Injectable,
    InternalServerErrorException,
} from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";
import { MemberDocument } from "src/company/schema/member.schema";
import { CompensationDocument } from "src/compensation/schema/compensation.schema";
import CreatedResponse from "src/shared/http/response/created.http";
import OkResponse from "src/shared/http/response/ok.http";
import { CreatePositionDto } from "./dto/create-position.dto";
import { DeletePositionDto } from "./dto/delete-position.dto";
import { PermissionsEnum } from "../shared/enum/permission.enum";
import { builtInPositions, builtInPositionsForPWP } from "../shared/constants/index";
import { PositionDocument } from "./schema/position.schema";
import NoContentResponse from "src/shared/http/response/no-content.http";
import { PaginationRequestDto } from "src/company/dto/pagination-request.dto";
import { UpdatePositionDto } from "./dto/update-position.dto";
import { CrewDocument } from "src/crew/schema/crew-management.schema";

@Injectable()
export class PositionService {
    constructor(
        @InjectModel("Position") private readonly positionModel: Model<PositionDocument>,
        @InjectModel("Member") private readonly memberModel: Model<MemberDocument>,
        @InjectModel("Compensation") private readonly compensationModel: Model<CompensationDocument>,
        @InjectModel("Crew") private readonly crewModel: Model<CrewDocument>,
    ) {}

    async createPosition(companyId: string, createPositionDto: CreatePositionDto) {
        try {
            const symbol = createPositionDto.position.replace(/\s/g, "");
            const position = await this.positionModel
                .exists({
                    position: createPositionDto.position,
                    companyId,
                    symbol,
                    deleted: false,
                })
                .exec();
            if (position) throw new HttpException("Position already exists", HttpStatus.BAD_REQUEST);
            const createdPosition = new this.positionModel({
                companyId,
                ...createPositionDto,
            });
            await createdPosition.save();
            return new CreatedResponse({ message: "Position created successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async deletePosition(companyId: string, deletePositionDto: DeletePositionDto) {
        try {
            await this.positionModel.findOneAndUpdate(
                {
                    _id: deletePositionDto.positionId,
                    companyId,
                    deleted: false,
                },
                {
                    $set: { deleted: true },
                },
                { new: true },
            );
            return new NoContentResponse({ message: "Position deleted successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async restorePosition(companyId: string, deletePositionDto: DeletePositionDto) {
        try {
            await this.positionModel.findOneAndUpdate(
                {
                    _id: deletePositionDto.positionId,
                    companyId,
                    deleted: true,
                },
                {
                    $set: { deleted: false },
                },
                { new: true },
            );
            return new OkResponse({ message: "Position retored successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updatePosition(companyId: string, updatePositionDto: UpdatePositionDto) {
        try {
            const position = await this.positionModel
                .findOne({
                    _id: updatePositionDto.id,
                    companyId,
                    deleted: false,
                })
                .exec();
            if (!position) throw new HttpException("Position does not exists", HttpStatus.BAD_REQUEST);
            await this.positionModel.updateOne(
                {
                    _id: updatePositionDto.id,
                    companyId,
                    deleted: false,
                },
                {
                    ...updatePositionDto,
                },
            );

            return new OkResponse({ message: "Position updated successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getCompanyPosition(
        userId: string,
        companyId: string,
        deleted: boolean,
        paginationRequestDto: PaginationRequestDto,
    ) {
        try {
            const limit = paginationRequestDto.limit || 10;
            const offset = limit * (paginationRequestDto.skip || 0);
            const position = await this.positionModel.find({ companyId, deleted }).skip(offset).limit(limit);
            return new OkResponse({ position });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async addDefaultPositions(userId: string, companyId: string, positionIdUUID, proPlusUserCheck: boolean) {
        try {
            const defaultPositions = [];

            const builtInPositionsArray = proPlusUserCheck
                ? builtInPositions(positionIdUUID)
                : builtInPositionsForPWP(positionIdUUID);

            builtInPositionsArray.forEach(function (eachObj) {
                const obj = {
                    _id: eachObj._id,
                    companyId,
                    createdBy: userId,
                    permissions: eachObj.permissions,
                    position: eachObj.position,
                    symbol: eachObj.symbol,
                    deletable: eachObj.deletable,
                };
                defaultPositions.push(obj);
            });
            const allPositions = await this.positionModel.insertMany(defaultPositions);
            return allPositions.find((item) => item.symbol === "Owner");
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    //check
    // async checkPermission(userId: string, companyId: string, requiredPositions: any) {
    //     try {
    //         const memberPosition = await this.compensationModel.findOne({ companyId, userId }).exec();
    //         const positionPermissions = memberPosition
    //             ? await this.positionModel
    //                   .findOne({
    //                       _id: memberPosition.positionId,
    //                       companyId,
    //                   })
    //                   .exec()
    //             : undefined;
    //         let response;
    //         const permMap = new Map();
    //         if (positionPermissions) {
    //             console.log("positionPermissions", positionPermissions);
    //             const a = positionPermissions.permissions.map((p) => permMap.set(p.resource, p.permissions));
    //             console.log("a", a);
    //             response = requiredPositions.every((reqPer) => {
    //                 if (permMap.has(reqPer.name) || permMap.has("all")) {
    //                     const permission: any[] = permMap.get(reqPer.name) || permMap.get("all");
    //                     console.log("permission", permission);
    //                     if (permission.includes(1)) {
    //                         return true;
    //                     }
    //                     console.log("reqPer", reqPer);
    //                     return reqPer.actions.every((ac) => permission.includes(ac));
    //                 }
    //                 return false;
    //             });
    //         } else if (requiredPositions.length > 0) response = false;
    //         else response = true;
    //         return response;
    //     } catch (error: any) {
    //         if (error instanceof HttpException) {
    //             throw error;
    //         }
    //         throw new InternalServerErrorException(error.message);
    //     }
    // }
    async checkPositionPermission(userId: string, companyId: string, requiredPositions: string[]) {
        try {
            const {
                data: { memberPosition },
            } = await this.getMemberPosition(userId, companyId);

            let response;
            if (memberPosition) {
                if (requiredPositions.includes(memberPosition.symbol)) response = true;
                else response = false;
            } else response = false;
            return { response, memberId: memberPosition?.memberId };
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }
    //check
    // async checkPermission(userId: string, companyId: string, requiredPositions: any) {
    //     try {
    //         const memberPosition = await this.compensationModel
    //             .findOne({ companyId, userId })
    //             .select("positionId")
    //             .exec();

    //         const positionPermissions = memberPosition
    //             ? await this.positionModel
    //                   .findOne({
    //                       _id: memberPosition.positionId,
    //                       companyId,
    //                   })
    //                   .exec()
    //             : undefined;
    //         // console.log({ positionPermissions });
    //         let hasPosition;
    //         let teamPermission = PermissionsEnum.None;
    //         const permMap = new Map();
    //         if (positionPermissions) {
    //             positionPermissions.permissions.map((p) => permMap.set(p.resource, p.permissions));
    //             hasPosition = requiredPositions.every((reqPer) => {
    //                 if (permMap.has(reqPer.name) || permMap.has("all")) {
    //                     const permission: any[] = permMap.get(reqPer.name) || permMap.get("all");

    //                     [teamPermission] = positionPermissions.permissions.find(
    //                         (item) => item.resource === reqPer.name,
    //                     ).permissions;
    //                     console.log({ permission, requiredPositions, reqPer, teamPermission });
    //                     if (permission.includes(PermissionsEnum.Full)) return true;
    //                     else if (permission.every((r) => reqPer.actions.includes(r))) return true;
    //                     else return false;
    //                 }
    //                 return false;
    //             });
    //         } else if (requiredPositions.length > 0) hasPosition = false;
    //         else hasPosition = true;

    //         if (hasPosition) {
    //             return { hasPosition, teamPermission, positionId: memberPosition.positionId };
    //         } else return { hasPosition };
    //     } catch (error: any) {
    //         if (error instanceof HttpException) {
    //             throw error;
    //         }
    //         throw new InternalServerErrorException(error.message);
    //     }
    // }

    async checkPermissionNew2(userId: string, companyId: string, reqPerm: any, action: any) {
        try {
            const memberPosition = await this.compensationModel
                .findOne({ companyId, userId })
                .select("positionId")
                .exec();

            if (!memberPosition) {
                return { hasPosition: false };
            }

            const positionPermissions = await this.positionModel
                .findOne({
                    _id: memberPosition.positionId,
                    companyId,
                })
                .exec();

            if (!positionPermissions || !positionPermissions.permissions) {
                return { hasPosition: false };
            }

            // Build permission map
            const permissionMap = new Map();
            for (const category of positionPermissions.permissions) {
                for (const resource of category.resources) {
                    permissionMap.set(`${category.category}.${resource.name}`, {
                        permissions: resource.permissions,
                        crud: resource.crud,
                    });
                }
            }

            let teamPermission = PermissionsEnum.None;
            // Check required permissions
            let isAllowed = false;
            // requiredPermissions.every((reqPerm) => {
            const key = `${reqPerm.category}.${reqPerm.name}`;

            const resourcePermission = permissionMap.get(key);

            if (!resourcePermission) isAllowed = false;

            const { permissions, crud } = resourcePermission;
            teamPermission = permissions;
            if (permissions === PermissionsEnum.Full) isAllowed = true;

            isAllowed = reqPerm.actions.includes(permissions) && crud[action];

            return {
                isAllowed,
                teamPermission,
                positionId: memberPosition.positionId,
            };
        } catch (error: any) {
            console.log(error);

            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async checkPermissionNew(userId: string, companyId: string, reqPerm: any, action: string) {
        try {
            // Fetch the member's position and permissions in a single query
            const memberData = await this.compensationModel
                .findOne({ companyId, userId })
                .select("positionId")
                .lean()
                .exec();

            if (!memberData) {
                return { hasPosition: false };
            }

            // Fetch position permissions directly
            const positionData = await this.positionModel
                .findOne({ _id: memberData.positionId, companyId })
                .select("permissions symbol")
                .lean()
                .exec();

            if (!positionData || !positionData.permissions?.length) {
                return { hasPosition: false };
            }

            // Build permission map for quick lookup
            const permissionMap = positionData.permissions.reduce((map, category) => {
                category.resources.forEach((resource) => {
                    const key = `${category.category}.${resource.name}`;
                    map.set(key, { permissions: resource.permissions, crud: resource.crud });
                });
                return map;
            }, new Map());

            // Check if the requested permission exists
            const key = `${reqPerm.category}.${reqPerm.name}`;
            const { permissions, crud } = permissionMap.get(key);

            if (!permissions || permissions === PermissionsEnum.None) {
                return {
                    isAllowed: false,
                    teamPermission: PermissionsEnum.None,
                    positionId: memberData.positionId,
                    symbol: positionData.symbol,
                };
            }

            // Determine if the action is allowed
            const isAllowed = crud[action];

            return {
                isAllowed,
                teamPermission: permissions,
                positionId: memberData.positionId,
                symbol: positionData.symbol,
            };
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getMemberPosition(userId: string, companyId: string, memberId?: string) {
        try {
            const memberData = memberId
                ? await this.memberModel.findOne({ _id: memberId, company: companyId }).exec()
                : await this.memberModel.findOne({ user: userId, company: companyId }).exec();
            const memberCompensation = memberData
                ? await this.compensationModel
                      .findOne({
                          companyId,
                          memberId: memberData._id,
                      })
                      .exec()
                : undefined;
            const memberPosition: any = memberCompensation
                ? await this.positionModel.findOne({ _id: memberCompensation.positionId }).exec()
                : undefined;

            if (memberPosition) {
                memberPosition.memberId = memberData._id;
                return new OkResponse({ memberPosition });
            } else
                throw new HttpException(
                    "Your account does not have a designated role. Please contact the administrator for assistance.",
                    HttpStatus.CONFLICT,
                );
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    //TODO: improvements instead of fetching member data get from payload
    async permissionCheck(
        userId: string,
        companyId: string,
        memberId: string,
        managerId: string,
        permission: any,
        crewManagerId?: any, // for time card edits
    ) {
        const loggerId = await this.memberModel.findOne({
            company: companyId,
            user: userId,
            deleted: false,
        });

        if (permission === PermissionsEnum.Full) return true;
        else if (permission === PermissionsEnum.Managed) {
            if (loggerId._id === managerId || loggerId._id === crewManagerId || loggerId._id === memberId)
                return true;
            else
                throw new HttpException(
                    "You are unauthorized to perform this request",
                    HttpStatus.BAD_REQUEST,
                );
        } else if (permission === PermissionsEnum.Self) {
            if (loggerId._id !== memberId)
                throw new HttpException(
                    "You are unauthorized to perform this request",
                    HttpStatus.BAD_REQUEST,
                );
            else return true;
        } else
            throw new HttpException("You are unauthorized to perform this request", HttpStatus.BAD_REQUEST);
    }

    async getManagedMembersInternal(
        memberId: string,
        companyId: string,
        permissions: PermissionsEnum,
        positionSymbol?: string,
    ) {
        if (permissions === PermissionsEnum.None) throw new BadRequestException("Do not have permission");
        try {
            const query = {
                company: companyId,
                // deleted: false, // commented to access terminated members opps
                ...(permissions === PermissionsEnum.Managed
                    ? { $or: [{ managerId: memberId }, { _id: memberId }] }
                    : permissions === PermissionsEnum.Self
                    ? { _id: memberId }
                    : {}),
            };

            if (positionSymbol && positionSymbol === "subContractor") {
                const member = await this.memberModel.findById({ _id: memberId }, { subContractorId: 1 });
                return { members: [member?.subContractorId || memberId] };
            } else if (positionSymbol && positionSymbol === "Foreman") {
                const crew = await this.crewModel.findOne({ foremanId: memberId }, { _id: 1 });
                return { members: [crew?._id || memberId] };
            } else {
                const members = await this.memberModel.aggregate([
                    {
                        $match: query,
                    },
                    {
                        $project: {
                            _id: 1,
                        },
                    },
                ]);
                return { members: members.map((m) => m._id) };
            }
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }
}
