import { <PERSON>du<PERSON> } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { ClientSchema } from "src/client/schema/client.schema";
import { CompanySettingSchema } from "src/company/schema/company-setting.schema";
import { OpportunitySchema } from "src/opportunity/schema/opportunity.schema";
import { ProjectController } from "./project.controller";
import { ProjectService } from "./project.service";
import { CategorySchema } from "./schema/category.schema";
import { CrewPositionSchema } from "./schema/crew-position.schema";
import { InputSchema } from "./schema/input.schema";
import { MaterialSchema } from "./schema/material.schema";
import { OrderSchema } from "./schema/order.schema";
import { PackageSchema } from "./schema/package.schema";
import { PriceSchema } from "./schema/price-schema";
import { ProjectTypeSchema } from "./schema/project-type.schema";
import { ProjectSchema } from "./schema/project.schema";
import { SubCategorySchema } from "./schema/sub-category.schema";
import { TaskSchema } from "./schema/task.schema";
import { TaxJurisdictionSchema } from "./schema/tax-jurisdiction.schema";
import { UnitSchema } from "./schema/unit.schema";
import { CrmModule } from "src/crm/crm.module";
import { OptionsSchema } from "./schema/options.schema";
import { WorkTaskSchema } from "src/work-task/schema/work-task.schema";
import { CustomProjectModule } from "src/custom-project/custom-project.module";
import { OpportunityModule } from "src/opportunity/opportunity.module";
import { ContactSchema } from "src/contacts/schema/contact.schema";

@Module({
    imports: [
        MongooseModule.forFeature([
            { name: "Unit", schema: UnitSchema },
            { name: "Task", schema: TaskSchema },
            { name: "Input", schema: InputSchema },
            { name: "Price", schema: PriceSchema },
            { name: "Contact", schema: ContactSchema },
            { name: "Order", schema: OrderSchema },
            { name: "Project", schema: ProjectSchema },
            { name: "Category", schema: CategorySchema },
            { name: "Material", schema: MaterialSchema },
            { name: "Package", schema: PackageSchema },
            { name: "CrewPosition", schema: CrewPositionSchema },
            { name: "WorkTask", schema: WorkTaskSchema },
            { name: "ProjectType", schema: ProjectTypeSchema },
            { name: "Opportunity", schema: OpportunitySchema },
            { name: "SubCategory", schema: SubCategorySchema },
            { name: "TaxJurisdiction", schema: TaxJurisdictionSchema },
            { name: "CompanySetting", schema: CompanySettingSchema },
            { name: "Options", schema: OptionsSchema },
        ]),
        // PositionModule,
        // RoleModule,
        CrmModule,
        CustomProjectModule,
        OpportunityModule,
    ],
    providers: [ProjectService],
    controllers: [ProjectController],
    exports: [ProjectService],
})
export class ProjectModule {}
