import {
    Body,
    Controller,
    Delete,
    Get,
    Param,
    ParseUUIDPipe,
    Patch,
    Post,
    Query,
    UseGuards,
} from "@nestjs/common";
import {
    ApiTags,
    ApiOperation,
    ApiInternalServerErrorResponse,
    ApiNotFoundResponse,
    ApiUnauthorizedResponse,
    ApiBearerAuth,
    ApiQuery,
} from "@nestjs/swagger";
import { Positions, Roles } from "src/auth/guards/auth.guard";
import { PaginationRequestDto } from "src/company/dto/pagination-request.dto";
import HttpResponse from "src/shared/http/response/response.http";
import { CreateCategoryDto } from "./dto/create-category.dto";
import { CreateInputDto } from "./dto/create-input.dto";
import { CreateMaterialDto } from "./dto/create-material.dto";
import { CreateOrderDto } from "./dto/create-order.dto";
import { CreatePackageDto } from "./dto/create-package.dto";
import { CreateProjectTypeDto } from "./dto/create-project-type.dto";
import { CreateProjectDto } from "./dto/create-project.dto";
import { CreateSubCategoryDto } from "./dto/create-sub-category.dto";
import { CreateTaskDto } from "./dto/create-task.dto";
import { CreateTaxJurisdictionDto } from "./dto/create-tax-jurisdiction.dto";
import { CreateUnitDto } from "./dto/create-unit.dto";
import { DeleteCategoryDto } from "./dto/delete-category.dto";
import { DeleteInputDto } from "./dto/delete-input.dto";
import { DeleteMaterialDto } from "./dto/delete-material.dto";
import { DeletePackageDto } from "./dto/delete-package.dto";
import { DeleteProjectTypeDto } from "./dto/delete-project-type.dto";
import { DeleteProjectDto } from "./dto/delete-project.dto";
import { DeleteSubCategoryDto } from "./dto/delete-sub-category.dto";
import { DeleteTaskDto } from "./dto/delete-task.dto";
import { DeleteTaxJurisdictionDto } from "./dto/delete-tax-jurisdiction.dto";
import { DeleteUnitDto } from "./dto/delete-unit.dto";
import { RestoreCategoryDto } from "./dto/restore-category.dto";
import { RestoreInputDto } from "./dto/restore-input.dto";
import { RestoreMaterialDto } from "./dto/restore-material.dto";
import { RestorePackageDto } from "./dto/restore-package.dto";
import { RestoreProjectTypeDto } from "./dto/restore-project-type-dto";
import { RestoreSubCategoryDto } from "./dto/restore-sub-category.schema";
import { RestoreTaskDto } from "./dto/restore-task.dto";
import { RestoreTaxJurisdictionDto } from "./dto/restore-tax-jurisdiction.dto";
import { RestoreUnitDto } from "./dto/restore-unit.dto";
import { UpdateCategoryDto } from "./dto/update-category.dto";
import { UpdateInputDto } from "./dto/update-input.dto";
import { UpdateMaterialDto } from "./dto/update-material.dto";
import { UpdateMaterialOrderDto } from "./dto/update-material-order.dto";
import { UpdateOppClientDto } from "./dto/update-opp-client.dto";
import { UpdatePackageDto } from "./dto/update-package.dto";
import { UpdateProjectTypeDto } from "./dto/update-project-type.dto";
import { UpdateSubCategoryDto } from "./dto/update-sub-category.schema";
import { UpdateTaskDto } from "./dto/update-task.dto";
import { UpdateTaxJurisdictionDto } from "./dto/update-tax-jurisdiction.dto";
import { UpdateUnitDto } from "./dto/update-unit.dto";
import { ProjectService } from "./project.service";
import { UpdateOrderDto } from "./dto/update-order.dto";
import { DeleteOrderDto } from "./dto/delete-order.dto";
import { RestoreOrderDto } from "./dto/restore-order.dto";
import { UserRolesEnum } from "src/company/enum/role.enum";
import { UserAuthGuard } from "src/auth/guards/jwt-auth.guard";
import { PermissionsEnum } from "src/shared/enum/permission.enum";
import { GetInputsDto } from "./dto/get-inputs.dto";
import { GetTaskDto } from "./dto/get-task-dto";
import { RestoreProjectDto } from "./dto/restore-project.dto";
import { GetOrderDto } from "./dto/get-order.dto";
import { DeletePriceDto } from "./dto/delete-price.dto";
import { RestorePriceDto } from "./dto/restore-price.dto";
import { ProjectInputsDto } from "./dto/project-input.dto";
import { AddPriceDto } from "./dto/add-price.dto";
import { UpdatePriceDto } from "./dto/update-price.dto";
import { GetMatDto } from "./dto/get-mat.dto";
import { CreateOptionDto } from "./dto/create-options.dto";
import { UpdateOptionDto } from "./dto/update-options.dto";
import { DeleteRestoreDto } from "src/shared/dto/delete-retore.dto";
import { Auth, GetUser } from "src/auth/decorator/auth.decorator";
import { JwtUserPayload } from "src/auth/interface/auth.interface";
import { SubscriptionPlanTypeEnum } from "src/shared/enum/subscriptions.enum";
import { CustomProjectService } from "src/custom-project/custom-project.service";
import { GetPackageOptionDto } from "./dto/get-package-option.dto";
import { UpdateSequenceDto } from "src/crm/dto/updateSequence.dto";
import { AddMaterialToOrderDto } from "./dto/add-material-order.dto";
import { moduleNames } from "src/shared/constants/constant";

@ApiTags("Project")
@ApiBearerAuth()
@UseGuards(UserAuthGuard)
@Controller({ path: "project", version: "1" })
export class ProjectController {
    constructor(
        private readonly projectService: ProjectService,
        private readonly customProjectService: CustomProjectService,
    ) {}

    /**
     * Create a new unit
     * @param userId - The ID of the user making the request.
     * @param createUnitDto - The data needed to create a new unit.
     * @returns - An HTTP response indicating success or failure.
     */
    @ApiOperation({ summary: "Create Unit" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Post("create-unit")
    async createUnit(
        @GetUser() user: JwtUserPayload,
        @Body() createUnitDto: CreateUnitDto,
    ): Promise<HttpResponse> {
        return this.projectService.createUnit(user.companyId, createUnitDto);
    }

    /**
     * Delete a unit
     * @param userId - The ID of the user making the request.
     * @param deleteUnitDto - The DTO containing the unit ID.
     * @returns The HTTP response.
     */
    @ApiOperation({ summary: "Delete Unit" })
    @ApiNotFoundResponse({ description: "Unit not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Delete("delete-unit")
    async deleteUnit(
        @GetUser() user: JwtUserPayload,
        @Body() deleteUnitDto: DeleteUnitDto,
    ): Promise<HttpResponse> {
        return this.projectService.deleteUnit(user._id, deleteUnitDto);
    }

    /**
     * Perm delete unit
     * @param userId - The ID of the user making the request.
     * @param deleteUnitDto - The DTO containing the unit ID.
     * @returns A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Delete Unit" })
    @ApiNotFoundResponse({ description: "Unit not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Delete("perm-delete-unit")
    async permDeleteUnit(
        @GetUser() user: JwtUserPayload,
        @Body() deleteUnitDto: DeleteUnitDto,
    ): Promise<HttpResponse> {
        return this.projectService.permDeleteUnit(user.companyId, deleteUnitDto);
    }

    /**
     * Restore a unit
     * @param userId - The ID of the user making the request.
     * @param restoreUnitDto - The unit restore data.
     * @returns - A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Restore Unit" })
    @ApiNotFoundResponse({ description: "Unit not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Patch("restore-unit")
    async restoreUnit(
        @GetUser() user: JwtUserPayload,
        @Body() restoreUnitDto: RestoreUnitDto,
    ): Promise<HttpResponse> {
        return this.projectService.restoreUnit(user._id, restoreUnitDto);
    }

    /**
     * Update unit
     * @param userId - The ID of the user making the request.
     * @param updateUnitDto - The unit sequence update data.
     * @returns - A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Update Unit" })
    @ApiNotFoundResponse({ description: "Unit not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Patch("update-unit")
    async updateUnit(
        @GetUser() user: JwtUserPayload,
        @Body() updateUnitDto: UpdateUnitDto,
    ): Promise<HttpResponse> {
        return this.projectService.updateUnit(user._id, updateUnitDto);
    }

    /**
     * Get unit for a company
     * @param userId - User ID of the requester
     * @param companyId - Company ID of the Company to get unit for
     * @param deleted - Whether to include deleted unit in the results
     * @param paginationRequestDto - Pagination request details (offset, limit, etc.)
     * @returns List of unit for the specified Company
     */
    @ApiOperation({ summary: "Get Unit" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Get("get-unit/deleted/:deleted")
    async getUnit(
        @GetUser() user: JwtUserPayload,
        @Param("deleted") deleted: boolean,
        @Query() paginationRequestDto: PaginationRequestDto,
    ): Promise<HttpResponse> {
        return this.projectService.getUnit(user._id, user.companyId, deleted, paginationRequestDto);
    }

    /**
     * Get unit for a company by unit id
     * @param userId User ID of the requester
     * @param companyId Company ID of the Company to get unit for
     * @param unitId the ID of the unit to retrieve
     * @param deleted whether to include deleted unit in the results
     * @returns  A Promise representing the HTTP response with the requested unit
     */
    @ApiOperation({ summary: "Get Unit by id" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Get("get-unit-by-id/unitId/:unitId/deleted/:deleted")
    async getUnitById(
        @GetUser() user: JwtUserPayload,
        @Param("unitId", ParseUUIDPipe) unitId: string,
        @Param("deleted") deleted: boolean,
    ): Promise<HttpResponse> {
        return this.projectService.getUnitById(user._id, user.companyId, unitId, deleted);
    }

    /**
     * Create a new input
     * @param userId - The ID of the user making the request.
     * @param createInputDto - The data needed to create a new input.
     * @returns - An HTTP response indicating success or failure.
     */
    @ApiOperation({ summary: "Create Input" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Post("create-input")
    async createInput(
        @GetUser() user: JwtUserPayload,
        @Body() createInputDto: CreateInputDto,
    ): Promise<HttpResponse> {
        return this.projectService.createInput(user.companyId, createInputDto);
    }

    /**
     * Delete a input
     * @param userId - The ID of the user making the request.
     * @param deleteInputDto - The DTO containing the input ID.
     * @returns The HTTP response.
     */
    @ApiOperation({ summary: "Delete Input" })
    @ApiNotFoundResponse({ description: "Input not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Delete("delete-input")
    async deleteInput(
        @GetUser() user: JwtUserPayload,
        @Body() deleteInputDto: DeleteInputDto,
    ): Promise<HttpResponse> {
        return this.projectService.deleteInput(user.companyId, deleteInputDto);
    }

    /**
     * Perm delete input
     * @param userId - The ID of the user making the request.
     * @param deleteInputDto - The DTO containing the input ID.
     * @returns A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Delete Input" })
    @ApiNotFoundResponse({ description: "Input not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Delete("perm-delete-input")
    async permDeleteInput(
        @GetUser() user: JwtUserPayload,
        @Body() deleteInputDto: DeleteInputDto,
    ): Promise<HttpResponse> {
        return this.projectService.permDeleteInput(user.companyId, deleteInputDto);
    }

    /**
     * Restore a input
     * @param userId - The ID of the user making the request.
     * @param restoreInputDto - The input restore data.
     * @returns - A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Restore Input" })
    @ApiNotFoundResponse({ description: "Input not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Patch("restore-input")
    async restoreInput(
        @GetUser() user: JwtUserPayload,
        @Body() restoreInputDto: RestoreInputDto,
    ): Promise<HttpResponse> {
        return this.projectService.restoreInput(user._id, restoreInputDto);
    }

    /**
     * Update input
     * @param userId - The ID of the user making the request.
     * @param updateInputDto - The input sequence update data.
     * @returns - A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Update Input" })
    @ApiNotFoundResponse({ description: "Input not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Patch("update-input")
    async updateInput(
        @GetUser() user: JwtUserPayload,
        @Body() updateInputDto: UpdateInputDto,
    ): Promise<HttpResponse> {
        return this.projectService.updateInput(user._id, updateInputDto);
    }

    /**
     * Get input for a company
     * @param userId - User ID of the requester
     * @param companyId - Company ID of the Company to get input for
     * @param deleted - Whether to include deleted input in the results
     * @param paginationRequestDto - Pagination request details (offset, limit, etc.)
     * @returns List of input for the specified Company
     */
    @ApiOperation({ summary: "Get Input" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Get("get-input/deleted/:deleted")
    async getInput(
        @GetUser() user: JwtUserPayload,
        @Param("deleted") deleted: boolean,
        @Query() getInputsDto: GetInputsDto,
    ): Promise<HttpResponse> {
        return this.projectService.getInput(user._id, user.companyId, deleted, getInputsDto);
    }

    /**
     * Get input for a company by input id
     * @param userId User ID of the requester
     * @param companyId Company ID of the Company to get input for
     * @param inputId the ID of the input to retrieve
     * @param deleted whether to include deleted input in the results
     * @returns  A Promise representing the HTTP response with the requested input
     */
    @ApiOperation({ summary: "Get Input by id" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Get("get-input-by-id/inputId/:inputId/deleted/:deleted")
    async getInputById(
        @GetUser() user: JwtUserPayload,
        @Param("inputId", ParseUUIDPipe) inputId: string,
        @Param("deleted") deleted: boolean,
    ): Promise<HttpResponse> {
        return this.projectService.getInputById(user._id, user.companyId, inputId, deleted);
    }

    /**
     * Create a new category
     * @param userId - The ID of the user making the request.
     * @param createCategoryDto - The data needed to create a new category.
     * @returns - An HTTP response indicating success or failure.
     */
    @ApiOperation({ summary: "Create Category" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Post("create-category")
    async createCategory(
        @GetUser() user: JwtUserPayload,
        @Body() createCategoryDto: CreateCategoryDto,
    ): Promise<HttpResponse> {
        return this.projectService.createCategory(user.companyId, createCategoryDto);
    }

    /**
     * Delete a category
     * @param userId - The ID of the user making the request.
     * @param deleteCategoryDto - The DTO containing the category ID.
     * @returns The HTTP response.
     */
    @ApiOperation({ summary: "Delete Category" })
    @ApiNotFoundResponse({ description: "Category not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Delete("delete-category")
    async deleteCategory(
        @GetUser() user: JwtUserPayload,
        @Body() deleteCategoryDto: DeleteCategoryDto,
    ): Promise<HttpResponse> {
        return this.projectService.deleteCategory(user.companyId, deleteCategoryDto);
    }

    /**
     * Perm Delete a category
     * @param userId - The ID of the user making the request.
     * @param deleteCategoryDto - The DTO containing the category ID.
     * @returns The HTTP response.
     */
    @ApiOperation({ summary: "Delete Category" })
    @ApiNotFoundResponse({ description: "Category not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Delete("perm-delete-category")
    async permDeleteCategory(
        @GetUser() user: JwtUserPayload,
        @Body() deleteCategoryDto: DeleteCategoryDto,
    ): Promise<HttpResponse> {
        return this.projectService.permDeleteCategory(user.companyId, deleteCategoryDto);
    }

    /**
     * Restore a category
     * @param userId - The ID of the user making the request.
     * @param restoreCategoryDto - The category restore data.
     * @returns - A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Restore Category" })
    @ApiNotFoundResponse({ description: "Category not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Patch("restore-category")
    async restoreCategory(
        @GetUser() user: JwtUserPayload,
        @Body() restoreCategoryDto: RestoreCategoryDto,
    ): Promise<HttpResponse> {
        return this.projectService.restoreCategory(user.companyId, restoreCategoryDto);
    }

    /**
     * Update category
     * @param userId - The ID of the user making the request.
     * @param updateCategoryDto - The category update data.
     * @returns - A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Update Category" })
    @ApiNotFoundResponse({ description: "Category not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Patch("update-category")
    async updateCategory(
        @GetUser() user: JwtUserPayload,
        @Body() updateCategoryDto: UpdateCategoryDto,
    ): Promise<HttpResponse> {
        return this.projectService.updateCategory(user._id, updateCategoryDto);
    }

    /**
     * Get category for a company
     * @param userId - User ID of the requester
     * @param companyId - Company ID of the Company to get category for
     * @param deleted - Whether to include deleted category in the results
     * @param paginationRequestDto - Pagination request details (offset, limit, etc.)
     * @returns List of category for the specified Company
     */
    @ApiOperation({ summary: "Get Category" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Get("get-category/deleted/:deleted")
    async getCategory(
        @GetUser() user: JwtUserPayload,
        @Param("deleted") deleted: boolean,
        @Query() paginationRequestDto: PaginationRequestDto,
    ): Promise<HttpResponse> {
        return this.projectService.getCategory(user._id, user.companyId, deleted, paginationRequestDto);
    }

    /**
     * Get category for a company by category id
     * @param userId User ID of the requester
     * @param companyId Company ID of the Company to get category for
     * @param categoryId the ID of the category to retrieve
     * @param deleted whether to include deleted cateory in the results
     * @returns  A Promise representing the HTTP response with the requested unit
     */
    @ApiOperation({ summary: "Get Category by id" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Get("get-category-by-id/categoryId/:categoryId/deleted/:deleted")
    async getCategoryById(
        @GetUser() user: JwtUserPayload,
        @Param("categoryId", ParseUUIDPipe) categoryId: string,
        @Param("deleted") deleted: boolean,
    ): Promise<HttpResponse> {
        return this.projectService.getCategoryById(user._id, user.companyId, categoryId, deleted);
    }

    /**
     * Create a new sub category
     * @param userId - The ID of the user making the request.
     * @param createSubCategoryDto - The data needed to create a new sub category.
     * @returns - An HTTP response indicating success or failure.
     */
    @ApiOperation({ summary: "Create SubCategory" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Post("create-sub-category")
    async createSubCategory(
        @GetUser() user: JwtUserPayload,
        @Body() createSubCategoryDto: CreateSubCategoryDto,
    ): Promise<HttpResponse> {
        return this.projectService.createSubCategory(user.companyId, createSubCategoryDto);
    }

    /**
     * Delete a sub-category
     * @param userId - The ID of the user making the request.
     * @param deleteSubCategoryDto - The DTO containing the sub-category ID.
     * @returns The HTTP response.
     */
    @ApiOperation({ summary: "Delete Sub Category" })
    @ApiNotFoundResponse({ description: "Sub Category not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Delete("delete-sub-category")
    async deleteSubCategory(
        @GetUser() user: JwtUserPayload,
        @Body() deleteSubCategoryDto: DeleteSubCategoryDto,
    ): Promise<HttpResponse> {
        return this.projectService.deleteSubCategory(user._id, deleteSubCategoryDto);
    }

    /**
     * Perm Delete a sub-category
     * @param userId - The ID of the user making the request.
     * @param deleteSubCategoryDto - The DTO containing the sub category ID.
     * @returns The HTTP response.
     */
    @ApiOperation({ summary: "Delete Sub Category" })
    @ApiNotFoundResponse({ description: "Sub Category not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Delete("perm-delete-sub-category")
    async permDeleteSubCategory(
        @GetUser() user: JwtUserPayload,
        @Body() deleteSubCategoryDto: DeleteSubCategoryDto,
    ): Promise<HttpResponse> {
        return this.projectService.permDeleteSubCategory(user._id, deleteSubCategoryDto);
    }

    /**
     * Restore a sub category
     * @param userId - The ID of the user making the request.
     * @param restoreSubCategoryDto - The sub-category restore data.
     * @returns - A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Restore Sub Category" })
    @ApiNotFoundResponse({ description: "Sub Category not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Patch("restore-sub-category")
    async restoreSubCategory(
        @GetUser() user: JwtUserPayload,
        @Body() restoreSubCategoryDto: RestoreSubCategoryDto,
    ): Promise<HttpResponse> {
        return this.projectService.restoreSubCategory(user._id, restoreSubCategoryDto);
    }

    /**
     * Update subcategory
     * @param userId - The ID of the user making the request.
     * @param updateSubCategoryDto - The sub-category update data.
     * @returns - A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Update SubCategory" })
    @ApiNotFoundResponse({ description: "SubCategory not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Patch("update-sub-category")
    async updateSubCategory(
        @GetUser() user: JwtUserPayload,
        @Body() updateSubCategoryDto: UpdateSubCategoryDto,
    ): Promise<HttpResponse> {
        return this.projectService.updateSubCategory(user._id, updateSubCategoryDto);
    }

    /**
     * Get sub-category for a company
     * @param userId - User ID of the requester
     * @param companyId - Company ID of the Company to get sub-category for
     * @param deleted - Whether to include deleted sub-category in the results
     * @param paginationRequestDto - Pagination request details (offset, limit, etc.)
     * @returns List of sub-category for the specified Company
     */
    @ApiOperation({ summary: "Get SubCategory" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Get("get-sub-category/deleted/:deleted")
    async getSubCategory(
        @GetUser() user: JwtUserPayload,
        @Param("deleted") deleted: boolean,
        @Query() paginationRequestDto: PaginationRequestDto,
    ): Promise<HttpResponse> {
        return this.projectService.getSubCategory(user._id, user.companyId, deleted, paginationRequestDto);
    }

    /**
     * Get sub-category for a company by category id
     * @param userId User ID of the requester
     * @param companyId Company ID of the Company to get sub-category for
     * @param categoryId the ID of the category of sub-categories
     * @param deleted whether to include deleted sub-cateory in the results
     * @returns  A Promise representing the HTTP response with the requested subcategory
     */
    @ApiOperation({ summary: "Get SubCategory" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Get("get-sub-category-by-category-id/category/:categoryId/deleted/:deleted")
    async getSubCategoryByCategoryId(
        @GetUser() user: JwtUserPayload,
        @Param("categoryId", ParseUUIDPipe) categoryId: string,
        @Param("deleted") deleted: boolean,
        @Query() paginationRequestDto: PaginationRequestDto,
    ): Promise<HttpResponse> {
        return this.projectService.getSubCategoryByCategoryId(
            user._id,
            user.companyId,
            categoryId,
            deleted,
            paginationRequestDto,
        );
    }

    /**
     * Get sub-category for a company by sub-category id
     * @param userId User ID of the requester
     * @param companyId Company ID of the Company to get sub-category for
     * @param subCategoryId the ID of the sub-category to retrieve
     * @param deleted whether to include deleted sub-cateory in the results
     * @returns  A Promise representing the HTTP response with the requested subcategory
     */
    @ApiOperation({ summary: "Get Sub Category by id" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Get("get-sub-category-by-id/subCategoryId/:subCategoryId/deleted/:deleted")
    async getSubCategoryById(
        @GetUser() user: JwtUserPayload,
        @Param("subCategoryId", ParseUUIDPipe) subCategoryId: string,
        @Param("deleted") deleted: boolean,
    ): Promise<HttpResponse> {
        return this.projectService.getSubCategoryById(user._id, user.companyId, subCategoryId, deleted);
    }

    /**
     * Create a new material
     * @param userId - The ID of the user making the request.
     * @param createMaterialDto - The data needed to create a new material.
     * @returns - An HTTP response indicating success or failure.
     */
    @ApiOperation({ summary: "Create Material" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Post("create-material")
    async createMaterial(
        @GetUser() user: JwtUserPayload,
        @Body() createMaterialDto: CreateMaterialDto,
    ): Promise<HttpResponse> {
        return this.projectService.createMaterial(user.companyId, createMaterialDto);
    }

    /**
     * Delete a material
     * @param userId - The ID of the user making the request.
     * @param deleteMaterialDto - The DTO containing the material ID.
     * @returns The HTTP response.
     */
    @ApiOperation({ summary: "Delete Material" })
    @ApiNotFoundResponse({ description: "Material not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Delete("delete-material")
    async deleteMaterial(
        @GetUser() user: JwtUserPayload,
        @Body() deleteMaterialDto: DeleteMaterialDto,
    ): Promise<HttpResponse> {
        return this.projectService.deleteMaterial(user.companyId, deleteMaterialDto);
    }

    /**
     * Perm Delete a material
     * @param userId - The ID of the user making the request.
     * @param deleteMaterialDto - The DTO containing the material ID.
     * @returns The HTTP response.
     */
    @ApiOperation({ summary: "Perm Delete Material" })
    @ApiNotFoundResponse({ description: "Material not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Delete("perm-delete-material")
    async permDeleteMaterial(
        @GetUser() user: JwtUserPayload,
        @Body() deleteMaterialDto: DeleteMaterialDto,
    ): Promise<HttpResponse> {
        return this.projectService.permDeleteMaterial(user.companyId, deleteMaterialDto);
    }

    /**
     * Restore a material
     * @param userId - The ID of the user making the request.
     * @param restoreMaterialDto - The material restore data.
     * @returns - A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Restore Material" })
    @ApiNotFoundResponse({ description: "Material not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Patch("restore-material")
    async restoreMaterial(
        @GetUser() user: JwtUserPayload,
        @Body() restoreMaterialDto: RestoreMaterialDto,
    ): Promise<HttpResponse> {
        return this.projectService.restoreMaterial(user._id, restoreMaterialDto);
    }

    /**
     * Update material
     * @param userId - The ID of the user making the request.
     * @param updateCategoryDto - The material update data.
     * @returns - A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Update Material" })
    @ApiNotFoundResponse({ description: "Material not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Patch("update-material")
    async updateMaterial(
        @GetUser() user: JwtUserPayload,
        @Body() updateMaterialDto: UpdateMaterialDto,
    ): Promise<HttpResponse> {
        return this.projectService.updateMaterial(user._id, updateMaterialDto);
    }

    /**
     * update material order
     * @param orderId - The id of the order which needs to be updated
     * @param UpdateMaterialOrderDto update material order
     * @returns - A promise that is resolved to an HTTP response.
     */
    @ApiOperation({ summary: "Update Material" })
    @ApiNotFoundResponse({ description: "Material not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Patch("update-materials-for-order/:orderId")
    async updateMaterialOrder(
        @GetUser() user: JwtUserPayload,
        @Param("orderId", ParseUUIDPipe) orderId: string,
        @Body() updateMaterialOrderDto: UpdateMaterialOrderDto,
    ): Promise<HttpResponse> {
        return this.projectService.updateMaterialOrder(orderId, updateMaterialOrderDto);
    }

    @ApiOperation({ summary: "Add materials to an existing order" })
    @ApiNotFoundResponse({ description: "Order not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Patch("add-materials-to-order/:orderId")
    async addMaterialsToOrder(
        @Param("orderId", ParseUUIDPipe) orderId: string,
        @Body() addMaterialToOrderDto: AddMaterialToOrderDto,
    ) {
        return this.projectService.addMaterialsToOrder(orderId, addMaterialToOrderDto);
    }

    /**
     * Fetch list of tasks where this Input is used
     * @param userId The user ID.
     * @param companyId The company ID.
     * @param inputId The input ID.
     * @returns A `Promise` that resolves to an `HttpResponse` object containing the project tasks.
     */
    @ApiOperation({ summary: "Fetch list of tasks where this Input is used" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Get("tasks/used-by-input/:inputId")
    async getTasksUsedByInput(
        @GetUser() user: JwtUserPayload,
        @Param("inputId", ParseUUIDPipe) inputId: string,
    ): Promise<HttpResponse> {
        return this.projectService.getTasksUsedByInput(user.companyId, inputId);
    }

    /**
     * Fetch list of packages and options where this task is used
     * @param userId The user ID.
     * @param companyId The company ID.
     * @param taskId The task ID.
     * @returns A `Promise` that resolves to an `HttpResponse` object containing the project tasks.
     */
    @ApiOperation({ summary: "Fetch list of packages and options where this task is used" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Get("packages-options/used-by-tasks/:taskId")
    async getPackagesOptionsUsedByTask(
        @GetUser() user: JwtUserPayload,
        @Param("taskId", ParseUUIDPipe) taskId: string,
    ): Promise<HttpResponse> {
        return this.projectService.getPackagesOptionsUsedByTask(user.companyId, taskId);
    }

    /**
     * Fetch Material Project Task.
     * @param userId The user ID.
     * @param companyId The company ID.
     * @param materialId The material ID.
     * @returns A `Promise` that resolves to an `HttpResponse` object containing the project tasks.
     */
    @ApiOperation({ summary: "Fetch Material Project Task" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Get("material-project-task/material/:materialId")
    async getMeterialProjectTask(
        @GetUser() user: JwtUserPayload,
        @Param("materialId", ParseUUIDPipe) materialId: string,
    ): Promise<HttpResponse> {
        return this.projectService.getMaterialProjectTask(user.companyId, materialId);
    }

    /**
     * Get material for a company
     * @param userId - User ID of the requester
     * @param companyId - Company ID of the Company to get material for
     * @param deleted - Whether to include deleted material in the results
     * @param paginationRequestDto - Pagination request details (offset, limit, etc.)
     * @returns List of material for the specified Company
     */
    @ApiOperation({ summary: "Get Material" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Get("get-material/deleted/:deleted")
    async getMaterial(
        @GetUser() user: JwtUserPayload,
        @Param("deleted") deleted: boolean,
        @Query() getMatDto: GetMatDto,
    ): Promise<HttpResponse> {
        return this.projectService.getMaterial(user._id, user.companyId, deleted, getMatDto);
    }

    /**
     * Get material for a company by material id
     * @param userId User ID of the requester
     * @param companyId Company ID of the Company to get material for
     * @param materialId the ID of the material to retrieve
     * @param deleted whether to include deleted material in the results
     * @returns  A Promise representing the HTTP response with the requested material
     */
    @ApiOperation({ summary: "Get Material by id" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Get("get-material-by-id/materialId/:materialId/deleted/:deleted")
    async getMaterialById(
        @GetUser() user: JwtUserPayload,
        @Param("materialId", ParseUUIDPipe) materialId: string,
        @Param("deleted") deleted: boolean,
    ): Promise<HttpResponse> {
        return this.projectService.getMaterialById(user._id, user.companyId, materialId, deleted);
    }

    // /**
    //  * Create a new Crew Position
    //  * @param userId - The ID of the user making the request.
    //  * @param createCrewPositionDto - The data needed to create a new crew position.
    //  * @returns - An HTTP response indicating success or failure.
    //  */
    // @ApiOperation({ summary: "Create CrewPosition" })
    // @ApiInternalServerErrorResponse({ description: "Server Error" })
    // @Auth()
    // @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    // @Post("create-crew-position")
    // async createCrewPosition(
    //     @GetUser() user: JwtUserPayload,
    //     @Body() createCrewPositionDto: CreateCrewPositionDto,
    // ): Promise<HttpResponse> {
    //     return this.projectService.createCrewPosition(user._id, createCrewPositionDto);
    // }

    // /**
    //  * Delete a crew position
    //  * @param userId - The ID of the user making the request.
    //  * @param deleteCrewPositionDto - The DTO containing the crew position ID.
    //  * @returns The HTTP response.
    //  */
    // @ApiOperation({ summary: "Delete CrewPosition" })
    // @ApiNotFoundResponse({ description: "CrewPosition not found" })
    // @ApiInternalServerErrorResponse({ description: "Server Error" })
    // @Auth()
    // @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    // @Delete("delete-crew-position")
    // async deleteCrewPosition(
    //     @GetUser() user: JwtUserPayload,
    //     @Body() deleteCrewPositionDto: DeleteCrewPositionDto,
    // ): Promise<HttpResponse> {
    //     return this.projectService.deleteCrewPosition(user._id, deleteCrewPositionDto);
    // }

    // /**
    //  * Perm Delete a crew position
    //  * @param userId - The ID of the user making the request.
    //  * @param deleteCrewPositionDto - The DTO containing the crew position ID.
    //  * @returns The HTTP response.
    //  */
    // @ApiOperation({ summary: "Delete CrewPosition" })
    // @ApiNotFoundResponse({ description: "CrewPosition not found" })
    // @ApiInternalServerErrorResponse({ description: "Server Error" })
    // @Auth()
    // @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    // @Delete("perm-delete-crew-position")
    // async permDeleteCrewPosition(
    //     @GetUser() user: JwtUserPayload,
    //     @Body() deleteCrewPositionDto: DeleteCrewPositionDto,
    // ): Promise<HttpResponse> {
    //     return this.projectService.permDeleteCrewPosition(user._id, deleteCrewPositionDto);
    // }

    // /**
    //  * Restore a crew position
    //  * @param userId - The ID of the user making the request.
    //  * @param restoreCrewPositionDto - The crew position restore data.
    //  * @returns - A promise that resolves to an HTTP response.
    //  */
    // @ApiOperation({ summary: "Restore CrewPosition" })
    // @ApiNotFoundResponse({ description: "CrewPosition not found" })
    // @ApiInternalServerErrorResponse({ description: "Server Error" })
    // @Auth()
    // @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    // @Patch("restore-crew-position")
    // async restoreCrewPosition(
    //     @GetUser() user: JwtUserPayload,
    //     @Body() restoreCrewPositionDto: RestoreCrewPositionDto,
    // ): Promise<HttpResponse> {
    //     return this.projectService.restoreCrewPosition(user._id, restoreCrewPositionDto);
    // }

    // /**
    //  * Update crew position
    //  * @param userId - The ID of the user making the request.
    //  * @param updateCrewPositionDto - The crew position update data.
    //  * @returns - A promise that resolves to an HTTP response.
    //  */
    // @ApiOperation({ summary: "Update CrewPosition" })
    // @ApiNotFoundResponse({ description: "CrewPosition not found" })
    // @ApiInternalServerErrorResponse({ description: "Server Error" })
    // @Auth()
    // @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    // @Patch("update-crew-position")
    // async updateCrewPosition(
    //     @GetUser() user: JwtUserPayload,
    //     @Body() updateCrewPositionDto: UpdateCrewPositionDto,
    // ): Promise<HttpResponse> {
    //     return this.projectService.updateCrewPosition(user._id, updateCrewPositionDto);
    // }

    // /**
    //  * Get crew position for a company
    //  * @param userId - User ID of the requester
    //  * @param companyId - Company ID of the Company to get crew position for
    //  * @param deleted - Whether to include deleted crew position in the results
    //  * @param paginationRequestDto - Pagination request details (offset, limit, etc.)
    //  * @returns List of crew position for the specified Company
    //  */
    // @ApiOperation({ summary: "Get CrewPosition" })
    // @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    // @Get("get-crew-position/deleted/:deleted")
    // async getCrewPosition(
    //     @GetUser() user: JwtUserPayload,
    //     @Param("companyId", ParseUUIDPipe) companyId: string,
    //     @Param("deleted") deleted: boolean,
    //     @Query() paginationRequestDto: PaginationRequestDto,
    // ): Promise<HttpResponse> {
    //     return this.projectService.getCrewPosition(user._id, user.companyId, deleted, paginationRequestDto);
    // }

    // /**
    //  * Get crew position for a company by crew position id
    //  * @param userId User ID of the requester
    //  * @param companyId Company ID of the Company to get crew position for
    //  * @param crewPositionId the ID of the crew position to retrieve
    //  * @param deleted whether to include deleted crew position in the results
    //  * @returns  A Promise representing the HTTP response with the requested crew position
    //  */
    // @ApiOperation({ summary: "Get CrewPosition by id" })
    // @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    // @Get("get-crew-position-by-id/crewPositionId/:crewPositionId/deleted/:deleted")
    // async getCrewPositionById(
    //     @GetUser() user: JwtUserPayload,
    //     @Param("companyId", ParseUUIDPipe) companyId: string,
    //     @Param("crewPositionId", ParseUUIDPipe) crewPositionId: string,
    //     @Param("deleted") deleted: boolean,
    // ): Promise<HttpResponse> {
    //     return this.projectService.getCrewPositionById(user._id, user.companyId, crewPositionId, deleted);
    // }

    /**
     * Create a new project type
     * @param userId - The ID of the user making the request.
     * @param createProjectTypeDto - The data needed to create a new project type.
     * @returns - An HTTP response indicating success or failure.
     */
    @ApiOperation({ summary: "Create ProjectType" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Post("create-project-type")
    async createProjectType(
        @GetUser() user: JwtUserPayload,
        @Body() createProjectTypeDto: CreateProjectTypeDto,
    ): Promise<HttpResponse> {
        return this.projectService.createProjectType(user.companyId, createProjectTypeDto);
    }

    /**
     * Delete a project type
     * @param userId - The ID of the user making the request.
     * @param deleteProjectTypeDto - The DTO containing the project type ID.
     * @returns The HTTP response.
     */
    @ApiOperation({ summary: "Delete ProjectType" })
    @ApiNotFoundResponse({ description: "ProjectType not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Delete("delete-project-type")
    async deleteProjectType(
        @GetUser() user: JwtUserPayload,
        @Body() deleteProjectTypeDto: DeleteProjectTypeDto,
    ): Promise<HttpResponse> {
        return this.projectService.deleteProjectType(user._id, deleteProjectTypeDto);
    }

    /**
     * Perm delete project type
     * @param userId - The ID of the user making the request.
     * @param deleteProjectTypeDto - The DTO containing the project type ID.
     * @returns A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Perm Delete ProjectType" })
    @ApiNotFoundResponse({ description: "ProjectType not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Delete("perm-delete-project-type")
    async permDeleteProjectType(
        @GetUser() user: JwtUserPayload,
        @Body() deleteProjectTypeDto: DeleteProjectTypeDto,
    ): Promise<HttpResponse> {
        return this.projectService.permDeleteProjectType(user.companyId, deleteProjectTypeDto);
    }

    /**
     * Restore a project type
     * @param userId - The ID of the user making the request.
     * @param restoreProjectTypeDto - The project type restore data.
     * @returns - A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Restore ProjectType" })
    @ApiNotFoundResponse({ description: "ProjectType not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Patch("restore-project-type")
    async restoreProjectType(
        @GetUser() user: JwtUserPayload,
        @Body() restoreProjectTypeDto: RestoreProjectTypeDto,
    ): Promise<HttpResponse> {
        return this.projectService.restoreProjectType(user._id, restoreProjectTypeDto);
    }

    /**
     * Update project type
     * @param userId - The ID of the user making the request.
     * @param updateUnitDto - The project type update data.
     * @returns - A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Update ProjectType" })
    @ApiNotFoundResponse({ description: "ProjectType not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Patch("update-project-type")
    async updateProjectType(
        @GetUser() user: JwtUserPayload,
        @Body() updateProjectTypeDto: UpdateProjectTypeDto,
    ): Promise<HttpResponse> {
        return this.projectService.updateProjectType(user.companyId, updateProjectTypeDto);
    }

    /**
     * Get project type for a company
     * @param userId - User ID of the requester
     * @param companyId - Company ID of the Company to get project type for
     * @param deleted - Whether to include deleted project type in the results
     * @param paginationRequestDto - Pagination request details (offset, limit, etc.)
     * @returns List of project type for the specified Company
     */
    @ApiOperation({ summary: "Get ProjectType" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Get("get-project-type/deleted/:deleted")
    async getProjectType(
        @GetUser() user: JwtUserPayload,
        @Param("deleted") deleted: boolean,
        @Query() paginationRequestDto: PaginationRequestDto,
    ): Promise<HttpResponse> {
        return this.projectService.getProjectType(user._id, user.companyId, deleted, paginationRequestDto);
    }

    /**
     * Get project type for a company by project type id
     * @param userId User ID of the requester
     * @param companyId Company ID of the Company to get project type for
     * @param projectTypeId the ID of the project type to retrieve
     * @param deleted whether to include deleted projecy type in the results
     * @returns  A Promise representing the HTTP response with the requested project type
     */
    @ApiOperation({ summary: "Get ProjectType by id" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Get("get-project-type-by-id/projectTypeId/:projectTypeId/deleted/:deleted")
    async getProjectTypeById(
        @GetUser() user: JwtUserPayload,
        @Param("projectTypeId", ParseUUIDPipe) projectTypeId: string,
        @Param("deleted") deleted: boolean,
    ): Promise<HttpResponse> {
        return this.projectService.getProjectTypeById(user._id, user.companyId, projectTypeId, deleted);
    }

    /**
     * Create a new task
     * @param userId - The ID of the user making the request.
     * @param createTaskDto - The data needed to create a new task.
     * @returns - An HTTP response indicating success or failure.
     */
    @ApiOperation({ summary: "Create Task" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Post("create-task")
    async createTask(
        @GetUser() user: JwtUserPayload,
        @Body() createTaskDto: CreateTaskDto,
    ): Promise<HttpResponse> {
        return this.projectService.createTask(user.companyId, createTaskDto);
    }

    /**
     * Delete a task
     * @param userId - The ID of the user making the request.
     * @param deleteTaskDto - The DTO containing the task ID.
     * @returns The HTTP response.
     */
    @ApiOperation({ summary: "Delete Task" })
    @ApiNotFoundResponse({ description: "Task not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Delete("delete-task")
    async deleteTask(
        @GetUser() user: JwtUserPayload,
        @Body() deleteTaskDto: DeleteTaskDto,
    ): Promise<HttpResponse> {
        return this.projectService.deleteTask(user._id, deleteTaskDto);
    }

    /**
     * Perm Delete a task
     * @param userId - The ID of the user making the request.
     * @param deleteTaskDto - The DTO containing the task ID.
     * @returns The HTTP response.
     */
    @ApiOperation({ summary: "Delete Task" })
    @ApiNotFoundResponse({ description: "Task not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Delete("perm-delete-task")
    async permDeleteTask(
        @GetUser() user: JwtUserPayload,
        @Body() deleteTaskDto: DeleteTaskDto,
    ): Promise<HttpResponse> {
        return this.projectService.permDeleteTask(user.companyId, deleteTaskDto);
    }

    /**
     * Restore a task
     * @param userId - The ID of the user making the request.
     * @param restoreTaskDto - The task restore data.
     * @returns - A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Restore Task" })
    @ApiNotFoundResponse({ description: "Task not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Patch("restore-task")
    async restoreTask(
        @GetUser() user: JwtUserPayload,
        @Body() restoreTaskDto: RestoreTaskDto,
    ): Promise<HttpResponse> {
        return this.projectService.restoreTask(user._id, restoreTaskDto);
    }

    /**
     * Update task
     * @param userId - The ID of the user making the request.
     * @param updateTaskDto - The task update data.
     * @returns - A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Update Task" })
    @ApiNotFoundResponse({ description: "Task not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Patch("update-task")
    async updateTask(
        @GetUser() user: JwtUserPayload,
        @Body() updateTaskDto: UpdateTaskDto,
    ): Promise<HttpResponse> {
        return this.projectService.updateTask(user.companyId, updateTaskDto);
    }

    /**
     * Update task sequence
     * @param userId - The ID of the user making the request.
     * @param UpdateSequenceDto - The task sequence update data.
     * @returns - A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Update Task Sequence" })
    @ApiNotFoundResponse({ description: "Task not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Patch("update-task-sequence")
    async updateTaskSequence(
        @GetUser() user: JwtUserPayload,
        @Body() updateTaskSequenceDto: UpdateSequenceDto,
    ): Promise<HttpResponse> {
        return this.projectService.updateTaskSequence(user.companyId, updateTaskSequenceDto);
    }

    /**
     * Get task for a company
     * @param userId - User ID of the requester
     * @param companyId - Company ID of the Company to get task for
     * @param deleted - Whether to include deleted task in the results
     * @param paginationRequestDto - Pagination request details (offset, limit, etc.)
     * @returns List of task for the specified Company
     */
    @ApiOperation({ summary: "Get Task" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Get("get-task/deleted/:deleted")
    async getTask(
        @GetUser() user: JwtUserPayload,
        @Param("deleted") deleted: boolean,
        @Query() getTaskDto: GetTaskDto,
    ): Promise<HttpResponse> {
        return this.projectService.getTask(user._id, user.companyId, deleted, getTaskDto);
    }

    /**
     * Get task for a company by task id
     * @param userId User ID of the requester
     * @param companyId Company ID of the Company to get task for
     * @param taskId the ID of the task to retrieve
     * @param deleted whether to include deleted task in the results
     * @returns  A Promise representing the HTTP response with the requested task
     */
    @ApiOperation({ summary: "Get Task by id" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Get("get-task-by-id/taskId/:taskId/deleted/:deleted")
    async getTaskById(
        @GetUser() user: JwtUserPayload,
        @Param("taskId", ParseUUIDPipe) taskId: string,
        @Param("deleted") deleted: boolean,
    ): Promise<HttpResponse> {
        return this.projectService.getTaskById(user._id, user.companyId, taskId, deleted);
    }

    /**
     * Create a new package
     * @param userId - The ID of the user making the request.
     * @param createPackageDto - The data needed to create a new package.
     * @returns - An HTTP response indicating success or failure.
     */
    @ApiOperation({ summary: "Create Package" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Post("create-package")
    async createPackage(
        @GetUser() user: JwtUserPayload,
        @Body() createPackageDto: CreatePackageDto,
    ): Promise<HttpResponse> {
        return this.projectService.createPackage(user.companyId, createPackageDto);
    }

    /**
     * Delete a package
     * @param userId - The ID of the user making the request.
     * @param deletePackageDto - The DTO containing the package ID.
     * @returns The HTTP response.
     */
    @ApiOperation({ summary: "Delete Package" })
    @ApiNotFoundResponse({ description: "Package not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Delete("delete-package")
    async deletePackage(
        @GetUser() user: JwtUserPayload,
        @Body() deletePackageDto: DeletePackageDto,
    ): Promise<HttpResponse> {
        return this.projectService.deletePackage(user._id, deletePackageDto);
    }

    /**
     * Perm Delete a package
     * @param userId - The ID of the user making the request.
     * @param deletePackageDto - The DTO containing the package ID.
     * @returns The HTTP response.
     */
    @ApiOperation({ summary: "Delete Package" })
    @ApiNotFoundResponse({ description: "Package not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Delete("perm-delete-package")
    async permDeletePackage(
        @GetUser() user: JwtUserPayload,
        @Body() deletePackageDto: DeletePackageDto,
    ): Promise<HttpResponse> {
        return this.projectService.permDeletePackage(user.companyId, deletePackageDto);
    }

    /**
     * Restore a package
     * @param userId - The ID of the user making the request.
     * @param restorePackageDto - The package restore data.
     * @returns - A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Restore Package" })
    @ApiNotFoundResponse({ description: "Package not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Patch("restore-package")
    async restorePackage(
        @GetUser() user: JwtUserPayload,
        @Body() restorePackageDto: RestorePackageDto,
    ): Promise<HttpResponse> {
        return this.projectService.restorePackage(user._id, restorePackageDto);
    }

    /**
     * Update package
     * @param userId - The ID of the user making the request.
     * @param updatePackageDto - The package sequence update data.
     * @returns - A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Update Package" })
    @ApiNotFoundResponse({ description: "Package not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Patch("update-package")
    async updatePackage(
        @GetUser() user: JwtUserPayload,
        @Body() updatePackageDto: UpdatePackageDto,
    ): Promise<HttpResponse> {
        return this.projectService.updatePackage(user._id, updatePackageDto);
    }

    /**
     * Get package for a company
     * @param userId - User ID of the requester
     * @param companyId - Company ID of the Company to get package for
     * @param deleted - Whether to include deleted package in the results
     * @param paginationRequestDto - Pagination request details (offset, limit, etc.)
     * @returns List of package for the specified Company
     */
    @ApiOperation({ summary: "Get Package" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Get("get-package/deleted/:deleted/")
    async getPackage(
        @GetUser() user: JwtUserPayload,
        @Param("deleted") deleted: boolean,
        @Query() getPackageDto: GetPackageOptionDto,
    ): Promise<HttpResponse> {
        return this.projectService.getPackage(user._id, user.companyId, deleted, getPackageDto);
    }

    /**
     * Get package for a company by package id
     * @param userId User ID of the requester
     * @param companyId Company ID of the Company to get package for
     * @param packageId the ID of the package to retrieve
     * @param deleted whether to include deleted package in the results
     * @returns  A Promise representing the HTTP response with the requested package
     */
    @ApiOperation({ summary: "Get Package by id" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Get("get-package-by-id/packageId/:packageId/deleted/:deleted")
    async getPackageById(
        @GetUser() user: JwtUserPayload,
        @Param("packageId", ParseUUIDPipe) packageId: string,
        @Param("deleted") deleted: boolean,
    ): Promise<HttpResponse> {
        return this.projectService.getPackageById(user._id, user.companyId, packageId, deleted);
    }

    /**
     * Create or update project
     * @param userId - The ID of the user upserting the project.
     * @param createProjectDto -  The data to upsert the project with.
     * @returns - A Promise resolving to a CreatedResponse object.
     */
    @ApiOperation({ summary: "Create or Update Project" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Positions({
        category: "crm",
        name: moduleNames.crm.projects,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed],
    })
    @Post("upsert-project")
    async upsertProject(
        @GetUser() user: JwtUserPayload,
        @Body() createProjectDto: CreateProjectDto,
    ): Promise<HttpResponse> {
        return this.projectService.upsertProject(user.companyId, createProjectDto);
    }

    /**
     *Delete a project.
     *@param userId The ID of the user making the request.
     *@param deleteProjectDto The DTO containing the ID of the project to be deleted and the ID of the company it belongs to.
     *@returns A NoContentResponse with a success message if the project was deleted successfully.
     *@throws HttpException with a message "Project not found" if the project with the given ID and company ID was not found.
     *@throws InternalServerErrorException with the error message if any other error occurs during the deletion process.
     */
    @ApiOperation({ summary: "Delete Project" })
    @ApiNotFoundResponse({ description: "Project not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Positions({
        category: "crm",
        name: moduleNames.crm.projects,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed],
    })
    @Delete("delete-project")
    async deleteProject(
        @GetUser() user: JwtUserPayload,
        @Body() deleteProjectDto: DeleteProjectDto,
    ): Promise<HttpResponse> {
        return this.projectService.deleteProject(user.memberId, user.companyId, deleteProjectDto);
    }

    /**
     *Perm Delete a project.
     *@param userId The ID of the user making the request.
     *@param deleteProjectDto The DTO containing the ID of the project to be deleted and the ID of the company it belongs to.
     *@returns A NoContentResponse with a success message if the project was deleted successfully.
     *@throws HttpException with a message "Project not found" if the project with the given ID and company ID was not found.
     *@throws InternalServerErrorException with the error message if any other error occurs during the deletion process.
     */
    @ApiOperation({ summary: "Perm Delete Project" })
    @ApiNotFoundResponse({ description: "Project not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Positions({
        category: "crm",
        name: moduleNames.crm.projects,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed],
    })
    @Delete("perm-delete-project")
    async permDeleteProject(
        @GetUser() user: JwtUserPayload,
        @Body() deleteProjectDto: DeleteProjectDto,
    ): Promise<HttpResponse> {
        return this.projectService.permDeleteProject(user.companyId, deleteProjectDto);
    }

    /**
     *Restores a deleted Project.
     *@param userId The ID of the user performing the operation.
     *@param restoreProjectDto The DTO containing the ID of the project to be restored.
     *@returns An OkResponse with a success message.
     *@throws An HttpException if the project with the specified ID is not found or if an internal server error occurs.
     */
    @ApiOperation({ summary: "Restore Project" })
    @ApiNotFoundResponse({ description: "Project not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Patch("restore-project")
    async restoreProject(
        @GetUser() user: JwtUserPayload,
        @Body() restoreProjectDto: RestoreProjectDto,
    ): Promise<HttpResponse> {
        return this.projectService.restoreProject(user.memberId, user.companyId, restoreProjectDto);
    }

    /**
     *Retrieves project for a given company ID, including deleted records if requested.
     *@param userId - The ID of the user making the request.
     *@param companyId - The ID of the company for which to retrieve project.
     *@param deleted - Whether to include deleted project in the results.
     *@param paginationRequestDto - The pagination options for the query.
     *@returns A response containing an array of projects.
     *@throws {HttpException} When a client error occurs (e.g. invalid input parameters).
     *@throws {InternalServerErrorException} When a server error occurs.
     */
    @ApiOperation({ summary: "Get Project" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Get("get-project/oppId/:oppId/deleted/:deleted")
    async getProject(
        @GetUser() user: JwtUserPayload,
        @Param("oppId", ParseUUIDPipe) oppId: string,
        @Param("deleted") deleted: boolean,
        @Query() paginationRequestDto: PaginationRequestDto,
    ): Promise<HttpResponse> {
        return this.projectService.getProject(user._id, user.companyId, oppId, deleted, paginationRequestDto);
    }

    /**
     *Retrieves Opportunity list for operation stages of a given company ID, including deleted records if requested.
     *@param userId - The ID of the user making the request.
     *@param companyId - The ID of the company for which to retrieve Opportunity.
     *@param deleted - Whether to include deleted Opportunity in the results.
     *@param paginationRequestDto - The pagination options for the query.
     *@returns A response containing an array of Opportunitys.
     *@throws {HttpException} When a client error occurs (e.g. invalid input parameters).
     *@throws {InternalServerErrorException} When a server error occurs.
     */
    @ApiOperation({ summary: "Get Project" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Get("get-opp-for-clockin")
    async getOppForPO(
        @GetUser() user: JwtUserPayload,
        @Query() paginationRequestDto: PaginationRequestDto,
    ): Promise<HttpResponse> {
        if (user.planType === SubscriptionPlanTypeEnum.PROPLUS)
            return this.projectService.getOppForPO(user._id, user.companyId, paginationRequestDto);
        else
            return this.projectService.getOppForCustomProjects(
                user._id,
                user.companyId,
                paginationRequestDto,
            );
    }

    /**
     *Retrieves Opp for Clock in page with pitch & layers for mobile app of a given company ID, including deleted records if requested.
     *@param userId - The ID of the user making the request.
     *@param companyId - The ID of the company for which to retrieve Opportunity.
     *@param deleted - Whether to include deleted Opportunity in the results.
     *@param paginationRequestDto - The pagination options for the query.
     *@returns A response containing an array of Opportunitys.
     *@throws {HttpException} When a client error occurs (e.g. invalid input parameters).
     *@throws {InternalServerErrorException} When a server error occurs.
     */
    @ApiOperation({ summary: "Get Project" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Get("get-opp-for-clockin-mobile")
    async getOppForPOForMobile(
        @GetUser() user: JwtUserPayload,
        @Query() paginationRequestDto: PaginationRequestDto,
    ): Promise<HttpResponse> {
        if (user.planType === SubscriptionPlanTypeEnum.PROPLUS)
            return this.projectService.getOppForPOForMobile(user._id, user.companyId, paginationRequestDto);
        else
            return this.projectService.getOppForCustomProjects(
                user._id,
                user.companyId,
                paginationRequestDto,
            );
    }

    /**
     *Retrieves the Project by its ID.
     *@param {string} userId - The ID of the user.
     *@param {string} companyId - The ID of the company.
     *@param {string} taxId - The ID of the Project.
     *@param {boolean} deleted - A flag indicating whether the Project is deleted or not.
     *@returns {Promise<OkResponse>} An object containing the retrieved Project.
     *@throws {HttpException} If an HTTP exception occurs.
     *@throws {InternalServerErrorException} If an internal server error occurs.
     */
    @ApiOperation({ summary: "Get Project by id" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Get("get-project-by-id/projectId/:projectId/deleted/:deleted")
    async getProjectById(
        @GetUser() user: JwtUserPayload,
        @Param("projectId", ParseUUIDPipe) projectId: string,
        @Param("deleted") deleted: boolean,
    ): Promise<HttpResponse> {
        return this.projectService.getProjectById(user._id, user.companyId, projectId, deleted);
    }

    /**
     *Creates a new tax jurisdiction for a given user.
     *@param {string} userId - The ID of the user creating the tax jurisdiction.
     *@param {CreateTaxDto} createTaxDto - The data to create the tax jurisdiction with.
     *@returns {Promise<CreatedResponse>} - A Promise resolving to a CreatedResponse object.
     *@throws {HttpException} If the tax jurisdiction already exists.
     *@throws {InternalServerErrorException} If an error occurs while creating the tax jurisdiction.
     */
    @ApiOperation({ summary: "Create Tax Jurisdictions" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Post("create-tax")
    async createTaxJurisdictions(
        @GetUser() user: JwtUserPayload,
        @Body() createTaxDto: CreateTaxJurisdictionDto,
    ): Promise<HttpResponse> {
        return this.projectService.createTaxJurisdictions(user.companyId, createTaxDto);
    }

    /**
     *Deletes a tax jurisdiction from the database.
     *@param userId - The ID of the user making the request.
     *@param deleteTaxDto - The data transfer object containing the ID of the tax jurisdiction to be deleted.
     *@throws {HttpException} - Throws an HTTP exception if the tax jurisdiction does not exist or if there is a server error.
     *@throws {InternalServerErrorException} - Throws an internal server error exception if there is an unexpected error.
     *@returns {NoContentResponse} - Returns a response indicating that the tax jurisdiction was deleted successfully.
     */
    @ApiOperation({ summary: "Delete Tax Jurisdictions" })
    @ApiNotFoundResponse({ description: "Tax Jurisdictions not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Delete("delete-tax")
    async deleteTaxJurisdictions(
        @GetUser() user: JwtUserPayload,
        @Body() deleteTaxDto: DeleteTaxJurisdictionDto,
    ): Promise<HttpResponse> {
        return this.projectService.deleteTaxJurisdictions(user._id, deleteTaxDto);
    }

    /**
     *Delete a tax jurisdiction permanently.
     *@param userId The ID of the user making the request.
     *@param deleteTaxDto The DTO containing the ID of the tax jurisdiction to be deleted and the ID of the company it belongs to.
     *@returns A NoContentResponse with a success message if the tax jurisdiction was deleted successfully.
     *@throws HttpException with a message "Tax not found" if the tax jurisdiction with the given ID and company ID was not found.
     *@throws InternalServerErrorException with the error message if any other error occurs during the deletion process.
     */
    @ApiOperation({ summary: "Delete Tax Jurisdictions" })
    @ApiNotFoundResponse({ description: "Tax Jurisdictions not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Delete("perm-delete-tax")
    async permDeleteTaxJurisdictions(
        @GetUser() user: JwtUserPayload,
        @Body() deleteTaxDto: DeleteTaxJurisdictionDto,
    ): Promise<HttpResponse> {
        return this.projectService.permDeleteTaxJurisdictions(user.companyId, deleteTaxDto);
    }

    /**
     *Restores a deleted tax jurisdiction.
     *@param userId The ID of the user performing the operation.
     *@param restoreTaxDto The DTO containing the ID of the tax to be restored.
     *@returns An OkResponse with a success message.
     *@throws An HttpException if the tax with the specified ID is not found or if an internal server error occurs.
     */
    @ApiOperation({ summary: "Restore Tax Jurisdictions" })
    @ApiNotFoundResponse({ description: "Tax Jurisdictions not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Patch("restore-tax")
    async restoreTax(
        @GetUser() user: JwtUserPayload,
        @Body() restoreTaxDto: RestoreTaxJurisdictionDto,
    ): Promise<HttpResponse> {
        return this.projectService.restoreTax(user._id, restoreTaxDto);
    }

    /**
     *Update a tax jurisdiction with the given data
     *@param userId - The ID of the user making the request
     *@param updateTaxDto - The data to update the tax jurisdiction with
     *@returns A success response with a message indicating the tax jurisdiction was updated successfully
     *@throws HttpException if the tax jurisdiction does not exist or if there is an internal server error
     */
    @ApiOperation({ summary: "Update Tax Jurisdictions" })
    @ApiNotFoundResponse({ description: "Tax Jurisdictions not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Patch("update-tax")
    async updateTaxJurisdictions(
        @GetUser() user: JwtUserPayload,
        @Body() updateTaxDto: UpdateTaxJurisdictionDto,
    ): Promise<HttpResponse> {
        return this.projectService.updateTaxJurisdictions(user._id, updateTaxDto);
    }

    /**
     *Retrieves tax jurisdictions for a given company ID, including deleted records if requested.
     *@param userId - The ID of the user making the request.
     *@param companyId - The ID of the company for which to retrieve tax jurisdictions.
     *@param deleted - Whether to include deleted tax jurisdictions in the results.
     *@param paginationRequestDto - The pagination options for the query.
     *@returns A response containing an array of tax jurisdictions.
     *@throws {HttpException} When a client error occurs (e.g. invalid input parameters).
     *@throws {InternalServerErrorException} When a server error occurs.
     */
    @ApiOperation({ summary: "Get Tax Jurisdictions" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Get("get-tax/deleted/:deleted")
    async getTaxJurisdictions(
        @GetUser() user: JwtUserPayload,
        @Param("deleted") deleted: boolean,
        @Query() paginationRequestDto: PaginationRequestDto,
    ): Promise<HttpResponse> {
        return this.projectService.getTaxJurisdictions(
            user._id,
            user.companyId,
            deleted,
            paginationRequestDto,
        );
    }

    /**
     *Retrieves the tax jurisdictions by its ID.
     *@param {string} userId - The ID of the user.
     *@param {string} companyId - The ID of the company.
     *@param {string} taxId - The ID of the tax jurisdiction.
     *@param {boolean} deleted - A flag indicating whether the tax jurisdiction is deleted or not.
     *@returns {Promise<OkResponse>} An object containing the retrieved tax jurisdiction.
     *@throws {HttpException} If an HTTP exception occurs.
     *@throws {InternalServerErrorException} If an internal server error occurs.
     */
    @ApiOperation({ summary: "Get Tax Jurisdictions by id" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Get("get-tax-by-id/taxId/:taxId/deleted/:deleted")
    async getTaxJurisdictionsById(
        @GetUser() user: JwtUserPayload,
        @Param("taxId", ParseUUIDPipe) taxId: string,
        @Param("deleted") deleted: boolean,
    ): Promise<HttpResponse> {
        return this.projectService.getTaxJurisdictionsById(user._id, user.companyId, taxId, deleted);
    }

    @ApiOperation({ summary: "Create Order" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Post("create-order")
    async createOrder(
        @GetUser() user: JwtUserPayload,
        @Body() createOrderDto: CreateOrderDto,
    ): Promise<HttpResponse> {
        return this.projectService.createOrder(user.companyId, createOrderDto);
    }

    @ApiOperation({ summary: "Update Opp Client" })
    @ApiNotFoundResponse({ description: "Opp CLient not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Patch("setAccepted")
    async setAccepted(
        @GetUser() user: JwtUserPayload,
        @Body() updateOppClientDto: UpdateOppClientDto,
    ): Promise<HttpResponse> {
        return this.projectService.setAccepted(user._id, user.companyId, user.memberId, updateOppClientDto);
    }

    /**
     *Update a Order with the given data
     *@param userId - The ID of the user making the request
     *@param updateOrderDto - The data to update the Order with
     *@returns A success response with a message indicating the Order was updated successfully
     *@throws HttpException if the Order does not exist or if there is an internal server error
     */
    @ApiOperation({ summary: "Update Order" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Patch("update-order")
    async updateOrder(
        @GetUser() user: JwtUserPayload,
        @Body() updateOrderDto: UpdateOrderDto,
    ): Promise<HttpResponse> {
        return this.projectService.updateOrder(user.companyId, updateOrderDto);
    }

    /**
     *Delete a Order permanently.
     *@param userId The ID of the user making the request.
     *@param deleteOrderDto The DTO containing the ID of the Order to be deleted and the ID of the company it belongs to.
     *@returns A NoContentResponse with a success message if the Order was deleted successfully.
     *@throws HttpException with a message "Order not found" if the Order with the given ID and company ID was not found.
     *@throws InternalServerErrorException with the error message if any other error occurs during the deletion process.
     */
    @ApiOperation({ summary: "Delete Order" })
    @ApiNotFoundResponse({ description: "Order not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Delete("delete-order")
    async deleteOrder(
        @GetUser() user: JwtUserPayload,
        @Body() deleteOrderDto: DeleteOrderDto,
    ): Promise<HttpResponse> {
        return this.projectService.deleteOrder(user.companyId, deleteOrderDto);
    }

    /**
     *Restores a deleted Order .
     *@param userId The ID of the user performing the operation.
     *@param restoreOrderDto The DTO containing the ID of the Order to be restored.
     *@returns An OkResponse with a success message.
     *@throws An HttpException if the Order with the specified ID is not found or if an internal server error occurs.
     */
    @ApiOperation({ summary: "Restore Order s" })
    @ApiNotFoundResponse({ description: "Order s not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Patch("restore-order")
    async restoreOrder(
        @GetUser() user: JwtUserPayload,
        @Body() restoreOrderDto: RestoreOrderDto,
    ): Promise<HttpResponse> {
        return this.projectService.restoreOrder(user._id, restoreOrderDto);
    }

    /**
    //  *Deletes a Order from the database.
    //  *@param userId - The ID of the user making the request.
    //  *@param deleteOrderDto - The data transfer object containing the ID of the Order to be deleted.
    //  *@throws {HttpException} - Throws an HTTP exception if the Order does not exist or if there is a server error.
    //  *@throws {InternalServerErrorException} - Throws an internal server error exception if there is an unexpected error.
    //  *@returns {NoContentResponse} - Returns a response indicating that the Order was deleted successfully.
    //  */
    // @ApiOperation({ summary: "Delete Order" })
    // @ApiNotFoundResponse({ description: "Order not found" })
    // @ApiInternalServerErrorResponse({ description: "Server Error" })
    // @Delete("perm-delete-order")
    // async permDeleteOrder(
    //     @GetUser() user: JwtUserPayload,
    //     @Body() deleteOrderDto: DeleteOrderDto,
    // ): Promise<HttpResponse> {
    //     return this.projectService.deleteOrder(user.companyId, deleteOrderDto);
    // }

    /**
     *Retrieves order for a given company ID, including deleted records if requested.
     *@param userId - The ID of the user making the request.
     *@param companyId - The ID of the company for which to retrieve order.
     *@param deleted - Whether to include deleted order in the results.
     *@param paginationRequestDto - The pagination options for the query.
     *@returns A response containing an array of order.
     *@throws {HttpException} When a client error occurs (e.g. invalid input parameters).
     *@throws {InternalServerErrorException} When a server error occurs.
     */
    @ApiOperation({ summary: "Get Order" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Get("get-order")
    async getOrder(
        @GetUser() user: JwtUserPayload,
        @Query() getOrderDto: GetOrderDto,
    ): Promise<HttpResponse> {
        return this.projectService.getOrder(user.companyId, getOrderDto);
    }

    /**
     *Retrieves the order by its ID.
     *@param {string} userId - The ID of the user.
     *@param {string} companyId - The ID of the company.
     *@param {string} orderId - The ID of the order.
     *@param {boolean} deleted - A flag indicating whether the order is deleted or not.
     *@returns {Promise<OkResponse>} An object containing the retrieved order.
     *@throws {HttpException} If an HTTP exception occurs.
     *@throws {InternalServerErrorException} If an internal server error occurs.
     */
    @ApiOperation({ summary: "Get Order by id" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Get("get-order-by-id/orderId/:orderId/deleted/:deleted")
    async getOrderById(
        @GetUser() user: JwtUserPayload,
        @Param("orderId", ParseUUIDPipe) orderId: string,
        @Param("deleted") deleted: boolean,
    ): Promise<HttpResponse> {
        return this.projectService.getOrderById(user._id, user.companyId, orderId, deleted);
    }

    /**
     * Create a new price
     * @param userId - The ID of the user making the request.
     * @param companyId - The ID of the company .
     * @param projectId - The ID of the project .
     * @param createdBy - The ID of the member .
     * @returns The HTTP response.
     */
    @ApiOperation({ summary: "Create Price" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Post("create-price")
    async createPrice(
        @GetUser() user: JwtUserPayload,
        @Body() addPriceDto: AddPriceDto,
    ): Promise<HttpResponse> {
        return this.projectService.addPrice(user.companyId, addPriceDto);
    }

    /**
     * Get price
     * @param userId - The ID of the user making the request.
     * @param companyId - The ID of the company .
     * @param projectId - The ID of the project .
     * @param deleted - The boolean value.
     * @returns The HTTP response.
     */
    @ApiOperation({ summary: "Get Prices" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Get("price/projectId/:projectId/deleted/:deleted")
    async getPrices(
        @GetUser() user: JwtUserPayload,
        @Param("projectId", ParseUUIDPipe) projectId: string,
        @Param("deleted") deleted: boolean,
    ): Promise<HttpResponse> {
        return this.projectService.getPrices(user._id, user.companyId, projectId, deleted);
    }

    /**
     * Get price by id
     * @param userId - The ID of the user making the request.
     * @param companyId - The ID of the company .
     * @param priceId - The ID of the price .
     * @returns The HTTP response.
     */
    @ApiOperation({ summary: "Get Price by id" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Get("price-by-id/priceId/:priceId")
    async getPriceById(
        @GetUser() user: JwtUserPayload,
        @Param("priceId", ParseUUIDPipe) priceId: string,
    ): Promise<HttpResponse> {
        return this.projectService.getPriceById(user._id, user.companyId, priceId);
    }

    /**
     * @description Get all active prices of opp other than selected price
     * @param userId - The ID of the user making the request.
     * @param companyId - The ID of the company.
     * @param priceId - The price of the selected project to filter prices.
     * @param oppId - The ID of the opportunity to filter prices.
     * @returns The HTTP response containing the filtered prices.
     */
    @ApiOperation({ summary: "Get all active prices of opp other than selected price" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Get("opp-all-active-price/oppId/:oppId/priceId/:priceId")
    async getPriceByProjectType(
        @GetUser() user: JwtUserPayload,
        @Param("oppId", ParseUUIDPipe) oppId: string,
        @Param("priceId", ParseUUIDPipe) priceId: string,
    ): Promise<HttpResponse> {
        return this.projectService.getPriceByProjectType(user.companyId, priceId, oppId);
    }

    /**
     * Update active price
     * @param userId - The ID of the user making the request.
     * @param companyId - The ID of the company .
     * @param projectId - The ID of the project .
     * @param priceId - The ID of the price to be updated .
     * @returns The HTTP response.
     */
    @ApiOperation({ summary: "Update active Price" })
    @ApiNotFoundResponse({ description: "price not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Patch("price-activate/projectId/:projectId/priceId/:priceId")
    async setPriceActive(
        @GetUser() user: JwtUserPayload,
        @Param("projectId", ParseUUIDPipe) projectId: string,
        @Param("priceId", ParseUUIDPipe) priceId: string,
    ): Promise<HttpResponse> {
        return this.projectService.setPriceActive(user.companyId, projectId, priceId, false);
    }

    /**
     * Delete a price
     * @param userId - The ID of the user making the request.
     * @param deletePriceDto - The DTO containing the price ID.
     * @returns The HTTP response.
     */
    @ApiOperation({ summary: "Delete Price" })
    @ApiNotFoundResponse({ description: "Price not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Delete("delete-price")
    async deletePrice(
        @GetUser() user: JwtUserPayload,
        @Body() deletePriceDto: DeletePriceDto,
    ): Promise<HttpResponse> {
        return this.projectService.deletePrice(user.companyId, deletePriceDto);
    }

    /**
     * Perm delete price
     * @param userId - The ID of the user making the request.
     * @param deletePriceDto - The DTO containing the price ID.
     * @returns A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Delete Price" })
    @ApiNotFoundResponse({ description: "Price not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Delete("perm-delete-price")
    async permDeletePrice(
        @GetUser() user: JwtUserPayload,
        @Body() deletePriceDto: DeletePriceDto,
    ): Promise<HttpResponse> {
        return this.projectService.permDeletePrice(user.companyId, deletePriceDto);
    }

    /**
     * Restore a price
     * @param userId - The ID of the user making the request.
     * @param restorePriceDto - The price restore data.
     * @returns - A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Restore Price" })
    @ApiNotFoundResponse({ description: "Price not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Patch("restore-price")
    async restorePrice(
        @GetUser() user: JwtUserPayload,
        @Body() restorePriceDto: RestorePriceDto,
    ): Promise<HttpResponse> {
        return this.projectService.restorePrice(user._id, restorePriceDto);
    }

    /**
     * get search list of projects using search for project report
     * @param userId - The ID of the user making the request.
     * @param companyId - The ID of the company making the request.
     * @param search - The text to search.
     * @returns - A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Project Active Search" })
    @ApiNotFoundResponse({ description: "Project not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @ApiQuery({
        name: "search",
        type: String,
        description: "search keyword",
        required: false,
    })
    @Get("project-active-search")
    async activeSearch(
        @GetUser() user: JwtUserPayload,
        @Query("search") search?: string,
    ): Promise<HttpResponse> {
        if (user.planType === SubscriptionPlanTypeEnum.PROPLUS) {
            return this.projectService.activeSearch(search, user.companyId);
        } else {
            return this.projectService.activeSearchForCustomProject(user.companyId, search);
        }
    }

    /**
     * update packageId selected & discount for the order in price
     * @param priceId - The ID of the price to be updated.
     * @param selectedPackage - The Array of selected Id of packages.
     * @returns - A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Update selected Packages & discount in price" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    // @ApiBody({
    //     type: [String],
    //     description: "selected package Id",
    //     required: false,
    // })
    @Patch("update-price")
    async updatePrice(@Body() updatePriceDto: UpdatePriceDto): Promise<HttpResponse> {
        return this.projectService.updatePrice(updatePriceDto);
    }

    /**
     * check if project has accepted order
     * @param priceId - The ID of the price to be updated.
     * @returns - A promise that resolves to an boolean response.
     */
    @ApiOperation({ summary: "Update selected Packages" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Get("check-project-accepted/projectId/:projectId")
    async checkIfProjectAccepted(@Param("projectId", ParseUUIDPipe) projectId: string): Promise<boolean> {
        return this.projectService.checkIfProjectAccepted(projectId);
    }

    /**
     * get project inputs to show
     * @param companyId - The ID of the company to get inputs.
     * @param projectType - The ID of the projectType to get updated.
     * @returns - A promise that resolves to an boolean response.
     */
    @ApiOperation({ summary: "get project inputs to show" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Get("project-inputs")
    async projectInputDetails(
        @GetUser() user: JwtUserPayload,
        @Query() projectInputsDto: ProjectInputsDto,
    ): Promise<HttpResponse> {
        return this.projectService.projectInputDetails(user.companyId, projectInputsDto);
    }

    /**
     * API to get group name that are in use by tasks
     * @param companyId
     * @param projectTypeId
     */
    @ApiOperation({ summary: "get group name that are in use by tasks" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Get("task-groups-in-use")
    async projectTypeGroupCheck(@Param("companyId") companyId: string): Promise<HttpResponse> {
        return this.projectService.projectTypeGroupCheck(companyId);
    }

    /**
     * Create a new Option
     * @param userId - The ID of the user making the request.
     * @param createOptionDto - The data needed to create a new Option.
     * @returns - An HTTP response indicating success or failure.
     */
    @ApiOperation({ summary: "Create Option" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Post("create-option")
    async createOption(
        @GetUser() user: JwtUserPayload,
        @Body() createOptionDto: CreateOptionDto,
    ): Promise<HttpResponse> {
        return this.projectService.createOption(user.companyId, createOptionDto);
    }

    /**
     * Update Option
     * @param userId - The ID of the user making the request.
     * @param updateOptionDto - The Option sequence update data.
     * @returns - A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Update Option" })
    @ApiNotFoundResponse({ description: "Option not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Patch("update-option")
    async updateOption(
        @GetUser() user: JwtUserPayload,
        @Body() updateOptionDto: UpdateOptionDto,
    ): Promise<HttpResponse> {
        return this.projectService.updateOption(user._id, updateOptionDto);
    }

    /**
     * Delete a Option
     * @param userId - The ID of the user making the request.
     * @param deleteOptionDto - The DTO containing the Option ID.
     * @returns The HTTP response.
     */
    @ApiOperation({ summary: "Delete Option" })
    @ApiNotFoundResponse({ description: "Option not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Delete("delete-option")
    async deleteOption(
        @GetUser() user: JwtUserPayload,
        @Body() deleteOptionDto: DeleteRestoreDto,
    ): Promise<HttpResponse> {
        return this.projectService.deleteOption(user._id, deleteOptionDto);
    }

    /**
     * Restore a Option
     * @param userId - The ID of the user making the request.
     * @param restoreOptionDto - The Option restore data.
     * @returns - A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Restore Option" })
    @ApiNotFoundResponse({ description: "Option not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Patch("restore-option")
    async restoreOption(
        @GetUser() user: JwtUserPayload,
        @Body() restoreOptionDto: DeleteRestoreDto,
    ): Promise<HttpResponse> {
        return this.projectService.restoreOption(user._id, restoreOptionDto);
    }

    /**
     * Get Option for a company
     * @param userId - User ID of the requester
     * @param companyId - Company ID of the Company to get Option for
     * @param deleted - Whether to include deleted Option in the results
     * @param paginationRequestDto - Pagination request details (offset, limit, etc.)
     * @returns List of Option for the specified Company
     */
    @ApiOperation({ summary: "Get Option" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Get("get-option/deleted/:deleted/")
    async getOption(
        @GetUser() user: JwtUserPayload,
        @Param("deleted") deleted: boolean,
        @Query() getOptionDto: GetPackageOptionDto,
    ): Promise<HttpResponse> {
        return this.projectService.getOption(user._id, user.companyId, deleted, getOptionDto);
    }

    /**
     * Get Option for a company by Option id
     * @param userId User ID of the requester
     * @param companyId Company ID of the Company to get Option for
     * @param OptionId the ID of the Option to retrieve
     * @param deleted whether to include deleted Option in the results
     * @returns  A Promise representing the HTTP response with the requested Option
     */
    @ApiOperation({ summary: "Get Option by id" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Get("get-option-by-id/OptionId/:OptionId/deleted/:deleted")
    async getOptionById(
        @GetUser() user: JwtUserPayload,
        @Param("OptionId", ParseUUIDPipe) OptionId: string,
        @Param("deleted") deleted: boolean,
    ): Promise<HttpResponse> {
        return this.projectService.getOptionById(user._id, user.companyId, OptionId, deleted);
    }
}
