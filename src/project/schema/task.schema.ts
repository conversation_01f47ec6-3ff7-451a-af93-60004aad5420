import { Schema, SchemaFactory, Prop } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import { Document } from "mongoose";
import { UUIDProp } from "src/shared/decorator/uuid.decorator";

export type TaskDocument = Task & Document;

@Schema({ timestamps: true, id: false, collection: "Task" })
export class Task {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @UUIDProp()
    companyId: string;

    @Prop({ required: true })
    type: string;

    @Prop({ required: true })
    name: string;

    @Prop()
    group: string;

    @Prop()
    description?: string;

    @Prop({ required: true })
    unit: string;

    @Prop({ required: false })
    unitName?: string;

    @Prop({ required: true })
    input: any[];

    @Prop({ required: true })
    material: any[];

    @Prop({ required: true })
    labor: any[];

    @Prop({ required: true })
    waste: number;

    @Prop({ default: false })
    deleted: boolean;

    @Prop({ default: false })
    default: boolean;

    @Prop({ default: true })
    active: boolean;

    @Prop({ default: false })
    hidden: boolean;

    @Prop({ required: false })
    code?: string;

    @Prop({ required: false })
    order?: number;

    @UUIDProp()
    createdBy: string;

    @Prop()
    createdAt?: Date;

    @Prop()
    modifiedAt?: Date;
}

export const TaskSchema = SchemaFactory.createForClass(Task);
