import { Schema, SchemaFactory, Prop } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import { Document, SchemaTypes } from "mongoose";
import { UUIDProp } from "src/shared/decorator/uuid.decorator";

export type ProjectTypeDocument = ProjectType & Document;

export class SalesCommision {
    // all values are stored in decimal for percentage eg. 10/100
    @Prop({ required: true, default: 0.1 })
    commission: number;

    @Prop({ required: false })
    jobSale: number;

    @Prop({ required: false })
    jobStart: number;

    @Prop({ required: false })
    jobCompletion: number;
}
@Schema({ timestamps: true, id: false, collection: "ProjectType" })
export class ProjectType {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @UUIDProp()
    companyId: string;

    @Prop({ required: true })
    name: string;

    @Prop({ default: false })
    typeReplacement: boolean;

    @Prop({ required: true })
    description: string;

    @Prop({ default: false })
    permitRequired: boolean;

    @Prop({ default: false })
    asbTestRequired: boolean;

    @Prop({ default: false })
    deleted: boolean;

    @Prop({ required: false })
    colorCode?: string;

    @UUIDProp()
    createdBy: string;

    @Prop({ required: false })
    markup?: number;

    @Prop({ required: false })
    deposit?: number;

    @Prop({ required: false })
    downPmt?: number;

    @Prop({ required: false })
    typeMinimum?: number;

    @Prop({ default: false })
    usesPitch: boolean;

    @Prop({ type: SchemaTypes.Mixed, required: false })
    groups: any;

    @Prop({ required: false, type: SchemaTypes.Mixed })
    pitchMod?: any;

    @Prop({ required: false, type: SchemaTypes.Mixed })
    priceColor?: any;

    @Prop({ type: Object })
    salesCommision: SalesCommision;

    @Prop({ required: false, type: Array })
    questions: string[];

    /**
     * used to calc minimum travel fee
     */
    @Prop({ required: false })
    minTravelPpl?: number;

    @Prop()
    createdAt?: Date;

    @Prop()
    modifiedAt?: Date;
}

export const ProjectTypeSchema = SchemaFactory.createForClass(ProjectType);
