import { Schema, SchemaFactory, Prop } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import { Document } from "mongoose";
import { UUIDProp } from "src/shared/decorator/uuid.decorator";

export type CrewPositionDocument = CrewPosition & Document;

@Schema({ timestamps: true, id: false, collection: "CrewPosition" })
export class CrewPosition {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @UUIDProp()
    companyId: string;

    @Prop({ required: true })
    title: string;

    @Prop({ required: false })
    code?: string;

    @Prop({ required: true })
    rate: number;

    @Prop({ default: false })
    deleted: boolean;

    @Prop({ default: true })
    deletable: boolean;

    @UUIDProp()
    createdBy: string;

    @Prop()
    createdAt?: Date;

    @Prop()
    modifiedAt?: Date;
}

export const CrewPositionSchema = SchemaFactory.createForClass(CrewPosition);
