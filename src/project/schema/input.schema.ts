import { Schema, SchemaFactory, Prop } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import { Document } from "mongoose";
import { UUIDProp } from "src/shared/decorator/uuid.decorator";

export type InputDocument = Input & Document;

@Schema({ timestamps: true, id: false, collection: "Input" })
export class Input {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @UUIDProp()
    companyId: string;

    @Prop({ required: true })
    name: string;

    @Prop({ required: true })
    unit: string;

    @Prop({ required: true })
    projectType: string;

    @Prop({ required: true })
    orderNumber: number;

    @Prop({ default: false })
    deleted: boolean;

    @Prop({ default: false })
    hidden: boolean;

    @Prop({ default: false })
    isNew: boolean;

    @Prop()
    code?: string;

    @UUIDProp()
    createdBy: string;

    @Prop()
    createdAt?: Date;

    @Prop()
    modifiedAt?: Date;

    @Prop()
    deletedAt?: Date;
}

export const InputSchema = SchemaFactory.createForClass(Input);
