import { Schema, SchemaFactory, Prop } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import { Document } from "mongoose";
import { UUIDProp } from "src/shared/decorator/uuid.decorator";

export type SubCategoryDocument = SubCategory & Document;

@Schema({ timestamps: true, id: false, collection: "SubCategory" })
export class SubCategory {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @Prop({ required: true })
    categoryId: string;

    @UUIDProp()
    companyId: string;

    @Prop({ required: true })
    name: string;

    @Prop({ default: false })
    deleted: boolean;

    @UUIDProp()
    createdBy: string;

    @Prop()
    createdAt?: Date;

    @Prop()
    modifiedAt?: Date;
}

export const SubCategorySchema = SchemaFactory.createForClass(SubCategory);
