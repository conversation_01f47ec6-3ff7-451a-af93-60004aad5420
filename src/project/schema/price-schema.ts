import { Schema, SchemaFactory, Prop } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import mongoose, { Document } from "mongoose";

export type PriceDocument = Price & Document;

@Schema({ timestamps: true, id: false, strict: false, collection: "Price" })
export class Price {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @Prop({ required: true })
    companyId: string;

    @Prop({ required: true })
    projectId: string;

    @Prop({ required: true })
    oppId: string;

    // @Prop()
    // clientId?: string;

    @Prop({ required: true })
    contactId: string;

    @Prop({ required: false })
    avgPitch: number;

    @Prop({ required: true, type: mongoose.Schema.Types.Mixed })
    projectType: any;

    @Prop({ required: true })
    tasks: any[];

    @Prop({ required: true, type: mongoose.Schema.Types.Mixed })
    variables: any;

    @Prop({ required: true })
    createdBy: string;

    @Prop({ default: false })
    deleted: boolean;

    @Prop({ default: true })
    active: boolean;

    @Prop({ required: false })
    discount?: number;

    @Prop({ required: false })
    duration?: number;

    @Prop({ required: false, type: mongoose.Schema.Types.Mixed })
    tax?: any;

    @Prop({ required: true })
    state: string;

    @Prop()
    createdAt?: Date;

    @Prop()
    modifiedAt?: Date;
}

export const PriceSchema = SchemaFactory.createForClass(Price);
