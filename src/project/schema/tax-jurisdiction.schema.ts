import { Schema, SchemaFactory, Prop } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import { Document } from "mongoose";
import { UUIDProp } from "src/shared/decorator/uuid.decorator";

export type TaxJurisdictionDocument = TaxJurisdiction & Document;

@Schema({ timestamps: true, id: false, collection: "TaxJurisdiction" })
export class TaxJurisdiction {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @UUIDProp()
    companyId: string;

    @Prop({ required: true })
    name: string;

    @Prop({ required: false })
    city: string;

    @Prop({ required: false })
    state: string;

    @Prop({ required: true })
    rate: number;

    @Prop({ default: 0 })
    salesTax: number;

    @UUIDProp()
    createdBy: string;

    @Prop({ default: false })
    deleted: boolean;

    @Prop()
    createdAt?: Date;

    @Prop()
    modifiedAt?: Date;
}

export const TaxJurisdictionSchema = SchemaFactory.createForClass(TaxJurisdiction);
