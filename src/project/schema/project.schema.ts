import { Schema, SchemaFactory, Prop } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import { Document } from "mongoose";
import { UUIDProp } from "src/shared/decorator/uuid.decorator";

export type ProjectDocument = Project & Document;

@Schema({ timestamps: true, id: false, strict: false, collection: "Project" })
export class Project {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @UUIDProp()
    companyId: string;

    // @UUIDProp({ required: false })
    // clientId?: string;

    @UUIDProp()
    contactId: string;

    @Prop({ required: true })
    name: string;

    @Prop({ required: false })
    notes?: string;

    @UUIDProp()
    projectType: string;

    @UUIDProp({ required: false })
    priceId?: string;

    @UUIDProp({ required: false })
    oppId?: string;

    @UUIDProp({ required: false })
    orderId?: string;

    @UUIDProp()
    createdBy: string;

    @Prop({ default: false })
    deleted: boolean;

    @Prop()
    createdAt?: Date;

    @Prop()
    modifiedAt?: Date;
}

export const ProjectSchema = SchemaFactory.createForClass(Project);
