import { Schema, SchemaFactory, Prop } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import { Document } from "mongoose";
import { VendorEnum } from "../enum/vendor.enum";
import { UUIDProp } from "src/shared/decorator/uuid.decorator";

export type MaterialDocument = Material & Document;

@Schema({ timestamps: true, id: false, collection: "Material" })
export class Material {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @UUIDProp()
    companyId: string;

    @UUIDProp()
    categoryId: string;

    @UUIDProp()
    subCategoryId: string;

    @Prop({ required: true })
    name: string;

    @Prop()
    description?: string;

    @Prop({ required: true })
    unitId: string;

    @Prop({ required: true })
    cost: number;

    @Prop({ required: true, type: Number, enum: VendorEnum })
    vendor: VendorEnum;

    @Prop({ default: false })
    inv: boolean;

    @Prop({ default: false })
    deleted: boolean;

    @UUIDProp()
    createdBy: string;

    @Prop({ required: false })
    code?: string;

    @Prop({ required: false, default: false })
    invActive?: boolean;

    @Prop({ required: false })
    variationsList?: any[];

    @Prop({ required: false, default: false })
    variations: boolean;

    @Prop()
    createdAt?: Date;

    @Prop()
    modifiedAt?: Date;
}

export const MaterialSchema = SchemaFactory.createForClass(Material);
