import { ApiProperty } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsString, <PERSON><PERSON>otEmpty, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsUUID } from "class-validator";

export class CreateCategoryDto {
    @ApiProperty({ description: "Category name", required: true })
    @IsString()
    @MinLength(2)
    @MaxLength(20)
    @Transform(({ value }) => value.trim(), { toClassOnly: true })
    @IsNotEmpty()
    name: string;

    // @ApiProperty({ description: "Company", required: true })
    // @IsUUID()
    // @IsNotEmpty()
    // companyId: string;

    @ApiProperty({ description: "Created By", required: true })
    @IsUUID()
    @IsNotEmpty()
    createdBy: string;
}
