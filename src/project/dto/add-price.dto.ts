import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsNotEmpty, IsUUID, IsOptional, IsN<PERSON>ber } from "class-validator";

export class AddPriceDto {
    // @ApiProperty({ description: "Company", required: true })
    // @IsUUID()
    // @IsNotEmpty()
    // companyId: string;

    @ApiProperty({ description: "Project", required: true })
    @IsUUID()
    @IsNotEmpty()
    projectId: string;

    @ApiProperty({ description: "Created By", required: true })
    @IsUUID()
    @IsNotEmpty()
    createdBy: string;

    @ApiPropertyOptional({ description: "discount", required: false })
    @IsOptional()
    @IsNumber()
    discount?: number;

    @ApiPropertyOptional({ description: "selected Package data", required: false })
    @IsOptional()
    selectedPackage?: any;
}
