import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsBoolean, IsNotEmpty, IsNumber, IsOptional, IsString, IsUUID } from "class-validator";

export class CreateOptionDto {
    // @ApiProperty()
    // @IsUUID()
    // @IsNotEmpty()
    // companyId: string;

    @ApiProperty()
    @IsString()
    @IsNotEmpty()
    type: string;

    @ApiProperty()
    @IsString()
    @Transform(({ value }) => value.trim(), { toClassOnly: true })
    @IsNotEmpty()
    name: string;

    @ApiPropertyOptional()
    @IsBoolean()
    @IsOptional()
    active: boolean;

    @ApiProperty()
    @IsString()
    @IsNotEmpty()
    description: string;

    @ApiPropertyOptional()
    @IsNumber()
    @IsOptional()
    order?: number;

    @ApiPropertyOptional()
    @IsNumber()
    @IsOptional()
    upsell?: number;

    @ApiPropertyOptional()
    @IsOptional()
    selectedGroups?: any[];

    @ApiPropertyOptional()
    @IsOptional()
    packagesId?: any[];

    @ApiPropertyOptional()
    @IsOptional()
    taskArray?: any[];

    @ApiPropertyOptional()
    @IsOptional()
    replaceTask?: any[];

    @ApiPropertyOptional()
    @IsOptional()
    @IsBoolean()
    useMinPrice?: boolean;

    @ApiPropertyOptional()
    @IsOptional()
    @IsNumber()
    minPrice?: number;
}
