import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import {
    IsString,
    IsNotEmpty,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    IsUUID,
    IsOptional,
    IsBoolean,
    ValidateNested,
} from "class-validator";
import { skylightTypeEnum } from "../enum/skylight.enum";
import { Transform } from "class-transformer";

interface UserInputs {
    otherDetails: any[];
    roofAreas: {
        name: number;
        pitch: number;
        size: number;
        layers: number;
        ventArea: boolean;
        instPly: boolean;
        rmvPly: boolean;
        noAccess: boolean;
        twoStory: boolean;
    }[];
    bVents: number[];
    stemVents: number[];
    chimneys: {
        width: number;
        length: number;
    }[];
    skylights: {
        skylightType: skylightTypeEnum;
        width: number;
        length: number;
    }[];
    sunTunnels: number[];
    pipes: string[];
    eaves: number;
    rakes: number;
    sideWall: number;
    endWall: number;
    ridges: number;
    valleys: number;
    hips: number;
    pitchChange: number;
    // splitPipe2: number;
    // pipeFlash4: number;
    pipeFlash123: number;
    canVentsExist: number;
    ridgeVentExist: number;
    reroofAreas: any;
    // rawData: any;
    roofSQ: any;
    pitch: any;
    // roofSizeDetail: any;
}

export class CreateProjectDto {
    // @ApiProperty({ description: "Company", required: true })
    // @IsUUID()
    // @IsNotEmpty()
    // companyId: string;

    @ApiProperty({ description: "Contact id", required: true })
    @IsUUID()
    @IsNotEmpty()
    contactId: string;

    @ApiProperty({ description: "Opp id", required: true })
    @IsUUID()
    @IsNotEmpty()
    oppId: string;

    @ApiProperty({ description: "Name", required: true })
    @IsString()
    @MinLength(2)
    @MaxLength(100)
    @IsNotEmpty()
    @Transform(({ value }) => value.trim(), { toClassOnly: true })
    name: string;

    @ApiPropertyOptional({ description: "notes", required: false })
    @IsOptional()
    @IsString()
    notes?: string;

    @ApiProperty({ description: "Project type Id" })
    @IsUUID()
    @IsNotEmpty()
    projectType: string;

    @ApiPropertyOptional({
        description:
            "Project roof replacement inputs as array of object containing input id, name, value and unit",
    })
    @IsOptional()
    projectInputs?: any;

    @ApiPropertyOptional({
        description: "Project inputs entered by user",
        type: "object",
    })
    @IsOptional()
    @ValidateNested()
    customData: UserInputs;

    @ApiProperty({ description: "Created By", required: true })
    @IsUUID()
    @IsNotEmpty()
    createdBy: string;

    @ApiProperty({ description: "newProject", required: true })
    @IsBoolean()
    @IsNotEmpty()
    newProject: boolean;

    @ApiPropertyOptional({ description: "Project Id" })
    @IsUUID()
    @IsOptional()
    projectId?: string;

    @ApiPropertyOptional({ description: "Created Date" })
    @IsOptional()
    currDate?: any;
}
