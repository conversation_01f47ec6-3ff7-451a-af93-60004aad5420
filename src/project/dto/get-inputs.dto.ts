import { ApiPropertyOptional } from "@nestjs/swagger";
import { PaginationDto } from "src/shared/dto/pagination.dto";

export class GetInputsDto extends PaginationDto {
    @ApiPropertyOptional({ description: "Unit symbol" })
    unit?: string;

    @ApiPropertyOptional({ description: "Project Type Id" })
    projectType?: string;

    @ApiPropertyOptional({ description: "is this a new field" })
    isNew?: boolean;
}
