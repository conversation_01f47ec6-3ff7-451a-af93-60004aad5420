import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsNotEmpty, IsOptional, IsUUID } from "class-validator";

export class ProjectInputsDto {
    // @ApiProperty({ description: "companyId", required: true })
    // @IsUUID()
    // @IsNotEmpty()
    // companyId: string;

    @ApiProperty({ description: "projectTypeId", required: true })
    @IsUUID()
    @IsNotEmpty()
    projectType: string;

    @ApiPropertyOptional({ description: "projectId", required: true })
    @IsUUID()
    @IsOptional()
    projectId?: string;
}
