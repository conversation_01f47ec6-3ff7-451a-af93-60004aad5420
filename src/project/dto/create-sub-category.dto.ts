import { ApiProperty } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsString, IsNotEmpty, <PERSON><PERSON>eng<PERSON>, <PERSON><PERSON><PERSON>th, IsUUID } from "class-validator";

export class CreateSubCategoryDto {
    @ApiProperty({ description: "SubCategory name", required: true })
    @IsString()
    @MinLength(2)
    @MaxLength(20)
    @IsNotEmpty()
    @Transform(({ value }) => value.trim(), { toClassOnly: true })
    name: string;

    @ApiProperty({ description: "category", required: true })
    @IsUUID()
    @IsNotEmpty()
    categoryId: string;

    // @ApiProperty({ description: "Company", required: true })
    // @IsUUID()
    // @IsNotEmpty()
    // companyId: string;

    @ApiProperty({ description: "Created By", required: true })
    @IsUUID()
    @IsNotEmpty()
    createdBy: string;
}
