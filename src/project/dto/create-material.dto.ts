import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import {
    IsString,
    IsNotEmpty,
    MaxLength,
    MinLength,
    IsUUID,
    IsEnum,
    IsNumber,
    IsBoolean,
    IsOptional,
} from "class-validator";
import { VendorEnum } from "../enum/vendor.enum";
import { Transform } from "class-transformer";

export class CreateMaterialDto {
    // @ApiProperty({ description: "Company", required: true })
    // @IsUUID()
    // @IsNotEmpty()
    // companyId: string;

    @ApiProperty({ description: "Material name", required: true })
    @IsString()
    @MinLength(2)
    @MaxLength(60)
    @Transform(({ value }) => value.trim(), { toClassOnly: true })
    @IsNotEmpty()
    name: string;

    @ApiProperty({ description: "Category id", required: true })
    @IsString()
    @IsNotEmpty()
    categoryId: string;

    @ApiProperty({ description: "sub CategoryId", required: true })
    @IsString()
    @IsNotEmpty()
    subCategoryId: string;

    @ApiProperty({ description: "Description", required: true })
    @IsString()
    @IsNotEmpty()
    description: string;

    @ApiProperty({ description: "Unit id", required: true })
    @IsString()
    @IsNotEmpty()
    unitId: string;

    @ApiProperty({ description: "Cost", required: true })
    @IsNumber()
    @IsNotEmpty()
    cost: number;

    @ApiProperty({ description: "vendor enum", required: true })
    @IsNumber()
    @IsNotEmpty()
    @IsEnum(VendorEnum)
    vendor: VendorEnum;

    @ApiPropertyOptional({ description: "inv", required: true })
    @IsBoolean()
    @IsOptional()
    inv?: boolean;

    @ApiProperty({ description: "Created By", required: true })
    @IsUUID()
    @IsNotEmpty()
    createdBy: string;
}
