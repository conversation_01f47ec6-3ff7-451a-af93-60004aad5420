import { ApiProperty } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsString, IsNotEmpty, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsUUID } from "class-validator";

export class CreateUnitDto {
    @ApiProperty({ description: "Unit name", required: true })
    @IsString()
    @MinLength(2)
    @MaxLength(20)
    @IsNotEmpty()
    @Transform(({ value }) => value.trim(), { toClassOnly: true })
    name: string;

    @ApiProperty({ description: "Unit symbol", required: true })
    @IsString()
    @IsNotEmpty()
    symbol: string;

    // @ApiProperty({ description: "Company", required: true })
    // @IsUUID()
    // @IsNotEmpty()
    // companyId: string;

    @ApiProperty({ description: "Created By", required: true })
    @IsUUID()
    @IsNotEmpty()
    createdBy: string;
}
