import { ApiProperty, ApiPropertyOptional, PickType } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { CreateOrderDto } from "./create-order.dto";
import { IsUUID, IsNotEmpty, IsOptional, ValidateNested, IsObject, IsNumber } from "class-validator";
import { Transform } from "class-transformer";

interface ActualTotals {
    actualPrice: number;
    actualLaborCost: number;
    actualDays: number;
    subcontractorCost: number;
    inventoryCost: number;
    qbCOGS: number;
    other: number;
    receipts: number;
    actualMatCost: number;
}

function toNumberWithMax2Decimals(value: any): number {
    const num = value === "" ? 0 : Number(value);
    return Math.round(num * 100) / 100;
}

export class ModifiedBudget {
    @Transform(({ value }) => toNumberWithMax2Decimals(value))
    @Transform(({ value }) => toNumberWithMax2Decimals(value))
    @IsNumber()
    subContractorTotal: number;

    isSubcontractorOnly: boolean;

    @Transform(({ value }) => toNumberWithMax2Decimals(value))
    @Transform(({ value }) => toNumberWithMax2Decimals(value))
    @IsNumber()
    rawLaborBudget: number;
}

export class UpdateOrderDto {
    @ApiProperty({ description: "Order Id", required: true })
    @IsUUID()
    @IsNotEmpty()
    orderId: string;

    @ApiPropertyOptional({
        description: "Actual Totals",
        type: "object",
    })
    @IsOptional()
    @ValidateNested()
    actualTotals: ActualTotals;

    @ApiProperty({ description: "Project Id", required: false })
    @IsUUID()
    @IsOptional()
    projectId?: string;

    @ApiPropertyOptional({ description: "colors" })
    @IsOptional()
    @IsObject()
    colors?: object;

    @ApiPropertyOptional({ description: "notes" })
    @IsOptional()
    notes?: string;

    @ApiPropertyOptional({ description: "Material List boolean value" })
    @IsOptional()
    originalMatList?: boolean;

    @ApiPropertyOptional({ description: "modified Budget" })
    @IsOptional()
    @ValidateNested()
    @Type(() => ModifiedBudget)
    modifiedBudget?: ModifiedBudget;
}
