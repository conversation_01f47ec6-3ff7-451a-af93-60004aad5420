import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import {
    IsString,
    IsNotEmpty,
    MaxLength,
    MinLength,
    IsUUID,
    IsBoolean,
    IsOptional,
    IsNumber,
    IsArray,
} from "class-validator";

export class SalesCommision {
    @ApiPropertyOptional({ required: true, default: 0.1 })
    @IsNumber()
    @IsNotEmpty()
    commission?: number;

    @ApiPropertyOptional({ required: false })
    @IsNumber()
    @IsOptional()
    jobSale?: number;

    @ApiPropertyOptional({ required: false })
    @IsNumber()
    @IsOptional()
    jobStart?: number;

    @ApiPropertyOptional({ required: false })
    @IsNumber()
    @IsOptional()
    jobCompletion?: number;
}

export class CreateProjectTypeDto {
    // @ApiProperty({ description: "Company", required: true })
    // @IsUUID()
    // @IsNotEmpty()
    // companyId: string;

    @ApiProperty({ description: "Name", required: true })
    @IsString()
    @MinLength(2)
    @MaxLength(50)
    @IsNotEmpty()
    @Transform(({ value }) => value.trim(), { toClassOnly: true })
    name: string;

    @ApiProperty({ description: "Description", required: true })
    @IsString()
    @IsNotEmpty()
    description: string;

    @ApiPropertyOptional({ description: "Permit Required" })
    @IsBoolean()
    @IsOptional()
    permitRequired: boolean;

    @ApiPropertyOptional({ description: "Asbestos Test" })
    @IsBoolean()
    @IsOptional()
    asbTestRequired: boolean;

    @ApiProperty({ description: "Created By", required: true })
    @IsUUID()
    @IsNotEmpty()
    createdBy: string;

    @ApiPropertyOptional({ description: "color Code" })
    @IsString()
    @IsOptional()
    colorCode?: string;

    @ApiPropertyOptional({ description: "project type markup %" })
    @IsNumber()
    @IsOptional()
    markup?: number;

    @ApiPropertyOptional({ description: "project type deposit %" })
    @IsNumber()
    @IsOptional()
    deposit?: number;

    @ApiPropertyOptional({ description: "project type down Payment %" })
    @IsNumber()
    @IsOptional()
    downPmt?: number;

    @ApiPropertyOptional({ description: "Job Minimum value" })
    @IsOptional()
    @IsNumber()
    typeMinimum?: number;

    @ApiPropertyOptional({ description: "Uses Pitch" })
    @IsBoolean()
    @IsOptional()
    usesPitch: boolean;

    @ApiPropertyOptional({ description: "Pitch mod values" })
    @IsOptional()
    pitchMod?: any;

    @ApiPropertyOptional({ description: "Group" })
    @IsOptional()
    groups?: any;

    @ApiPropertyOptional({ description: "colors to show on price page" })
    @IsOptional()
    priceColor?: any;

    @ApiPropertyOptional({ description: "Sales Commission Settings" })
    @IsNotEmpty()
    salesCommision: SalesCommision;

    @ApiPropertyOptional({ description: "Question to ask for project type" })
    @IsOptional()
    @IsArray()
    questions?: string[];

    @ApiPropertyOptional({ description: "Minimum travel people" })
    @IsOptional()
    @IsNumber()
    minTravelPpl?: number;
}
