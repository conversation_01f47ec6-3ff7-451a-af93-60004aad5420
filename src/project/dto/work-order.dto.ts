import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsNotEmpty, IsOptional, IsUUID } from "class-validator";

// eslint-disable-next-line @typescript-eslint/no-empty-interface
// interface BasePackageDto {}
export class WorkOrderDto {
    // @ApiProperty({ description: "Company", required: true })
    // @IsUUID()
    // @IsNotEmpty()
    // companyId: string;

    @ApiProperty({ description: "Project", required: true })
    @IsUUID()
    @IsNotEmpty()
    projectId: string;

    @ApiPropertyOptional({ description: "chosen Upgrades", type: "any" })
    @IsOptional()
    chosenUpgrades?: any;
}
