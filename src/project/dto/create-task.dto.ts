import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import {
    IsString,
    IsNotEmpty,
    MaxLength,
    MinLength,
    IsUUID,
    IsArray,
    IsNumber,
    IsOptional,
    IsEnum,
    IsBoolean,
} from "class-validator";
import { TaskGroups } from "../enum/task-group.enum";
import { Transform } from "class-transformer";

export class CreateTaskDto {
    // @ApiProperty({ description: "Company", required: true })
    // @IsUUID()
    // @IsNotEmpty()
    // companyId: string;

    @ApiProperty({ description: "Type", required: true })
    @IsString()
    @IsNotEmpty()
    type: string;

    @ApiProperty({ description: "Task name", required: true })
    @IsString()
    @MinLength(2)
    @MaxLength(100)
    @IsNotEmpty()
    @Transform(({ value }) => value.trim(), { toClassOnly: true })
    name: string;

    @ApiProperty({ description: "Task description", required: true })
    @IsString()
    @IsNotEmpty()
    description: string;

    @ApiProperty({ description: "Task unit", required: true })
    @IsString()
    @IsNotEmpty()
    unit: string;

    @ApiPropertyOptional({ description: "Task unit Name", required: false })
    @IsOptional()
    unitName?: string;

    @ApiProperty({ description: "Task input", required: true })
    @IsArray()
    @IsNotEmpty()
    input: any[];

    @ApiProperty({ description: "Task material", required: true })
    @IsArray()
    @IsNotEmpty()
    material: any[];

    @ApiProperty({ description: "Task labor", required: true })
    @IsArray()
    @IsNotEmpty()
    labor: any[];

    @ApiProperty({ description: "Task waste", required: true })
    @IsNumber()
    @IsNotEmpty()
    waste: number;

    @ApiProperty({ description: "Created By", required: true })
    @IsUUID()
    @IsNotEmpty()
    createdBy: string;

    @ApiPropertyOptional({ description: "group" })
    @IsOptional()
    @IsString()
    group?: string;

    @ApiPropertyOptional({ description: "active" })
    @IsOptional()
    @IsBoolean()
    active?: boolean;

    @ApiProperty({ description: "Order", required: false })
    @IsNumber()
    @IsOptional()
    order?: number;
}
