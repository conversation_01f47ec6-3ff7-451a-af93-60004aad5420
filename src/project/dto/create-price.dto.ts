import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsString, IsNotEmpty, IsUUID, IsOptional, IsNumber } from "class-validator";

export class CreatePriceDto {
    // @ApiProperty({ description: "Company", required: true })
    // @IsUUID()
    // @IsNotEmpty()
    // companyId: string;

    @ApiProperty({ description: "Project", required: true })
    @IsUUID()
    @IsNotEmpty()
    projectId: string;

    @ApiProperty({ description: "Opp", required: true })
    @IsUUID()
    @IsNotEmpty()
    oppId: string;

    @ApiProperty({ description: "contact", required: true })
    @IsUUID()
    @IsNotEmpty()
    contactId: string;

    @ApiProperty({ description: "ProjectType object" })
    projectType: any;

    @ApiPropertyOptional({ description: "tasks" })
    @IsString()
    // @IsNotEmpty()
    tasks?: any;

    @ApiProperty({ description: "Variables", required: true })
    // @IsString()
    @IsNotEmpty()
    variables: any;

    @ApiProperty({ description: "Created By", required: true })
    @IsUUID()
    @IsNotEmpty()
    createdBy: string;

    @ApiProperty({ description: "roofSize", required: false })
    @IsOptional()
    roofSize?: any;

    @ApiProperty({ description: "packages", required: false })
    @IsOptional()
    packages?: any;

    @ApiPropertyOptional({ description: "discount", required: false })
    @IsOptional()
    @IsNumber()
    discount?: number;

    @ApiProperty({ description: "options", required: false })
    @IsOptional()
    options?: any;

    @ApiPropertyOptional({ description: "duration" })
    @IsOptional()
    @IsNumber()
    duration?: number;

    @ApiPropertyOptional({ description: "tax object" })
    @IsOptional()
    tax?: any;

    @ApiPropertyOptional({ description: "state" })
    @IsNotEmpty()
    @IsString()
    state: string;

    @ApiPropertyOptional({ description: "avg pitch" })
    @IsOptional()
    @IsNumber()
    avgPitch: number;
}
