import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsArray, IsBoolean, IsNotEmpty, IsNumber, IsOptional, IsString, IsUUID } from "class-validator";

export class ProjectDTO {
    @ApiProperty({ description: "Project Id", required: false })
    @IsOptional()
    @IsUUID()
    projectId: string;

    @ApiProperty({ description: "Type of the project", required: false })
    @IsOptional()
    @IsString()
    type?: string;

    @ApiProperty({ description: "Name of the project", required: false })
    @IsOptional()
    @IsString()
    name?: string;

    @ApiProperty({ description: "Notes for the project", required: false })
    @IsOptional()
    @IsString()
    notes?: string;

    @ApiProperty({ description: "Colors for the project", required: false })
    @IsOptional()
    colors?: any;

    @ApiProperty({ description: "Base package for the project", required: false })
    @IsOptional()
    @IsString()
    basePackage?: string;

    @ApiProperty({ description: "Chosen options for the project", required: false })
    @IsOptional()
    @IsArray()
    chosenOptions?: string[];

    @ApiProperty({ description: "Average pitch for the project", required: false })
    @IsOptional()
    @IsNumber()
    avgPitch?: number;

    @ApiProperty({ description: "Work order for the project", required: false })
    @IsOptional()
    @IsArray()
    workOrder?: any[];

    @ApiProperty({ description: "Price totals for the project", required: false })
    @IsOptional()
    priceTotals?: any;
}

export class CreateOrderDto {
    // @ApiProperty({ description: "Company Id", required: true })
    // @IsUUID()
    // @IsNotEmpty()
    // companyId: string;

    @ApiProperty({ description: "Opp Id", required: true })
    @IsUUID()
    @IsNotEmpty()
    oppId: string;

    @ApiProperty({ description: "Project Id", required: true })
    @IsUUID()
    @IsNotEmpty()
    projectId: string;

    @ApiProperty({ description: "ProjectPrice Id", required: true })
    @IsUUID()
    @IsNotEmpty()
    projectPriceId: string;

    @ApiProperty({ description: "Price totals", required: true })
    @IsNotEmpty()
    priceTotals: any;

    @ApiProperty({ description: "Material list", required: true })
    @IsNotEmpty()
    matList: any[];

    @ApiProperty({ description: "Created by Id", required: true })
    @IsUUID()
    @IsNotEmpty()
    createdBy: string;

    @ApiProperty({ description: "Updating or creating flag", required: true })
    @IsBoolean()
    @IsNotEmpty()
    isUpdate: boolean;

    @ApiPropertyOptional({ description: "Projects", required: false, type: [ProjectDTO] })
    @IsOptional()
    @IsArray()
    projects?: ProjectDTO[];

    @ApiPropertyOptional({ description: "payment type" })
    @IsString()
    @IsOptional()
    paymentType?: string;
}
