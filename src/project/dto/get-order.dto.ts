import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsNotEmpty, IsOptional, IsUUID } from "class-validator";
import { PaginationDto } from "src/shared/dto/pagination.dto";

export class GetOrderDto extends PaginationDto {
    // @ApiProperty({ description: "Company", required: true })
    // @IsUUID()
    // @IsNotEmpty()
    // companyId: string;

    @ApiProperty({ description: "Deleted", required: true })
    @IsNotEmpty()
    deleted: boolean;

    @ApiPropertyOptional({ description: "oppId" })
    @IsOptional()
    oppId?: string;
}
