import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsNotEmpty, IsN<PERSON>ber, IsOptional, IsString, IsUUID } from "class-validator";

export class UpdateOppClientDto {
    // @ApiProperty({ description: "Company Id", required: true })
    // @IsUUID()
    // @IsNotEmpty()
    // companyId: string;

    @ApiProperty({ description: "Opp Id", required: true })
    @IsUUID()
    @IsNotEmpty()
    oppId: string;

    @ApiProperty({ description: "project Id", required: true })
    @IsUUID()
    @IsNotEmpty()
    projectId: string;

    @ApiProperty({ description: "Order Id", required: true })
    @IsUUID()
    @IsNotEmpty()
    orderId: string;

    @ApiProperty({ description: "Sold Value", required: true })
    @IsNumber()
    @IsNotEmpty()
    soldValue: number;

    @ApiProperty({ description: "real Rev Value", required: true })
    @IsNumber()
    @IsNotEmpty()
    realRevValue: number;

    @ApiProperty({ description: "contact Id", required: true })
    @IsUUID()
    @IsNotEmpty()
    contactId: string;

    @ApiPropertyOptional({ description: "discount", required: true })
    @IsOptional()
    discount?: number;

    @ApiPropertyOptional({ description: "paymentType" })
    @IsString()
    @IsOptional()
    paymentType?: string;
}
