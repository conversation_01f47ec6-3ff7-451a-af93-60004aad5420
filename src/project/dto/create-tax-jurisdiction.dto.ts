import { ApiProperty } from "@nestjs/swagger";
import { IsString, IsNotEmpty, MaxLength, MinLength, IsUUID, IsNumber, IsOptional } from "class-validator";

export class CreateTaxJurisdictionDto {
    @ApiProperty({ description: "Tax name", required: true })
    @IsString()
    @MinLength(2)
    @MaxLength(20)
    @IsNotEmpty()
    name: string;

    @ApiProperty({ description: "city", required: false })
    @IsOptional()
    @IsString()
    city?: string;

    @ApiProperty({ description: "State", required: false })
    @IsOptional()
    @IsString()
    state?: string;

    @ApiProperty({ description: "Tax rate", required: true })
    @IsNumber()
    @IsNotEmpty()
    rate: number;

    @ApiProperty({ description: "Material Tax rate", required: false })
    @IsOptional()
    @IsNumber()
    salesTax?: number;

    // @ApiProperty({ description: "Company", required: true })
    // @IsUUID()
    // @IsNotEmpty()
    // companyId: string;
}
