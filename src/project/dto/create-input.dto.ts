import { ApiProperty } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsString, IsNotEmpty, MaxLength, <PERSON><PERSON>ength, <PERSON>UUI<PERSON>, <PERSON>N<PERSON><PERSON> } from "class-validator";

export class CreateInputDto {
    @ApiProperty({ description: "Unit name", required: true })
    @IsString()
    @MinLength(2)
    @MaxLength(50)
    @Transform(({ value }) => value.trim(), { toClassOnly: true })
    @IsNotEmpty()
    name: string;

    @ApiProperty({ description: "Unit symbol", required: true })
    @IsString()
    @IsNotEmpty()
    unit: string;

    @ApiProperty({ description: "Project Type Id", required: true })
    @IsNotEmpty()
    @IsUUID()
    projectType: string;

    @ApiProperty({ description: "Order Number", required: true })
    @IsNumber()
    @IsNotEmpty()
    orderNumber: number;

    // @ApiProperty({ description: "Company", required: true })
    // @IsUUID()
    // @IsNotEmpty()
    // companyId: string;

    @ApiProperty({ description: "Created By", required: true })
    @IsUUID()
    @IsNotEmpty()
    createdBy: string;
}
