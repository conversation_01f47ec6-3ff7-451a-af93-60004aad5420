import { ApiPropertyOptional } from "@nestjs/swagger";
import { IsOptional } from "class-validator";
import { PaginationDto } from "src/shared/dto/pagination.dto";

export class GetMatDto extends PaginationDto {
    @ApiPropertyOptional({ description: "category Id" })
    @IsOptional()
    categoryId?: string;

    @ApiPropertyOptional({ description: "subCategory Id" })
    @IsOptional()
    subCategoryId?: string;
}
