import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsBoolean, IsNotEmpty, IsNumber, IsOptional, IsString, IsUUID } from "class-validator";

export class CreatePackageDto {
    // @ApiProperty()
    // @IsUUID()
    // @IsNotEmpty()
    // companyId: string;

    @ApiProperty()
    @IsString()
    @IsNotEmpty()
    type: string;

    @ApiPropertyOptional()
    @IsBoolean()
    @IsOptional()
    active: boolean;

    @ApiProperty()
    @IsString()
    @Transform(({ value }) => value.trim(), { toClassOnly: true })
    @IsNotEmpty()
    name: string;

    @ApiProperty()
    @IsString()
    @IsNotEmpty()
    description: string;

    @ApiPropertyOptional()
    @IsNumber()
    @IsOptional()
    order?: number;

    @ApiPropertyOptional()
    @IsNumber()
    @IsOptional()
    upsell?: number;

    @ApiPropertyOptional()
    @IsOptional()
    taskArray?: any[];

    @ApiPropertyOptional()
    @IsBoolean()
    @IsOptional()
    deleted: boolean;
}
