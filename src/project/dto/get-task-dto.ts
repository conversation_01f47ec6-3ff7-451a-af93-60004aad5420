import { ApiPropertyOptional } from "@nestjs/swagger";
import { IsOptional } from "class-validator";
import { PaginationDto } from "src/shared/dto/pagination.dto";
import { Transform } from "class-transformer";

export class GetTaskDto extends PaginationDto {
    @ApiPropertyOptional({ description: "type" })
    @IsOptional()
    type?: string;

    @ApiPropertyOptional({ description: "group id array" })
    @IsOptional()
    group?: string;

    @ApiPropertyOptional({ description: "serach" })
    @IsOptional()
    @Transform(({ value }) => value.trim().toLowerCase(), { toClassOnly: true })
    search?: string;
}
