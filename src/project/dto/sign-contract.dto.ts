import { ApiProperty, ApiPropertyOptional, PickType } from "@nestjs/swagger";
import { WorkOrderDto } from "./work-order.dto";
import { IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString } from "class-validator";
import { paymentMethodEnum } from "../enum/payment-method.enum";

export class SignContractDto extends PickType(WorkOrderDto, ["projectId", "chosenUpgrades"] as const) {
    @ApiPropertyOptional({ description: "pmtMethod", enum: paymentMethodEnum })
    @IsEnum(paymentMethodEnum)
    @IsOptional()
    pmtMethod?: string;

    // @ApiPropertyOptional({ description: "manualChange" })
    // // @IsNumber()
    // @IsOptional()
    // manualChange?: number;
}
