import { ApiProperty } from "@nestjs/swagger";
import { IsString, <PERSON><PERSON>otEmpty, MaxLength, <PERSON><PERSON>eng<PERSON>, IsUUID, IsNumber, IsBoolean } from "class-validator";

export class CreateCrewPositionDto {
    @ApiProperty({ description: "Crew position", required: true })
    @IsString()
    @MinLength(2)
    @MaxLength(20)
    @IsNotEmpty()
    title: string;

    @ApiProperty({ description: "Rate", required: true })
    @IsNumber()
    @IsNotEmpty()
    rate: number;

    // @ApiProperty({ description: "Company", required: true })
    // @IsUUID()
    // @IsNotEmpty()
    // companyId: string;

    @ApiProperty({ description: "Created By", required: true })
    @IsUUID()
    @IsNotEmpty()
    createdBy: string;

    // @ApiProperty({ description: "deletable", required: true, default: true })
    // @IsBoolean()
    // @IsNotEmpty()
    // deletable: boolean;
}
