import {
    HttpException,
    Injectable,
    InternalServerErrorException,
    Logger,
    OnModuleInit,
} from "@nestjs/common";
import Stripe from "stripe";

import { ICustomerData } from "./interface/customer.interface";
import { Currencies, StripePIStatus } from "src/shared/constants/constant";
import { ResponseMessage } from "src/shared/constants/responseMessage";
import { DiscountDuration, SubscriptionItem } from "src/shared/enum/subscriptions.enum";

@Injectable()
export class StripeService implements OnModuleInit {
    public stripe: any;
    // constructor() {}

    async onModuleInit() {
        this.stripe = new Stripe(process.env.STRIPE_API_KEY, {
            apiVersion: "2024-06-20",
            maxNetworkRetries: 2,
        });
    }

    async createCustomer(customerData: ICustomerData) {
        const customerResponse = await this.stripe.customers.create(customerData);
        return customerResponse?.id;
    }

    async createPlan(planData) {
        try {
            const stripeCreateProduct = {
                name: planData.name,
                description: planData.description,
                images: [planData.image],
                default_price_data: {
                    currency: Currencies.USD,
                    unit_amount: planData.price * 100,
                    recurring: {
                        interval: planData.renewalPeriod,
                        interval_count: planData.renewalNumber,
                    },
                    metadata: { subItem: SubscriptionItem.MAIN },
                },
            };

            /* creating a product in stripe */
            const stripeResponse = await this.stripe.products.create(stripeCreateProduct);

            return {
                stripeProductId: stripeResponse.id,
                stripePriceId: stripeResponse.default_price,
            };
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async createNewPrice(amount: number, prodID: string, interval: string, subItem: string) {
        /* createing a new price */
        const priceData = await this.stripe.prices.create({
            unit_amount: amount * 100,
            currency: Currencies.USD,
            product: prodID,
            recurring: {
                interval,
                interval_count: 1,
            },
            metadata: { subItem },
        });
        return priceData?.id;
    }

    async updateProduct(prodID: string, updateData: any) {
        try {
            return await this.stripe.products.update(prodID, updateData);
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getProductDetails(prodId: string) {
        return this.stripe.products.retrieve(prodId);
    }

    async createStripeCoupon(percentOff, duration) {
        try {
            const coupon = await this.stripe.coupons.create({
                percent_off: percentOff,
                duration: duration || DiscountDuration.FOREVER,
            });
            return { couponId: coupon.id };
        } catch (error) {}
    }

    async paymentIntentWithoutCard(stripeCustomerId: string, stripePriceId: string) {
        try {
            const subscriptionData = await this.stripe.subscriptions.create({
                customer: stripeCustomerId,
                items: [{ price: stripePriceId }],
                payment_settings: {
                    payment_method_options: {
                        card: {
                            request_three_d_secure: "automatic",
                        },
                    },
                    payment_method_types: ["card"],
                    save_default_payment_method: "on_subscription",
                },
                expand: ["latest_invoice.payment_intent"],
                metadata: {
                    subscribedTeamSize: 1,
                    description: "Payment without card",
                },
            });

            const { status: PaymentStatus } = subscriptionData?.latest_invoice;
            if (PaymentStatus !== "paid") {
                return {
                    success: false,
                    subscriptionId: subscriptionData.id,
                };
            }
            return {
                subscriptionId: subscriptionData.id,
                success: true,
            };
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async createPaymentIntent(
        stripeCustomerId: string,
        stripePriceId: string,
        stripePaymentResourceId: string,
        discountCouponId: string,
        freeTrial: number,
        { extraAmountToPaid, productId }: any,
        subscriptionInterval: string,
        subscribedTeamSize,
    ) {
        try {
            const customerData = await this.stripe.customers.retrieve(stripeCustomerId);

            if (customerData["invoice_settings"]["default_payment_method"] != stripePaymentResourceId) {
                /* setting a default resource */
                await this.setDefaultPaymentMethod(stripeCustomerId, stripePaymentResourceId);
            }
            const priceItems: any = [{ price: stripePriceId }];

            if (extraAmountToPaid) {
                const extraPriceId = await this.createNewPrice(
                    extraAmountToPaid,
                    productId,
                    subscriptionInterval,
                    SubscriptionItem.EXTRA,
                );
                priceItems.push({ price: extraPriceId });
            }

            /* creating a subscription for customer */
            const subscriptionData = await this.stripe.subscriptions.create({
                customer: stripeCustomerId,
                items: priceItems,
                ...(freeTrial ? { trial_period_days: freeTrial } : {}), // Conditionally add trial_period_days if provided
                discounts: discountCouponId ? [{ coupon: discountCouponId }] : [],
                payment_settings: {
                    payment_method_options: {
                        card: {
                            request_three_d_secure: "automatic",
                        },
                    },
                    payment_method_types: ["card"],
                    save_default_payment_method: "on_subscription",
                },
                expand: ["latest_invoice.payment_intent"],
                metadata: {
                    subscribedTeamSize,
                },
            });

            if (subscriptionData.latest_invoice && subscriptionData.latest_invoice.payment_intent) {
                const { status: PiStatus, client_secret: clientSecret } =
                    subscriptionData.latest_invoice.payment_intent;

                // Creating message based on payment intent status
                let resMessage;
                if (PiStatus == StripePIStatus.requireAction) {
                    resMessage = ResponseMessage.STRIPE_PAYMENT_ACTION_REQUIRED;
                } else if (PiStatus == StripePIStatus.success) {
                    resMessage = ResponseMessage.STRIPE_PAYMENT_SUCCESS;
                } else {
                    resMessage = ResponseMessage.STRIPE_PAYMENT_FAILED;
                }

                return {
                    requireAction: PiStatus == StripePIStatus.requireAction,
                    subscriptionId: subscriptionData.id,
                    clientSecret,
                    error: false,
                    message: resMessage,
                };
            } else if (subscriptionData.status === "trialing") {
                return {
                    requireAction: false,
                    subscriptionId: subscriptionData.id,
                    clientSecret: null,
                    error: false,
                    message: ResponseMessage.STRIPE_TRIAL_PERIOD_ACTIVE,
                };
            } else {
                return {
                    requireAction: null,
                    subscriptionId: subscriptionData.id,
                    clientSecret: null,
                    error: false,
                    message: ResponseMessage.STRIPE_PAYMENT_NO_INTENT,
                };
            }
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async setDefaultPaymentMethod(stripeCustomerId: string, stripePaymentResourceId: string) {
        try {
            /* setting the default payment option */
            return await this.stripe.customers.update(stripeCustomerId, {
                invoice_settings: {
                    default_payment_method: stripePaymentResourceId,
                },
            });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async findUser(customerId: string) {
        try {
            return await this.stripe.customers.retrieve(customerId);
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getCustomerSubscriptionById(subId: string) {
        try {
            return this.stripe.subscriptions.retrieve(subId);
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updateMonthlySubscriptionPrice({
        userSubscriptionId,
        extraSubscriptionItemId,
        newPriceId,
        subscribedTeamSize,
    }) {
        try {
            return await this.stripe.subscriptions.update(userSubscriptionId, {
                items: extraSubscriptionItemId
                    ? [
                          {
                              id: extraSubscriptionItemId, // The ID of the existing subscription item
                              price: newPriceId, // Replace the existing price with the new one
                          },
                      ]
                    : [
                          {
                              price: newPriceId,
                              qanitit: 1,
                          },
                      ],
                proration_behavior: "none", // No proration
                metadata: {
                    subscribedTeamSize,
                    description: "Updated Subscription price for new users (monthly)",
                },
            });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updateYearlySubscriptionPrice({
        userSubscriptionId,
        stripeCustomerId,
        extraSubscriptionItemId,
        newPriceId,
        stripeProductId,
        amountToCharge,
        numberOfMonths,
        subscribedTeamSize,
    }) {
        try {
            // Create the invoice first
            const resCreatedInvoice = await this.stripe.invoices.create({
                customer: stripeCustomerId,
                auto_advance: true, // Auto-finalize the invoice
                metadata: {
                    subscribedTeamSize,
                    description: "Updated Subscription price for new users (yearly)",
                },
            });

            const newPriceForInvoice = await this.stripe.prices.create({
                unit_amount: Math.round(amountToCharge * 100), // Amount in cents
                currency: Currencies.USD,
                product: stripeProductId, // The product this price is associated with
            });

            // Create the invoice item linked to the invoice
            await this.stripe.invoiceItems.create({
                customer: stripeCustomerId,
                price: newPriceForInvoice.id,
                quantity: numberOfMonths,
                currency: Currencies.USD,
                description: "One-time charge for remaining months",
                invoice: resCreatedInvoice.id, // Link invoice item to the specific invoice
            });

            if (resCreatedInvoice.status === "draft") {
                await this.stripe.invoices.pay(resCreatedInvoice.id);
            }

            const updatedSubscription = await this.stripe.subscriptions.update(userSubscriptionId, {
                items: extraSubscriptionItemId
                    ? [
                          {
                              id: extraSubscriptionItemId, // The ID of the existing subscription item
                              price: newPriceId, // Replace the existing price with the new one
                              quantity: 12,
                          },
                      ]
                    : [
                          {
                              price: newPriceId, // Add the new extra feature price
                              quantity: 12,
                          },
                      ],
                proration_behavior: "none", // No proration
                metadata: {
                    subscribedTeamSize,
                    description: "Updated Subscription price for new users (yearly)",
                },
            });

            // If no extraSubscriptionItemId was provided, find the newly added item
            let newSubscriptionItemId = null;
            if (!extraSubscriptionItemId) {
                const newItem = updatedSubscription.items.data.find((item) => item.price.id === newPriceId);
                if (newItem) {
                    newSubscriptionItemId = newItem.id;
                    return newSubscriptionItemId;
                }
            }

            return newSubscriptionItemId;
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getCustomerSubscriptionItem(subscriptionItemId: string) {
        try {
            return await this.stripe.subscriptionItems.retrieve(subscriptionItemId);
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async cancelSubscription(subId: string) {
        try {
            const cancellationResponse = await this.stripe.subscriptions.update(subId, {
                cancel_at_period_end: true,
            });
            return {
                isCancelledSuccess: cancellationResponse.cancel_at_period_end,
                cancelAt: cancellationResponse.cancel_at,
            };
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async enableCanceledPlan(subId: string) {
        try {
            const updatedSubscription = await this.stripe.subscriptions.update(subId, {
                cancel_at_period_end: false,
            });

            // Check if the update was successful
            if (updatedSubscription.cancel_at_period_end === false) {
                return true; // Indicate success
            } else {
                return false; // Indicate failure
            }
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async deleteSubscription(subscriptionId: string) {
        try {
            // Call the Stripe API to cancel the subscription
            const canceledSubscription = await this.stripe.subscriptions.cancel(subscriptionId, {
                invoice_now: true, // Set to true to immediately cancel the subscription and generate an invoice
            });
            return canceledSubscription;
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async cancelSubscriptions(subIds: string[]) {
        try {
            await this.stripe.subscriptions.update(subIds, {
                cancel_at_period_end: true,
            });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async createSubscriptionSchedule(
        cusId: string,
        time: number,
        priceId: string,
        paymentResouceId: string,
        discountCouponId: string,
        freeTrial: number,
        { extraAmountToPaid, productId }: any,
        subscriptionInterval: string,
        subscribedTeamSize: number,
    ) {
        try {
            const priceItems: any = [{ price: priceId }];

            if (extraAmountToPaid) {
                const extraPriceId = await this.createNewPrice(
                    extraAmountToPaid,
                    productId,
                    subscriptionInterval,
                    SubscriptionItem.EXTRA,
                );
                priceItems.push({ price: extraPriceId });
            }

            return await this.stripe.subscriptionSchedules.create({
                customer: cusId,
                start_date: time + 300, // 5 mins after current subscription ends
                end_behavior: "release",
                phases: [
                    {
                        items: priceItems,
                        ...(freeTrial ? { trial_end: time + freeTrial * 86400 } : {}), // Conditionally include trial_end
                        discounts: discountCouponId ? [{ coupon: discountCouponId }] : [],
                        default_payment_method: paymentResouceId,
                        collection_method: "charge_automatically",
                        metadata: {
                            subscribedTeamSize,
                        },
                    },
                ],
            });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async deleteSubscriptionSchedule(stripeSubscriptionScheduleId) {
        try {
            return await this.stripe.subscriptionSchedules.cancel(stripeSubscriptionScheduleId);
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async fetchPaymentIntent(paymentIntentId: string) {
        try {
            return await this.stripe.paymentIntents.retrieve(paymentIntentId);
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    webhookEventListener(requestData, signature) {
        try {
            return this.stripe.webhooks.constructEvent(
                requestData,
                signature,
                process.env.STRIPE_WEBHOOK_SECRET,
            );
        } catch (error) {
            Logger.log("webhook error here:", error?.message);
        }
    }

    async cancelSubscriptionNow(subId: string) {
        try {
            return await this.stripe.subscriptions.cancel(subId);
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async addPaymentMethods(
        paymentToken: string,
        stripeCustomerId: string,
        { billingAddress, name, email, phone }: any,
    ): Promise<{ paymentMethodId: string; card_brand: string }> {
        try {
            /* adding a new card in customer payment methods */
            const paymentMethod = await this.stripe.paymentMethods.create({
                type: "card",
                card: { token: paymentToken },
                ...(billingAddress && {
                    billing_details: {
                        email: email,
                        name: name,
                        phone: phone,
                        address: billingAddress,
                    },
                }),
            });

            /* attaching the payment method to a customer */
            await this.stripe.paymentMethods.attach(paymentMethod.id, {
                customer: stripeCustomerId,
            });
            return {
                paymentMethodId: paymentMethod?.id,
                card_brand: paymentMethod?.card?.brand,
            };
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async listPaymentMethods(stripeCustomerId: string): Promise<any> {
        return await this.stripe.paymentMethods.list({
            customer: stripeCustomerId,
            type: "card",
        });
    }

    // Function to find the subcription list of a customer
    async subscriptionListOfCustomer(customerId: string) {
        const subscriptions = await this.stripe.subscriptions.list({ customer: customerId });
        return subscriptions.data;
    }

    // Function to fetch the invoice list of customer
    async invoiceListOfCustomer(customerId: string) {
        const invoices = await this.stripe.invoices.list({ customer: customerId });
        return invoices.data;
    }

    async getPaymentMethods(customerId) {
        const paymentMethods = await this.stripe.paymentMethods.list({
            customer: customerId,
            type: "card",
        });
        return paymentMethods.data;
    }

    async getDefaultPaymentMethod(customerId) {
        const customer = await this.stripe.customers.retrieve(customerId);
        if (customer.invoice_settings.default_payment_method) {
            return this.stripe.paymentMethods.retrieve(
                customer.invoice_settings.default_payment_method as string,
            );
        }
        return null;
    }
}
