import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsUUID, IsNotEmpty, IsOptional, IsNumber, IsDate, IsString } from "class-validator";

export class UpdateOppCommissionDto {
    // @ApiProperty({ description: "Company Id" })
    // @IsUUID()
    // @IsNotEmpty()
    // companyId: string;

    @ApiProperty({ description: "Opportunity Id" })
    @IsUUID()
    @IsNotEmpty()
    opportunityId: string;

    @ApiPropertyOptional({ description: "total commision" })
    @IsOptional()
    @IsNumber()
    totalCommission?: number;

    @ApiPropertyOptional({ description: "sale commision" })
    @IsOptional()
    @IsNumber()
    saleCommission?: number;

    @ApiPropertyOptional({ description: "start commision" })
    @IsOptional()
    @IsNumber()
    startCommission?: number;

    @ApiPropertyOptional({ description: "completed commision" })
    @IsOptional()
    @IsNumber()
    completedCommission?: number;

    @ApiPropertyOptional({ description: "modification amount" })
    @IsOptional()
    @IsNumber()
    modificationAmount?: number;

    @ApiPropertyOptional({ description: "modification date" })
    @IsOptional()
    @Transform(({ value }) => new Date(value))
    @IsDate()
    date?: Date;

    @ApiPropertyOptional({ description: "modification reason" })
    @IsOptional()
    @IsString()
    reason?: number;
}
