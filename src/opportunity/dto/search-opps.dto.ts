import { ApiPropertyOptional } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsUUID, IsOptional } from "class-validator";

export class SearchOpportunityDto {
    @ApiPropertyOptional({ description: "sales person" })
    @IsUUID()
    @IsOptional()
    salesPerson?: string;

    @ApiPropertyOptional({ description: "project manager" })
    @IsUUID()
    @IsOptional()
    projectManager?: string;

    @ApiPropertyOptional({ description: "for search" })
    @IsOptional()
    @Transform(({ value }) => value.trim().toLowerCase(), { toClassOnly: true })
    search?: string;
}
