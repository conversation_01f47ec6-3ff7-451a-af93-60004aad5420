import { ApiProperty } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsDate, IsNotEmpty, IsString, IsUUID } from "class-validator";

export class UpdateOpportunityDateDto {
    // @ApiProperty({ description: "Company Id" })
    // @IsUUID()
    // @IsNotEmpty()
    // companyId: string;

    @ApiProperty({ description: "Opportunity Id" })
    @IsUUID()
    @IsNotEmpty()
    opportunityId: string;

    @ApiProperty({ description: "Date Label" })
    @IsString()
    @IsNotEmpty()
    dateLabel: string;

    @ApiProperty({ description: "Current date" })
    @IsDate()
    @Transform(({ value }) => new Date(value))
    @IsNotEmpty()
    currDate: Date;

    @ApiProperty({ description: "Date" })
    @IsDate()
    @Transform(({ value }) => new Date(value))
    @IsNotEmpty()
    date: Date;

    @ApiProperty({ description: "Updated by" })
    @IsUUID()
    @IsNotEmpty()
    updatedBy: string;
}
