import { ApiProperty } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsDate, IsNotEmpty, IsNumber, IsString, IsUUID } from "class-validator";

export class AddModificationCommissionDto {
    @ApiProperty()
    @IsNotEmpty()
    @IsUUID()
    oppId: string;

    @ApiProperty()
    @IsNotEmpty()
    @IsUUID()
    salesPersonId: string;

    @ApiProperty()
    @IsNotEmpty()
    @IsNumber()
    amount: number;

    @ApiProperty()
    @IsNotEmpty()
    @IsString()
    reason: string;

    @ApiProperty()
    @IsNotEmpty()
    @IsUUID()
    createdBy: string;

    @ApiProperty()
    @IsDate()
    @IsNotEmpty()
    @Transform(({ value }) => new Date(value))
    date: Date;
}
