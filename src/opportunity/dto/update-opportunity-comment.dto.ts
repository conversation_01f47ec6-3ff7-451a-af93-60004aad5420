import { ApiProperty } from "@nestjs/swagger";
import { IsDate, IsNotEmpty, IsString } from "class-validator";
import { DeleteOpportunityCommentDto } from "./delete-opportunity-comment.dto";
import { Transform } from "class-transformer";

export class UpdateOpportunityCommentDto extends DeleteOpportunityCommentDto {
    @ApiProperty({ description: "Notes", required: true })
    @IsString()
    @IsNotEmpty()
    body: string;

    @ApiProperty({ description: "Current Date", required: true })
    @IsDate()
    @Transform(({ value }) => new Date(value))
    @IsNotEmpty()
    currDate: Date;
}
