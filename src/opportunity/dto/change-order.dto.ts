import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsBoolean, IsDate, IsNumber, IsOptional, IsString, IsUUID } from "class-validator";

export class ChangeOrderDto {
    @ApiProperty({ description: "position number" })
    @IsNumber()
    num: number;

    @ApiProperty({ description: "name" })
    @IsString()
    name: string;

    @ApiProperty({ description: "description" })
    @IsString()
    description: string;

    @ApiProperty({ description: "materials" })
    @IsNumber()
    materials: number;

    @ApiProperty({ description: "labor" })
    @IsNumber()
    labor: number;

    @ApiProperty({ description: "addCost" })
    @IsNumber()
    addCost: number;

    @ApiProperty({ description: "jobCost" })
    @IsNumber()
    jobCost: number;

    @ApiProperty({ description: "tax" })
    @IsNumber()
    tax: number;

    @ApiProperty({ description: "total" })
    @IsNumber()
    total: number;

    @ApiProperty({ description: "deleted", default: false })
    @IsBoolean()
    deleted: boolean;

    // @ApiProperty({ description: "changeOrderValue" })
    // @IsNumber()
    // changeOrderValue: number;

    // @ApiProperty({ description: "changeOrderRRValue" })
    // @IsNumber()
    // changeOrderRRValue: number;

    @ApiPropertyOptional({ description: "oldJobCost" })
    @IsOptional()
    @IsNumber()
    oldJobCost?: number;

    @ApiPropertyOptional({ description: "oldMaterials" })
    @IsOptional()
    @IsNumber()
    oldMaterials?: number;

    @ApiPropertyOptional({ description: "signed by sales person", default: false })
    @IsOptional()
    @IsBoolean()
    signedBySales?: boolean;

    @ApiPropertyOptional({ description: "Date" })
    @IsOptional()
    @Transform(({ value }) => new Date(value))
    @IsDate()
    date: Date;

    @ApiPropertyOptional({ description: "modify commission id", required: false })
    @IsOptional()
    @IsUUID()
    modificationId?: string;

    @ApiPropertyOptional({ description: "raw labor cost", required: false })
    @IsOptional()
    @IsNumber()
    rawLaborCost?: number;

    @ApiPropertyOptional({ description: "man hours", required: false })
    @IsOptional()
    @IsNumber()
    manHours?: number;

    @ApiPropertyOptional({ description: "labour burden amount", required: false })
    @IsOptional()
    @IsNumber()
    laborBurden?: number;

    @ApiPropertyOptional({ description: "Material markup amount", required: false })
    @IsOptional()
    @IsNumber()
    mMarkup?: number;

    @ApiPropertyOptional({ description: "man hour cost", required: false })
    @IsOptional()
    @IsNumber()
    manHourCost?: number;

    @ApiPropertyOptional({ description: "Is Overriden", default: false })
    @IsOptional()
    @IsBoolean()
    overrideTotalCost?: boolean;

    @ApiPropertyOptional({ description: "Value of work task", required: false })
    @IsOptional()
    @IsNumber()
    workTaskValue?: number;

    @ApiPropertyOptional({ description: "Is Sub Contractor", default: false })
    @IsOptional()
    @IsBoolean()
    isSubContractor?: boolean;

    @ApiPropertyOptional({ description: "raw material cost", required: false })
    @IsOptional()
    @IsNumber()
    rawMatCost?: number;

    @ApiProperty({ description: "Is markup exist or not", default: false })
    @IsBoolean()
    markupEnabled: boolean;
}
