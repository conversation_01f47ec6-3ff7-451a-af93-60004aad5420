import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsString, IsUUID } from "class-validator";
import { DeleteRestoreDto } from "src/shared/dto/delete-retore.dto";

export class DeleteOpportunityCheckpointDto extends DeleteRestoreDto {
    @ApiProperty({ description: "checkpoint symbol" })
    @IsString()
    @IsNotEmpty()
    symbol: string;

    @ApiProperty({ description: "checkpoint name" })
    @IsString()
    @IsNotEmpty()
    name: string;

    @ApiProperty({ description: "member id" })
    @IsUUID()
    @IsNotEmpty()
    memberId: string;

    @ApiProperty({ description: "date" })
    @IsNotEmpty()
    date: Date;
}
