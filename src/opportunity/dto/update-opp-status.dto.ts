import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsEnum, IsUUID } from "class-validator";
import { OpportunityStatusEnum } from "../enum/opportunityStatus.enum";

export class UpdateOppStatusDto {
    @ApiProperty({ description: "Opportunity Id" })
    @IsUUID()
    @IsNotEmpty()
    opportunityId: string;

    @ApiProperty({ description: "Status" })
    @IsEnum(OpportunityStatusEnum)
    @IsNotEmpty()
    status: OpportunityStatusEnum;
}
