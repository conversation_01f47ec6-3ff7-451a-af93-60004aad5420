import { ApiPropertyOptional, OmitType } from "@nestjs/swagger";
import { CreateOpportunityDto } from "./create-opportunity.dto";
import { IsOptional, IsString } from "class-validator";

export class CreateWarrantyOpportunityDto extends OmitType(CreateOpportunityDto, [
    "salesPerson",
    "leadSource",
] as const) {
    @ApiPropertyOptional({ description: "sales person" })
    @IsString()
    @IsOptional()
    salesPerson?: string;

    @ApiPropertyOptional({ description: "lead source" })
    @IsString()
    @IsOptional()
    leadSource?: string;
}
