import { ApiPropertyOptional } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsOptional, IsEnum, IsString } from "class-validator";
import { PaginationDto } from "src/shared/dto/pagination.dto";
import { OpportunityStatusEnum } from "../enum/opportunityStatus.enum";

export class GetOpportunityDto extends PaginationDto {
    @ApiPropertyOptional({ description: "sales person ids (comma-separated)", example: "id1,id2" })
    @IsOptional()
    @Transform(({ value }) => value?.split(",") || [])
    @IsString({ each: true })
    salesPerson?: string[];

    @ApiPropertyOptional({ description: "project manager ids (comma-separated)", example: "id3,id4" })
    @IsOptional()
    @Transform(({ value }) => value?.split(",") || [])
    @IsString({ each: true })
    projectManager?: string[];

    @ApiPropertyOptional({ description: "opportunity type ids (comma-separated)", example: "type1,type2" })
    @IsOptional()
    @Transform(({ value }) => value?.split(",") || [])
    @IsString({ each: true })
    oppType?: string[];

    @ApiPropertyOptional({ description: "for active inactive and lost opportunities" })
    @IsOptional()
    @IsEnum(OpportunityStatusEnum)
    status?: OpportunityStatusEnum;

    @ApiPropertyOptional({ description: "crew ids (comma-separated)", example: "crew1,crew2" })
    @IsOptional()
    @Transform(({ value }) => value?.split(",") || [])
    @IsString({ each: true })
    crew?: string[];
}
