import { ApiProperty } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsDate, IsNotEmpty, IsString, IsUUID } from "class-validator";

export class UpdateCompleteStageDto {
    // @ApiProperty({ description: "Company Id" })
    // @IsUUID()
    // @IsNotEmpty()
    // companyId: string;

    @ApiProperty({ description: "Opportunity Id" })
    @IsUUID()
    @IsNotEmpty()
    opportunityId: string;

    @ApiProperty({ description: "Stage completed" })
    @IsString()
    @IsNotEmpty()
    stageCompleted: string;

    @ApiProperty({ description: "New Stage" })
    @IsString()
    @IsNotEmpty()
    newStage: string;

    @ApiProperty({ description: "Current date" })
    @IsDate()
    @Transform(({ value }) => new Date(value))
    @IsNotEmpty()
    currDate: Date;

    @ApiProperty({ description: "Completed by" })
    @IsUUID()
    @IsNotEmpty()
    completedBy: string;
}
