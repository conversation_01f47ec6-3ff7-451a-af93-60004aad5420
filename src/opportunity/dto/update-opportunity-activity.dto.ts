import { ApiProperty } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsDate, IsNotEmpty, IsString, IsUUID } from "class-validator";
import { DeleteRestoreDto } from "src/shared/dto/delete-retore.dto";

export class UpdateOppActivityDto extends DeleteRestoreDto {
    @ApiProperty({ description: "Body", required: true })
    @IsString()
    @IsNotEmpty()
    body: string;

    @ApiProperty({ description: "lead date" })
    @IsDate()
    @Transform(({ value }) => new Date(value))
    @IsNotEmpty()
    currDate: Date;

    @ApiProperty({ description: "Member", required: true })
    @IsUUID()
    @IsNotEmpty()
    memberId: string;
}
