import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsBoolean, IsDate, IsNotEmpty, IsOptional, IsString, IsUUID } from "class-validator";

export class UpdateChecklistDto {
    // @ApiProperty({ description: "Company Id" })
    // @IsUUID()
    // @IsNotEmpty()
    // companyId: string;

    @ApiProperty({ description: "Opportunity Id" })
    @IsUUID()
    @IsNotEmpty()
    opportunityId: string;

    @ApiProperty({ description: "Stage uuid" })
    @IsString()
    @IsNotEmpty()
    stage: string;

    @ApiProperty({ description: "Current date" })
    @IsDate()
    @Transform(({ value }) => new Date(value))
    @IsNotEmpty()
    currDate: Date;

    @ApiProperty({ description: "uuid of step" })
    @IsString()
    @IsNotEmpty()
    key: string;

    @ApiPropertyOptional({ description: "value" })
    // @IsString()
    @IsOptional()
    value?: any;

    @ApiProperty({ description: "boolean", default: false })
    @IsBoolean()
    @IsNotEmpty()
    boolean: boolean;

    @ApiProperty({ description: "Updated by" })
    @IsUUID()
    @IsNotEmpty()
    updatedBy: string;
}
