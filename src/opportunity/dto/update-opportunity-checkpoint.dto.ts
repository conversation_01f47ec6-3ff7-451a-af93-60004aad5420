import { ApiProperty } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsDate, IsNotEmpty, IsUUID } from "class-validator";
import { DeleteRestoreDto } from "src/shared/dto/delete-retore.dto";

export class UpdateOpportunityCheckpointDto extends DeleteRestoreDto {
    @ApiProperty({ description: "checkpoint date" })
    @IsDate()
    @Transform(({ value }) => new Date(value))
    @IsNotEmpty()
    currDate: Date;

    @ApiProperty({ description: "checkpointId", required: true })
    @IsUUID()
    @IsNotEmpty()
    checkpointId: string;

    @ApiProperty({ description: "memberId", required: true })
    @IsUUID()
    @IsNotEmpty()
    memberId: string;
}
