import { ApiProperty } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsDate, IsNotEmpty, IsString, IsUUID } from "class-validator";
import { DeleteRestoreDto } from "src/shared/dto/delete-retore.dto";

export class UpdateOppStageDto extends DeleteRestoreDto {
    @ApiProperty({ description: "New stage", required: true })
    @IsString()
    @IsNotEmpty()
    newStage: string;

    @ApiProperty({ description: "lead date" })
    @IsDate()
    @Transform(({ value }) => new Date(value))
    @IsNotEmpty()
    currDate: Date;
}
