import { ApiProperty } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsUUID, IsNotEmpty, IsString } from "class-validator";
import { DeleteRestoreDto } from "src/shared/dto/delete-retore.dto";

export class LostUnLostOpportunityDto extends DeleteRestoreDto {
    @ApiProperty({ description: "Member", required: true })
    @IsUUID()
    @IsNotEmpty()
    memberId: string;

    @ApiProperty({ description: "reason", required: true })
    @IsString()
    @IsNotEmpty()
    reason: string;

    @ApiProperty({ description: "lost date", required: true })
    @Transform(({ value }) => new Date(value))
    @IsNotEmpty()
    date: Date;
}
