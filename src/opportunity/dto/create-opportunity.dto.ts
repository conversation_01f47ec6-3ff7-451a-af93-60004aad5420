import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsArray, IsDate, IsNotEmpty, IsNumber, IsOptional, IsString, IsUUID } from "class-validator";
import { Transform } from "class-transformer";

export class CreateOpportunityDto {
    // @ApiProperty({ description: "CompanyId" })
    // @IsUUID()
    // @IsNotEmpty()
    // companyId: string;

    @ApiProperty({ description: "Project Type Id" })
    @IsUUID()
    @IsNotEmpty()
    // @IsEnum(OpportunityTypeEnum)
    oppType: string;

    @ApiPropertyOptional({ description: "oppNotes" })
    @IsString()
    @IsOptional()
    oppNotes?: string;

    @ApiProperty({ description: "Contact Id" })
    @IsUUID()
    @IsNotEmpty()
    contactId: string;

    @ApiProperty({ description: "first name" })
    @IsString()
    @IsNotEmpty()
    @Transform(({ value }) => value.trim(), { toClassOnly: true })
    firstName: string;

    @ApiPropertyOptional({ description: "last name" })
    @IsString()
    @Transform(({ value }) => value.trim(), { toClassOnly: true })
    @IsOptional()
    lastName?: string;

    @ApiPropertyOptional({ description: "street" })
    @IsString()
    @IsOptional()
    street?: string;

    @ApiPropertyOptional({ description: "city" })
    @IsString()
    @IsOptional()
    city?: string;

    @ApiPropertyOptional({ description: "state" })
    @IsString()
    @IsOptional()
    state?: string;

    @ApiPropertyOptional({ description: "zip" })
    @IsString()
    @IsOptional()
    zip?: string;

    @ApiProperty({ description: "lead source" })
    @IsString()
    @IsNotEmpty()
    leadSource: string;

    @ApiProperty({ description: "lead source id" })
    @IsNotEmpty()
    leadSourceId: string;

    @ApiPropertyOptional({ description: "campaign id" })
    @IsString()
    @IsOptional()
    campaignId: string;

    @ApiPropertyOptional({ description: "lead cost" })
    // @IsString()
    @Transform(({ value }) => Number(value))
    @IsOptional()
    leadCost: number;

    @ApiPropertyOptional({ description: "referred by" })
    // @IsUUID()
    @IsOptional()
    referredBy?: string;

    @ApiProperty({ description: "distance" })
    @IsNumber()
    @IsOptional()
    distance?: number;

    @ApiProperty({ description: "duration" })
    @IsNumber()
    @IsOptional()
    duration?: number;

    @ApiProperty({ description: "sales person" })
    @IsString()
    @IsNotEmpty()
    salesPerson: string;

    @ApiProperty({ description: "stage" })
    @IsString()
    @IsNotEmpty()
    stage: string;

    @ApiProperty({ description: "lead date" })
    @IsDate()
    @Transform(({ value }) => new Date(value))
    @IsNotEmpty()
    newLeadDate: Date;

    @ApiProperty({ description: "opp date" })
    @IsDate()
    @Transform(({ value }) => new Date(value))
    @IsNotEmpty()
    oppDate: Date;

    @ApiPropertyOptional({ description: "needs Assessment Date" })
    @IsOptional()
    @IsDate()
    @Transform(({ value }) => new Date(value))
    needsAssessmentDate?: Date;

    @ApiProperty({ description: "coment" })
    @IsArray()
    @IsNotEmpty()
    comments: any;

    @ApiProperty({ description: "finance fee", required: false })
    @IsOptional()
    @IsNumber()
    financeFee?: number;

    @ApiProperty({ description: "created by" })
    @IsUUID()
    @IsNotEmpty()
    createdBy: string;

    // @ApiPropertyOptional({ description: "Lead Id" })
    // @IsOptional()
    // @IsUUID()
    // leadId?: string;

    @ApiPropertyOptional({ description: "current Date" })
    @IsOptional()
    @IsDate()
    @Transform(({ value }) => new Date(value))
    currentDate?: Date;

    @ApiPropertyOptional({ description: "client lat" })
    @IsString()
    @IsOptional()
    oppLat?: string;

    @ApiPropertyOptional({ description: "client long" })
    @IsString()
    @IsOptional()
    oppLong?: string;

    @ApiPropertyOptional({ description: "CSR Id" })
    @IsOptional()
    @IsUUID()
    csrId?: string;

    @ApiPropertyOptional({ description: "Lead Id" })
    @IsOptional()
    leadId?: string;

    @ApiPropertyOptional({ description: "Original Contact Id (for lead transfer and linking)" })
    @IsOptional()
    @IsUUID()
    originalContact?: string;

    @ApiPropertyOptional({ description: "create lead", default: false })
    @IsOptional()
    createLead?: boolean;
}
