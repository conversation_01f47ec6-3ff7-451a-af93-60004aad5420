import { ApiProperty, ApiPropertyOptional, PartialType } from "@nestjs/swagger";
import { IsDate, IsNotEmpty, IsOptional, IsString, IsUUID } from "class-validator";
import { CreateOpportunityDto } from "./create-opportunity.dto";
import { Transform } from "class-transformer";

export class UpdateOpportunityDto extends PartialType(CreateOpportunityDto) {
    @ApiProperty({ description: "Opportunity Id", required: true })
    @IsUUID()
    @IsNotEmpty()
    opportunityId: string;

    @ApiProperty({ description: "edited by Id", required: true })
    @IsUUID()
    @IsNotEmpty()
    editedBy: string;

    @ApiProperty({ description: "current date" })
    @IsDate()
    @Transform(({ value }) => new Date(value))
    @IsNotEmpty()
    currDate: Date;

    @ApiPropertyOptional({ description: "project manager", required: false })
    @IsUUID()
    @IsOptional()
    projectManager?: string;

    @ApiProperty({ description: "PO", required: false })
    @IsNotEmpty()
    @IsString()
    @Transform(({ value }) => value.trim(), { toClassOnly: true })
    PO: string;

    @ApiProperty({ description: "num", required: false })
    @IsNotEmpty()
    @IsString()
    @Transform(({ value }) => value.trim(), { toClassOnly: true })
    num: string;
}
