import { ApiPropertyOptional } from "@nestjs/swagger";
import { PaginationDto } from "src/shared/dto/pagination.dto";
import { IsOptional, IsString } from "class-validator";

export class GetOldOpportunityDto extends PaginationDto {
    @ApiPropertyOptional({ description: "opp type" })
    @IsOptional()
    @IsString()
    typeId?: string;

    @ApiPropertyOptional({ description: "Sales Person Id" })
    @IsOptional()
    @IsString()
    salesPerson?: string;
}
