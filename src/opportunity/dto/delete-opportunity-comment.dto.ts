import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsUUID } from "class-validator";
import { DeleteRestoreDto } from "src/shared/dto/delete-retore.dto";

export class DeleteOpportunityCommentDto extends DeleteRestoreDto {
    @ApiProperty({ description: "Opp Id", required: true })
    @IsUUID()
    @IsNotEmpty()
    oppId: string;

    @ApiProperty({ description: "Member Id", required: true })
    @IsUUID()
    @IsNotEmpty()
    memberId: string;
}
