import { Schema, SchemaFactory, Prop } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import { Document } from "mongoose";
import { UUIDProp } from "src/shared/decorator/uuid.decorator";

export type CommissionModificationDocument = CommissionModification & Document;

@Schema({ timestamps: true, id: false, collection: "CommissionModification" })
export class CommissionModification {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @UUIDProp()
    companyId: string;

    @UUIDProp()
    oppId: string;

    @UUIDProp()
    salesPersonId: string;

    @Prop({ required: false })
    amount: number;

    @Prop({ required: false })
    reason: string;

    @UUIDProp()
    createdBy: string;

    @Prop()
    date?: Date;
}

export const CommissionModificationSchema = SchemaFactory.createForClass(CommissionModification);
