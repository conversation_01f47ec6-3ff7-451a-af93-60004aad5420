import { Schema, SchemaFactory, Prop } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import { Document } from "mongoose";
import { UUIDProp } from "src/shared/decorator/uuid.decorator";

export type OpportunityActivityDocument = OpportunityActivity & Document;

@Schema({ timestamps: true, id: false, strict: false, collection: "OpportunityActivity" })
export class OpportunityActivity {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @UUIDProp()
    companyId: string;

    @UUIDProp()
    oppId?: string;

    @Prop({ required: true, type: Array })
    activities: any;

    @Prop()
    createdAt?: Date;

    @Prop()
    modifiedAt?: Date;
}

export const OpportunityActivitySchema = SchemaFactory.createForClass(OpportunityActivity);
