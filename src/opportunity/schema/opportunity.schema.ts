import { <PERSON>hem<PERSON>, SchemaFactory, Prop } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import mongoose, { Document } from "mongoose";
import { OpportunityStatusEnum } from "../enum/opportunityStatus.enum";
import { UUIDProp } from "src/shared/decorator/uuid.decorator";

export type OpportunityDocument = Opportunity & Document;

class SalesCommisionObject {
    @Prop()
    total: number;

    @Prop()
    sale: number;

    @Prop()
    start: number;

    @Prop()
    completed: number;
}

@Schema({ timestamps: true, id: false, strict: false, collection: "Opportunity" })
export class Opportunity {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @UUIDProp()
    companyId: string;

    @UUIDProp()
    oppType: string;

    @UUIDProp({ required: false })
    clientId: string;

    @UUIDProp()
    contactId: string;

    @Prop({ required: true })
    PO: string;

    @Prop({ required: true })
    num: string;

    @Prop({ required: false })
    street?: string;

    @Prop({ required: false })
    city?: string;

    @Prop({ required: false })
    state?: string;

    @Prop({ required: false })
    zip?: string;

    @Prop({ required: false })
    leadSource?: string;

    @Prop({ required: false })
    leadSourceId: string;

    @Prop({ required: false })
    campaignId?: string;

    @Prop({ default: false })
    selfGen: boolean;

    @Prop({ required: false })
    leadCost?: number;

    // @Prop({ required: false })
    // leadId?: string;

    @Prop({ required: false })
    referredBy?: string;

    @Prop({ required: false })
    distance: number;

    @Prop({ required: false })
    duration: number;

    @Prop({ required: false })
    salesPerson?: string;

    @UUIDProp()
    stage: string;

    @Prop({ required: true, type: Array })
    comments: any;

    @Prop({})
    newLeadDate: Date;

    @Prop({ required: true })
    oppDate: Date;

    @Prop({ required: false })
    saleDate?: Date;

    @Prop({ required: false })
    jobStartedDate?: Date;

    @Prop({ required: false })
    jobCompletedDate?: Date;

    @UUIDProp()
    createdBy: string;

    @Prop({ default: false })
    deleted: boolean;

    @Prop({ required: true })
    type: string;

    @Prop({ default: OpportunityStatusEnum.Active, enum: OpportunityStatusEnum })
    status: OpportunityStatusEnum;

    @Prop({ required: false })
    projectManager?: string;

    @Prop({ type: Object })
    checkpoint?: {
        [key: string]: string;
    };

    @Prop({ type: SalesCommisionObject, required: false })
    salesCommission?: SalesCommisionObject;

    @Prop({ type: Object })
    stepsChecklist: object;

    @Prop({ required: false })
    orderId?: string;

    @Prop({ required: false })
    csrId?: string;

    @Prop({ required: false })
    acceptedProjectId?: string;

    @Prop({ required: false })
    acceptedType?: string;

    @Prop({ required: false })
    paymentType: string;

    @Prop({ required: false })
    financeFee: number;

    @Prop({ default: false })
    warrantyType: boolean;

    @Prop({ required: false })
    oppLat?: string;

    @Prop({ required: false })
    oppLong?: string;

    @Prop({ type: Object, required: false })
    checkpointActivity: object;

    @Prop()
    createdAt?: Date;

    @Prop()
    modifiedAt?: Date;

    @Prop()
    changeOrderValue?: number;

    @Prop()
    changeOrderRRValue?: number;

    @Prop()
    budgetScore?: number;

    @Prop()
    soldValue?: number;

    @Prop({ type: [Object] })
    changeOrders: [];
}

export const OpportunitySchema = SchemaFactory.createForClass(Opportunity);
