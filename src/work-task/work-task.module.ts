import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { WorkTaskSchema } from "./schema/work-task.schema";
import { WorkTaskController } from "./work-task.controller";
import { WorkTaskService } from "./work-task.service";
import { TaskSchema } from "src/project/schema/task.schema";

@Module({
    imports: [
        MongooseModule.forFeature([
            { name: "WorkTask", schema: WorkTaskSchema },
            { name: "Task", schema: TaskSchema },
        ]),
        // PositionModule,
        // RoleModule,
    ],
    providers: [WorkTaskService],
    controllers: [WorkTaskController],
    exports: [WorkTaskService],
})
export class WorkTaskModule {}
