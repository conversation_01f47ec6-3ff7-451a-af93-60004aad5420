import {
    Body,
    Controller,
    Delete,
    Get,
    Param,
    ParseUUIDPipe,
    Patch,
    Post,
    Query,
    UseGuards,
} from "@nestjs/common";
import {
    ApiBearerAuth,
    ApiConflictResponse,
    ApiInternalServerErrorResponse,
    ApiNotFoundResponse,
    ApiOperation,
    ApiTags,
    ApiUnauthorizedResponse,
} from "@nestjs/swagger";
import { Roles } from "src/auth/guards/auth.guard";
import { UserAuthGuard } from "src/auth/guards/jwt-auth.guard";
import { PaginationRequestDto } from "src/company/dto/pagination-request.dto";
import { UserRolesEnum } from "src/company/enum/role.enum";
import HttpResponse from "src/shared/http/response/response.http";
import { CreateWorkTaskDto } from "./dto/create-work-task.dto";
import { DeleteWorkTaskDto } from "./dto/delete-work-task.dto";
import { RestoreWorkTaskDto } from "./dto/restore-work-task.dto";
import { UpdateWorkTaskDto } from "./dto/update-work-task.dto";
import { WorkTaskService } from "./work-task.service";
import { UpdateSequenceDto } from "src/crm/dto/updateSequence.dto";
import { Auth, GetUser } from "src/auth/decorator/auth.decorator";
import { JwtUserPayload } from "src/auth/interface/auth.interface";

@ApiTags("WorkTask")
@ApiBearerAuth()
@Auth()
@ApiInternalServerErrorResponse({ description: "Server Error" })
@ApiUnauthorizedResponse({ description: "Unauthorized request" })
@Controller({ path: "work-task", version: "1" })
export class WorkTaskController {
    constructor(private readonly workTaskService: WorkTaskService) {}

    @ApiOperation({ summary: "Create Work Task" })
    @ApiConflictResponse({ description: "Work Task already exist" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Post("create-work-task")
    async createWorkTask(
        @GetUser() user: JwtUserPayload,
        @Body() createWorkTaskDto: CreateWorkTaskDto,
    ): Promise<HttpResponse> {
        return this.workTaskService.createWorkTask(user.companyId, createWorkTaskDto);
    }

    @ApiOperation({ summary: "Update workTask sequence" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Patch("update-work-task-sequence")
    async updateWorkTaskSequence(
        @GetUser() user: JwtUserPayload,
        @Body() updateWorkTaskSequenceDto: UpdateSequenceDto,
    ): Promise<HttpResponse> {
        return this.workTaskService.updateWorkTaskSequence(user.companyId, updateWorkTaskSequenceDto);
    }

    @ApiOperation({ summary: "Delete Work Task" })
    @ApiNotFoundResponse({ description: "Work Task not found" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Delete("delete-work-task")
    async deleteWorkTask(
        @GetUser() user: JwtUserPayload,
        @Body() deleteWorkTaskDto: DeleteWorkTaskDto,
    ): Promise<HttpResponse> {
        return this.workTaskService.deleteWorkTask(user._id, user.companyId, deleteWorkTaskDto);
    }

    @ApiOperation({ summary: "Restore Work Task" })
    @ApiNotFoundResponse({ description: "Work Task not found" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Patch("restore-work-task")
    async restoreWorkTask(
        @GetUser() user: JwtUserPayload,
        @Body() restoreWorkTaskDto: RestoreWorkTaskDto,
    ): Promise<HttpResponse> {
        return this.workTaskService.restoreWorkTask(user._id, user.companyId, restoreWorkTaskDto);
    }

    @ApiOperation({ summary: "Update Work Task" })
    @ApiNotFoundResponse({ description: "WorkTask not found" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Patch("update-work-task")
    async updateWorkTask(
        @GetUser() user: JwtUserPayload,
        @Body() updateWorkTaskDto: UpdateWorkTaskDto,
    ): Promise<HttpResponse> {
        return this.workTaskService.updateWorkTask(user._id, user.companyId, updateWorkTaskDto);
    }

    // get company work task
    @ApiOperation({ summary: "Get WorkTask" })
    @Get("get-work-task/deleted/:deleted")
    async getWorkTask(
        @GetUser() user: JwtUserPayload,
        @Param("deleted") deleted: boolean,
        @Query() paginationRequestDto: PaginationRequestDto,
    ): Promise<HttpResponse> {
        return this.workTaskService.getWorkTask(user._id, user.companyId, deleted, paginationRequestDto);
    }

    @ApiOperation({ summary: "Get Member WorkTask" })
    @Get("get-member-work-test/member/:memberId")
    async getWorkTask1(
        @GetUser() user: JwtUserPayload,
        @Param("memberId", ParseUUIDPipe) memberId: string,
    ): Promise<HttpResponse> {
        return this.workTaskService.getMemberWorkTask(user._id, user.companyId, memberId);
    }
}
