import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsString, IsNotEmpty, IsUUID, IsOptional, IsBoolean, IsNumber } from "class-validator";

export class CreateWorkTaskDto {
    // @ApiProperty({ description: "Company Id", required: true })
    // @IsUUID()
    // @IsNotEmpty()
    // companyId: string;

    @ApiProperty({ description: "Work task Name", required: true })
    @IsString()
    @IsNotEmpty()
    name: string;

    @ApiPropertyOptional({ description: "Work task Description" })
    @IsString()
    @IsOptional()
    description: string;

    @ApiPropertyOptional({ description: "Piece work" })
    @IsOptional()
    @IsBoolean()
    pieceWork?: boolean;

    @ApiPropertyOptional({ description: "Show on score board" })
    @IsOptional()
    @IsBoolean()
    showOnScoreboard?: boolean;

    @ApiPropertyOptional({ description: "add travel expense" })
    @IsOptional()
    @IsBoolean()
    addTravel: boolean;

    @ApiProperty({ description: "Position", required: true })
    @IsNotEmpty()
    position: any[];

    @ApiPropertyOptional({ description: "rate" })
    @IsOptional()
    @IsNumber()
    rate?: number;

    @ApiProperty({ description: "Created By", required: true })
    @IsUUID()
    @IsNotEmpty()
    createdBy: string;
}
