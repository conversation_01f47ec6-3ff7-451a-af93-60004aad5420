import { Schema, SchemaFactory, Prop } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import { Document } from "mongoose";
import { UUIDProp } from "src/shared/decorator/uuid.decorator";

export type WorkTaskDocument = WorkTask & Document;

@Schema({ timestamps: true, id: false, collection: "WorkTask" })
export class WorkTask {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @UUIDProp()
    companyId: string;

    @Prop({ required: true })
    name: string;

    @Prop({ required: false })
    description: string;

    @Prop({ required: false })
    pieceWork: boolean;

    @Prop({ required: false })
    showOnScoreboard: boolean;

    @Prop({ required: false })
    addTravel: boolean;

    @Prop({ required: false })
    position: any[];

    @UUIDProp()
    createdBy: string;

    @Prop({ default: false })
    deleted: boolean;

    @Prop({ required: false })
    sequence: number;

    @Prop({ required: false })
    rate: number;

    @Prop({ required: false })
    code: string;

    @Prop()
    createdAt?: Date;

    @Prop()
    modifiedAt?: Date;
}

export const WorkTaskSchema = SchemaFactory.createForClass(WorkTask);
