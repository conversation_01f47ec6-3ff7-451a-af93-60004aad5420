import {
    BadRequestException,
    ConflictException,
    HttpException,
    HttpStatus,
    Injectable,
    InternalServerErrorException,
} from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";
import { PaginationRequestDto } from "src/company/dto/pagination-request.dto";
import { PositionService } from "src/position/position.service";
import { PositionDocument } from "src/position/schema/position.schema";
import CreatedResponse from "src/shared/http/response/created.http";
import NoContentResponse from "src/shared/http/response/no-content.http";
import OkResponse from "src/shared/http/response/ok.http";
import { CreateWorkTaskDto } from "./dto/create-work-task.dto";
import { DeleteWorkTaskDto } from "./dto/delete-work-task.dto";
import { UpdateWorkTaskDto } from "./dto/update-work-task.dto";
import { WorkTaskDocument } from "./schema/work-task.schema";
import { defaultWorkTask, defaultWorkTaskPWP } from "src/shared/constants";
import { UpdateSequenceDto } from "src/crm/dto/updateSequence.dto";
import { TaskDocument } from "src/project/schema/task.schema";

@Injectable()
export class WorkTaskService {
    constructor(
        private readonly positionService: PositionService,
        @InjectModel("WorkTask") private readonly workTaskModel: Model<WorkTaskDocument>,
        @InjectModel("Task") private readonly taskModel: Model<TaskDocument>,
    ) {}

    async createWorkTask(companyId: string, createWorkTaskDto: CreateWorkTaskDto) {
        try {
            const workTask = await this.workTaskModel
                .exists({
                    companyId,
                    name: createWorkTaskDto.name,
                    deleted: false,
                })
                .exec();
            if (workTask) throw new HttpException("WorkTask already exists", HttpStatus.BAD_REQUEST);

            const allWorkTasks = await this.workTaskModel
                .find({
                    companyId,
                    deleted: false,
                })
                .select("sequence");

            // Calculate the next sequence number
            const lastSequence =
                allWorkTasks.length > 0
                    ? Math.max(...allWorkTasks.map((task) => task?.sequence || 0)) + 1
                    : 1;

            const createdWorkTask = new this.workTaskModel({
                companyId,
                ...createWorkTaskDto,
                sequence: lastSequence,
            });
            await createdWorkTask.save();
            return new CreatedResponse({ message: "WorkTask created successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updateWorkTaskSequence(companyId: string, updateWorkTaskSequenceDto: UpdateSequenceDto) {
        const { data } = updateWorkTaskSequenceDto;

        if (!data?.length) {
            return new OkResponse({ message: "Nothing to updates!" });
        }

        try {
            const bulkOperations = data.map(({ _id, sequence }) => ({
                updateOne: {
                    filter: { _id, companyId },
                    update: { $set: { sequence } },
                },
            }));

            const result = await this.workTaskModel.bulkWrite(bulkOperations, { ordered: false });

            if (result?.modifiedCount > 0) {
                return new OkResponse({ message: "WorkTask sequences updated successfully!" });
            }

            return new OkResponse({ message: "No sequences were updated!" });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async deleteWorkTask(userId: string, companyId: string, deleteWorkTaskDto: DeleteWorkTaskDto) {
        try {
            const task = await this.taskModel.countDocuments({
                companyId,
                "labor.worker": deleteWorkTaskDto.id,
                deleted: false,
            });

            if (task > 0)
                throw new ConflictException(
                    "The workTask you're trying to delete is currently in use and cannot be deleted.",
                );
            const workTask = await this.workTaskModel.findOne({
                _id: deleteWorkTaskDto.id,
            });
            if (workTask?.pieceWork)
                throw new BadRequestException("PieceWork enabled: not allowed to delete ");
            await this.workTaskModel.findOneAndUpdate(
                { _id: deleteWorkTaskDto.id, companyId, deleted: false },
                {
                    $set: { deleted: true },
                },
                { new: true },
            );
            return new NoContentResponse({ message: "WorkTask deleted successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async restoreWorkTask(userId: string, companyId: string, deleteWorkTaskDto: DeleteWorkTaskDto) {
        try {
            await this.workTaskModel.findOneAndUpdate(
                { _id: deleteWorkTaskDto.id, companyId, deleted: true },
                {
                    $set: { deleted: false },
                },
                { new: true },
            );
            return new OkResponse({ message: "WorkTask restored successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updateWorkTask(userId: string, companyId: string, updateWorkTaskDto: UpdateWorkTaskDto) {
        try {
            await this.workTaskModel.findOneAndUpdate(
                { _id: updateWorkTaskDto.workTaskId, companyId, deleted: false },
                {
                    $set: { ...updateWorkTaskDto },
                },
                { new: true },
            );
            return new OkResponse({ message: "WorkTask updated successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getWorkTask(
        userId: string,
        companyId: string,
        deleted: boolean,
        paginationRequestDto: PaginationRequestDto,
    ) {
        try {
            const limit = paginationRequestDto.limit || 10;
            const offset = limit * (paginationRequestDto.skip || 0);
            const workTask = await this.workTaskModel
                .find({ companyId, deleted })
                .skip(offset)
                .limit(limit)
                .sort({ sequence: 1 });
            return new OkResponse({ workTask });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getMemberWorkTask(userId: string, companyId: string, memberId: string) {
        try {
            const {
                data: { memberPosition },
            } = await this.positionService.getMemberPosition(userId, companyId, memberId);
            const workTask = await this.workTaskModel
                .find({
                    companyId,
                    position: memberPosition._id,
                    deleted: false,
                })
                .sort({ sequence: 1 })
                .exec();
            return new OkResponse({ workTask });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async addDefaultWorkTask(
        companyId: string,
        createdBy: string,
        positionIdUUID,
        workTaskUUID,
        proPlusUserCheck: boolean,
    ) {
        try {
            const positionIds = Object.values(positionIdUUID);

            const defaultValues = proPlusUserCheck
                ? defaultWorkTask(positionIds, workTaskUUID)
                : defaultWorkTaskPWP(positionIds, workTaskUUID);
            const defaultWorkTasks = defaultValues.map((value) => ({
                ...value,
                companyId,
                createdBy,
                deleted: false,
            }));

            await this.workTaskModel.insertMany(defaultWorkTasks);
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }
}
