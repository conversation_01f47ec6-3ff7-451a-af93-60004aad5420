import { Modu<PERSON> } from "@nestjs/common";
import { GpsService } from "./gps.service";
import { GpsController } from "./gps.controller";
import { MongooseModule } from "@nestjs/mongoose";
import { GpsSchema } from "./schema/gps.schema";
import { PositionModule } from "src/position/position.module";
import { RoleModule } from "src/role/role.module";

@Module({
    imports: [MongooseModule.forFeature([{ name: "Gps", schema: GpsSchema }]),
    //  PositionModule, RoleModule
    ],
    controllers: [GpsController],
    providers: [GpsService],
})
export class GpsModule {}
