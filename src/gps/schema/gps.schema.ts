// export class Gps {}
import { Schema as MongooseSchema, Document } from "mongoose"; // 👈 actual mongoose schema
import { Schema, SchemaFactory, Prop } from "@nestjs/mongoose"; // nest-specific stuff
import { randomUUID } from "crypto";
import { GpsActivityEnum } from "../enum/gps-activity.enum";
import { UUIDProp } from "src/shared/decorator/uuid.decorator";

export type GpsDocument = Gps & Document;

@Schema({ timestamps: false, id: false, collection: "Gps" })
export class Gps {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @UUIDProp()
    companyId: string;

    @UUIDProp()
    timeCardId: string;

    @UUIDProp()
    memberId: string;

    @Prop({
        type: {
            type: String,
            enum: ["MultiPoint"],
            required: true,
        },
        coordinates: {
            type: MongooseSchema.Types.Mixed,
            required: true,
        },
    })
    location: {
        type: string;
        coordinates: Array<[number, number, number, string]>;
    };
}

export const GpsSchema = SchemaFactory.createForClass(Gps);
