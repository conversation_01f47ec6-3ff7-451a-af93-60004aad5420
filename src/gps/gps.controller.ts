import { Controller, Get, Post, Body, Param, ParseUUIDPipe, Query } from "@nestjs/common";
import { GpsService } from "./gps.service";
import { AddGpsDto } from "./dto/create-gps.dto";
import { ApiTags, ApiBearerAuth, ApiOperation, ApiInternalServerErrorResponse } from "@nestjs/swagger";
import { Auth, GetUser } from "src/auth/decorator/auth.decorator";
import HttpResponse from "src/shared/http/response/response.http";
import { FindAllGps } from "./dto/get-all-gps.dto";
import { JwtUserPayload } from "src/auth/interface/auth.interface";

@ApiTags("Gps")
@ApiBearerAuth()
@Auth()
@Controller({ path: "gps", version: "1" })
export class GpsController {
    constructor(private readonly gpsService: GpsService) {}

    @ApiOperation({ summary: "Add gps location" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Post("add-gps")
    async addGps(@GetUser() user: JwtUserPayload, @Body() addGpsDto: AddGpsDto[]): Promise<HttpResponse> {
        return this.gpsService.addGps(user.companyId, addGpsDto);
    }

    @Get("all-gps")
    async findAll(@GetUser() user: JwtUserPayload, @Query() findAllGps: FindAllGps): Promise<HttpResponse> {
        return this.gpsService.findAll(user.companyId, findAllGps);
    }

    @Get("recent-gps-location/member/:memberId")
    async recentGpsLocation(
        @GetUser() user: JwtUserPayload,
        @Param("memberId", ParseUUIDPipe) memberId: string,
    ): Promise<HttpResponse> {
        return this.gpsService.recentGpsLocation(user.companyId, memberId);
    }
}
