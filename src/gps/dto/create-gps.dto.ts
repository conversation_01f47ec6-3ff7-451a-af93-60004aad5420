import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import {
    IsEnum,
    IsNotEmpty,
    IsOptional,
    IsUUID,
    ValidateNested,
    IsDate,
    IsNumber,
    IsIn,
    IsArray,
} from "class-validator";
import { Type } from "class-transformer";
import { GpsActivityEnum } from "../enum/gps-activity.enum";

export class CoordinateDto {
    @ApiProperty({ description: "Longitude" })
    @IsNumber()
    long: number;

    @ApiProperty({ description: "Latitude" })
    @IsNumber()
    lat: number;

    @ApiProperty({
        description: "type of activity",
        enum: GpsActivityEnum,
        default: GpsActivityEnum.OTHER,
    })
    @IsEnum(GpsActivityEnum)
    activity: GpsActivityEnum;

    @ApiProperty({ description: "Timestamp for coordinate" })
    @IsDate()
    @Type(() => Date)
    createdAt: Date;
}

export class GeoLocationDto {
    @ApiProperty({
        enum: ["MultiPoint"],
        description: "GeoJSON type",
    })
    @IsIn(["MultiPoint"])
    type: string;

    // @ApiProperty({
    //     description: "Coordinates follow GeoJSON rules, varies by type",
    //     example: [-122.4194, 37.7749], // example for Point
    // })
    // @IsArray()
    // coordinates: any;

    @ApiProperty({
        description: "Coordinates follow GeoJSON rules, with activity and createdAt",
        example: [-122.4194, 37.7749, 1, "2025-04-08T12:00:00.000Z"], // example for Point
    })
    @ValidateNested({ each: true })
    @Type(() => CoordinateDto)
    @IsArray()
    coordinates: CoordinateDto[];
}

export class AddGpsDto {
    @ApiProperty({ description: "GeoJSON location" })
    @ValidateNested()
    @Type(() => GeoLocationDto)
    location: GeoLocationDto;

    @ApiProperty({ description: "memberId" })
    @IsUUID()
    @IsNotEmpty()
    memberId: string;

    @ApiProperty({ description: "time card id for member" })
    @IsUUID()
    @IsNotEmpty()
    timeCardId: string;

    // @ApiProperty({
    //     description: "type of activity",
    //     enum: GpsActivityEnum,
    //     default: GpsActivityEnum.OTHER,
    // })
    // @IsEnum(GpsActivityEnum)
    // @IsNotEmpty()
    // activity: GpsActivityEnum;

    // @ApiPropertyOptional({ description: "createdAt" })
    // @IsOptional()
    // createdAt?: Date;
}
