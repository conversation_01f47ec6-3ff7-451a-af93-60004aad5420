import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { Document } from "mongoose";
import { randomUUID } from "crypto";

export const USER_FUTURE_SUBSCRIPTION_COLLECTION_NAME = "Userfuturesubscriptions";

export type UserFutureSubscriptionDocument = UserFutureSubscription & Document;

@Schema({
    timestamps: true,
    versionKey: false,
    id: false,
    collection: USER_FUTURE_SUBSCRIPTION_COLLECTION_NAME,
})
export class UserFutureSubscription {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @Prop({ type: String })
    userId: string;

    @Prop({ type: String })
    planId: string;

    @Prop({ type: String })
    stripeSubscriptionScheduleId: string;

    @Prop({ type: Number, required: true })
    subscriptionStartsAt: number;

    @Prop()
    subscribedTeamSize: number;
}

export const UserFutureSubscriptionSchema = SchemaFactory.createForClass(UserFutureSubscription);
