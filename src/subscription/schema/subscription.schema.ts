import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { randomUUID } from "crypto";

export const SUBSCRIPTION_PLAN_COLLECTION_NAME = "SubscriptionPlans";

export type SubscriptionPlanDocument = SubscriptionPlan & Document;

@Schema({ timestamps: true, versionKey: false, id: false, collection: SUBSCRIPTION_PLAN_COLLECTION_NAME })
export class SubscriptionPlan {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @Prop({ required: true })
    name: string;

    @Prop({ required: true })
    description: string;

    @Prop({ required: true })
    planImage: string;

    @Prop({ required: false, index: true })
    stripeProductId: string;

    @Prop({ type: String, required: false })
    defaultPrice: any;

    @Prop({ type: [{ type: String }] })
    availablePrices: string[];

    @Prop({ type: [{ type: String }] })
    featureList: string[];

    @Prop({ required: false, default: 0 })
    discount: number;

    @Prop({ required: false, default: false })
    discountEnabled: boolean;

    @Prop({ required: false })
    freeTrial: number;

    @Prop({ default: false })
    isDefault: boolean;

    @Prop({ required: true, default: false, index: true })
    deleted: boolean;
}

export const SubscriptionPlanSchema = SchemaFactory.createForClass(SubscriptionPlan);
