import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { Document } from "mongoose";
import { randomUUID } from "crypto";
import { SubscriptionRenewalPeriod } from "src/shared/enum/subscriptions.enum";

export const SUBSCRIPTION_PRICE_COLLECTION_NAME = "SubscriptionPrices";

export type SubscriptionPriceDocument = SubscriptionPrice & Document;

@Schema({ timestamps: true, id: false, versionKey: false, collection: SUBSCRIPTION_PRICE_COLLECTION_NAME })
export class SubscriptionPrice {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @Prop({ required: true })
    price: number;

    @Prop({ required: true, default: false })
    recurring: boolean;

    @Prop({ required: true })
    currency: string;

    @Prop({
        type: String,
        enum: SubscriptionRenewalPeriod,
        default: SubscriptionRenewalPeriod.MONTH,
    })
    renewalPeriod: SubscriptionRenewalPeriod;

    @Prop({ default: 1 })
    renewalNumber: number;

    @Prop({ required: true })
    stripePriceId: string;

    @Prop({ required: true })
    stripeProductId: string;
}

export const SubscriptionPriceSchema = SchemaFactory.createForClass(SubscriptionPrice);
