import { <PERSON>du<PERSON> } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";

import { SubscriptionController } from "./subscription.controller";
import { SubscriptionService } from "./subscription.service";
import { SUBSCRIPTION_PLAN_COLLECTION_NAME, SubscriptionPlanSchema } from "./schema/subscription.schema";
import { SUBSCRIPTION_PRICE_COLLECTION_NAME, SubscriptionPriceSchema } from "./schema/price.schema";
import { UserSchema } from "src/user/schema/user.schema";
import { CompanySchema } from "src/company/schema/company.schema";
import {
    USER_FUTURE_SUBSCRIPTION_COLLECTION_NAME,
    UserFutureSubscriptionSchema,
} from "./schema/future-subscriptions.schema";
import { SubscriptionHookService } from "./subscription-hook.service";
import { StripeModule } from "src/stripe/stripe.module";
import { MailModule } from "src/mail/mail.module";
import { CompanyAnalyticsSchema } from "src/company/schema/company-analytics.schema";
import { AdminSchema } from "src/admin/schema/admin.schema";

@Module({
    imports: [
        MongooseModule.forFeature([
            { name: SUBSCRIPTION_PLAN_COLLECTION_NAME, schema: SubscriptionPlanSchema },
            { name: SUBSCRIPTION_PRICE_COLLECTION_NAME, schema: SubscriptionPriceSchema },
            { name: USER_FUTURE_SUBSCRIPTION_COLLECTION_NAME, schema: UserFutureSubscriptionSchema },
            { name: "Admin", schema: AdminSchema },
            { name: "User", schema: UserSchema },
            { name: "Company", schema: CompanySchema },
            { name: "CompanyAnalytics", schema: CompanyAnalyticsSchema },
        ]),
        StripeModule,
        MailModule,
    ],
    controllers: [SubscriptionController],
    providers: [SubscriptionService, SubscriptionHookService],
    exports: [SubscriptionService],
})
export class SubscriptionModule {}
