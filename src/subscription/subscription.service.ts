import {
    BadRequestException,
    ConflictException,
    HttpException,
    Injectable,
    InternalServerErrorException,
    NotFoundException,
} from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";

import { StripeService } from "src/stripe/stripe.service";
import { CreatePlanDto, UpdatePlanDto } from "./dto/plan.dto";
import { SUBSCRIPTION_PRICE_COLLECTION_NAME, SubscriptionPriceDocument } from "./schema/price.schema";
import { SUBSCRIPTION_PLAN_COLLECTION_NAME, SubscriptionPlanDocument } from "./schema/subscription.schema";
import OkResponse from "src/shared/http/response/ok.http";
import { Currencies } from "src/shared/constants/constant";
import { ResponseMessage } from "src/shared/constants/responseMessage";
import {
    CreateSubscriptionDto,
    AddPaymentMethodDto,
    UpgradeSubscriptionDto,
} from "./dto/user-subscription.dto";
import { UserDocument } from "src/user/schema/user.schema";
import { CompanyDocument } from "src/company/schema/company.schema";
import {
    DiscountDuration,
    SubscriptionItem,
    SubscriptionPlanTypeEnum,
    SubscriptionRenewalPeriod,
    SubscriptionStatusEnum,
} from "src/shared/enum/subscriptions.enum";
import {
    USER_FUTURE_SUBSCRIPTION_COLLECTION_NAME,
    UserFutureSubscriptionDocument,
} from "./schema/future-subscriptions.schema";
import { CompanyAnalyticsDocument } from "src/company/schema/company-analytics.schema";
import { AdminDocument } from "src/admin/schema/admin.schema";
import { AdminRoleEnum } from "src/company/enum/role.enum";

@Injectable()
export class SubscriptionService {
    constructor(
        @InjectModel(SUBSCRIPTION_PLAN_COLLECTION_NAME)
        private subscriptionPlanModel: Model<SubscriptionPlanDocument>,
        @InjectModel(SUBSCRIPTION_PRICE_COLLECTION_NAME)
        private subscriptionPriceModel: Model<SubscriptionPriceDocument>,
        @InjectModel(USER_FUTURE_SUBSCRIPTION_COLLECTION_NAME)
        private futureSubsDocModel: Model<UserFutureSubscriptionDocument>,
        @InjectModel("Admin")
        private adminModel: Model<AdminDocument>,
        @InjectModel("User")
        private userModel: Model<UserDocument>,
        @InjectModel("Company")
        private companyModel: Model<CompanyDocument>,
        @InjectModel("CompanyAnalytics")
        private companyAnalyticsModel: Model<CompanyAnalyticsDocument>,
        private readonly stripeService: StripeService,
    ) {}

    async createPlan(planInput: CreatePlanDto) {
        try {
            const { name, price, isDefault } = planInput;

            const [existingPlan, defaultPlan] = await Promise.all([
                this.subscriptionPlanModel.findOne({ name, deleted: false }),
                isDefault ? this.subscriptionPlanModel.findOne({ isDefault: true, deleted: false }) : null,
            ]);

            if (existingPlan?.defaultPrice?.price === price) {
                throw new ConflictException(ResponseMessage.PLAN_PRICE_EXIST);
            }

            if (isDefault && defaultPlan) {
                throw new ConflictException(ResponseMessage.DEFAULT_PLAN_EXISTS);
            }

            /* creating a product in stripe */
            const productData = {
                name: planInput.name,
                description: planInput.description,
                active: true,
                image: planInput.planImage,
                price: planInput.price,
                renewalPeriod: planInput.renewalPeriod,
                renewalNumber: planInput.renewalNumber,
            };

            // if (isDefault) {
            //     productData.renewalPeriod = SubscriptionRenewalPeriod.YEAR;
            //     productData.renewalNumber = 1;
            // }

            const prodCreateRes = await this.stripeService.createPlan(productData);

            /* storing the product in database */
            const savedProductData = await this.subscriptionPlanModel.create({
                name: planInput.name,
                planImage: planInput.planImage,
                description: planInput.description,
                stripeProductId: prodCreateRes.stripeProductId,
                freeTrial: planInput.freeTrial,
                discount: planInput.discount,
                featureList: planInput?.featureList,
                isDefault: planInput?.isDefault,
            });

            /* saving the price in database */
            const priceData = await this.subscriptionPriceModel.create({
                price: planInput.price,
                currency: Currencies.USD,
                recurring: planInput.recurring,
                renewalPeriod: productData.renewalPeriod,
                renewalNumber: productData.renewalNumber,
                stripePriceId: prodCreateRes.stripePriceId,
                stripeProductId: prodCreateRes.stripeProductId,
            });

            /* updating the price data in subscriptions */
            await this.subscriptionPlanModel.updateOne(
                { _id: savedProductData._id },
                { defaultPrice: priceData._id, availablePrices: [priceData._id] },
            );

            return new OkResponse({ message: "plan created successfully" });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async fetchAllPlans() {
        try {
            const query = { deleted: false };

            const plans = await this.subscriptionPlanModel.aggregate([
                { $match: query },
                {
                    $lookup: {
                        from: SUBSCRIPTION_PRICE_COLLECTION_NAME,
                        localField: "defaultPrice",
                        foreignField: "_id",
                        as: "defaultPrice",
                    },
                },
                { $unwind: "$defaultPrice" },
                {
                    $sort: {
                        isDefault: -1,
                        "defaultPrice.price": 1,
                    },
                },
            ]);

            const {
                baseUserCount: defaultUserCount,
                extraUserChargeYearly: extraMemberChargeForAnnualSub,
                extraUserChargeMonthly: extraMemberChargeForMonthlySub,
            } = await this.adminModel.findOne({ role: AdminRoleEnum.SuperAdmin });

            return new OkResponse({
                plans,
                defaultUserCount,
                extraMemberChargeForAnnualSub,
                extraMemberChargeForMonthlySub,
            });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async fetchPlan(planId: string) {
        try {
            const plan = await this.subscriptionPlanModel
                .findOne({
                    _id: planId,
                })
                .populate("defaultPrice", "", this.subscriptionPriceModel)
                .exec();
            return new OkResponse({ plan });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updatePlan(planId: string, planInput: UpdatePlanDto) {
        try {
            let newPrice = false;

            const planData = await this.subscriptionPlanModel
                .findOne({ _id: planId })
                .populate("defaultPrice", "", this.subscriptionPriceModel)
                .exec();

            if (!planData) {
                throw new NotFoundException(ResponseMessage.SUBSCRIPTION_NOT_FOUND);
            }

            const updateData = {};
            const updateToStripe = {};

            if (planInput?.name && planData.name !== planInput.name) {
                updateData["name"] = planInput.name;
                updateToStripe["name"] = planInput.name;
            }

            if (planInput?.description && planData.description !== planInput.description) {
                updateData["description"] = planInput.description;
                updateToStripe["description"] = planInput.description;
            }

            if (planInput?.planImage && planData.planImage !== planInput.planImage) {
                updateData["planImage"] = planInput.planImage;
                updateToStripe["images"] = [planInput.planImage];
            }

            if (planData?.discount !== planInput.discount) {
                updateData["discount"] = planInput.discount;
            }

            if (planData?.freeTrial !== planInput.freeTrial) {
                updateData["freeTrial"] = planInput.freeTrial;
            }

            if (planData?.discountEnabled !== planInput.discountEnabled) {
                updateData["discountEnabled"] = planInput.discountEnabled;
            }

            if (planInput?.price > 0 && planData?.defaultPrice?.price !== planInput.price) {
                newPrice = true;
            }

            if (planInput?.featureList && planData.featureList !== planInput.featureList) {
                updateData["featureList"] = planInput.featureList;
            }

            if (newPrice) {
                const newStripePrice = await this.stripeService.createNewPrice(
                    planInput.price,
                    planData.stripeProductId,
                    planInput.renewalPeriod,
                    SubscriptionItem.MAIN,
                );

                updateToStripe["defaultPrice"] = newStripePrice;

                const newDatabasePrice = await this.createNewPrice(
                    planInput.price,
                    newStripePrice,
                    planData.stripeProductId,
                );

                updateData["defaultPrice"] = newDatabasePrice;
                updateData["availablePrices"] = [...planData.availablePrices, newDatabasePrice];

                // TODO: Effect on user subscription of plan updation
                // await this.subscribedCustomerQueue.add("subscribed-customer-job", {
                //     subscriptionId: planInput.planId,
                //     priceId: newStripePrice,
                // });
            }

            if (Object.keys(updateToStripe).length) {
                await this.stripeService.updateProduct(planData.stripeProductId, updateToStripe);
            }

            if (Object.keys(updateData).length) {
                await this.subscriptionPlanModel.updateOne({ _id: planData._id }, updateData);
            } else if (!newPrice && planInput?.price > 0) {
                await this.subscriptionPriceModel.updateOne(
                    { _id: planData.defaultPrice._id },
                    { price: planInput.price },
                );
            }

            return new OkResponse({ message: ResponseMessage.SUBSCRIPTION_UPDATE });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async deletePlan(planId: string) {
        try {
            const plan = await this.subscriptionPlanModel.findById(planId);
            if (!plan || plan.deleted) throw new NotFoundException(ResponseMessage.PLAN_NOT_EXIST);
            // TODO: handle the effect of deleted plan on subscriber
            // this.cancelSubscriptionQueue.add("cancel-subscription-job", {
            //     planId,
            // });

            await this.subscriptionPlanModel.findOneAndUpdate(
                { _id: planId },
                {
                    deleted: true,
                },
            );

            return new OkResponse({ message: "plan deleted successfully" });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async createNewPrice(price: number, stripePriceId: string, stripeProductId: string): Promise<string> {
        try {
            const newPriceObject = await this.subscriptionPriceModel.create({
                price,
                recurring: true,
                currency: Currencies.USD,
                renewalPeriod: SubscriptionRenewalPeriod.MONTH,
                renewalNumber: 1,
                stripePriceId: stripePriceId,
                stripeProductId: stripeProductId,
            });
            return newPriceObject._id;
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async subscribeToPlan(companyId: string, planId: string, paymentIntentDto: CreateSubscriptionDto) {
        try {
            const { paymentToken, cardLast4Digit, teamSize, billingAddress } = paymentIntentDto;
            /* check user exist or not */
            const userSubscription = await this.companyModel.findOne({
                _id: companyId,
            });
            if (!userSubscription) throw new NotFoundException("Company not exists");

            const { stripeCustomerId } = userSubscription;
            const userData = await this.userModel.findOne({ _id: userSubscription.owner });

            /* check subscription exist or not */
            const planData = await this.subscriptionPlanModel
                .findOne({
                    _id: planId,
                    deleted: false,
                })
                .populate("defaultPrice", "", this.subscriptionPriceModel)
                .exec();

            if (!planData) throw new NotFoundException(ResponseMessage.SUBSCRIPTION_NOT_FOUND);

            if (
                (userSubscription.status === SubscriptionStatusEnum.ACTIVE ||
                    userSubscription.status === SubscriptionStatusEnum.TRIALING) &&
                userSubscription.planType !== SubscriptionPlanTypeEnum.FREE
            )
                throw new ConflictException("User already has active plan");

            let discountCoupon;
            if (planData.discountEnabled && planData.discount) {
                discountCoupon = await this.stripeService.createStripeCoupon(
                    planData.discount,
                    DiscountDuration.FOREVER,
                );
            }

            const {
                baseUserCount: defaultUserCount,
                extraUserChargeYearly: extraMemberChargeForAnnualSub,
                extraUserChargeMonthly: extraMemberChargeForMonthlySub,
            } = await this.adminModel.findOne({ role: AdminRoleEnum.SuperAdmin });

            const activeTeamMembers = teamSize ? teamSize : defaultUserCount;

            const extraAmountToPaid = this.calculatePriceForExtraMember(
                activeTeamMembers,
                planData.defaultPrice.renewalPeriod,
                { defaultUserCount, extraMemberChargeForAnnualSub, extraMemberChargeForMonthlySub },
            );

            if (userSubscription.planType === SubscriptionPlanTypeEnum.FREE) {
                await this.stripeService.deleteSubscription(userSubscription?.stripeSubscriptionId);
            }

            let stripePaymentMethodId;

            /* list the payment method whether the card is already been used by this customer or not */
            const { paymentMethodId } = await this.checkPaymentMethodAttached(
                stripeCustomerId,
                cardLast4Digit,
            );

            if (!paymentMethodId) {
                /* add a new payment method by cards */
                const { paymentMethodId } = await this.stripeService.addPaymentMethods(
                    paymentToken,
                    stripeCustomerId,
                    {
                        billingAddress,
                        name: `${userData.firstName} ${userData.lastName}`,
                        email: userData.email,
                        phone: userData.phone,
                    },
                );
                stripePaymentMethodId = paymentMethodId;
            } else {
                stripePaymentMethodId = paymentMethodId;
            }

            const subscriptionData = await this.stripeService.createPaymentIntent(
                stripeCustomerId,
                planData.defaultPrice.stripePriceId,
                stripePaymentMethodId,
                discountCoupon?.couponId,
                planData.freeTrial,
                { extraAmountToPaid, productId: planData.stripeProductId },
                planData.defaultPrice.renewalPeriod,
                activeTeamMembers,
            );

            return new OkResponse({ subscriptionData });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async upgradeSubscription(companyId: string, planId: string, upgradePlanDto: UpgradeSubscriptionDto) {
        try {
            const { paymentToken, cardLast4Digit, teamSize, billingAddress } = upgradePlanDto;

            const subscriptionData = await this.companyModel.findOne({
                _id: companyId,
            });
            const { owner: userPrimaryId, stripeCustomerId } = subscriptionData;

            const userData = await this.userModel.findOne({ _id: subscriptionData.owner });

            if (!subscriptionData && subscriptionData.planType !== SubscriptionPlanTypeEnum.FREE)
                throw new NotFoundException(ResponseMessage.NO_SUBSCRIPTION);

            /* check upgrade plan is same as current plan */
            if (subscriptionData.planId == planId)
                throw new ConflictException(ResponseMessage.UPGRADE_CURRENT_PLAN_FAILED);

            const currentPlan = await this.subscriptionPlanModel
                .findOne({
                    _id: subscriptionData.planId,
                })
                .populate("defaultPrice", "", this.subscriptionPriceModel);

            if (!currentPlan) throw new NotFoundException(ResponseMessage.PLAN_NOT_EXIST);

            /* check the plan data */
            const planData = await this.subscriptionPlanModel
                .findOne({
                    _id: planId,
                    is_deleted: false,
                })
                .populate("defaultPrice", "", this.subscriptionPriceModel)
                .exec();
            if (!planData) throw new NotFoundException(ResponseMessage.PLAN_NOT_EXIST);

            /* list the payment method whether the card is already been used by this customer or not */
            const { paymentMethodId } = await this.checkPaymentMethodAttached(
                stripeCustomerId,
                cardLast4Digit,
            );
            let stripePaymentMethodId: string;
            if (!paymentMethodId) {
                /* add a new payment method by cards */
                const { paymentMethodId } = await this.stripeService.addPaymentMethods(
                    paymentToken,
                    stripeCustomerId,
                    {
                        billingAddress,
                        name: `${userData.firstName} ${userData.lastName}`,
                        email: userData.email,
                        phone: userData.phone,
                    },
                );
                stripePaymentMethodId = paymentMethodId;
            } else {
                stripePaymentMethodId = paymentMethodId;
            }

            let discountCoupon;
            if (planData.discountEnabled && planData.discount) {
                discountCoupon = await this.stripeService.createStripeCoupon(
                    planData.discount,
                    DiscountDuration.FOREVER,
                );
            }

            const {
                baseUserCount: defaultUserCount,
                extraUserChargeYearly: extraMemberChargeForAnnualSub,
                extraUserChargeMonthly: extraMemberChargeForMonthlySub,
            } = await this.adminModel.findOne({ role: AdminRoleEnum.SuperAdmin });

            const activeTeamMembers = teamSize ? teamSize : defaultUserCount;

            const extraAmountToPaid = this.calculatePriceForExtraMember(
                activeTeamMembers,
                planData.defaultPrice.renewalPeriod,
                { defaultUserCount, extraMemberChargeForAnnualSub, extraMemberChargeForMonthlySub },
            );

            if (subscriptionData.planType === SubscriptionPlanTypeEnum.FREE) {
                await this.stripeService.deleteSubscription(subscriptionData?.stripeSubscriptionId);
            }

            /* check whether the user already subscribed this plan for future */
            const futureSubscriptionAvailable = await this.futureSubsDocModel.findOne({
                userId: userPrimaryId,
            });

            if (futureSubscriptionAvailable) {
                if (futureSubscriptionAvailable.planId != planData._id) {
                    await this.stripeService.deleteSubscriptionSchedule(
                        futureSubscriptionAvailable.stripeSubscriptionScheduleId,
                    );

                    const scheduledData = await this.stripeService.createSubscriptionSchedule(
                        stripeCustomerId,
                        subscriptionData.subscriptionEndDate,
                        planData.defaultPrice.stripePriceId,
                        stripePaymentMethodId,
                        discountCoupon?.couponId,
                        planData.freeTrial,
                        { extraAmountToPaid, productId: planData.stripeProductId },
                        planData.defaultPrice.renewalPeriod,
                        activeTeamMembers,
                    );
                    await this.futureSubsDocModel.updateOne(
                        {
                            user: userPrimaryId,
                        },
                        {
                            planId: planData._id,
                            stripeSubscriptionScheduleId: scheduledData.id,
                            subscribedTeamSize: activeTeamMembers,
                        },
                    );
                }
            } else {
                let cancelAt = subscriptionData.subscriptionEndDate;

                /* cancel current plan */
                cancelAt = await this.cancelCurrentPlan(
                    subscriptionData.stripeSubscriptionId,
                    subscriptionData._id,
                );
                /* creating a subscription schedule */
                const scheduledData = await this.stripeService.createSubscriptionSchedule(
                    stripeCustomerId,
                    cancelAt,
                    planData.defaultPrice.stripePriceId,
                    stripePaymentMethodId,
                    discountCoupon?.couponId,
                    planData.freeTrial,
                    { extraAmountToPaid, productId: planData.stripeProductId },
                    planData.defaultPrice.renewalPeriod,
                    activeTeamMembers,
                );

                await this.futureSubsDocModel.create({
                    userId: userPrimaryId,
                    planId: planData._id,
                    stripeSubscriptionScheduleId: scheduledData.id,
                    subscriptionStartsAt: cancelAt + 300,
                    subscribedTeamSize: activeTeamMembers,
                });
            }
            await this.companyModel.updateOne({ _id: companyId }, { isUpgraded: true });
            return new OkResponse({
                status: true,
                message: ResponseMessage.SUBSCRIPTION_SCHEDULE_SUCCESS,
            });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async cancelSubscription(companyId: string) {
        try {
            const subscriptionData = await this.companyModel.findOne({
                _id: companyId,
            });

            if (subscriptionData?.status !== SubscriptionStatusEnum.ACTIVE)
                throw new NotFoundException(ResponseMessage.NO_SUBSCRIPTION);

            /* cancel current plan */
            await this.cancelCurrentPlan(subscriptionData.stripeSubscriptionId, subscriptionData._id);

            return new OkResponse({
                status: true,
                message: ResponseMessage.SUBSCRIPTION_CANCEL_SUCCESS,
            });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async cancelFutureSubscription(companyId: string) {
        try {
            const subscriptionData = await this.companyModel.findOne({
                _id: companyId,
            });

            const futureSubscriptionData = await this.futureSubsDocModel.findOne({
                userId: subscriptionData?.owner,
            });

            if (!subscriptionData) throw new NotFoundException(ResponseMessage.NO_SUBSCRIPTION);

            /* cancel the schedule subscription in stripe */
            const cancelResponse = await this.stripeService.deleteSubscriptionSchedule(
                futureSubscriptionData.stripeSubscriptionScheduleId,
            );

            /* update the data in database */
            if (!cancelResponse)
                throw new InternalServerErrorException(ResponseMessage.SUBSCRIPTION_CANCEL_FAILED);
            await this.futureSubsDocModel.deleteOne({ _id: futureSubscriptionData._id });

            const canceled = await this.stripeService.enableCanceledPlan(
                subscriptionData?.stripeSubscriptionId,
            );
            if (canceled) {
                await this.companyModel.updateOne(
                    { _id: companyId },
                    { $set: { cancelAt: null, isUpgraded: false } },
                );
            }
            return new OkResponse({
                status: true,
                message: ResponseMessage.SUBSCRIPTION_CANCEL_SUCCESS,
            });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async checkPaymentMethodAttached(
        stripeCustomerId: string,
        cardLast4Digit: string,
    ): Promise<{ paymentMethodId: string; card_brand: string }> {
        const paymentMethods = await this.stripeService.listPaymentMethods(stripeCustomerId);
        const isCardAttached = paymentMethods.data.find((method) => method.card.last4 === cardLast4Digit);
        return {
            paymentMethodId: isCardAttached?.id,
            card_brand: isCardAttached?.card?.brand,
        };
    }

    async cancelCurrentPlan(
        stripeUserSubscriptionId: string,
        userCurrentSubscriptionId: string,
    ): Promise<number> {
        /* cancel the subscription in stripe */
        const cancelResponse = await this.stripeService.cancelSubscription(stripeUserSubscriptionId);

        /* update the data in database */
        if (!cancelResponse)
            throw new InternalServerErrorException(ResponseMessage.SUBSCRIPTION_CANCEL_FAILED);

        await this.companyModel.updateOne(
            { _id: userCurrentSubscriptionId },
            { cancelAt: cancelResponse.cancelAt },
        );

        return cancelResponse.cancelAt;
    }

    async getUserSubscription(companyId: string) {
        try {
            const userSubscriptions = await this.companyModel
                .findOne({
                    _id: companyId,
                    status: {
                        $in: [
                            SubscriptionStatusEnum.ACTIVE,
                            SubscriptionStatusEnum.CANCELLED,
                            SubscriptionStatusEnum.TRIALING,
                        ],
                    },
                })
                .populate({
                    path: "planId",
                    model: this.subscriptionPlanModel,
                    populate: {
                        path: "defaultPrice",
                        model: this.subscriptionPriceModel,
                    },
                })
                // .select(
                //     "companyName owner createdAt planId foundingYear cancelAt interval subscriptionEndDate subscriptionStartDate status planType subscribedTeamSize teamSize nextCycle",
                // )
                .exec();

            const analyticData = await this.companyAnalyticsModel
                .findOne({ companyId })
                .select("totalPayment totalTeamSize totalProjects");
            const futureSubscription = await this.futureSubsDocModel
                .find({ userId: userSubscriptions.owner })
                .populate({
                    path: "planId",
                    model: this.subscriptionPlanModel,
                    populate: {
                        path: "defaultPrice",
                        model: this.subscriptionPriceModel,
                    },
                })
                .exec();
            return new OkResponse({
                userSubscriptions,
                analyticData,
                upcomingSubscription: futureSubscription,
            });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async chargesToBuyUsers(companyId: string, userCount: number) {
        try {
            // Fetch user subscription details and plan data
            const userSubscription = await this.companyModel
                .findOne({ _id: companyId })
                .populate("planId", "", this.subscriptionPlanModel);

            if (!userSubscription) throw new NotFoundException("You don't have an active subscription");
            if (userSubscription.status === SubscriptionStatusEnum.CANCELLED)
                throw new NotFoundException("Your subscription is cancelled");

            // Ensure the user is on an annual plan
            if (userSubscription.interval !== SubscriptionRenewalPeriod.YEAR) {
                throw new BadRequestException("You are not on an annual plan");
            }

            // Fetch the plan data
            const planData = await this.subscriptionPlanModel
                .findOne({ _id: userSubscription.planId })
                .populate("defaultPrice", "", this.subscriptionPriceModel);

            // Calculate remaining months
            const numberOfMonths = Math.round(
                (userSubscription.subscriptionEndDate * 1000 - Date.now()) /
                    (1000 * 60 * 60 * 24 * 30.436875),
            );

            const {
                baseUserCount: deafultUserCount,
                extraUserChargeYearly: extraMemberChargeForAnnualSub,
                extraUserChargeMonthly: extraMemberChargeForMonthlySub,
            } = await this.adminModel.findOne({ role: AdminRoleEnum.SuperAdmin });

            const monthlyCharges = userCount * extraMemberChargeForAnnualSub;
            const newAmount = monthlyCharges * numberOfMonths;
            const message = `You have to pay $${newAmount} for the remaining ${numberOfMonths} months`;

            // Return the calculated response
            return new OkResponse({
                newAmount,
                numberOfMonths,
                renewalPeriod: userSubscription.interval,
                stripeSubscriptionId: userSubscription.stripeSubscriptionId,
                stripeSubscriptionItemId: userSubscription.stripeSubscriptionItemId,
                extraStripeSubscriptionItemId: userSubscription.extraStripeSubscriptionItemId,
                stripeProductId: planData.stripeProductId,
                stripePriceId: planData.defaultPrice.stripePriceId,
                message,
                deafultUserCount,
                extraMemberChargeForMonthlySub,
                extraMemberChargeForAnnualSub,
                newTeamSize: userSubscription.subscribedTeamSize + userCount,
                monthlyCharges,
                stripeCustomerId: userSubscription.stripeCustomerId,
            });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async buyUsers(companyId: string, userCount: number) {
        try {
            // Get charges and user data
            const { data } = await this.chargesToBuyUsers(companyId, userCount);
            const {
                numberOfMonths,
                renewalPeriod,
                stripeSubscriptionId,
                extraStripeSubscriptionItemId,
                stripeProductId,
                newTeamSize,
                extraMemberChargeForAnnualSub,
                deafultUserCount,
                monthlyCharges,
                stripeCustomerId,
            } = data;

            // Ensure the user is on an annual plan
            if (renewalPeriod !== SubscriptionRenewalPeriod.YEAR) {
                throw new BadRequestException("You are not on an Annual Plan");
            }

            // Create a new price on Stripe
            const newPrice = await this.stripeService.createNewPrice(
                (newTeamSize - deafultUserCount) * extraMemberChargeForAnnualSub,
                stripeProductId,
                SubscriptionRenewalPeriod.YEAR,
                SubscriptionItem.EXTRA,
            );

            // Update the Stripe subscription
            const newSubscriptionItemId = await this.stripeService.updateYearlySubscriptionPrice({
                userSubscriptionId: stripeSubscriptionId,
                stripeCustomerId,
                extraSubscriptionItemId: extraStripeSubscriptionItemId,
                newPriceId: newPrice,
                stripeProductId,
                amountToCharge: monthlyCharges,
                numberOfMonths,
                subscribedTeamSize: newTeamSize,
            });
            if (newSubscriptionItemId) {
                await this.companyModel.updateOne(
                    {
                        _id: companyId,
                    },
                    {
                        extraStripeSubscriptionItemId: newSubscriptionItemId,
                    },
                );
            }

            // Return success response
            return new OkResponse({ message: `Successfully bought ${userCount} new users` });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async listSubscribers() {
        try {
            const subscribers = await this.companyModel
                .find({
                    status: {
                        $in: [
                            SubscriptionStatusEnum.ACTIVE,
                            SubscriptionStatusEnum.CANCELLED,
                            SubscriptionStatusEnum.TRIALING,
                        ],
                    },
                })
                .populate("planId", "", this.subscriptionPlanModel)
                .populate("owner", "", this.userModel)
                .exec();
            return new OkResponse({ subscribers });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    calculatePriceForExtraMember(
        teamSize: number,
        renewalPeriod: string,
        { defaultUserCount, extraMemberChargeForAnnualSub, extraMemberChargeForMonthlySub }: any,
    ) {
        const extraUsers = teamSize - defaultUserCount;
        if (extraUsers <= 0) return 0;
        if (renewalPeriod === SubscriptionRenewalPeriod.YEAR) {
            return extraUsers * extraMemberChargeForAnnualSub;
        }

        return extraUsers * extraMemberChargeForMonthlySub;
    }

    async getCustomerDetails(companyId: string) {
        try {
            const companyData = await this.companyModel.findOne({ _id: companyId });
            const customerId = companyData.stripeCustomerId;
            // const subscriptions = await this.stripeService.subscriptionListOfCustomer(customerId);

            const invoices = await this.stripeService.invoiceListOfCustomer(customerId);
            const plans = await this.subscriptionPlanModel.find({
                deleted: false,
            });
            const subscriptionDetails = [];

            for (const invoice of invoices) {
                let planName;
                if (invoice.subscription) {
                    const { name } = plans.find(
                        (plan) => plan.stripeProductId === invoice?.lines?.data[0]?.price?.product,
                    );
                    planName = name;
                } else {
                    const planData = plans.find(
                        (plan) => plan.stripeProductId === invoice?.lines?.data[0]?.price?.product,
                    );
                    planName = planData?.name || "One time payment";
                }

                subscriptionDetails.push({
                    planName,
                    amountPaid: invoice?.amount_paid / 100,
                    dateOfSubscription: new Date(invoice?.created * 1000),
                    invoiceLink: invoice.hosted_invoice_url,
                    invoicePdf: invoice.invoice_pdf,
                    // status
                });
            }

            // Sort by date of subscription
            subscriptionDetails.sort((a, b) => a.dateOfSubscription - b.dateOfSubscription);

            // Format date as ISO string for display purposes
            const formattedDetails = subscriptionDetails.map((detail) => ({
                ...detail,
                dateOfSubscription: detail.dateOfSubscription.toISOString(),
            }));

            return new OkResponse({ userInvoiceData: formattedDetails });
        } catch (error) {
            console.error("Error retrieving customer details:", error);
            throw error;
        }
    }

    async fetchPaymentMethods(companyId: string) {
        try {
            // Fetch company and Stripe customer data
            const companyData = await this.companyModel.findOne({ _id: companyId });
            const customerId = companyData.stripeCustomerId;

            // Fetch all payment methods and the default payment method
            const [paymentMethods, defaultPaymentMethod] = await Promise.all([
                this.stripeService.getPaymentMethods(customerId),
                this.stripeService.getDefaultPaymentMethod(customerId),
            ]);

            const filteredPaymentMethods = (paymentMethods || []).filter(
                (pm) => pm?.id !== defaultPaymentMethod?.id,
            );

            // Format payment methods
            const formattedPaymentMethods = filteredPaymentMethods.map((method) => ({
                id: method.id,
                name: method.billing_details?.name || "N/A",
                last4: method.card?.last4 || "N/A",
                brand: method.card?.brand || "N/A",
            }));

            // Format the default payment method (only if it exists)
            const formattedDefaultPaymentMethod = defaultPaymentMethod
                ? {
                      id: defaultPaymentMethod.id,
                      name: defaultPaymentMethod.billing_details?.name || "N/A",
                      last4: defaultPaymentMethod.card?.last4 || "N/A",
                      brand: defaultPaymentMethod.card?.brand || "N/A",
                  }
                : {};

            // Return a response with formatted data
            return new OkResponse({
                paymentMethods: formattedPaymentMethods,
                defaultPaymentMethod: formattedDefaultPaymentMethod,
            });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async addPaymentMethod(companyId: string, paymentDetails: AddPaymentMethodDto) {
        try {
            const { billingAddress, paymentToken, cardLast4Digit } = paymentDetails;

            // Fetch company data with the owner field populated
            const companyData = await this.companyModel
                .findOne({ _id: companyId })
                .populate({
                    path: "owner",
                    model: this.userModel,
                })
                .exec();

            const stripeCustomerId = companyData.stripeCustomerId;
            const userData: any = companyData.owner;

            if (!stripeCustomerId) {
                throw new HttpException("Stripe customer ID not found for the company", 400);
            }

            if (!userData) {
                throw new HttpException("Owner information not found", 400);
            }

            const { paymentMethodId: paymentMethodExists } = await this.checkPaymentMethodAttached(
                stripeCustomerId,
                cardLast4Digit,
            );
            if (paymentMethodExists) throw new ConflictException("This card already added");

            // Add a new payment method by card
            const { paymentMethodId } = await this.stripeService.addPaymentMethods(
                paymentToken,
                stripeCustomerId,
                {
                    billingAddress,
                    name: `${userData.firstName} ${userData.lastName}`,
                    email: userData.email,
                    phone: userData.phone,
                },
            );

            // Return success response
            return new OkResponse({ message: "Payment method updated successfully", paymentMethodId });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message || "Something went wrong");
        }
    }
    async switchDefaultPaymentMethod(companyId: string, paymentMethodId: string) {
        try {
            // Fetch company data with the owner field populated
            const companyData = await this.companyModel
                .findOne({ _id: companyId })
                .populate({
                    path: "owner",
                    model: this.userModel,
                })
                .exec();

            const stripeCustomerId = companyData.stripeCustomerId;
            const userData: any = companyData.owner;

            if (!stripeCustomerId) {
                throw new HttpException("Stripe customer ID not found for the company", 400);
            }

            if (!userData) {
                throw new HttpException("Owner information not found", 400);
            }

            await this.stripeService.setDefaultPaymentMethod(stripeCustomerId, paymentMethodId);

            // Return success response
            return new OkResponse({ message: "Default payment method switched successfully" });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message || "Something went wrong");
        }
    }
}
