import {
    Controller,
    Post,
    Get,
    Put,
    Body,
    ParseUUI<PERSON>ip<PERSON>,
    Param,
    <PERSON>,
    <PERSON>ers,
    Req,
    UseGuards,
} from "@nestjs/common";
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiExcludeEndpoint } from "@nestjs/swagger";

import { SubscriptionService } from "./subscription.service";
import {
    CreateSubscriptionDto,
    AddPaymentMethodDto,
    UpgradeSubscriptionDto,
} from "./dto/user-subscription.dto";
import RequestWithRawBody from "src/middlewares/rawbodymiddleware/requestWithRawBody.interface";
import { UserAuthGuard } from "src/auth/guards/jwt-auth.guard";
import { SubscriptionHookService } from "./subscription-hook.service";
import { GetUser } from "src/auth/decorator/auth.decorator";
import { JwtUserPayload } from "src/auth/interface/auth.interface";

@ApiTags("Subscription")
@Controller({ path: "subscription", version: "1" })
export class SubscriptionController {
    constructor(
        private readonly subscriptionService: SubscriptionService,
        private readonly subscriptionHookService: SubscriptionHookService,
    ) {}

    @Get("fetch-plans")
    @ApiOperation({ summary: "Fetch all plans" })
    @ApiResponse({ status: 200, description: "All plans have been successfully fetched." })
    @ApiResponse({ status: 400, description: "Bad Request" })
    async fetchAllPlans() {
        return this.subscriptionService.fetchAllPlans();
    }

    @Get("fetch-plan/:planId")
    @ApiOperation({ summary: "Fetch plan by id" })
    @ApiResponse({ status: 200, description: "All plans have been successfully fetched." })
    @ApiResponse({ status: 400, description: "Bad Request" })
    async fetchPlan(@Param("planId", ParseUUIDPipe) planId: string) {
        return this.subscriptionService.fetchPlan(planId);
    }

    // User Subscription APIS
    @ApiBearerAuth()
    @UseGuards(UserAuthGuard)
    @Post("create-subscription/:planId")
    async subscriberToPlan(
        @GetUser("user") user: JwtUserPayload,
        @Param("planId") planId: string,
        @Body() paymentIntentDto: CreateSubscriptionDto,
    ) {
        return this.subscriptionService.subscribeToPlan(user.companyId, planId, paymentIntentDto);
    }

    @ApiBearerAuth()
    @UseGuards(UserAuthGuard)
    @Put("upgrade-subscription/:planId")
    async upgradeSubscription(
        @GetUser("user") user: JwtUserPayload,
        @Param("planId") upgradeSubscriptionPlanId: string,
        @Body() upgradePlanDto: UpgradeSubscriptionDto,
    ) {
        return this.subscriptionService.upgradeSubscription(
            user.companyId,
            upgradeSubscriptionPlanId,
            upgradePlanDto,
        );
    }

    @ApiBearerAuth()
    @UseGuards(UserAuthGuard)
    @Patch("cancel-subscription")
    async cancelSubscription(@GetUser("user") user: JwtUserPayload) {
        return this.subscriptionService.cancelSubscription(user.companyId);
    }

    @ApiBearerAuth()
    @UseGuards(UserAuthGuard)
    @Patch("cancel-future-subscription")
    async cancelFutureSubscription(@GetUser("user") user: JwtUserPayload) {
        return this.subscriptionService.cancelFutureSubscription(user.companyId);
    }

    @UseGuards(UserAuthGuard)
    @ApiBearerAuth()
    @Get("user-subscriptions")
    async getUserSubscriptions(@GetUser("user") user: JwtUserPayload) {
        return this.subscriptionService.getUserSubscription(user.companyId);
    }

    @ApiBearerAuth()
    @UseGuards(UserAuthGuard)
    @Post("buy-users/:userCount")
    async buyUsers(@GetUser("user") user: JwtUserPayload, @Param("userCount") usersCount: number) {
        return this.subscriptionService.buyUsers(user.companyId, usersCount);
    }

    @ApiBearerAuth()
    @UseGuards(UserAuthGuard)
    @Get("charge-to-buy-users/:userCount")
    async chargesToBuyUsers(@GetUser("user") user: JwtUserPayload, @Param("userCount") usersCount: number) {
        return this.subscriptionService.chargesToBuyUsers(user.companyId, usersCount);
    }

    @Get("list-subscribers")
    async listSubscribers() {
        return this.subscriptionService.listSubscribers();
    }

    @ApiExcludeEndpoint()
    @Post("/processSubscription")
    async paymentwebhook(@Headers("stripe-signature") signature: string, @Req() request: RequestWithRawBody) {
        try {
            /* processing the webhook */
            await this.subscriptionHookService.processWebHook(request.rawBody, signature);
            return { status: true };
        } catch (error) {
            return error;
        }
    }

    @Get("user-invoices")
    @ApiBearerAuth()
    @UseGuards(UserAuthGuard)
    async userInvoices(@GetUser("user") user: JwtUserPayload) {
        return this.subscriptionService.getCustomerDetails(user.companyId);
    }

    @Get("payment-method")
    @ApiBearerAuth()
    @UseGuards(UserAuthGuard)
    async paymentMethod(@GetUser("user") user: JwtUserPayload) {
        return this.subscriptionService.fetchPaymentMethods(user.companyId);
    }

    @Post("add-payment-method/")
    @ApiBearerAuth()
    @UseGuards(UserAuthGuard)
    async addPaymentMethod(
        @GetUser("user") user: JwtUserPayload,
        @Body() paymentDetails: AddPaymentMethodDto,
    ) {
        return this.subscriptionService.addPaymentMethod(user.companyId, paymentDetails);
    }

    @Post("switch-default-payment-method/:paymentMethodId")
    @ApiBearerAuth()
    @UseGuards(UserAuthGuard)
    async switchDefaultPaymentMethod(
        @GetUser("user") user: JwtUserPayload,
        @Param("paymentMethodId") paymentMethodId: string,
    ) {
        return this.subscriptionService.switchDefaultPaymentMethod(user.companyId, paymentMethodId);
    }
}
