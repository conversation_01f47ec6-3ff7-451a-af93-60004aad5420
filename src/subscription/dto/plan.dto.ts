import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import {
    IsString,
    IsNumber,
    IsBoolean,
    IsOptional,
    IsPositive,
    IsNotEmpty,
    IsEnum,
    IsArray,
} from "class-validator";

enum SubscriptionRenewalPeriod {
    MONTH = "month",
    YEAR = "year",
}

export class CreatePlanDto {
    @ApiProperty({ description: "Name of the plan" })
    @IsString()
    @IsNotEmpty()
    readonly name: string;

    @ApiProperty({ description: "Description of the plan" })
    @IsString()
    @IsNotEmpty()
    readonly description: string;

    @ApiProperty({ description: "Price of the plan" })
    @IsNumber()
    readonly price: number;

    @ApiProperty({ description: "Is the plan recurring?" })
    @IsBoolean()
    readonly recurring: boolean;

    @ApiProperty({ description: "Image URL for the plan" })
    @IsString()
    @IsNotEmpty()
    readonly planImage: string;

    @ApiProperty({
        description: "Renewal period of the plan",
        enum: SubscriptionRenewalPeriod,
        default: SubscriptionRenewalPeriod.MONTH,
    })
    @IsEnum(SubscriptionRenewalPeriod)
    readonly renewalPeriod: SubscriptionRenewalPeriod;

    @ApiProperty({ description: "Number of renewals for the plan", default: 1 })
    @IsNumber()
    readonly renewalNumber: number;

    @ApiProperty({ description: "Discount in %", default: 0 })
    @IsOptional()
    @IsNumber()
    readonly discount: number;

    @ApiProperty({ description: "If discount on", default: false })
    @IsOptional()
    @IsBoolean()
    readonly discountEnabled: boolean;

    @ApiProperty({ description: "Free trial in Days", default: 0 })
    @IsOptional()
    @IsNumber()
    readonly freeTrial: number;

    @ApiPropertyOptional({ description: "Plan feature list" })
    @IsOptional()
    @IsArray()
    featureList: string[];

    @ApiPropertyOptional({ description: "Plan is default or not" })
    @IsNotEmpty()
    @IsBoolean()
    isDefault: boolean;
}

export class UpdatePlanDto {
    @ApiProperty({ description: "Name of the plan" })
    @IsOptional()
    @IsString()
    readonly name: string;

    @ApiPropertyOptional({ description: "Description of the plan" })
    @IsOptional()
    @IsString()
    readonly description?: string;

    @ApiProperty({ description: "Price of the plan" })
    @IsOptional()
    @IsNumber()
    readonly price: number;

    @ApiPropertyOptional({ description: "Image URL for the plan" })
    @IsOptional()
    @IsString()
    readonly planImage?: string;

    @ApiProperty({ description: "Discount amount in %", default: 0 })
    @IsOptional()
    @IsNumber()
    readonly discount: number;

    @ApiProperty({ description: "If Discount on", default: false })
    @IsOptional()
    @IsBoolean()
    readonly discountEnabled: boolean;

    @ApiProperty({ description: "Free trial in Days", default: 0 })
    @IsOptional()
    @IsNumber()
    readonly freeTrial: number;

    @ApiPropertyOptional({ description: "Plan feature list" })
    @IsOptional()
    @IsArray()
    featureList: string[];

    @ApiProperty({
        description: "Renewal period of the plan",
        enum: SubscriptionRenewalPeriod,
        default: SubscriptionRenewalPeriod.MONTH,
    })
    @IsOptional()
    @IsEnum(SubscriptionRenewalPeriod)
    readonly renewalPeriod: SubscriptionRenewalPeriod;

    @ApiProperty({ description: "Number of renewals for the plan" })
    @IsOptional()
    @IsNumber()
    readonly renewalNumber: number;
}
