import { IsString, <PERSON><PERSON>ptional, <PERSON>N<PERSON>Empty, <PERSON><PERSON><PERSON><PERSON> } from "class-validator";

import { ApiProperty } from "@nestjs/swagger";

export class CreateSubscriptionDto {
    @ApiProperty({ description: "cardToken", required: true })
    @IsNotEmpty()
    @IsString()
    paymentToken: string;

    @ApiProperty({ description: "cardLast4Digit", required: true })
    @IsNotEmpty()
    @IsString()
    cardLast4Digit: string;

    @ApiProperty({ description: "user count", required: false })
    @IsOptional()
    @IsNumber()
    teamSize?: number;

    @ApiProperty({ description: "billing details", required: false })
    @IsOptional()
    billingAddress?: any;
}

export class UpgradeSubscriptionDto {
    @ApiProperty({ description: "stripe token for payment" })
    @IsNotEmpty()
    readonly paymentToken?: string;

    @ApiProperty({ description: "cardLast4Digit", required: true })
    @IsNotEmpty()
    @IsString()
    cardLast4Digit?: string;

    @ApiProperty({ description: "user count", required: false })
    @IsOptional()
    @IsNumber()
    teamSize?: number;

    @ApiProperty({ description: "billing details", required: false })
    @IsOptional()
    billingAddress?: any;
}

export class AddPaymentMethodDto {
    @ApiProperty({ description: "cardToken", required: true })
    @IsNotEmpty()
    @IsString()
    paymentToken: string;

    @ApiProperty({ description: "cardLast4Digit", required: true })
    @IsNotEmpty()
    @IsString()
    cardLast4Digit: string;

    @ApiProperty({ description: "billing details", required: false })
    @IsNotEmpty()
    billingAddress?: any;
}
