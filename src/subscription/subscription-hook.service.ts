import { Injectable, Logger } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";

import {
    USER_FUTURE_SUBSCRIPTION_COLLECTION_NAME,
    UserFutureSubscriptionDocument,
} from "./schema/future-subscriptions.schema";
import { SUBSCRIPTION_PLAN_COLLECTION_NAME, SubscriptionPlanDocument } from "./schema/subscription.schema";
import { SUBSCRIPTION_PRICE_COLLECTION_NAME, SubscriptionPriceDocument } from "./schema/price.schema";
import {
    PaymentStatus,
    SubscriptionItem,
    SubscriptionPlanTypeEnum,
    SubscriptionStatusEnum,
} from "src/shared/enum/subscriptions.enum";
import { ResponseMessage } from "src/shared/constants/responseMessage";
import { UserDocument } from "src/user/schema/user.schema";
import { CompanyDocument } from "src/company/schema/company.schema";
import { StripeService } from "src/stripe/stripe.service";
import { MailService } from "src/mail/mail.service";
import OkResponse from "src/shared/http/response/ok.http";
import { CompanyAnalytics } from "src/company/schema/company-analytics.schema";
import { CompanyAnalyticsService } from "src/company/company-analytics.service";

@Injectable()
export class SubscriptionHookService {
    constructor(
        @InjectModel(SUBSCRIPTION_PLAN_COLLECTION_NAME)
        private subscriptionPlanModel: Model<SubscriptionPlanDocument>,
        @InjectModel(SUBSCRIPTION_PRICE_COLLECTION_NAME)
        private subscriptionPriceModel: Model<SubscriptionPriceDocument>,
        @InjectModel(USER_FUTURE_SUBSCRIPTION_COLLECTION_NAME)
        private futureSubsDocModel: Model<UserFutureSubscriptionDocument>,
        @InjectModel("User")
        private userModel: Model<UserDocument>,
        @InjectModel("Company")
        private companyModel: Model<CompanyDocument>,
        @InjectModel("CompanyAnalytics")
        private companyAnalyticsModel: Model<CompanyAnalytics>,
        private readonly stripeService: StripeService,
        private readonly mailService: MailService,
        private readonly companyAnalyticsService: CompanyAnalyticsService,
    ) {}
    async processWebHook(requestData: Buffer, signature: string): Promise<any> {
        try {
            const event = this.stripeService.webhookEventListener(requestData, signature);
            let eventDetails;
            let res;
            switch (event.type) {
                case "invoice.payment_succeeded":
                    const invoicePaymentSucceeded = event.data.object;
                    /* check the payment status */
                    if (invoicePaymentSucceeded.status == PaymentStatus.PAID) {
                        const eventDetails = {
                            customerId: invoicePaymentSucceeded.customer,
                            subscriptionId: invoicePaymentSucceeded.subscription,
                            subscriptionItem: invoicePaymentSucceeded.lines.data[0]?.subscription_item,
                            amountPaid: invoicePaymentSucceeded.total,
                            metadata: invoicePaymentSucceeded.metadata,
                        };
                        if (invoicePaymentSucceeded?.lines?.data.length) {
                            invoicePaymentSucceeded?.lines?.data.forEach((line) => {
                                if (line?.price?.metadata?.subItem === SubscriptionItem.MAIN) {
                                    eventDetails["subscriptionItem"] = line?.subscription_item;
                                }
                                if (line?.price?.metadata?.subItem === SubscriptionItem.EXTRA) {
                                    eventDetails["extraSubscriptionItem"] = line?.subscription_item;
                                }
                            });
                        }
                        if (eventDetails.subscriptionId) {
                            const subscription = await this.stripeService.getCustomerSubscriptionById(
                                eventDetails.subscriptionId,
                            );

                            if (subscription.metadata && Object.keys(subscription.metadata).length > 0) {
                                eventDetails.metadata = subscription.metadata;
                            }
                            await this.processSuccessSubscriptionPayment(eventDetails);
                        } else {
                            eventDetails["productId"] = invoicePaymentSucceeded?.lines?.data[0].price.product;
                            await this.processOneTimeInvoicePayment(eventDetails);
                        }
                    }
                    break;
                case "customer.subscription.created":
                    // For handling the subscription cancellation
                    const subscriptionSuccessObject = event.data.object;
                    /* check the payment status */
                    if (subscriptionSuccessObject.status == SubscriptionStatusEnum.TRIALING) {
                        eventDetails = {
                            customerId: invoicePaymentSucceeded.customer,
                            paymentIntent: invoicePaymentSucceeded.payment_intent,
                            subscriptionId: invoicePaymentSucceeded.subscription,
                            amountPaid: invoicePaymentSucceeded.total,
                            metadata: invoicePaymentSucceeded.metadata,
                        };
                        if (invoicePaymentSucceeded?.lines?.data.length) {
                            invoicePaymentSucceeded?.lines?.data.forEach((line) => {
                                if (line?.price?.metadata?.subItem === SubscriptionItem.MAIN) {
                                    eventDetails["subscriptionItem"] = line?.subscription_item;
                                }
                                if (line?.price?.metadata?.subItem === SubscriptionItem.EXTRA) {
                                    eventDetails["extraSubscriptionItem"] = line?.subscription_item;
                                }
                            });
                        }
                        if (eventDetails.subscriptionId) {
                            const subscription = await this.stripeService.getCustomerSubscriptionById(
                                eventDetails.subscriptionId,
                            );

                            if (subscription.metadata && Object.keys(subscription.metadata).length > 0) {
                                eventDetails.metadata = subscription.metadata;
                            }
                        }
                        res = await this.processTrialingSubscription(eventDetails);
                    }
                    break;
                case "invoice.payment_failed":
                    // For handling the payment failed
                    const paymentFailedObject = event.data.object;
                    eventDetails = {
                        customerId: paymentFailedObject.customer,
                        paymentIntent: paymentFailedObject.payment_intent,
                        subscriptionId: paymentFailedObject.subscription,
                        planId: paymentFailedObject.items.data[0].plan.product,
                    };
                    res = await this.processPaymentFailed(eventDetails);
                    break;

                case "customer.subscription.deleted":
                    // For handling the subscription cancellation
                    const subscriptionCancelObject = event.data.object;
                    if (subscriptionCancelObject.status == SubscriptionStatusEnum.CANCELLED) {
                        eventDetails = {
                            customerId: subscriptionCancelObject.customer,
                            subscriptionId: subscriptionCancelObject.id,
                            planId: subscriptionCancelObject.items.data[0].plan.product,
                            status: subscriptionCancelObject.status,
                            subscriptionItem: invoicePaymentSucceeded.lines.data[0].subscription_item,
                            metadata: Object.keys(
                                invoicePaymentSucceeded.subscription_details?.metadata || {},
                            ).length
                                ? invoicePaymentSucceeded.subscription_details.metadata
                                : invoicePaymentSucceeded.metadata,
                        };
                        res = await this.cancelUserSubscriptionByHook(eventDetails);
                    }
                    break;
                default:
                    Logger.log(`Unhandled event type is ${event.type}`);
            }
            return new OkResponse({ res });
        } catch (error) {
            return error;
        }
    }

    async processSuccessSubscriptionPayment(userSubscription: any): Promise<any> {
        try {
            const {
                customerId,
                amountPaid,
                subscriptionId,
                subscriptionItem,
                extraSubscriptionItem,
                metadata,
            } = userSubscription;

            /* find user data */
            const userData = await this.userModel.findOne({ stripeCustomerId: customerId });
            if (!userData) {
                Logger.log(`[Invoice.paymentsuccess] Invalid User ${customerId}`);
                return {
                    status: true,
                    message: `[Invoice.paymentsuccess] Invalid user ${customerId}`,
                };
            }

            /* find subscription data */
            const stripeUserCurrentSubscriptionData = await this.stripeService.getCustomerSubscriptionById(
                subscriptionId,
            );

            /* retrieving the user subscription Item */
            const stripeSubscriptionItem = await this.stripeService.getCustomerSubscriptionItem(
                subscriptionItem,
            );

            const planData = await this.subscriptionPlanModel
                .findOne({ stripeProductId: stripeSubscriptionItem?.price?.product })
                .exec();

            if (!planData) {
                Logger.log(`[Invoice.paymentsuccess] Product not available in database`);
                return;
            }
            const { _id: userId } = userData;
            const { _id: planId } = planData;

            /* check subscription exist for user */
            const subscriptionData = await this.companyModel.findOne({
                owner: userId,
            });

            const currentUnixTimeSec = Math.floor(Date.now() / 1000);
            const subscribedTeamSize = Number(metadata?.subscribedTeamSize ?? 5);

            let subscriptionDataToSave: any = {
                planId,
                amountPaid: amountPaid / 100,
                status: SubscriptionStatusEnum.ACTIVE,
                stripeSubscriptionId: subscriptionId,
                stripeSubscriptionItemId: subscriptionItem,
                extraStripeSubscriptionItemId: extraSubscriptionItem || "",
                cancelAt: null,
                planType: amountPaid === 0 ? SubscriptionPlanTypeEnum.FREE : SubscriptionPlanTypeEnum.PRO,
                subscribedTeamSize,
                nextCycle: stripeUserCurrentSubscriptionData.current_period_end + 300,
                interval: stripeSubscriptionItem?.price?.recurring?.interval,
            };

            if (amountPaid === 0 && subscriptionData.status !== SubscriptionStatusEnum.TRIALING) {
                subscriptionDataToSave = {
                    ...subscriptionDataToSave,
                    subscriptionEndDate: null,
                };
            } else if (
                subscriptionData?.subscriptionEndDate &&
                Number(subscriptionData.subscriptionEndDate) > Number(currentUnixTimeSec)
            ) {
                subscriptionDataToSave = {
                    ...subscriptionDataToSave,
                    subscriptionEndDate: stripeUserCurrentSubscriptionData.current_period_end,
                    isUpgraded: false,
                };
            } else {
                subscriptionDataToSave = {
                    ...subscriptionDataToSave,
                    subscriptionStartDate: stripeUserCurrentSubscriptionData.current_period_start,
                    subscriptionEndDate: stripeUserCurrentSubscriptionData.current_period_end,
                };
            }

            await this.companyModel.updateOne({ _id: subscriptionData._id }, subscriptionDataToSave);

            // update company analatyics
            this.companyAnalyticsService.updateCompanyPayAnalytics(subscriptionData._id, {
                amount: amountPaid,
                planId,
                subscribedTeamSize,
            });

            const futureSubscriptionData = await this.futureSubsDocModel.findOne({
                userId,
            });

            if (futureSubscriptionData) {
                if (
                    stripeUserCurrentSubscriptionData.schedule !=
                    futureSubscriptionData.stripeSubscriptionScheduleId
                ) {
                    await this.stripeService.deleteSubscriptionSchedule(
                        futureSubscriptionData.stripeSubscriptionScheduleId,
                    );
                }
                /* removing it from future table after activation */
                await this.futureSubsDocModel.deleteOne({ _id: futureSubscriptionData._id });
            }

            if (!planData?.isDefault) {
                // sending mail
                this.mailService.sendSuccessfulPayment(
                    subscriptionData.companyName,
                    userData.email,
                    planData.name,
                );
            }

            Logger.log(`[Invoice.paymentsuccess] Done`);
            return {
                success: true,
                message: ResponseMessage.PROCESSING_DONE,
            };
        } catch (error) {
            Logger.log(
                `[Invoice.paymentsuccess] Error while processing the user subscription data ${error.message}`,
            );
        }
    }

    async processOneTimeInvoicePayment(userSubscription: any): Promise<any> {
        try {
            const { customerId, amountPaid, metadata, productId } = userSubscription;

            /* find user data */
            const userData = await this.userModel.findOne({ stripeCustomerId: customerId });
            if (!userData) {
                Logger.log(`[Invoice.paymentsuccess] Invalid User ${customerId}`);
                return {
                    status: true,
                    message: `[Invoice.paymentsuccess] Invalid user ${customerId}`,
                };
            }
            let planId;
            if (productId) {
                const planData = await this.subscriptionPlanModel
                    .findOne({ stripeProductId: productId })
                    .exec();
                if (!planData) {
                    Logger.log(`[Invoice.paymentsuccess] Product not available in database`);
                    return;
                }
                planId = planData?._id;
            }

            const { _id: userId } = userData;

            /* check subscription exist for user */
            const subscriptionData = await this.companyModel.findOne({
                owner: userId,
            });

            let subscriptionDataToSave;

            const subscribedTeamSize = Number(metadata?.subscribedTeamSize ?? 5);

            if (
                subscriptionData?.status === SubscriptionStatusEnum.ACTIVE &&
                subscriptionData?.planType !== SubscriptionPlanTypeEnum.FREE
            ) {
                subscriptionDataToSave = {
                    $inc: { amountPaid: amountPaid / 100 }, // Increment the amountPaid field
                    $set: { subscribedTeamSize }, // Set the subscribedTeamSize field
                };
            }

            await this.companyModel.updateOne({ _id: subscriptionData._id }, subscriptionDataToSave);

            if (planId) {
                // update company analatyics
                this.companyAnalyticsService.updateCompanyPayAnalytics(subscriptionData._id, {
                    amount: amountPaid,
                    planId,
                    subscribedTeamSize,
                });
            }

            //   TODO: add the message for extra users
            // // sending mail
            // this.mailService.sendSuccessfulPayment(
            //     subscriptionData.companyName,
            //     userData.email,
            //     planData.name,
            // );

            Logger.log(`[Invoice.paymentsuccess] Done`);
            return {
                success: true,
                message: ResponseMessage.PROCESSING_DONE,
            };
        } catch (error) {
            Logger.log(
                `[Invoice.paymentsuccess] Error while processing the user one time time payment data ${error.message}`,
            );
        }
    }

    async processPaymentFailed(paymentFailedDto: any) {
        try {
            /* check teh payment intent data */
            const paymentIntentData = await this.stripeService.fetchPaymentIntent(
                paymentFailedDto.paymentIntent,
            );

            if (paymentIntentData?.last_payment_error?.code === "card_declined") {
                /* retrieving the user subscription Item */

                const planData = await this.subscriptionPlanModel
                    .findOne({
                        stripeProductId: paymentFailedDto?.planId,
                    })
                    .exec();

                /* check existing customer */
                const userData = await this.userModel.findOne({
                    stripeCustomerId: paymentFailedDto.customerId,
                });
                if (!userData) {
                    Logger.log(`[invoice.payment_failed] Invalid User ${paymentFailedDto.customerId}`);
                    return {
                        status: true,
                        message: `[invoice.payment_failed] Invalid User ${paymentFailedDto.customerId}`,
                    };
                }

                /* check subscription exist for in database */
                const subscriptionData = await this.companyModel.findOne({
                    stripeSubscriptionId: paymentFailedDto.subscriptionId,
                });

                if (subscriptionData) {
                    /* cancelling the subscription immediately */
                    const cancelResponse = await this.stripeService.cancelSubscriptionNow(
                        paymentFailedDto.subscriptionId,
                    );
                    if (cancelResponse && cancelResponse.status == SubscriptionStatusEnum.CANCELLED) {
                        /* cancelling the subscription app level */
                        await this.companyModel.updateOne(
                            { stripe_user_subscription_id: paymentFailedDto.subscriptionId },
                            {
                                status: SubscriptionStatusEnum.CANCELLED,
                                planType: SubscriptionPlanTypeEnum.FREE,
                            },
                        );
                    }
                }
                // sending mail
                this.mailService.sendFailedPayment(
                    subscriptionData.companyName,
                    userData.email,
                    planData.name,
                );
            }

            return {
                status: true,
                message: ResponseMessage.PROCESSING_DONE,
            };
        } catch (error) {
            Logger.log(`[invoice.payment_failed] ${error.message}`);
        }
    }

    async cancelUserSubscriptionByHook(cancelDto: any) {
        try {
            /* check existing customer */
            const userData = await this.userModel.findOne({ stripeCustomerId: cancelDto.customerId });
            if (!userData) {
                Logger.log(`[customer.subscription.deleted] Invalid User ${cancelDto.customerId}`);
                return {
                    status: true,
                    message: `[customer.subscription.deleted] Invalid User ${cancelDto.customerId}`,
                };
            }

            /* check plan is valid */
            const planData = await this.subscriptionPlanModel
                .findOne({
                    stripeProductId: cancelDto.planId,
                })
                .exec();
            if (!planData) {
                Logger.log(
                    `[customer.subscription.deleted] ${cancelDto.planId} Product not available in database`,
                );
                return {
                    status: true,
                    message: `[customer.subscription.deleted] ${cancelDto.planId} Product not available in database`,
                };
            }

            /* check subscription exist for current user */
            const subscriptionData = await this.companyModel.findOne({
                stripeSubscriptionId: cancelDto.subscriptionId,
            });

            if (!subscriptionData) {
                Logger.log(
                    `[customer.subscription.deleted] Invalid User Subscription ${cancelDto.subscriptionId}`,
                );
                return {
                    status: true,
                    message: `[customer.subscription.deleted] Invalid User Subscription ${cancelDto.subscriptionId}`,
                };
            }

            /* find subscription data */
            const stripeUserCurrentSubscriptionData = await this.stripeService.getCustomerSubscriptionById(
                cancelDto.subscriptionId,
            );

            if (stripeUserCurrentSubscriptionData?.status == SubscriptionStatusEnum.CANCELLED) {
                /* updating the subscription status to canceled */
                await this.companyModel.updateOne(
                    { stripeSubscriptionId: cancelDto.subscriptionId },
                    { status: SubscriptionStatusEnum.CANCELLED, planType: SubscriptionPlanTypeEnum.FREE },
                );
                const planData = await this.subscriptionPlanModel
                    .findOne({ deleted: false, isDefault: true })
                    .populate("defaultPrice", "", this.subscriptionPriceModel);
                await this.stripeService.paymentIntentWithoutCard(
                    cancelDto.customerId,
                    planData.defaultPrice.stripePriceId,
                );
            }
            // sending mail
            this.mailService.sendSuccessfulCancel(
                subscriptionData.companyName,
                userData.email,
                planData.name,
            );
        } catch (error) {
            Logger.log(`[customer.subscription.deleted] ${error.message}`);
        }
    }

    async processTrialingSubscription(userSubscription: any): Promise<any> {
        try {
            const { customerId, amountPaid, subscriptionId, subscriptionItem, metadata } = userSubscription;

            /* find user data */
            const userData = await this.userModel.findOne({ stripeCustomerId: customerId });
            if (!userData) {
                Logger.log(`[Invoice.paymentsuccess] Invalid User ${customerId}`);
                return {
                    status: true,
                    message: `[Invoice.paymentsuccess] Invalid user ${customerId}`,
                };
            }

            /* find subscription data */
            const stripeUserCurrentSubscriptionData = await this.stripeService.getCustomerSubscriptionById(
                subscriptionId,
            );

            /* retrieving the user subscription Item */
            const stripeSubscriptionItem = await this.stripeService.getCustomerSubscriptionItem(
                subscriptionItem,
            );

            const planData = await this.subscriptionPlanModel
                .findOne({
                    stripeProductId: stripeSubscriptionItem?.price?.product,
                })
                .exec();

            if (!planData) {
                Logger.log(`[Invoice.paymentsuccess] Product not available in database`);
                return;
            }
            const { _id: userId } = userData;
            const { _id: planId } = planData;

            /* check subscription exist for user */
            const subscriptionData = await this.companyModel.findOne({
                owner: userId,
            });

            const subscribedTeamSize = Number(metadata?.subscribedTeamSize);

            const subscriptionDataToSave = {
                planId,
                amountPaid: amountPaid / 100,
                status: SubscriptionStatusEnum.ACTIVE,
                subscriptionStartDate: stripeUserCurrentSubscriptionData.current_period_start,
                subscriptionEndDate: stripeUserCurrentSubscriptionData.current_period_end,
                nextCycle: stripeUserCurrentSubscriptionData.current_period_end + 300,
                interval: stripeSubscriptionItem?.price?.recurring?.interval,
                stripeSubscriptionId: subscriptionId,
                stripeSubscriptionItemId: subscriptionItem,
                cancelAt: null,
                planType: SubscriptionPlanTypeEnum.PRO,
                subscribedTeamSize,
            };

            await this.companyModel.updateOne({ _id: subscriptionData._id }, subscriptionDataToSave);

            Logger.log(`[Invoice.paymentsuccess] Done`);
            return {
                success: true,
                message: ResponseMessage.PROCESSING_DONE,
            };
        } catch (error) {
            Logger.log(
                `[Invoice.paymentsuccess] Error while processing the trial subscription data ${error.message}`,
            );
        }
    }
}
