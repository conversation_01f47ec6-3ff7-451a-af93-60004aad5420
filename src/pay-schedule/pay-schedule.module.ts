import { Module, forwardRef } from "@nestjs/common";
import { PayScheduleController } from "./pay-schedule.controller";
import { PayScheduleService } from "./pay-schedule.service";
import { MongooseModule } from "@nestjs/mongoose";
import { PayScheduleSchema } from "./schema/pay-schedule.schema";
import { PositionModule } from "src/position/position.module";
import { RoleModule } from "src/role/role.module";
import { CompensationSchema } from "src/compensation/schema/compensation.schema";
import { CompanyModule } from "src/company/company.module";

@Module({
    imports: [
        MongooseModule.forFeature([
            { name: "PaySchedule", schema: PayScheduleSchema },
            { name: "Compensation", schema: CompensationSchema },
        ]),
        
        // PositionModule,
        // RoleModule,
        
        // forwardRef(() => CompanyModule),
    ],
    providers: [PayScheduleService],
    controllers: [PayScheduleController],
    exports: [PayScheduleService],
})
export class PayScheduleModule {}
