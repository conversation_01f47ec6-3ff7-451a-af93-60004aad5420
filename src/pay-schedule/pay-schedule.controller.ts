import { Body, Controller, Delete, Get, Param, ParseUUI<PERSON>ipe, <PERSON>, Post } from "@nestjs/common";
import {
    ApiTags,
    ApiOperation,
    ApiInternalServerErrorResponse,
    ApiConflictResponse,
    ApiNotFoundResponse,
    ApiUnauthorizedResponse,
    ApiBearerAuth,
} from "@nestjs/swagger";
import { Roles } from "src/auth/guards/auth.guard";
import { UserRolesEnum } from "src/company/enum/role.enum";
import HttpResponse from "src/shared/http/response/response.http";
import { CreatePayScheduleDto } from "./dto/create-pay-schedule.dto";
import { DeletePayScheduleDto } from "./dto/delete-pay-schedule.dto";
import { PayScheduleService } from "./pay-schedule.service";
import { Auth, GetUser } from "src/auth/decorator/auth.decorator";
import { JwtUserPayload } from "src/auth/interface/auth.interface";

@ApiBearerAuth()
@ApiTags("PaySchedule")
@Auth()
@Controller({ path: "pay-schedule", version: "1" })
export class PayScheduleController {
    constructor(private readonly payScheduleService: PayScheduleService) {}

    /**
     *Create a new pay schedule for a user.
     *@param userId The ID of the user creating the pay schedule.
     *@param createPayScheduleDto The DTO containing information about the pay schedule to create.
     *@returns A Promise that resolves to an HttpResponse.
     */
    @ApiOperation({ summary: "Create PaySchedule" })
    @ApiConflictResponse({ description: "PaySchedule already exist" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Post("create-pay-schedule")
    async createPaySchedule(
        @GetUser() user: JwtUserPayload,
        @Body() createPayScheduleDto: CreatePayScheduleDto,
    ): Promise<HttpResponse> {
        return this.payScheduleService.createPaySchedule(user.companyId, createPayScheduleDto);
    }

    /**
     * Edits an existing pay schedule.
     * @param userId The ID of the user making the request.
     * @param payScheduleId The ID of the pay schedule to edit.
     * @param createPayScheduleDto The data needed to update the pay schedule.
     * @returns The HTTP response with the result of the edit operation.
     */
    @ApiOperation({ summary: "Edit PaySchedule" })
    @ApiNotFoundResponse({ description: "Crew Member not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Patch("edit-pay-schedule/pay-schedule/:payScheduleId")
    async editPaySchedule(
        @GetUser() user: JwtUserPayload,
        @Param("payScheduleId") payScheduleId: string,
        @Body() createPayScheduleDto: CreatePayScheduleDto,
    ): Promise<HttpResponse> {
        return this.payScheduleService.editPaySchedule(user.companyId, payScheduleId, createPayScheduleDto);
    }

    /**
     * Deletes a pay schedule.
     * @param userId the ID of the user making the request
     * @param deletePayScheduleDto the DTO containing the ID of the pay schedule to delete
     * @returns an HTTP response
     */
    @ApiOperation({ summary: "Delete PaySchedule" })
    @ApiNotFoundResponse({ description: "Department not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Delete("delete-pay-schedule")
    async deletePaySchedule(
        @GetUser() user: JwtUserPayload,
        @Body() deletePayScheduleDto: DeletePayScheduleDto,
    ): Promise<HttpResponse> {
        return this.payScheduleService.deletePaySchedule(user.companyId, deletePayScheduleDto);
    }

    /**
     * Get company paySchedules
     * @param userId - ID of the user making the request
     * @param companyId - ID of the company whose pay schedules are being retrieved
     * @returns - HttpResponse containing the pay schedules for the specified company
     */
    @ApiOperation({ summary: "Get PaySchedule" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })

    // @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Get("get-pay-schedules")
    async getPaySchedule(@GetUser() user: JwtUserPayload): Promise<HttpResponse> {
        return this.payScheduleService.getPaySchedules(user._id, user.companyId);
    }

    /**
     * Get company paySchedules by id
     * @param userId - ID of the user making the request
     * @param companyId - ID of the company whose pay schedules are being retrieved
     * @param payScheduleId - ID of the payschedule which is being retrieved
     * @returns HttpResponse containing the pay schedules for the specified company and payschedule id
     */
    @ApiOperation({ summary: "Get PaySchedule By Id" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Get("get-pay-schedule/pay-schedule/:payScheduleId")
    async getPayScheduleById(
        @GetUser() user: JwtUserPayload,
        @Param("payScheduleId", ParseUUIDPipe) payScheduleId: string,
    ): Promise<HttpResponse> {
        return this.payScheduleService.getPayScheduleById(user._id, user.companyId, payScheduleId);
    }

    /**
     * Get dates array for a pay schedules
     * @param paySchId - ID of the payschedule which is being retrieved
     * @returns HttpResponse containing the dates for pay schedules for the specified payschedule id
     */
    @ApiOperation({ summary: "Get dates array for a pay schedules" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Get("pay-effective-dates/pay-schedule/:payScheduleId/hireDate/:hireDate")
    async payEffDates(
        @Param("payScheduleId", ParseUUIDPipe) payScheduleId: string,
        @Param("hireDate") hireDate: Date,
    ): Promise<HttpResponse> {
        return this.payScheduleService.payEffDates(payScheduleId, hireDate);
    }
}
