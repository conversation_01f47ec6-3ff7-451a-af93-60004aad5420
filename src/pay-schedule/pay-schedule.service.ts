import { Injectable, HttpException, InternalServerErrorException, HttpStatus } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import { Model } from "mongoose";
import CreatedResponse from "src/shared/http/response/created.http";
import NoContentResponse from "src/shared/http/response/no-content.http";
import OkResponse from "src/shared/http/response/ok.http";
import { CreatePayScheduleDto } from "./dto/create-pay-schedule.dto";
import { DeletePayScheduleDto } from "./dto/delete-pay-schedule.dto";
import { PayScheduleDocument } from "./schema/pay-schedule.schema";
import { defaultPaySchedule } from "src/shared/constants";
import { CompensationDocument } from "src/compensation/schema/compensation.schema";
import { daysInMonth, sixMonthsAgo } from "src/shared/helpers/logics";
import { PeriodEnum } from "./enum/period.enum";

@Injectable()
export class PayScheduleService {
    constructor(
        @InjectModel("PaySchedule") private readonly payScheduleModel: Model<PayScheduleDocument>,
        @InjectModel("Compensation") private readonly compensationModel: Model<CompensationDocument>,
    ) {}

    async createPaySchedule(companyId: string, createPayScheduleDto: CreatePayScheduleDto) {
        try {
            const paySchedule = await this.payScheduleModel
                .exists({
                    companyId,
                    name: createPayScheduleDto.name,
                    deleted: false,
                })
                .exec();
            if (paySchedule) throw new HttpException("Pay Schedule already exists", HttpStatus.BAD_REQUEST);
            const id = randomUUID();
            const createdPaySchedule = new this.payScheduleModel({
                _id: id,
                companyId,
                ...createPayScheduleDto,
            });
            await createdPaySchedule.save();
            return new CreatedResponse({ message: "PaySchedule created successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async editPaySchedule(
        companyId: string,
        payScheduleId: string,
        createPayScheduleDto: CreatePayScheduleDto,
    ) {
        try {
            const paySchedule = await this.payScheduleModel
                .findOne({
                    companyId,
                    _id: payScheduleId,
                    deleted: false,
                })
                .exec();
            if (!paySchedule) throw new HttpException("Pay schedule does not exists", HttpStatus.BAD_REQUEST);

            const comp = await this.compensationModel.find({
                companyId,
                payScheduleId,
            });

            if (comp.length)
                throw new HttpException("Pay schedule is in use, can not update it", HttpStatus.BAD_REQUEST);

            await this.payScheduleModel.findOneAndUpdate(
                { _id: payScheduleId },
                {
                    $set: {
                        ...createPayScheduleDto,
                    },
                },
                { new: true },
            );
            return new OkResponse({ message: "PaySchedule updated successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async deletePaySchedule(companyId: string, deletePayScheduleDto: DeletePayScheduleDto) {
        try {
            const paySchedule = await this.payScheduleModel
                .findOne({
                    _id: deletePayScheduleDto.id,
                })
                .exec();
            if (!paySchedule) throw new HttpException("Pay Schedule does not exist", HttpStatus.BAD_REQUEST);

            const comp = await this.compensationModel.find({
                companyId,
                payScheduleId: deletePayScheduleDto.id,
            });

            if (comp.length)
                throw new HttpException("Pay schedule is in use, can not delete it", HttpStatus.BAD_REQUEST);

            await this.payScheduleModel.findOneAndUpdate(
                { _id: deletePayScheduleDto.id },
                {
                    $set: { deleted: true },
                },
                { new: true },
            );
            return new NoContentResponse({ message: "PaySchedule deleted successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getPaySchedules(userId: string, companyId: string) {
        try {
            const paySchedule = await this.payScheduleModel.find({ companyId, deleted: false }).exec();
            return new OkResponse({ paySchedule });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getPayScheduleById(userId: string, companyId: string, payScheduleId: string) {
        try {
            const paySchedule = await this.payScheduleModel
                .findOne({
                    companyId,
                    deleted: false,
                    _id: payScheduleId,
                })
                .exec();
            if (!paySchedule) throw new HttpException("PaySchedule does not exist", HttpStatus.BAD_REQUEST);
            return new OkResponse({ paySchedule });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async addDefaultPaySchedule(companyId: string, createdBy: string) {
        try {
            const data = defaultPaySchedule.map((value) => ({
                ...value,
                companyId,
                createdBy,
                deleted: false,
            }));
            await this.payScheduleModel.insertMany(data);
            return new CreatedResponse({
                message: "Default PaySchedule created successfully!",
            });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async payEffDates(paySchId: string, hireDate: Date) {
        try {
            const paySch = await this.payScheduleModel.findOne({ _id: paySchId });
            const payPeriod = paySch.period;
            const payEnd = paySch.payPeriodEndsOn;
            const payDay = paySch.paydayOn;
            const payEnd2 = paySch?.payPeriodEndsOn2;
            const payDay2 = paySch?.paydayOn2;

            const payDates = this.getPayPeriods2(payPeriod, payEnd, payDay, payEnd2, payDay2, hireDate);

            return new OkResponse({
                payDates,
            });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    //With pay period type string, end dates, and pay dates, find pay periods reaching back to when
    //the pay schedule was initiated(period End) reaching out 2 months from today
    getPayPeriods2(period, end1, payday1, end2, payday2, hireDate) {
        try {
            const sixMonth = sixMonthsAgo(new Date());

            const today = new Date();
            today.setMonth(today.getMonth() + 2);
            const payPeriods = [];
            // set end date to isodate and end of local day
            // end1 = new Date(end1.setUTCMinutes(23, 59, 99));
            end1 = new Date(end1);

            let increment;
            if (period === PeriodEnum.EveryWeek || period === PeriodEnum.EveryOtherWeek) {
                if (period == PeriodEnum.EveryWeek) {
                    increment = 7;
                } else if (period == PeriodEnum.EveryOtherWeek) {
                    increment = 14;
                }
                const start1 = new Date(end1);
                start1.setDate(start1.getDate() - (increment - 1));
                start1.setHours(0, 0, 0, 0);
                // let reportEnd = 0;
                let reportEnd = new Date(0); // Initialize reportEnd as a Date object
                let i = 0;
                while (reportEnd < today) {
                    const startDate = new Date(start1);
                    const endDate = new Date(end1);
                    const payDate = new Date(new Date(payday1).setHours(0, 0, 0, 0)); // + "T00:00";
                    const obj: any = {};
                    obj.periodStart = new Date(startDate.setDate(startDate.getDate() + increment * i));
                    obj.periodEnd = new Date(endDate.setDate(endDate.getDate() + increment * i));
                    obj.periodPayday = new Date(payDate.setDate(payDate.getDate() + increment * i));
                    // only adding dates that are not older than 6 months
                    if (obj.periodPayday > sixMonth && obj.periodPayday > hireDate) {
                        payPeriods.push(obj);
                    }
                    reportEnd = obj.periodEnd;
                    i++;
                }
            } else if (period === PeriodEnum.OncePerMonth) {
                const start1 = new Date(end1);
                start1.setDate(start1.getDate() + 1);
                // let reportEnd = 0;
                let reportEnd = new Date(0);
                let i = 0;
                while (reportEnd < today) {
                    const startMonth = end1.getMonth();
                    let subMonth = startMonth - 1;
                    const startDate = start1.getDate();
                    // if pay period ends on last day of current month, set endDate to 31 to choose each last day
                    let endDate;
                    if (daysInMonth(end1.getFullYear(), startMonth) === end1.getDate()) {
                        endDate = 31;
                    } else {
                        endDate = end1.getDate();
                    }
                    let payDate = Number(payday1.getDate());
                    let payMonth = startMonth;
                    if (payDate <= endDate && payMonth === startMonth) {
                        payMonth++;
                    }
                    const obj: any = {};
                    const thisMonthDays = daysInMonth(end1.getFullYear(), startMonth + i);
                    //on this loop, if there are less days in the month than the endDate, set to days in month
                    if (thisMonthDays < endDate) {
                        endDate = thisMonthDays;
                    }
                    //if
                    if (thisMonthDays === endDate) {
                        subMonth++;
                    }
                    // if there are less days in month than the payDate, set payDate to days in month
                    if (thisMonthDays < payDate) {
                        payDate = thisMonthDays;
                    }
                    obj.periodStart = new Date(end1.getFullYear(), subMonth + i, startDate, 0, 0, 0, 0);
                    obj.periodEnd = new Date(end1.getFullYear(), startMonth + i, endDate, 23, 59, 59, 999);
                    obj.periodPayday = new Date(end1.getFullYear(), payMonth + i, payDate, 0, 0, 0, 0);
                    // only adding dates that are not older than 6 months
                    if (obj.periodPayday > sixMonth && obj.periodPayday > hireDate) {
                        payPeriods.push(obj);
                    }
                    reportEnd = obj.periodEnd;
                    i++;
                }
            } else if (period === PeriodEnum.TwicePerMonth) {
                //FIRST GET DATES OF THE MONTH

                payday1 = Number(payday1.getDate());
                // end2 = new Date(end2.setHours(23, 59, 99, 999));
                end2 = new Date(end2);

                payday2 = Number(payday2.getDate());
                const payDate1 = payday1;
                const payDate2 = payday2;
                const endDate1 = end1.getDate();
                const endDate2 = end2.getDate();
                const startDate1 = new Date(end2);
                startDate1.setDate(startDate1.getDate() + 1);
                const startDate1_1 = startDate1.getDate();
                const startDate2 = new Date(end1);
                startDate2.setDate(startDate2.getDate() + 1);
                const startDate2_1 = startDate2.getDate();
                const startMonth = end1.getMonth(); // Jan
                const startYear = end1.getFullYear();
                // let reportEnd = 0;
                let reportEnd = new Date(0);

                let i = 0;
                while (reportEnd < today) {
                    const thisMonthDays = daysInMonth(startYear, startMonth + i);
                    const nextMonthDays = daysInMonth(startYear, startMonth + 1 + i);
                    //Create variables for the loop to prevent changing the presets
                    const s1 = startDate1_1;
                    let s2 = startDate2_1;
                    let e1 = endDate1;
                    let e2 = endDate2;
                    let p1 = payDate1;
                    let p2 = payDate2;
                    let s1Month = startMonth;
                    let e1Month = startMonth + 1;
                    let p1Month = startMonth + 2;
                    const obj: any = {};
                    obj.periodStart = new Date(startYear, s1Month + i, s1, 0, 0, 0, 0);
                    if (thisMonthDays < e1) {
                        e1 = thisMonthDays;
                    }
                    if (e1 > s1) {
                        e1Month--;
                        p1Month--;
                    }
                    obj.periodEnd = new Date(startYear, e1Month + i, e1, 23, 59, 59, 999);
                    if (thisMonthDays < p1) {
                        p1 = thisMonthDays;
                    }
                    //if paydate is greater than the endDate, subtract a month
                    if (payDate1 > endDate1) {
                        p1Month--;
                    }
                    obj.periodPayday = new Date(startYear, p1Month + i, p1, 0, 0, 0, 0);
                    // only adding dates that are not older than 6 months
                    if (obj.periodPayday > sixMonth && obj.periodPayday > hireDate) {
                        payPeriods.push(obj);
                    }
                    // if (startDate1 < startDate2) {
                    //     startMonth --;
                    // }
                    const obj2: any = {};
                    if (nextMonthDays < s2) {
                        s2 = nextMonthDays;
                    }
                    s1Month++;
                    if (s2 < e1) {
                        e1Month++;
                    }
                    obj2.periodStart = new Date(startYear, s1Month + i, s2, 0, 0, 0, 0);
                    if (nextMonthDays < e2) {
                        e2 = nextMonthDays;
                    }
                    obj2.periodEnd = new Date(startYear, e1Month + i, e2, 23, 59, 59, 999);
                    if (nextMonthDays < p2) {
                        p2 = nextMonthDays;
                    }
                    if (payDate2 < endDate2) {
                        p1Month++;
                    }
                    obj2.periodPayday = new Date(startYear, p1Month + i, p2, 0, 0, 0, 0);
                    // only adding dates that are not older than 6 months
                    if (obj.periodPayday > sixMonth && obj.periodPayday > hireDate) {
                        payPeriods.push(obj2);
                    }
                    reportEnd = obj2.periodEnd;

                    i++;
                }
            }

            return payPeriods.reverse();
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }
}
