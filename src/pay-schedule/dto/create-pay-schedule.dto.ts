import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import {
    IsString,
    IsNotEmpty,
    IsOptional,
    IsEnum,
    MaxLength,
    MinLength,
    IsUUID,
    IsNumber,
} from "class-validator";
import { PeriodEnum } from "../enum/period.enum";
import { Transform } from "class-transformer";

export class CreatePayScheduleDto {
    // @ApiProperty({ description: "Company", required: true })
    // @IsUUID()
    // @IsNotEmpty()
    // companyId: string;

    @ApiProperty({ description: "Name", required: true })
    @IsString()
    @MinLength(2)
    @MaxLength(30)
    @Transform(({ value }) => value.trim(), { toClassOnly: true })
    @IsNotEmpty()
    name: string;

    @ApiProperty({ description: "Period", required: true })
    @IsNumber()
    @IsNotEmpty()
    @IsEnum(PeriodEnum)
    period: PeriodEnum;

    @ApiProperty({ description: "First Pay Period", required: true })
    @IsNotEmpty()
    // @IsString()
    // @Transform(({ value }) => new Date(value))
    payPeriodEndsOn: any;

    @ApiProperty({ description: "First PayDay", required: true })
    // @Transform(({ value }) => new Date(value))
    @IsNotEmpty()
    paydayOn: any;

    @ApiProperty({ description: "Created By", required: true })
    @IsUUID()
    @IsNotEmpty()
    createdBy: string;

    @ApiPropertyOptional({ description: "Second Pay Period" })
    @IsOptional()
    // @IsString()
    // @Transform(({ value }) => new Date(value))
    payPeriodEndsOn2?: any;

    @ApiPropertyOptional({ description: "Second PayDay" })
    // @Transform(({ value }) => new Date(value))
    @IsOptional()
    paydayOn2?: any;
}
