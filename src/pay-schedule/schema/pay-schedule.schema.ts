import { Schema, SchemaFactory, Prop } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import mongoose, { Document } from "mongoose";
import { PeriodEnum } from "../enum/period.enum";
import { UUIDProp } from "src/shared/decorator/uuid.decorator";

export type PayScheduleDocument = PaySchedule & Document;

@Schema({ timestamps: true, id: false, collection: "PaySchedule" })
export class PaySchedule {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @UUIDProp()
    companyId: string;

    @Prop({ required: true })
    name: string;

    @Prop({ required: true, type: Number, enum: PeriodEnum })
    period: PeriodEnum;

    @Prop({ required: true, type: mongoose.Schema.Types.Mixed })
    payPeriodEndsOn: any;

    @Prop({ required: true, type: mongoose.Schema.Types.Mixed })
    paydayOn: any;

    @Prop({ required: false, type: mongoose.Schema.Types.Mixed })
    payPeriodEndsOn2?: any;

    @Prop({ required: false, type: mongoose.Schema.Types.Mixed })
    paydayOn2?: any;

    @UUIDProp()
    createdBy: string;

    @Prop({ default: false })
    deleted: boolean;

    @Prop()
    createdAt?: Date;

    @Prop()
    modifiedAt?: Date;
}

export const PayScheduleSchema = SchemaFactory.createForClass(PaySchedule);
