import { Module } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { SubcontractorSchema } from "./schema/subcontractor.schema";
import { SubcontractorsController } from "./subcontractor.controller";
import { SubcontractorService } from "./subcontractor.service";
import { CrewModule } from "src/crew/crew.module";
import { DailyLogSchema } from "src/daily-log/schema/daily-log.schema";
import { OrderSchema } from "src/project/schema/order.schema";
import { MemberSchema } from "src/company/schema/member.schema";

@Module({
    imports: [
        MongooseModule.forFeature([
            { name: "Subcontractor", schema: SubcontractorSchema },
            { name: "DailyLog", schema: DailyLogSchema },
            { name: "Order", schema: OrderSchema },
            { name: "Member", schema: MemberSchema },
        ]),
        CrewModule,
    ],
    providers: [SubcontractorService],
    controllers: [SubcontractorsController],
    exports: [SubcontractorService],
})
export class SubcontractorsModule {}
