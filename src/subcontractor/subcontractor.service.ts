import { BadRequestException, HttpException, Injectable, InternalServerErrorException } from "@nestjs/common";
import { InjectConnection, InjectModel } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import { Connection, Model } from "mongoose";
import CreatedResponse from "src/shared/http/response/created.http";
import OkResponse from "src/shared/http/response/ok.http";
import { RestoreSubcontractorDto } from "./dto/restore-subcontractor.dto";
import { RetireOrDeleteSubcontractorDto } from "./dto/retire-or-delete-subcontractor.dto";
import { UpsertSubcontractorDto } from "./dto/upsert-subcontractor.dto";
import { SubcontractorDocument } from "./schema/subcontractor.schema";
import { CrewService } from "src/crew/crew.service";
import { DailyLogDocument } from "src/daily-log/schema/daily-log.schema";
import { averageLayers, averagePitch } from "src/shared/helpers/logics";
import { OrderDocument } from "src/project/schema/order.schema";
import { GetSubcontractorDto } from "./dto/get-subcontractor.dto";
import { MemberDocument } from "src/company/schema/member.schema";

@Injectable()
export class SubcontractorService {
    constructor(
        private readonly crewService: CrewService,
        @InjectConnection() private readonly connection: Connection,
        @InjectModel("Subcontractor") private readonly subcontractorModel: Model<SubcontractorDocument>,
        @InjectModel("DailyLog") private readonly dailyLogModel: Model<DailyLogDocument>,
        @InjectModel("Order") private readonly orderModel: Model<OrderDocument>,
        @InjectModel("Member") private readonly memberModel: Model<MemberDocument>,
    ) {}

    async upsertSubcontractor(companyId: string, upsertSubcontractorDto: UpsertSubcontractorDto) {
        try {
            await this.subcontractorModel.findOneAndUpdate(
                {
                    _id: upsertSubcontractorDto._id ? upsertSubcontractorDto._id : randomUUID(),
                    companyId,
                    retired: false,
                    deleted: false,
                },
                {
                    $set: { companyId, ...upsertSubcontractorDto },
                },
                { upsert: true, new: true },
            );
            // .populate("companyId", "name", "Company");

            return new CreatedResponse({ message: "Subcontractor upserted successfully!" });
        } catch (error: any) {
            console.log(error);

            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async retireOrDeleteSubcontractor(
        companyId: string,
        retireOrDeleteSubcontractorDto: RetireOrDeleteSubcontractorDto,
    ) {
        try {
            await this.subcontractorModel.findOneAndUpdate(
                {
                    _id: retireOrDeleteSubcontractorDto.subcontractorId,
                    companyId,
                },
                {
                    $set: {
                        retired: retireOrDeleteSubcontractorDto.retired,
                        deleted: retireOrDeleteSubcontractorDto.deleted,
                    },
                },
                { new: true },
            );
            return new OkResponse({ message: "Subcontractor updated successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async restoreSubcontractor(companyId: string, restoreSubcontractorDto: RestoreSubcontractorDto) {
        try {
            await this.subcontractorModel.findOneAndUpdate(
                {
                    _id: restoreSubcontractorDto.subcontractorId,
                    companyId,
                },
                {
                    $set: {
                        retired: false,
                        deleted: false,
                    },
                },
                { new: true },
            );
            return new OkResponse({ message: "Subcontractor restored successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getCompanySubcontractors(companyId: string, getSubcontractorDto: GetSubcontractorDto) {
        try {
            const { search, active, retired, deleted } = getSubcontractorDto;

            const limit = getSubcontractorDto.limit || 10;
            const offset = limit * (getSubcontractorDto.skip || 0);
            const query = {
                companyId,
                deleted,
                retired,
                ...(typeof active === "boolean" && { isActive: active }),
            };

            if (search)
                query["$or"] = [
                    { name: { $regex: search, $options: "i" } },
                    { address: { $regex: search, $options: "i" } },
                    { city: { $regex: search, $options: "i" } },
                    { email: { $regex: search, $options: "i" } },
                    { phone: { $regex: search, $options: "i" } },
                    { state: { $regex: search, $options: "i" } },
                    { zip: { $regex: search, $options: "i" } },
                ];

            const subcontractor = await this.subcontractorModel
                .find(query)
                .skip(offset)
                .limit(limit)
                .select("createdAt isActive email mainContractorName name phone");
            return new OkResponse({ subcontractor });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getSubcontractorById(userId: string, companyId: string, subContractorId: string) {
        try {
            // const subcontractor = await this.subcontractorModel
            //     .findOne({ _id: subContractorId, companyId })
            //     .exec();
            // const members = await this.memberModel.find({ subContractorId });
            const [subcontractor, members] = await Promise.all([
                this.subcontractorModel.findOne({ _id: subContractorId, companyId }).exec(),
                this.memberModel.find({ subContractorId }),
            ]);

            const subContractorWithMembers = {
                ...subcontractor,
                memberIds: members.map((m) => ({
                    _id: m._id,
                    name: m.name,
                    email: m.email,
                    company: m.company,
                    user: m.user,
                })),
            };
            return new OkResponse({ subcontractor: subContractorWithMembers });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getSubcontractorByEmail(email: string) {
        try {
            return (
                await this.subcontractorModel.aggregate([
                    { $match: { email } },
                    {
                        $lookup: {
                            from: "Company",
                            localField: "companyId",
                            foreignField: "_id",
                            as: "company",
                            pipeline: [{ $project: { _id: 1, planType: 1, deleted: 1 } }],
                        },
                    },
                    {
                        $unwind: {
                            path: "$company",
                            preserveNullAndEmptyArrays: true,
                        },
                    },
                    {
                        $project: {
                            email: 1,
                            company: 1,
                        },
                    },
                ])
            )[0];
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getSubcontractorsToApprove(userId: string, companyId: string, start: Date, end: Date) {
        try {
            const approveStart = new Date(start);
            const approveEnd = new Date(end);

            const subs = await this.subcontractorModel.find({
                companyId,
                retired: { $ne: true },
                deleted: { $ne: true },
                isActive: true,
                // createdAt: { $lte: approveStart }, // not used in old time code
            });

            const updatedSubs: any[] = [];

            for (const sub of subs) {
                const data: any = sub;

                const dailyLog = await this.dailyLogModel.findOne({
                    crewId: sub._id,
                    date: { $gte: approveStart, $lt: approveEnd },
                    deleted: { $ne: true },
                });

                data.report = await this.subDayActivity(sub, dailyLog, companyId);

                data.report?.projects.map((proj) => {
                    proj.budget += proj.laborBurden;
                });

                updatedSubs.push(data);
            }

            return new OkResponse({ updatedSubs });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async subDayActivity(sub, dailyLog, companyId: string) {
        try {
            const subEarnings = {
                tearOffSQ: 0,
                addLayers: 0,
                instSheet: 0,
                rmvSheet: 0,
                tExtraFelt: 0,
                tCutRV: 0,
                tCutCan: 0,
                tVentPlug: 0,
                tBaffle: 0,
                roofingSQ: 0,
                install50Yr: 0,
                chimCF: 0,
                replaceSkylight: 0,
                eyebrows: 0,
                bayWindows: 0,
                addManHours: 0,
                addMaterials: 0,
                instFascia: 0,
                rmvFascia: 0,
                handLoadSQ: 0,
                highRoof: 0,
                nonBillHours: 0,
                nonBillPly: 0,
                percentDone: 0,
            };
            const dayStats: any = {
                _id: dailyLog?._id,
                vol: 0,
                rr: 0,
                budget: 0,
                laborBurden: 0,
                actual: 0,
                tearOffSq: 0,
                roofingSq: 0,
                projects: [],
                log: dailyLog,
                rrDollar: 0,
            };

            if (dailyLog?.projects.length) {
                for (const projectLog of dailyLog.projects) {
                    const order = await this.orderModel
                        .findOne({ oppId: projectLog.oppId, companyId, deleted: false })
                        .populate("projectId", null, "Project")
                        .populate("oppId", null, "Opportunity")
                        .exec();

                    const project: any = order?.projectId;
                    if (!order || !project) continue;

                    const stats = await this.crewService.projectDayCompleted(projectLog, companyId);

                    stats.log = projectLog;
                    dayStats.vol += stats.vol;
                    dayStats.rr += stats.rr;
                    dayStats.budget += stats.budget;
                    dayStats.budget += stats.laborBurden;
                    dayStats.tearOffSq += stats.tearOffDone || 0;
                    dayStats.roofingSq += stats.roofingDone || 0;
                    dayStats.projects.push(stats);

                    // Calculate Sub Earnings
                    const pitch = averagePitch(project) || 0;
                    const layers = averageLayers(project) || 1;
                    // Find tear off & roofing per SQ based on pitch & layers
                    const tPerSQ =
                        pitch >= 0 && pitch <= 7
                            ? sub.tearOff["4/12-7/12"]
                            : pitch > 7 && pitch <= 9
                            ? sub.tearOff["8/12-9/12"]
                            : pitch > 9 && pitch <= 11
                            ? sub.tearOff["10/12-11/12"]
                            : pitch > 11
                            ? sub.tearOff["12/12+"]
                            : 0;
                    const rPerSQ =
                        pitch >= 0 && pitch <= 7
                            ? sub.roofing["4/12-7/12"]
                            : pitch > 7 && pitch <= 9
                            ? sub.roofing["8/12-9/12"]
                            : pitch > 9 && pitch <= 11
                            ? sub.roofing["10/12-11/12"]
                            : pitch > 11
                            ? sub.roofing["12/12+"]
                            : 0;
                    // Tear Off
                    subEarnings.tearOffSQ += projectLog.tearOffSQ * tPerSQ;
                    subEarnings.addLayers += projectLog.tearOffSQ * (layers - 1) * sub.tearOff.addLayers;
                    subEarnings.instSheet +=
                        projectLog.instSheet * (sub.tearOff.installPlywood + sub.tearOff.removePlywood); // rmvSheet is not in use so install sheet is used for both
                    // subEarnings.rmvSheet += projectLog.rmvSheet * sub.tearOff.removePlywood;
                    subEarnings.tExtraFelt += projectLog.tExtraFelt * sub.tearOff.addFeltLayers;
                    subEarnings.tCutRV += projectLog.tCutRV * sub.tearOff.cutRidgeVent;
                    subEarnings.tCutCan += projectLog.tCutCan * sub.tearOff.cutCanVent;
                    subEarnings.tVentPlug += projectLog.tVentPlug * sub.tearOff.installVentPlug;
                    subEarnings.tBaffle += projectLog.tBaffle * sub.tearOff.installVentBaffles;
                    // Roofing
                    subEarnings.roofingSQ += projectLog.roofingSQ * rPerSQ;
                    subEarnings.install50Yr += projectLog.install50Yr * sub.roofing.installWindsor;
                    subEarnings.chimCF += projectLog.chimCF * sub.roofing.chimneyCounterFlashing;
                    subEarnings.replaceSkylight += projectLog.replaceSkylight * sub.roofing["R&RSkylight"];
                    subEarnings.eyebrows += projectLog.eyebrows * sub.roofing.eyebrow;
                    subEarnings.bayWindows += projectLog.bayWindows * sub.roofing.bayWindow;
                    // Extras
                    subEarnings.addManHours += projectLog.addManHours * sub.miscellaneous.extraWork;
                    subEarnings.instFascia += projectLog.instFascia * sub.miscellaneous.installFascia;
                    subEarnings.rmvFascia += projectLog.rmvFascia * sub.miscellaneous.removeFascia;
                    subEarnings.handLoadSQ += projectLog.handLoadSQ * sub.miscellaneous.handLoadRoof;
                    subEarnings.highRoof += projectLog.highRoof * sub.miscellaneous.stories;
                    subEarnings.nonBillHours += projectLog.nonBillHours * sub.miscellaneous.extraWork;
                    subEarnings.nonBillPly +=
                        projectLog.nonBillPly * (sub.tearOff.installPlywood + sub.tearOff.removePlywood);
                }
            }

            // Calculate Total
            for (const key in subEarnings) {
                dayStats.actual += subEarnings[key] || 0;
            }
            dayStats.rrDollar = dayStats.rr / dayStats.actual;
            dayStats.subEarnings = subEarnings;
            return dayStats;
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async removeMemberFromSubcontractor(companyId: string, subContractorId: string, memberId: string) {
        try {
            await this.memberModel.updateOne(
                { _id: memberId, companyId },
                { $unset: { subContractorId: "" } },
            );
            return new OkResponse({ message: "Member removed from Subcontractor successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) throw error;
            throw new InternalServerErrorException(error.message);
        }
    }
}
