import { <PERSON>hem<PERSON>, SchemaFactory, Prop } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import mongoose, { Document, Types } from "mongoose";
import { UUIDProp } from "src/shared/decorator/uuid.decorator";

export type SubcontractorDocument = Subcontractor & Document;

@Schema({ timestamps: true, id: false, collection: "Subcontractor" })
export class Subcontractor {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @UUIDProp()
    companyId: string;

    @Prop({ required: true })
    name: string;

    @Prop({ required: true })
    address: string;

    @Prop({ required: true })
    state: string;

    @Prop({ required: true })
    city: string;

    @Prop({ required: true })
    zip: string;

    @Prop({ default: false })
    agreementCompleted: boolean;

    @Prop({ default: false })
    isActive: boolean;

    @Prop({ required: true })
    mainContractorName: string;

    @Prop({ required: true })
    phone: string;

    @Prop({ required: true })
    email: string;

    @Prop({ default: false })
    retired: boolean;

    @Prop({ default: false })
    deleted: boolean;

    @Prop({ type: mongoose.Schema.Types.Mixed })
    tearOff: any;

    @Prop({ type: mongoose.Schema.Types.Mixed })
    roofing: any;

    @Prop({ type: mongoose.Schema.Types.Mixed })
    miscellaneous: any;

    @UUIDProp({ required: false })
    managerId?: string;

    // @Prop({ type: [String], ref: "Member" })
    // memberIds: string[];

    @Prop()
    createdAt?: Date;

    @Prop()
    modifiedAt?: Date;
}

export const SubcontractorSchema = SchemaFactory.createForClass(Subcontractor);
