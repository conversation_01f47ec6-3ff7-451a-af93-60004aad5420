import { Body, Controller, Delete, Get, Param, ParseUUIDPipe, Patch, Post, Query } from "@nestjs/common";
import {
    ApiBearerAuth,
    ApiConflictResponse,
    ApiInternalServerErrorResponse,
    ApiNotFoundResponse,
    ApiOperation,
    ApiTags,
    ApiUnauthorizedResponse,
} from "@nestjs/swagger";
import { Positions } from "src/auth/guards/auth.guard";
import { PermissionsEnum } from "src/shared/enum/permission.enum";
import HttpResponse from "src/shared/http/response/response.http";
import { RestoreSubcontractorDto } from "./dto/restore-subcontractor.dto";
import { RetireOrDeleteSubcontractorDto } from "./dto/retire-or-delete-subcontractor.dto";
import { UpsertSubcontractorDto } from "./dto/upsert-subcontractor.dto";
import { SubcontractorService } from "./subcontractor.service";
import { Auth, GetUser } from "src/auth/decorator/auth.decorator";
import { JwtUserPayload } from "src/auth/interface/auth.interface";
import { moduleNames } from "src/shared/constants/constant";
import { GetSubcontractorDto } from "./dto/get-subcontractor.dto";

@ApiTags("Subcontractors")
@ApiUnauthorizedResponse({ description: "Unauthorized request" })
@ApiInternalServerErrorResponse({ description: "Server Error" })
@Auth()
@ApiBearerAuth()
@Positions({
    category: "module",
    name: moduleNames.module.subcontractor,
    actions: [PermissionsEnum.Full],
})
@Controller({ path: "subcontractors", version: "1" })
export class SubcontractorsController {
    constructor(private readonly subcontractorService: SubcontractorService) {}

    @ApiOperation({ summary: "Upsert Subcontractor" })
    @ApiConflictResponse({ description: "Subcontractor already exist" })
    @Post("upsert-subcontractor")
    async upsertSubcontractor(
        @GetUser() user: JwtUserPayload,
        @Body() upsertSubcontractorDto: UpsertSubcontractorDto,
    ): Promise<HttpResponse> {
        return this.subcontractorService.upsertSubcontractor(user.companyId, upsertSubcontractorDto);
    }

    @ApiOperation({ summary: "RetireOrDelete Subcontractor" })
    @ApiNotFoundResponse({ description: "Subcontractor not found" })
    @Delete("retire-or-delete-subcontractor")
    async retireOrDeleteSubcontractor(
        @GetUser() user: JwtUserPayload,
        @Body() retireOrDeleteSubcontractorDto: RetireOrDeleteSubcontractorDto,
    ): Promise<HttpResponse> {
        return this.subcontractorService.retireOrDeleteSubcontractor(
            user.companyId,
            retireOrDeleteSubcontractorDto,
        );
    }

    @ApiOperation({ summary: "ReStore Subcontractor" })
    @ApiNotFoundResponse({ description: "Subcontractor not found" })
    @Patch("restore-subcontractor")
    async restoreSubcontractor(
        @GetUser() user: JwtUserPayload,
        @Body() restoreSubcontractorDto: RestoreSubcontractorDto,
    ): Promise<HttpResponse> {
        return this.subcontractorService.restoreSubcontractor(user.companyId, restoreSubcontractorDto);
    }

    @ApiOperation({ summary: "Get Company Subcontractors" })
    @Get("get-company-subcontractors")
    async getCrewMembers(
        @GetUser() user: JwtUserPayload,
        @Query() getSubcontractorDto: GetSubcontractorDto,
    ): Promise<HttpResponse> {
        return this.subcontractorService.getCompanySubcontractors(user.companyId, getSubcontractorDto);
    }

    @ApiOperation({ summary: "Get Subcontractor By id" })
    @Get("get-subcontractor-by-id/subcontractorId/:subcontractorId")
    async getCrewById(
        @GetUser() user: JwtUserPayload,
        @Param("subcontractorId", ParseUUIDPipe) subcontractorId: string,
    ): Promise<HttpResponse> {
        return this.subcontractorService.getSubcontractorById(user._id, user.companyId, subcontractorId);
    }

    @ApiOperation({ summary: "Get Subcontractor To Approve" })
    @Get("get-subcontractor-to-approve/date/:startDate/:endDate")
    async getSubcontractorsToApprove(
        @GetUser() user: JwtUserPayload,
        @Param("startDate") startDate: Date,
        @Param("endDate") endDate: Date,
    ): Promise<HttpResponse> {
        return this.subcontractorService.getSubcontractorsToApprove(
            user._id,
            user.companyId,
            startDate,
            endDate,
        );
    }

    @ApiOperation({ summary: "remove member from subcontractors" })
    @Delete("member/subcontractorId/:subcontractorId/memberId/:memberId")
    async removeMemberFromSubcontractor(
        @GetUser() user: JwtUserPayload,
        @Param("subcontractorId", ParseUUIDPipe) subcontractorId: string,
        @Param("memberId", ParseUUIDPipe) memberId: string,
    ): Promise<HttpResponse> {
        return this.subcontractorService.removeMemberFromSubcontractor(
            user.companyId,
            subcontractorId,
            memberId,
        );
    }
}
