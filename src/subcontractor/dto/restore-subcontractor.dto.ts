import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsUUID } from "class-validator";

export class RestoreSubcontractorDto {
    // @ApiProperty({ description: "Company Id", required: true })
    // @IsUUID()
    // @IsNotEmpty()
    // companyId: string;

    @ApiProperty({ description: "Subcontractor Id", required: true })
    @IsUUID()
    @IsNotEmpty()
    subcontractorId: string;
}
