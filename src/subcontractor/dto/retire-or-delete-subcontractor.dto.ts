import { ApiPropertyOptional } from "@nestjs/swagger";
import { IsBoolean, IsOptional } from "class-validator";
import { RestoreSubcontractorDto } from "./restore-subcontractor.dto";

export class RetireOrDeleteSubcontractorDto extends RestoreSubcontractorDto {
    @ApiPropertyOptional({ description: "Retired" })
    @IsBoolean()
    @IsOptional()
    retired: boolean;

    @ApiPropertyOptional({ description: "Deleted" })
    @IsBoolean()
    @IsOptional()
    deleted: boolean;
}
