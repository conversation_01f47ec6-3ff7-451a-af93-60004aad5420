import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsBoolean, IsOptional } from "class-validator";
import { PaginationRequestDto } from "src/company/dto/pagination-request.dto";

export class GetSubcontractorDto extends PaginationRequestDto {
    @ApiProperty({ description: "deleted", required: true })
    @IsBoolean()
    @Transform(({ value }) => value === "true" || value === true)
    deleted: boolean;

    @ApiProperty({ description: "retired", required: true })
    @IsBoolean()
    @Transform(({ value }) => value === "true" || value === true)
    retired: boolean;

    @ApiPropertyOptional({ description: "active", required: false })
    @Transform(({ value }) => value === "true" || value === true)
    @IsOptional()
    @IsBoolean()
    active: boolean;
}
