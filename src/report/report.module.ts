import { Modu<PERSON> } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { MemberSchema } from "src/company/schema/member.schema";
import { CrmCheckpointSchema } from "src/crm/schema/crm-checkpoint.schema";
import { CrmStageSchema } from "src/crm/schema/crm-stage.schema";
import { OpportunitySchema } from "src/opportunity/schema/opportunity.schema";
import { ReportController } from "./report.controller";
import { ReportService } from "./report.service";
import { CrewModule } from "src/crew/crew.module";
import { CrewSchema } from "src/crew/schema/crew-management.schema";
import { TimeCardSchema } from "src/time-card/schema/time-card.schema";
import { SubcontractorsModule } from "src/subcontractor/subcontractor.module";
import { SubcontractorSchema } from "src/subcontractor/schema/subcontractor.schema";
import { DailyLogSchema } from "src/daily-log/schema/daily-log.schema";
import { ProjectSchema } from "src/project/schema/project.schema";
import { OrderSchema } from "src/project/schema/order.schema";
import { CompanySettingSchema } from "src/company/schema/company-setting.schema";
import { ProjectTypeSchema } from "src/project/schema/project-type.schema";
import { PieceWorkSettingSchema } from "src/piece-work/schema/piece-work-setting.schema";
import { PieceWorkSchema } from "src/piece-work/schema/piece-work.schema";
import { PayScheduleSchema } from "src/pay-schedule/schema/pay-schedule.schema";
import { InputSchema } from "src/project/schema/input.schema";
import { TaskSchema } from "src/project/schema/task.schema";
import { WorkTaskSchema } from "src/work-task/schema/work-task.schema";
import { CompanyPaySchema } from "src/company/schema/company-pay.schema";
import { CompensationSchema } from "src/compensation/schema/compensation.schema";
import { CommissionModificationSchema } from "src/opportunity/schema/opp-commission.schema";
import { CustomProjectSchema } from "src/custom-project/schema/custom-project.schema";
import { DailyLogModule } from "src/daily-log/daily-log.module";
import { LeadSchema } from "src/lead/schema/lead.schema";
import { PositionModule } from "src/position/position.module";
import { ContactSchema } from "src/contacts/schema/contact.schema";
import { MarketingChannelSchema } from "src/marketing-setting/schema/channel.schema.dto";
import { LeadSourceSchema } from "src/marketing-setting/schema/lead-source.schema";
import { CampaignSchema } from "src/marketing-setting/schema/campaign.schema";

@Module({
    imports: [
        MongooseModule.forFeature([
            { name: "CrmStage", schema: CrmStageSchema },
            { name: "Opportunity", schema: OpportunitySchema },
            { name: "Member", schema: MemberSchema },
            { name: "CrmCheckpoint", schema: CrmCheckpointSchema },
            { name: "Crew", schema: CrewSchema },
            { name: "TimeCard", schema: TimeCardSchema },
            { name: "Subcontractor", schema: SubcontractorSchema },
            { name: "DailyLog", schema: DailyLogSchema },
            { name: "Project", schema: ProjectSchema },
            { name: "Order", schema: OrderSchema },
            { name: "CompanySetting", schema: CompanySettingSchema },
            { name: "ProjectType", schema: ProjectTypeSchema },
            { name: "PieceWorkSetting", schema: PieceWorkSettingSchema },
            { name: "PieceWork", schema: PieceWorkSchema },
            { name: "CompanyPay", schema: CompanyPaySchema },
            { name: "PaySchedule", schema: PayScheduleSchema },
            { name: "Task", schema: TaskSchema },
            { name: "Input", schema: InputSchema },
            { name: "WorkTask", schema: WorkTaskSchema },
            { name: "Compensation", schema: CompensationSchema },
            { name: "CommissionModification", schema: CommissionModificationSchema },
            { name: "CustomProject", schema: CustomProjectSchema },
            { name: "Lead", schema: LeadSchema },
            { name: "Contact", schema: ContactSchema },
            { name: "MarketingChannel", schema: MarketingChannelSchema },
            { name: "LeadSource", schema: LeadSourceSchema },
            { name: "Campaign", schema: CampaignSchema },
        ]),
        // PositionModule,
        // RoleModule,
        CrewModule,
        SubcontractorsModule,
        DailyLogModule,
        PositionModule,
    ],
    providers: [ReportService],
    controllers: [ReportController],
    exports: [ReportService],
})
export class ReportModule {}
