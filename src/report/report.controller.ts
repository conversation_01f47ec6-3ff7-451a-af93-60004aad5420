import { BadRequestException, Controller, Get, Param, ParseUUIDPipe, Query } from "@nestjs/common";
import {
    ApiBearerAuth,
    ApiInternalServerErrorResponse,
    ApiOperation,
    ApiTags,
    ApiUnauthorizedResponse,
} from "@nestjs/swagger";
import { Positions } from "src/auth/guards/auth.guard";
import { ReportService } from "./report.service";
import HttpResponse from "src/shared/http/response/response.http";
import { PermissionsEnum } from "src/shared/enum/permission.enum";
import { PositionService } from "src/position/position.service";
import { Auth, GetUser } from "src/auth/decorator/auth.decorator";
import { JwtUserPayload } from "src/auth/interface/auth.interface";
import { WeeklyProjectReportDto } from "./dto/weekly-project-report.dto";
import { moduleNames } from "src/shared/constants/constant";

@ApiTags("Report")
@Auth()
@ApiBearerAuth()
@ApiInternalServerErrorResponse({ description: "Server Error" })
@ApiUnauthorizedResponse({ description: "Unauthorized request" })
@Controller({ path: "report", version: "1" })
export class ReportController {
    constructor(
        private readonly reportService: ReportService,
        private readonly positionService: PositionService,
    ) {}

    @ApiOperation({ summary: "Get weekly sales report" })
    @Positions({
        category: "report",
        name: moduleNames.reports.weeklySales,
        actions: [PermissionsEnum.Full],
    })
    @Get("get-weekly-sales-report/:startDate/:endDate")
    async getWeeklySalesReport(
        @GetUser() user: JwtUserPayload,
        @Param("startDate") startDate: Date,
        @Param("endDate") endDate: Date,
    ) {
        return this.reportService.weeklySalesReport(user._id, user.companyId, startDate, endDate);
    }

    @ApiOperation({ summary: "Get sales person report" })
    @Positions({
        category: "report",
        name: moduleNames.reports.salesPerson,
        actions: [PermissionsEnum.Full, PermissionsEnum.Self],
    })
    @Get("get-sales-person-report/:startDate/:endDate")
    async getSalesPersonReport(
        @GetUser() user: JwtUserPayload,
        @Param("startDate") startDate: Date,
        @Param("endDate") endDate: Date,
        @Query("salesPersonId") salesPersonId: string,
    ) {
        if (user.teamPermission === PermissionsEnum.Self && salesPersonId !== user.memberId)
            throw new BadRequestException("Can Only view your own report");

        return this.reportService.getSalesPersonOrCustomReport(
            user._id,
            user.companyId,
            startDate,
            endDate,
            true,
            salesPersonId,
            false,
        );
    }

    @ApiOperation({ summary: "Get sales custom report" })
    @Positions({
        category: "report",
        name: moduleNames.reports.customSales,
        actions: [PermissionsEnum.Full, PermissionsEnum.Self],
    })
    @Get("get-custom-sales-person-report/:startDate/:endDate")
    async getCustomSalesPersonReport(
        @GetUser() user: JwtUserPayload,
        @Param("startDate") startDate: Date,
        @Param("endDate") endDate: Date,
        // @Query("isCSRReport") isCSRReport?: boolean,
        @Query("salesPersonId") salesPersonId?: string,
    ) {
        if (user.teamPermission === PermissionsEnum.Self && salesPersonId !== user.memberId)
            throw new BadRequestException("Can Only view your own report");

        return this.reportService.getSalesPersonOrCustomReport(
            user._id,
            user.companyId,
            startDate,
            endDate,
            false,
            salesPersonId,
            false,
        );
    }

    @ApiOperation({ summary: "Get CSR report" })
    @Positions({
        category: "report",
        name: moduleNames.reports.customSales,
        actions: [PermissionsEnum.Full, PermissionsEnum.Self],
    })
    @Get("get-csr-report/:startDate/:endDate")
    async getCSRReport(
        @GetUser() user: JwtUserPayload,
        @Param("startDate") startDate: Date,
        @Param("endDate") endDate: Date,
        // @Query("isCSRReport") isCSRReport?: boolean,
        @Query("salesPersonId") salesPersonId?: string,
    ) {
        if (user.teamPermission === PermissionsEnum.Self && salesPersonId !== user.memberId)
            throw new BadRequestException("Can Only view your own report");

        return this.reportService.getSalesPersonOrCustomReport(
            user._id,
            user.companyId,
            startDate,
            endDate,
            false,
            salesPersonId,
            true,
        );
    }

    @ApiOperation({ summary: "Get Marketing report" })
    @Positions({
        category: "report",
        name: moduleNames.reports.clientValue,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed, PermissionsEnum.Self],
    })
    @Get("marketing-report/:startDate/:endDate")
    async marketingReport(
        @GetUser() user: JwtUserPayload,
        @Param("startDate") startDate: Date,
        @Param("endDate") endDate: Date,
    ) {
        return this.reportService.marketingReportCopy(user.companyId, startDate, endDate, user);
    }

    @ApiOperation({ summary: "Get sales person conversion report" })
    @Positions({
        category: "report",
        name: moduleNames.reports.conversion,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed, PermissionsEnum.Self],
    })
    @Get("conversion-report/:startDate/:endDate")
    async conversionReport(
        @GetUser() user: JwtUserPayload,
        @Param("startDate") startDate: Date,
        @Param("endDate") endDate: Date,
        @Query("salesPersonId") salesPersonId: string,
    ) {
        //TODO need to update this logic for position check
        // const { response: positionCheck } = await this.positionService.checkPositionPermission(
        //     user._id,
        //     user.companyId,
        //     ["Owner", "GeneralManager", "SalesManager"],
        // );
        // if (!positionCheck) {
        //     throw new BadRequestException("You are unauthorized to perform this request");
        // }

        if (user.teamPermission === PermissionsEnum.Self && salesPersonId !== user.memberId)
            throw new BadRequestException("Can Only view your own report");

        return this.reportService.conversionReport(
            user._id,
            user.companyId,
            startDate,
            endDate,
            salesPersonId,
        );
    }

    @ApiOperation({ summary: "Get weekly production report" })
    @Positions({
        category: "report",
        name: moduleNames.reports.weeklyProduction,
        actions: [PermissionsEnum.Full],
    })
    @Get("get-weekly-production-report/:startDate/:endDate")
    async WeeklyProductionReport(
        @GetUser() user: JwtUserPayload,
        @Param("startDate") startDate: Date,
        @Param("endDate") endDate: Date,
    ) {
        return this.reportService.WeeklyProductionReport(user._id, user.companyId, startDate, endDate);
    }

    @ApiOperation({ summary: "Get KPI report" })
    @Positions({
        category: "report",
        name: moduleNames.reports.kpi,
        actions: [PermissionsEnum.Full],
    })
    @Get("get-kpi-report/:startDate/:endDate")
    async kpiReport(
        @GetUser() user: JwtUserPayload,
        @Param("startDate") startDate: Date,
        @Param("endDate") endDate: Date,
    ) {
        return this.reportService.kpiReport(user._id, user.companyId, startDate, endDate);
    }

    @ApiOperation({ summary: "Get production report" })
    @Auth()
    @ApiBearerAuth()
    @Positions({
        category: "report",
        name: moduleNames.reports.production,
        actions: [PermissionsEnum.Full],
    })
    @Get("get-production-report/:startDate/:endDate")
    async productionReport(
        @GetUser() user: JwtUserPayload,
        @Param("startDate") startDate: Date,
        @Param("endDate") endDate: Date,
    ) {
        return this.reportService.productionReport(user._id, user.companyId, startDate, endDate, user);
    }

    @ApiOperation({ summary: "Get project report" })
    @Positions({
        category: "report",
        name: moduleNames.reports.jobCost,
        actions: [PermissionsEnum.Full],
    })
    @Get("get-project-report/:oppId/:projectId")
    async projectReport(
        @GetUser() user: JwtUserPayload,
        @Param("oppId") oppId: string,
        @Param("projectId") projectId: string,
    ) {
        return this.reportService.projectReport(user._id, user.companyId, oppId);
    }

    @ApiOperation({ summary: "Get crew project report" })
    @Positions({
        category: "report",
        name: moduleNames.reports.crewJobCost,
        actions: [PermissionsEnum.Full],
    })
    @Get("crew-project-report/:oppId")
    async crewProjectReport(@GetUser() user: JwtUserPayload, @Param("oppId") oppId: string) {
        return this.reportService.crewProjectReport(user.companyId, oppId);
    }

    @ApiOperation({ summary: "Get crew scoreboard report" })
    @Positions({
        category: "report",
        name: moduleNames.reports.crewScoreboard,
        actions: [PermissionsEnum.Full],
    })
    @Get("get-crew-scoreboard-report/:startDate/:endDate")
    async crewScoreboard(
        @GetUser() user: JwtUserPayload,
        @Param("startDate") startDate: Date,
        @Param("endDate") endDate: Date,
    ) {
        return this.reportService.crewScoreboard(user._id, user.companyId, startDate, endDate);
    }

    @ApiOperation({ summary: "Get commission report" })
    @Positions({
        category: "report",
        name: moduleNames.reports.commission,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed, PermissionsEnum.Self],
    })
    @Get("get-commission-report/:startDate/:endDate/:monthStart")
    async commissionReport(
        @GetUser() user: JwtUserPayload,
        @Param("startDate") startDate: Date,
        @Param("endDate") endDate: Date,
        @Param("monthStart") monthStart: Date,
    ): Promise<HttpResponse> {
        return this.reportService.commissionReport(
            user._id,
            user.companyId,
            user.memberId,
            user.teamPermission,
            startDate,
            endDate,
            monthStart,
        );
    }

    // @ApiOperation({ summary: "Get payroll report" })
    // @Positions({
    //     name: "payroll reports",
    //     actions: [PermissionsEnum.Full],
    // })
    // @Get("get-payroll-report/:paySchId/:startDate/:endDate")
    // async teamOnPayrollReport(
    //     @GetUser() user: JwtUserPayload,
    //     @Param("paySchId", ParseUUIDPipe) paySchId: string,
    //     @Param("startDate") startDate: Date,
    //     @Param("endDate") endDate: Date,
    // ): Promise<HttpResponse> {
    //     return this.reportService.teamOnPayroll(user._id, user.companyId, paySchId, startDate, endDate);
    // }

    @ApiOperation({ summary: "Get Crew payroll report" })
    @Positions({
        category: "report",
        name: moduleNames.reports.crewPayroll,
        actions: [PermissionsEnum.Full],
    })
    @Get("crew-payroll-report/:startDate/:endDate")
    async crewPayrollReport(
        @GetUser() user: JwtUserPayload,
        @Param("startDate") startDate: Date,
        @Param("endDate") endDate: Date,
    ): Promise<HttpResponse> {
        return this.reportService.crewPayrollReport(user._id, user.companyId, startDate, endDate);
    }

    @ApiOperation({ summary: "Get Non Crew payroll report" })
    @Positions({
        category: "report",
        name: moduleNames.reports.nonCrewPayroll,
        actions: [PermissionsEnum.Full],
    })
    @Get("non-crew-payroll-report/:startDate/:endDate")
    async nonCrewPayrollReport(
        @GetUser() user: JwtUserPayload,
        @Param("startDate") startDate: Date,
        @Param("endDate") endDate: Date,
        @Query("salesPersonId") salesPersonId?: string,
    ): Promise<HttpResponse> {
        return this.reportService.nonCrewPayrollReport(
            user.memberId,
            user.companyId,
            startDate,
            endDate,
            user.teamPermission,
            salesPersonId,
        );
    }

    @ApiOperation({ summary: "Get payroll report" })
    @Positions({
        category: "report",
        name: moduleNames.reports.nonCrewPayroll,
        actions: [PermissionsEnum.Full],
    })
    @Get("payroll-report/:startDate/:endDate")
    async payrollReport(
        @GetUser() user: JwtUserPayload,
        @Param("startDate") startDate: Date,
        @Param("endDate") endDate: Date,
        @Query("salesPersonId") salesPersonId?: string,
    ): Promise<HttpResponse> {
        return this.reportService.payrollReport(
            user.memberId,
            user.companyId,
            startDate,
            endDate,
            user.teamPermission,
            salesPersonId,
        );
    }

    @ApiOperation({ summary: "Get weekly project report" })
    @Positions({
        category: "report",
        name: moduleNames.reports.weeklyProject,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed, PermissionsEnum.Self],
    })
    @Get("get-weekly-project-report/:startDate/:endDate")
    async weeklyProjectReport(
        @GetUser() user: JwtUserPayload,
        @Param("startDate") startDate: Date,
        @Param("endDate") endDate: Date,
        @Query() weeklyProjectReportDto: WeeklyProjectReportDto,
    ) {
        const { response: isRoofRepTechPerson } = await this.positionService.checkPositionPermission(
            user._id,
            user.companyId,
            ["RRTech", "Foreman"],
        );
        return this.reportService.weeklyProjectReport(
            user._id,
            user.companyId,
            startDate,
            endDate,
            weeklyProjectReportDto.crewIds,
        );
    }

    @ApiOperation({ summary: "Get client value report" })
    @Positions({
        category: "report",
        name: moduleNames.reports.clientValue,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed, PermissionsEnum.Self],
    })
    @Get("client-value-report/:startDate/:endDate")
    async clientValueReport(
        @GetUser() user: JwtUserPayload,
        @Param("startDate") startDate: Date,
        @Param("endDate") endDate: Date,
    ) {
        return this.reportService.clientValueReport(user.companyId, startDate, endDate);
    }
}
