import { Modu<PERSON> } from "@nestjs/common";
import { UserService } from "./user.service";
import { UserController } from "./user.controller";
import { MongooseModule } from "@nestjs/mongoose";
import { UserSchema } from "./schema/user.schema";
import { JwtService } from "@nestjs/jwt";
import { CompensationSchema } from "src/compensation/schema/compensation.schema";
import { PositionSchema } from "src/position/schema/position.schema";
import { PositionService } from "src/position/position.service";
import { MemberSchema } from "src/company/schema/member.schema";
import { PositionModule } from "src/position/position.module";

@Module({
    imports: [
        MongooseModule.forFeature([
            { name: "User", schema: UserSchema },
            { name: "Position", schema: PositionSchema },
            { name: "Compensation", schema: CompensationSchema },
            { name: "Member", schema: MemberSchema },
        ]),
        // PositionModule
    ],
    providers: [UserService],
    controllers: [UserController],
    exports: [UserService],
})
export class UserModule {}
