import {
    Injectable,
    BadRequestException,
    HttpException,
    HttpStatus,
    InternalServerErrorException,
    ConflictException,
} from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";
import { User, UserDocument } from "./schema/user.schema";
import { CreateUserDto } from "./dto/create-user.dto";
import OkResponse from "src/shared/http/response/ok.http";
import HttpResponse from "src/shared/http/response/response.http";
import { UpdateUserDto } from "./dto/update-user.dto";
import { UpdatePasswordDto } from "./dto/update-password.dto";
import * as crypto from "crypto";
import { argon2hash, argon2verify } from "src/auth/agron2/argon2";

@Injectable()
export class UserService {
    constructor(@InjectModel("User") private readonly userModel: Model<UserDocument>) {}

    async getUserByUsernameOrEmailOrToken(emailOrUsernameOrToken: string) {
        const value: any[] = [
            { email: emailOrUsernameOrToken },
            { passwordResetToken: emailOrUsernameOrToken },
        ];
        const user = await this.userModel.findOne({ $or: value }).exec();
        return user;
    }

    async getUserById(emailOrUsernameOrId: string) {
        return await this.userModel.findOne({ _id: emailOrUsernameOrId }).exec();
    }

    async getUserByMemberId(memberId: string) {
        return await this.userModel.findOne({ members: memberId }).exec();
    }

    async createUser(createUserDto: CreateUserDto) {
        const { password, confirmPassword, firstName, lastName } = createUserDto;
        try {
            if (password !== confirmPassword) throw new BadRequestException("Passwords don't match");
            const pass = await argon2hash(password);
            const createdUser = new this.userModel({
                ...createUserDto,
                password: pass,
            });
            await createdUser.save();
            return {
                user: {
                    _id: createdUser._id,
                    email: createdUser.email,
                },
            };
        } catch (error: any) {
            if (error.code === 11000) {
                throw new ConflictException("User already exists");
            } else if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getUserByEmail(email: string) {
        return this.userModel.findOne({ email });
    }

    /**
     * Gets a user by ID.
     *
     * @param id The ID of the user to retrieve.
     *
     * @throws {HttpException} If the user does not exist.
     * @throws {InternalServerErrorException} If there was an error retrieving the user.
     *
     * @returns A response with the user's information.
     */
    async getUser(id: string) {
        try {
            const user = await this.userModel.findOne({ _id: id }).exec();
            if (!user) throw new HttpException("User does not exist", HttpStatus.BAD_REQUEST);
            const userResponse = {
                id,
                firstName: user.firstName,
                lastName: user?.lastName,
                email: user.email,
                preferredName: user.preferredName ? user.preferredName : "",
                member: user.members,
                phone: user.phone,
                mobileAccess: user.mobileAccess,
                DOB: user?.DOB,
                imageUrl: user?.imageUrl,
            };
            return new OkResponse({ user: userResponse });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updateProfile(updateUserDto: UpdateUserDto) {
        try {
            const {
                updateUser,
                firstName,
                lastName,
                preferredName,
                phone,
                email,
                mobileAccess,
                DOB,
                imageUrl,
            } = updateUserDto;
            const user = await this.userModel.findOne({ _id: updateUser }).exec();
            if (!user) throw new HttpException("User does not exist", HttpStatus.BAD_REQUEST);

            await this.updateUserById(updateUser, user.email, {
                firstName,
                lastName,
                preferredName,
                phone,
                email,
                mobileAccess,
                DOB,
                imageUrl,
            });
            return new OkResponse({ message: "Profile Updated Successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updatePassword(id: string, updatePasswordDto: UpdatePasswordDto) {
        try {
            const { oldPassword, newPassword, confirmNewPassword } = updatePasswordDto;
            if (newPassword !== confirmNewPassword) throw new BadRequestException("Passwords don't match");
            if (oldPassword === newPassword)
                throw new BadRequestException("Old password and new password can't be same");
            const user = await this.userModel.findOne({ _id: id }).exec();
            if (!user) throw new HttpException("User does not exist", HttpStatus.BAD_REQUEST);
            if (!(await argon2verify(user.password, oldPassword))) {
                throw new BadRequestException("Invalid password");
            }
            await this.updateUserById(id, user.email, { password: newPassword });
            return new OkResponse({ message: "Password Updated Successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updateUserById(id: string, email: string, updateUserDto: UpdateUserDto): Promise<HttpResponse> {
        try {
            if (updateUserDto.password) updateUserDto.password = await argon2hash(updateUserDto.password);
            await this.userModel.updateOne({ email }, { $set: updateUserDto });
            return new OkResponse();
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async createPasswordResetToken(user: User): Promise<string> {
        const resetToken: string = crypto.randomBytes(32).toString("hex");
        user.passwordResetToken = crypto.createHash("sha3-256").update(resetToken).digest("hex");
        const timestamp = Date.now() + 10 * 60 * 1000; // NOTE: 10 mins to reset password
        user.passwordResetExpires = timestamp.toString();
        await this.userModel.updateOne({ email: user.email }, { $set: user });
        return resetToken;
    }
}
