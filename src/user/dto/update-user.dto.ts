import { ApiProperty, PartialType } from "@nestjs/swagger";
import { IsString, IsOptional, IsBoolean, IsDate } from "class-validator";
import { CreateUserDto } from "./create-user.dto";

export class UpdateUserDto extends PartialType(CreateUserDto) {
    @ApiProperty({ description: "id of user to be updated" })
    @IsOptional()
    @IsString()
    updateUser?: string;

    @ApiProperty({ description: "Password Reset Token" })
    @IsString()
    @IsOptional()
    passwordResetToken?: string;

    @ApiProperty({ description: "Password Reset Expires" })
    @IsString()
    @IsOptional()
    passwordResetExpires?: string;

    @ApiProperty({ description: "Allow mobile access" })
    @IsOptional()
    @IsBoolean()
    mobileAccess?: boolean;

    @ApiProperty({ description: "Date of birth" })
    @IsOptional()
    @IsString()
    DOB?: string;
}
