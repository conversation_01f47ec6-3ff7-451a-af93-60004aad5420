import { ApiProperty } from "@nestjs/swagger";
import { IsString, Matches, IsOptional } from "class-validator";
import { UserDto } from "src/shared/dto/user.dto";

export class CreateUserDto extends UserDto {
    @ApiProperty({ description: "Phone", required: true })
    @IsString()
    // @Matches(/^(\([0-9]{3}\) |[0-9]{3}-)[0-9]{3}-[0-9]{4}$/, {
    //     message: "Only US Phone numbers allowed",
    // })
    @IsOptional()
    phone?: string;

    customerId?: string;
}
