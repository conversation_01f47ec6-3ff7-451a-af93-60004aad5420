import { Schema, SchemaFactory, Prop } from "@nestjs/mongoose";
import { Document } from "mongoose";
import { Exclude } from "class-transformer";
import { randomUUID } from "crypto";
export type UserDocument = User & Document;

@Schema({ timestamps: true, id: false, collection: "User", versionKey: false })
export class User {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @Prop({ required: true })
    firstName: string;

    @Prop({ required: false })
    lastName: string;

    @Prop({ required: false })
    preferredName?: string;

    @Prop({ required: false })
    imageUrl?: string;

    @Prop({ required: true, unique: true })
    email: string;

    @Exclude({ toPlainOnly: true })
    // @Prop({ required: true, select: false })
    @Prop({ required: true })
    password: string;

    @Prop({ required: false })
    DOB?: string;

    @Prop({ default: null })
    passwordResetToken: string;

    @Prop({ default: null })
    passwordResetExpires: string;

    @Prop({ default: null })
    phone: string;

    @Prop()
    members?: [string];

    @Prop({ default: true })
    mobileAccess?: boolean;

    @Prop({ unique: true })
    stripeCustomerId: string;

    @Prop()
    createdAt?: Date;

    @Prop()
    modifiedAt?: Date;
}

export const UserSchema = SchemaFactory.createForClass(User);
