import { Schema, SchemaFactory, Prop } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import { Document } from "mongoose";
import { UserRolesEnum } from "src/company/enum/role.enum";
import { UUIDProp } from "src/shared/decorator/uuid.decorator";

export type RoleDocument = Role & Document;

@Schema({ timestamps: true, id: false, collection: "Role" })
export class Role {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @UUIDProp()
    companyId: string;

    @Prop({ required: true })
    memberId: string;

    @Prop({ required: true, type: Number, enum: UserRolesEnum })
    role: UserRolesEnum;

    @UUIDProp()
    createdBy: string;

    @Prop()
    createdAt?: Date;

    @Prop()
    modifiedAt?: Date;
}

export const RoleSchema = SchemaFactory.createForClass(Role);
