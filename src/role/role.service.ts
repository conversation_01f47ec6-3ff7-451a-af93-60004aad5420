import { HttpException, HttpStatus, Injectable, InternalServerErrorException } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";
import { UserRolesEnum } from "src/company/enum/role.enum";
import { MemberDocument } from "src/company/schema/member.schema";
import CreatedResponse from "src/shared/http/response/created.http";
import OkResponse from "src/shared/http/response/ok.http";
import { UpdateMemberRoleDto } from "./dto/update-member-role.dto";
import { RoleDocument } from "./schema/role.schema";
import { UserDocument } from "src/user/schema/user.schema";
import { CompanyDocument } from "src/company/schema/company.schema";
import { MailService } from "src/mail/mail.service";

@Injectable()
export class RoleService {
    constructor(
        private readonly mailService: MailService,
        @InjectModel("Member") private readonly memberModel: Model<MemberDocument>,
        @InjectModel("Role") private readonly roleModel: Model<RoleDocument>,
        @InjectModel("Company") private readonly companyModel: Model<CompanyDocument>,
        @InjectModel("User") private readonly userModel: Model<UserDocument>,
    ) {}

    async updateMemberRole(
        userId: string,
        companyId: string,
        logInMember: string,
        updateMemberRoleDto: UpdateMemberRoleDto,
    ) {
        const { memberId, role, currentMember } = updateMemberRoleDto;

        try {
            const member = await this.memberModel
                .findOne({
                    _id: memberId,
                    company: companyId,
                    deleted: false,
                })
                .exec();

            if (!member) {
                throw new HttpException("Member does not exist", HttpStatus.BAD_REQUEST);
            }
            const { email: reciverEmail, name: reciverName } = member;

            const company = await this.companyModel
                .findOne({
                    _id: companyId,
                    deleted: false,
                })
                .exec();

            const user = await this.userModel
                .findOne({
                    _id: userId,
                    deleted: false,
                })
                .exec();

            const { firstName: senderName, email } = user;

            const { companyName } = company;

            // Check if the role is already assigned to avoid unnecessary updates
            const existingRole = await this.roleModel.findOne({ companyId, memberId }).exec();
            if (existingRole?.role === role) {
                return new CreatedResponse({ message: "No changes made as the role is already assigned." });
            }

            if (role === UserRolesEnum.Owner) {
                const adminRoles = await this.roleModel
                    .find({
                        companyId: companyId,
                        role: 2,
                        deleted: false,
                    })
                    .exec();

                const adminMemberIds = adminRoles.map((role) => role.memberId);

                const oldMembers = await this.memberModel
                    .find({
                        _id: { $in: adminMemberIds },
                        company: companyId,
                        deleted: false,
                    })
                    .exec();

                for (const oldMember of oldMembers) {
                    const oldMemberName = oldMember.name?.trim() || null;
                    const oldMemberEmail = oldMember.email?.trim() || null;

                    if (oldMemberEmail) {
                        await this.mailService.sendMailToAllOldOwner(
                            oldMemberEmail,
                            companyName,
                            oldMemberName,
                            reciverName.trim(),
                        );
                    }
                }

                // Fetch details of the current member
                let currentMemberDetails = null;
                if (currentMember) {
                    currentMemberDetails = await this.memberModel
                        .findOne({
                            _id: currentMember,
                            company: companyId,
                            deleted: false,
                        })
                        .exec();
                }

                const currentMemberName = currentMemberDetails?.name?.trim() || null;

                // Send email to the new owner from the current owner
                await this.mailService.sendMailToNewOwner(
                    reciverEmail,
                    companyName,
                    currentMemberName, // Current owner's name
                    reciverName.trim(),
                );

                await this.mailService.sendMailToOldOwner(
                    currentMemberDetails?.email,
                    companyName,
                    currentMemberDetails?.name.trim(),
                    reciverName.trim(),
                );
            } else if (role === UserRolesEnum.Admin) {
                await this.mailService.sendMailToOwnerForAddingNewAdmin(
                    email,
                    companyName,
                    senderName.trim(),
                    reciverName.trim(),
                );

                await this.mailService.sendMailToAddNewOwner(
                    reciverEmail,
                    companyName,
                    senderName.trim(),
                    reciverName.trim(),
                );
            } else if (role === UserRolesEnum.Member) {
                await this.mailService.sendMailToChangeRoleToAdmin(
                    email,
                    companyName,
                    senderName.trim(),
                    reciverName.trim(),
                );

                await this.mailService.sendMailToChangeRole(
                    reciverEmail,
                    companyName,
                    senderName.trim(),
                    reciverName.trim(),
                );
            }

            const updateOperations = [];

            if (role === UserRolesEnum.Owner) {
                // Update new owner
                updateOperations.push(
                    this.roleModel.updateOne(
                        { companyId, memberId },
                        {
                            $set: { role, createdBy: logInMember },
                        },
                    ),
                );

                // Downgrade current owner to Member
                updateOperations.push(
                    this.roleModel.updateOne(
                        { companyId, memberId: currentMember },
                        {
                            $set: { role: UserRolesEnum.Member, createdBy: logInMember },
                        },
                    ),
                );
            } else {
                // Update the member's role
                updateOperations.push(
                    this.roleModel.updateOne(
                        { companyId, memberId },
                        {
                            $set: { role, createdBy: logInMember },
                        },
                    ),
                );
            }

            // Execute all update operations in parallel
            const results = await Promise.all(updateOperations);

            // Ensure at least one update occurred
            const updatesMade = results.some((result) => result.modifiedCount > 0);
            if (!updatesMade) {
                throw new HttpException(
                    "No updates were made. Please check the data provided.",
                    HttpStatus.BAD_REQUEST,
                );
            }

            return new CreatedResponse({ message: "Member role updated successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async createMemberRole(companyId: string, updateMemberRoleDto: UpdateMemberRoleDto) {
        try {
            const { memberId, role, createdBy, _id } = updateMemberRoleDto;
            const member = await this.memberModel
                .findOne({
                    _id: memberId,
                    company: companyId,
                    deleted: false,
                })
                .exec();
            if (!member) throw new HttpException("Member does not exists", HttpStatus.BAD_REQUEST);
            const createdRole = new this.roleModel({
                _id,
                role,
                companyId,
                memberId,
                createdBy,
            });
            await createdRole.save();
            return new CreatedResponse({ message: "Member Role inserted successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async checkRole(userId: string, companyId: string, requiredRoles: any) {
        try {
            const memberData = await this.memberModel.findOne({ company: companyId, user: userId }).exec();
            const memberRole = memberData
                ? await this.roleModel.findOne({ _id: memberData.roleId }).exec()
                : undefined;
            return requiredRoles.includes(memberRole.role);
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getMemberRole(userId: string, companyId: string, memberId: string) {
        try {
            const memberData = await this.memberModel.findOne({ company: companyId, _id: memberId }).exec();
            const memberRole = memberData
                ? await this.roleModel.findOne({ _id: memberData.roleId }).exec()
                : undefined;
            return new OkResponse({ role: memberRole });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }
}
