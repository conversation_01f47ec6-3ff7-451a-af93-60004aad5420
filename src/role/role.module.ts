import { Module, forwardRef } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { MemberSchema } from "src/company/schema/member.schema";
import { PositionModule } from "src/position/position.module";
import { RoleController } from "./role.controller";
import { RoleService } from "./role.service";
import { RoleSchema } from "./schema/role.schema";
import { CompanyModule } from "src/company/company.module";
import { CompanySchema } from "src/company/schema/company.schema";
import { MailModule } from "src/mail/mail.module";
import { UserModule } from "src/user/user.module";
import { UserSchema } from "src/user/schema/user.schema";

@Module({
    imports: [
        MongooseModule.forFeature([
            { name: "Role", schema: RoleSchema },
            { name: "Member", schema: MemberSchema },
            { name: "Company", schema: CompanySchema },
            { name: "User", schema: UserSchema },
           
        ]),
        MailModule,
        UserModule,
        // PositionModule,
        // forwardRef(() => CompanyModule),
    ],
    providers: [RoleService],
    controllers: [RoleController],
    exports: [RoleService],
})
export class RoleModule {}
