import { Body, Controller, Get, Param, ParseUUI<PERSON>ipe, Patch, Post, Put, UseGuards } from "@nestjs/common";
import {
    ApiInternalServerErrorResponse,
    ApiNotFoundResponse,
    ApiOperation,
    ApiTags,
    ApiUnauthorizedResponse,
} from "@nestjs/swagger";
import { Roles } from "src/auth/guards/auth.guard";
import { UserRolesEnum } from "src/company/enum/role.enum";
import HttpResponse from "src/shared/http/response/response.http";
import { UpdateMemberRoleDto } from "./dto/update-member-role.dto";
import { RoleService } from "./role.service";
import { Auth, GetUser } from "src/auth/decorator/auth.decorator";
import { JwtUserPayload } from "src/auth/interface/auth.interface";

@ApiTags("Role")
@Auth()
@ApiUnauthorizedResponse({ description: "Unauthorized request" })
@ApiInternalServerErrorResponse({ description: "Server Error" })
@Controller({ path: "role", version: "1" })
export class RoleController {
    constructor(private readonly roleService: RoleService) {}

    @ApiOperation({ summary: "Update Member Role" })
    @ApiNotFoundResponse({ description: "Role not found" })
    @Roles(UserRolesEnum.Owner)
    @Patch("update-member-role")
    async updateMemberRole(
        @GetUser() user: JwtUserPayload,
        @Body() updateMemberRoleDto: UpdateMemberRoleDto,
    ): Promise<HttpResponse> {
        return this.roleService.updateMemberRole(
            user._id,
            user.companyId,
            user.memberId,
            updateMemberRoleDto,
        );
    }

    @ApiOperation({ summary: "Get Member Role" })
    @Get("get-member-role/member/:memberId")
    async getMemberRole(
        @GetUser() user: JwtUserPayload,
        @Param("memberId", ParseUUIDPipe) memberId: string,
    ): Promise<HttpResponse> {
        return this.roleService.getMemberRole(user._id, user.companyId, memberId);
    }
}
