import { Body, Controller, Delete, Get, Param, Post, Put, Query } from "@nestjs/common";
import { ApiBearerAuth, ApiOperation, ApiTags } from "@nestjs/swagger";
import { Auth, GetUser } from "src/auth/decorator/auth.decorator";
import { JwtUserPayload } from "src/auth/interface/auth.interface";
import { ActionService } from "./action.service";
import { CreateNewActionDto } from "./dto/create-new-action.dto";

@ApiTags("Actions")
@ApiBearerAuth()
@Auth()
@Controller({ path: "actions", version: "1" })
export class ActionsController {
    constructor(private readonly actionService: ActionService) {}

    @Post("/contactOrOpp/:isContact/:oppOrContactId")
    @ApiOperation({ summary: "Create new action for contact/opportunity" })
    async createNewAction(
        @Param("isContact") isContact: boolean,
        @Param("oppOrContactId") oppOrContactId: string,
        @Body() createNewActionDto: CreateNewActionDto,
        @GetUser() user: JwtUserPayload,
    ) {
        return this.actionService.createNewAction(
            user.companyId,
            createNewActionDto,
            isContact,
            oppOrContactId,
        );
    }

    @Get("actionsWithHistory/:isContact/:oppOrContactId")
    @ApiOperation({ summary: "Get all actions from opp/contact" })
    async getActionsWithHistory(
        @Param("isContact") isContact: boolean,
        @Param("oppOrContactId") oppOrContactId: string,
        @GetUser() user: JwtUserPayload,
    ) {
        return this.actionService.getActionsWithHistory(user.companyId, isContact, oppOrContactId);
    }

    @Put("/complete/:isContact/:oppOrContactId")
    @ApiOperation({ summary: "Complete action for contact/opportunity" })
    async completeAction(
        @Param("isContact") isContact: boolean,
        @Param("oppOrContactId") oppOrContactId: string,
        @Body() createNewActionDto: CreateNewActionDto,
        @GetUser() user: JwtUserPayload,
    ) {
        return this.actionService.completeAction(
            user.companyId,
            user.memberId,
            oppOrContactId,
            createNewActionDto,
            isContact,
        );
    }
}
