import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { ContactSchema } from "../contacts/schema/contact.schema";
import { OpportunitySchema } from "src/opportunity/schema/opportunity.schema";
import { ContactsController } from "src/contacts/contacts.controller";
import { ContactsService } from "src/contacts/contacts.service";
import { ActionsController } from "./action.controller";
import { ActionService } from "./action.service";

@Module({
    imports: [
        MongooseModule.forFeature([
            { name: "Contact", schema: ContactSchema },
            { name: "Opportunity", schema: OpportunitySchema },
        ]),
    ],
    controllers: [ActionsController],
    providers: [ActionService],
    exports: [ActionService],
})
export class ActionModule {}
