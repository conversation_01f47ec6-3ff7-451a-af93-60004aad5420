import {
    BadRequestException,
    HttpException,
    HttpStatus,
    Injectable,
    InternalServerErrorException,
    NotFoundException,
} from "@nestjs/common";
import { InjectConnection, InjectModel } from "@nestjs/mongoose";
import { Connection, Model } from "mongoose";
import { OpportunityDocument } from "src/opportunity/schema/opportunity.schema";
import { ContactDocument } from "src/contacts/schema/contact.schema";
import CreatedResponse from "src/shared/http/response/created.http";
import { CreateNewActionDto } from "./dto/create-new-action.dto";
import OkResponse from "src/shared/http/response/ok.http";

@Injectable()
export class ActionService {
    constructor(
        @InjectConnection() private readonly connection: Connection,
        @InjectModel("Opportunity") private readonly opportunityModel: Model<OpportunityDocument>,
        @InjectModel("Contact") private readonly contactModel: Model<ContactDocument>,
    ) {}

    async createNewAction(
        companyId: string,
        createNewActionDto: CreateNewActionDto,
        isContact: boolean,
        oppOrContactId: string,
    ) {
        try {
            const { id, type, body, dueDate, assignTo, memberId, currDate } = createNewActionDto;

            const model = isContact ? this.contactModel : this.opportunityModel;

            const updateResult = await model.updateOne(
                { _id: oppOrContactId, companyId },
                {
                    $set: {
                        nextAction: {
                            _id: id,
                            type,
                            body,
                            due: dueDate,
                            assignTo,
                            createdBy: memberId,
                            createdAt: currDate,
                        },
                    },
                },
            );

            if (updateResult.modifiedCount === 0) {
                throw new HttpException("Failed to update changes!", HttpStatus.BAD_REQUEST);
            }

            return new CreatedResponse({ message: "Action created successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getActionsWithHistory(companyId: string, isContact: boolean, oppOrContactId: string) {
        try {
            if (isContact) {
                const [contactData, oppData] = await Promise.all([
                    this.contactModel.findOne(
                        { _id: oppOrContactId, companyId },
                        { actions: 1, nextAction: 1 },
                    ),
                    this.opportunityModel
                        .find(
                            { contactId: oppOrContactId, companyId },
                            { actions: 1, nextAction: 1, PO: 1, num: 1, _id: 1, stage: 1 },
                        )
                        .populate({
                            path: "stage",
                            model: "CrmStage",
                            select: "stageGroup",
                        })
                        .lean(),
                ]);
                const oppList = oppData as any[];

                const oppActions = oppList.flatMap((opp: any) =>
                    (opp.actions || []).map((action: any) => ({
                        ...action,
                        PO: opp.PO,
                        num: opp.num,
                        oppId: opp._id,
                        stageGroup: opp?.stage?.stageGroup,
                    })),
                );
                const oppNextActions = oppList
                    .filter((opp: any) => opp?.nextAction)
                    .map((opp: any) => ({
                        ...opp.nextAction,
                        PO: opp.PO,
                        num: opp.num,
                        oppId: opp._id,
                        stageGroup: opp.stage?.stageGroup,
                    }));

                const contactActions = contactData?.actions || [];

                const actions = [
                    ...contactActions.map((action) => ({ ...action, contactId: oppOrContactId })),
                    ...oppActions,
                ];
                actions.sort((a, b) => {
                    const dateA = a.completedAt ? new Date(a.completedAt).getTime() : -Infinity;
                    const dateB = b.completedAt ? new Date(b.completedAt).getTime() : -Infinity;
                    return dateB - dateA;
                });
                const nextAction = [
                    ...(contactData?.nextAction
                        ? [{ ...contactData.nextAction, contactId: oppOrContactId }]
                        : []),
                    ...oppNextActions,
                ];
                return new CreatedResponse({ history: actions, nextAction });
            } else {
                const oppAggResult = await this.opportunityModel.aggregate([
                    {
                        $match: {
                            _id: oppOrContactId,
                        },
                    },
                    {
                        $lookup: {
                            from: "Contact",
                            foreignField: "_id",
                            localField: "contactId",
                            as: "contact",
                        },
                    },
                    {
                        $lookup: {
                            from: "CrmStage",
                            foreignField: "_id",
                            localField: "stage",
                            as: "stage",
                            pipeline: [
                                {
                                    $project: {
                                        stageGroup: 1,
                                    },
                                },
                            ],
                        },
                    },
                    {
                        $unwind: {
                            path: "$stage",
                            preserveNullAndEmptyArrays: true,
                        },
                    },
                    {
                        $unwind: {
                            path: "$contact",
                            preserveNullAndEmptyArrays: false,
                        },
                    },
                    {
                        $project: {
                            "contact.actions": 1,
                            nextAction: {
                                $cond: {
                                    if: { $gt: ["$nextAction", null] },
                                    then: {
                                        $mergeObjects: [
                                            "$nextAction",
                                            {
                                                PO: "$PO",
                                                num: "$num",
                                                oppId: "$_id",
                                                stageGroup: "$stage.stageGroup",
                                            },
                                        ],
                                    },
                                    else: null,
                                },
                            },
                            actions: {
                                $map: {
                                    input: "$actions",
                                    as: "action",
                                    in: {
                                        $mergeObjects: [
                                            "$$action",
                                            {
                                                PO: "$PO",
                                                num: "$num",
                                                oppId: "$_id",
                                                stageGroup: "$stage.stageGroup",
                                            },
                                        ],
                                    },
                                },
                            },
                            "contact.nextAction": 1,
                            contactId: 1,
                        },
                    },
                ]);

                // console.log("Opp ::", oppData);
                const oppData = oppAggResult[0] as any;
                const allNextActions = [
                    ...(oppData?.nextAction ? [oppData.nextAction] : []),
                    ...(oppData?.contact?.nextAction
                        ? [{ ...oppData.contact.nextAction, contactId: oppData.contactId }]
                        : []),
                ].sort((a, b) => {
                    const dateA = a.completedAt ? new Date(a.completedAt).getTime() : -Infinity;
                    const dateB = b.completedAt ? new Date(b.completedAt).getTime() : -Infinity;
                    return dateB - dateA;
                });
                const allActions = [
                    ...(oppData?.actions || []),
                    ...(oppData?.contact?.actions || []).map((action) => ({
                        ...action,
                        contactId: oppData.contactId,
                    })),
                ].sort((a, b) => {
                    const dateA = a.completedAt ? new Date(a.completedAt).getTime() : -Infinity;
                    const dateB = b.completedAt ? new Date(b.completedAt).getTime() : -Infinity;
                    return dateB - dateA;
                });
                return new OkResponse({ nextAction: allNextActions, history: allActions });
            }
        } catch (error: any) {
            console.log(error);
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async completeAction(
        companyId: string,
        memberId: string,
        oppOrContactId: string,
        createNewActionDto: CreateNewActionDto,
        isContact: boolean,
    ) {
        try {
            const updatePayload = {
                $push: {
                    actions: {
                        _id: createNewActionDto.id,
                        type: createNewActionDto.type,
                        body: createNewActionDto.body,
                        due: createNewActionDto.dueDate,
                        assignTo: createNewActionDto.assignTo,
                        completedBy: memberId,
                        completedAt: createNewActionDto.currDate,
                    },
                },
            };

            const query = {
                _id: oppOrContactId,
                companyId,
                "actions._id": { $ne: createNewActionDto.id },
            };

            const model = isContact ? this.contactModel : this.opportunityModel;

            const result = await model.updateOne(query, updatePayload);

            if (result.modifiedCount === 0) {
                throw new HttpException("Action already exists or update failed!", HttpStatus.BAD_REQUEST);
            }

            return new OkResponse({ message: "Action completed successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }
}
