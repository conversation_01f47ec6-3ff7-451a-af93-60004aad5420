import { Schema, SchemaFactory, Prop } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import { Document } from "mongoose";
import { UUIDProp } from "src/shared/decorator/uuid.decorator";

export type ShareMediaDocument = ShareMedia & Document;

@Schema({ timestamps: true, id: false, collection: "ShareMedia", strict: false })
export class ShareMedia {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @UUIDProp()
    companyId: string;

    @UUIDProp({ unique: false })
    oppId: string;

    @Prop({
        type: {
            createdBy: { type: [String] },
            imageIds: { type: [String] },
            types: { type: [String] },
            tags: { type: [String] },
        },
        _id: false,
    })
    filters?: {
        createdBy?: string[];
        imageIds?: string[];
        types?: string[];
        tags?: string[];
    };

    @Prop()
    createdAt?: Date;

    @Prop()
    updatedAt?: Date;
}

export const ShareMediaSchema = SchemaFactory.createForClass(ShareMedia);

// Indexes
// ShareMediaSchema.index({ oppId: 1 });
// ShareMediaSchema.index({ companyId: 1, oppId: 1 });
// ShareMediaSchema.index({ "images.tags": 1 });
