import { Schema, SchemaFactory, Prop } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import { Document } from "mongoose";
import { UUIDProp } from "src/shared/decorator/uuid.decorator";

export type OpportunityMediaDocument = OpportunityMedia & Document;
export type MediaDocument = Media & Document;

@Schema({ timestamps: true, id: false, collection: "OpportunityMedia", strict: false })
export class OpportunityMedia {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @UUIDProp()
    companyId: string;

    @UUIDProp({ unique: false })
    oppId: string;

    @Prop({
        type: [
            {
                _id: { type: String, default: () => randomUUID() },
                name: { type: String, required: true },
                mimetype: { type: String, required: true },
                url: { type: String, required: false },
                thumbnail: { type: String, required: false },
                tags: { type: [String], default: [], validate: (tags: string[]) => tags.length <= 10 },
                location: {
                    type: { type: String, enum: ["Point"], default: "Point" },
                    coordinates: { type: [Number], required: false }, // [longitude, latitude]
                },
                createdAt: { type: Date, required: true },
                createdBy: { type: String, required: true },
                formId: { type: String, required: false },
                oppFormId: { type: String, required: false },
            },
        ],
        _id: true,
    })
    images: {
        _id: string;
        name: string;
        mimetype: string;
        url?: string;
        thumbnail?: string;
        tags?: string[];
        location?: {
            type: string;
            coordinates: [number, number];
        };
        createdAt: Date;
        createdBy: string;
        formId?: string;
        oppFormId?: string;
    }[];

    @Prop()
    createdAt?: Date;

    @Prop()
    updatedAt?: Date;
}

@Schema({ timestamps: true, id: false, collection: "Media", strict: true })
export class Media {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @UUIDProp()
    companyId: string;

    @UUIDProp({ required: false })
    oppId?: string;

    @Prop()
    name?: string;

    @Prop()
    mimetype?: string;

    @Prop()
    url?: string;

    @Prop()
    thumbnail?: string;

    @Prop()
    formFieldByName?: string;

    @Prop({ type: [String], default: [], validate: (tags: string[]) => tags.length <= 10 })
    tags: string[];

    @Prop({
        type: { type: String, enum: ["Point"], default: "Point" },
        coordinates: { type: [Number], required: false }, // [longitude, latitude]
    })
    location?: {
        type: string;
        coordinates: [number, number];
    };

    @Prop()
    builderFormId?: string;

    @Prop()
    formId?: string;

    @Prop()
    createdBy: string;

    @Prop()
    createdAt?: Date;

    @Prop()
    updatedAt?: Date;

    @Prop()
    stepId?: string;
}

export const OpportunityMediaSchema = SchemaFactory.createForClass(OpportunityMedia);
export const MediaSchema = SchemaFactory.createForClass(Media);

MediaSchema.index({ companyId: 1, oppId: 1 }); // Combined index
MediaSchema.index({ tags: 1 }); // Tag-based
