import { <PERSON><PERSON><PERSON><PERSON>, IsMongoId, <PERSON><PERSON><PERSON>al, IsString, IsUUID, ValidateNested } from "class-validator";
import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Type } from "class-transformer";

class ImageDto {
    @ApiProperty({ description: "Image ID", required: false })
    @IsString()
    _id: string;

    @ApiProperty({ description: "Image name", required: false })
    name: string;

    @ApiProperty({ description: "MIME type of the image", required: false })
    mimetype: string;

    @ApiProperty({ description: "Image URL", required: false })
    url?: string;

    @ApiProperty({ description: "Tags for the image", required: false, type: [String] })
    tags?: string[];

    @ApiProperty({ description: "Location of the image", required: false })
    location?: {
        type: string;
        coordinates: [number, number];
    };

    @ApiPropertyOptional({ description: "Image URL" })
    @IsOptional()
    @IsString()
    thumbnail?: string;

    @ApiPropertyOptional({ description: "Created at timestamp", required: false })
    createdAt: Date;

    @ApiProperty({ description: "Created by user ID", required: false })
    createdBy: string;
}

export class UpdateMediaDto {
    @ApiProperty({ description: "Opportunity Id to update", required: true })
    oppId: string;

    @ApiProperty({ description: "Array of images to append", required: true, type: [ImageDto] })
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => ImageDto)
    images: ImageDto[];
}
