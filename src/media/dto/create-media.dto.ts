import {
    IsArray,
    IsDate,
    IsOptional,
    IsString,
    ValidateNested,
    IsNotEmpty,
    IsEnum,
    IsUUID,
} from "class-validator";
import { Type } from "class-transformer";
import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";

class LocationDto {
    @ApiPropertyOptional({ description: "Type of location", example: "Point" })
    @IsEnum(["Point"], { message: "Location type must be 'Point'" })
    type: string;

    @ApiPropertyOptional({ description: "Coordinates of the location [longitude, latitude]" })
    @IsArray()
    @Type(() => Number)
    coordinates?: [number, number]; // [longitude, latitude]
}

class ImageDto {
    @ApiProperty({ description: "Image ID", required: false })
    @IsString()
    _id: string;

    @ApiProperty({ description: "Image name", required: true })
    @IsString()
    @IsNotEmpty()
    name: string;

    @ApiProperty({ description: "MIME type of the image", required: true })
    @IsString()
    mimetype: string;

    @ApiPropertyOptional({ description: "Image URL" })
    @IsOptional()
    @IsString()
    url?: string;

    @ApiPropertyOptional({ description: "Image URL" })
    @IsOptional()
    @IsString()
    thumbnail?: string;

    @ApiPropertyOptional({ description: "Form Feild" })
    @IsOptional()
    @IsString()
    formFieldByName?: string;

    @ApiPropertyOptional({ description: "Tags associated with the image", isArray: true })
    @IsArray()
    @IsOptional()
    tags?: string[];

    @ApiPropertyOptional({ description: "Geolocation of the image" })
    @IsOptional()
    @ValidateNested()
    @Type(() => LocationDto)
    location?: LocationDto;

    @ApiPropertyOptional({ description: "Form builder Associated with media" })
    @IsOptional()
    @IsString()
    builderFormId?: string;

    @ApiPropertyOptional({ description: "Form Associated with media" })
    @IsOptional()
    @IsString()
    formId?: string;

    @ApiProperty({ description: "Creation date of the image", required: false })
    @IsDate()
    @Type(() => Date)
    createdAt: Date;
}

export class CreateMediaDto {
    @ApiProperty({ description: "Opportunity ID", required: false })
    @IsOptional()
    @IsString()
    oppId?: string;

    @ApiPropertyOptional({ description: "Array of images", isArray: true })
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => ImageDto)
    images?: ImageDto[];

    @ApiPropertyOptional({ description: "Step ID (UUID)", required: false })
    @IsOptional()
    @IsUUID()
    stepId?: string;
}
