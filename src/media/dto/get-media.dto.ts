import { ApiPropertyOptional } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsOptional } from "class-validator";
import { PaginationDto } from "src/shared/dto/pagination.dto";

export class GetMediaQueryDto extends PaginationDto {
    @ApiPropertyOptional({ description: "tags of images" })
    @IsOptional()
    @Transform(({ value }) => (typeof value === "string" ? value.split(",") : value))
    tags?: string[];

    @ApiPropertyOptional({ description: "images uuids" })
    @IsOptional()
    @Transform(({ value }) => (typeof value === "string" ? value.split(",") : value))
    imageIds?: string[];

    @ApiPropertyOptional({ description: "images created by" })
    @IsOptional()
    @Transform(({ value }) => (typeof value === "string" ? value.split(",") : value))
    createdBy?: string[];

    @ApiPropertyOptional({ description: "images mimetype" })
    @IsOptional()
    @Transform(({ value }) => (typeof value === "string" ? value.split(",") : value))
    types?: string[];
}
