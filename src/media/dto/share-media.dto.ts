import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsArray, IsNotEmpty, IsOptional, IsUUID } from "class-validator";

class ShareMediaFilteryDto {
    @ApiPropertyOptional({ description: "images tags" })
    @IsOptional()
    @IsArray()
    tags?: string[];

    @ApiPropertyOptional({ description: "images uuids" })
    @IsOptional()
    @IsArray()
    imageIds?: string[];

    @ApiPropertyOptional({ description: "mimeType" })
    @IsOptional()
    @IsArray()
    types?: string[];

    @ApiPropertyOptional({ description: "created by member" })
    @IsOptional()
    @IsArray()
    createdBy?: string[];
}

export class ShareMediaQueryDto {
    @ApiProperty({ description: "opportunity id of media" })
    @IsNotEmpty()
    @IsUUID()
    oppId: string;

    @ApiPropertyOptional({ description: "filter of images" })
    @IsOptional()
    filters?: ShareMediaFilteryDto;
}
