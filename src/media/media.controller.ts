import { BadRequestException, Body, Controller, Delete, Get, Param, Post, Put, Query } from "@nestjs/common";
import { MediaService } from "./media.service";
import {
    ApiTags,
    ApiBearerAuth,
    ApiInternalServerErrorResponse,
    ApiUnauthorizedResponse,
    ApiOperation,
    ApiConflictResponse,
    ApiNotFoundResponse,
    ApiQuery,
} from "@nestjs/swagger";
import { Auth, GetUser } from "src/auth/decorator/auth.decorator";
import { Positions } from "src/auth/guards/auth.guard";
import { JwtUserPayload } from "src/auth/interface/auth.interface";
import { moduleNames } from "src/shared/constants/constant";
import { PermissionsEnum } from "src/shared/enum/permission.enum";
import HttpResponse from "src/shared/http/response/response.http";
import { CreateMediaDto } from "./dto/create-media.dto";
import { GetMediaQueryDto } from "./dto/get-media.dto";
import { ShareMediaQueryDto } from "./dto/share-media.dto";
import { UpdateMediaDto } from "./dto/update-media.dto";

@ApiTags("media")
@ApiBearerAuth()
@Auth()
@Controller({ path: "media", version: "1" })
@ApiInternalServerErrorResponse({ description: "Server Error" })
@ApiUnauthorizedResponse({ description: "Unauthorized request" })
export class MediaController {
    constructor(private readonly mediaService: MediaService) {}

    @ApiOperation({ summary: "Create Media" })
    @ApiConflictResponse({ description: "Media already exist" })
    @Positions({
        category: "module",
        name: moduleNames.module.media,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed, PermissionsEnum.Self],
    })
    @Post()
    async create(@GetUser() user: JwtUserPayload, @Body() createMediaDto: CreateMediaDto) {
        return this.mediaService.createMedia(user.companyId, user.memberId, createMediaDto);
    }

    @ApiOperation({ summary: "Update Media with new images" })
    @ApiNotFoundResponse({ description: "Media not found" })
    @Positions({
        category: "module",
        name: moduleNames.module.media,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed, PermissionsEnum.Self],
    })
    @Put()
    async updateMedia(@GetUser() user: JwtUserPayload, @Body() updateMediaDto: UpdateMediaDto) {
        return this.mediaService.updateMedia(user.companyId, updateMediaDto);
    }

    @ApiOperation({ summary: "Delete Images from Media" })
    @ApiNotFoundResponse({ description: "Images not found" })
    @Positions({
        category: "module",
        name: moduleNames.module.media,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed, PermissionsEnum.Self],
    })
    @Delete("images")
    async deleteImages(@GetUser() user: JwtUserPayload, @Query("imageIds") imageIds: string) {
        if (!imageIds) {
            throw new BadRequestException("Image IDs are required.");
        }

        const imageIdArray = imageIds.split(",");
        return this.mediaService.deleteImagesFromMedia(user.companyId, imageIdArray, user);
    }

    @ApiOperation({ summary: "Get Media by ID" })
    @ApiQuery({ name: "tags", required: false, type: [String], description: "Filter images by tags" })
    @Get("oppId/:oppId")
    @Positions({
        category: "module",
        name: moduleNames.module.media,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed, PermissionsEnum.Self],
    })
    async getMedia(
        @GetUser() user: JwtUserPayload,
        @Param("oppId") oppId: string,
        @Query() getMediaQueryDto: GetMediaQueryDto,
    ) {
        return this.mediaService.getMedia(user.companyId, oppId, getMediaQueryDto);
    }

    @ApiOperation({ summary: "Generate a token from UUIDs" })
    @Post("share-url")
    generateToken(@Body() shareMediaQueryDto: ShareMediaQueryDto, @GetUser() user: JwtUserPayload) {
        return this.mediaService.createShareMedia(user.companyId, shareMediaQueryDto);
    }

    /**
     * Get all media for a company
     * @param userId - User ID of the requester
     * @param companyId - Company ID of the Company to get opp for
     * @param deleted - Whether to include deleted opp in the results
     * @param paginationRequestDto - Pagination request details (offset, limit, etc.)
     * @returns List of opp for the specified Company
     */
    @ApiOperation({ summary: "Get all media" })
    @Positions({
        category: "module",
        name: moduleNames.module.media,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed, PermissionsEnum.Self],
    })
    @Get("all")
    async getAllMedia(
        @GetUser() user: JwtUserPayload,
        @Query() getMediaQueryDto: GetMediaQueryDto,
    ): Promise<HttpResponse> {
        return this.mediaService.getAllUploadedMedia(
            user.companyId,
            user.memberId,
            user.teamPermission,
            getMediaQueryDto,
        );
    }
}

@ApiTags("media")
@Controller({ path: "media", version: "1" })
@ApiInternalServerErrorResponse({ description: "Server Error" })
@ApiUnauthorizedResponse({ description: "Unauthorized request" })
export class OpenMediaController {
    constructor(private readonly mediaService: MediaService) {}

    @ApiOperation({ summary: "Reverse a token back to UUIDs" })
    @Get("share-url/:token")
    reverseToken(@Param("token") token: string) {
        return this.mediaService.getShareMedia(token);
    }
}
