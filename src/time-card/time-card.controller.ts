import {
    Body,
    Controller,
    Delete,
    Get,
    Param,
    ParseUUIDPipe,
    Patch,
    Post,
    Query,
    UseGuards,
} from "@nestjs/common";
import {
    ApiTags,
    ApiOperation,
    ApiConflictResponse,
    ApiInternalServerErrorResponse,
    ApiNotFoundResponse,
    ApiUnauthorizedResponse,
    ApiBearerAuth,
    ApiBody,
} from "@nestjs/swagger";
import { Positions } from "src/auth/guards/auth.guard";
import HttpResponse from "src/shared/http/response/response.http";
import { CreateTimeCardToClockInDto } from "./dto/create-time-card-to-clock-in.dto";
import { UpdateTimeCardToClockOutDto } from "./dto/update-time-card-to-clock-out.dto";
import { CancelDayOffDto } from "./dto/cancel-day-off.dto";
import { TakeDayOffDto } from "./dto/take-day-off.dto";
import { UpdateTimeCardDto } from "./dto/update-time-card.dto";
import { TimeCardService } from "./time-card.service";
import { UserAuthGuard } from "src/auth/guards/jwt-auth.guard";
import { GetMemberTimeCardsDto } from "./dto/get-member-recent-time-cards.dto";
import { PermissionsEnum } from "src/shared/enum/permission.enum";
import { PositionService } from "src/position/position.service";
import { CreateTimeCardDto } from "./dto/create-time-card.dto";
import { UpdateTimeCardToApproveOrRejectDto } from "./dto/update-time-card-to-approve-or-reject.dto";
import OkResponse from "src/shared/http/response/ok.http";
import { Auth, GetUser } from "src/auth/decorator/auth.decorator";
import { JwtUserPayload } from "src/auth/interface/auth.interface";
import { PaginationDto } from "src/shared/dto/pagination.dto";
import { CreatePtoDto } from "./dto/create-pto.dto";
import { moduleNames } from "src/shared/constants/constant";

@ApiTags("TimeCard")
// @UseGuards(UserAuthGuard)
@Auth()
@ApiBearerAuth()
@ApiInternalServerErrorResponse({ description: "Server Error" })
@ApiUnauthorizedResponse({ description: "Unauthorized request" })
@Controller({ path: "time-card", version: "1" })
export class TimeCardController {
    constructor(
        private readonly timeCardService: TimeCardService,
        private readonly positionService: PositionService,
    ) {}

    @ApiOperation({ summary: "Create TimeCard To ClockIn" })
    @ApiConflictResponse({ description: "TimeCard already exist" })
    @Post("create-time-card-to-clock-in")
    async createTimeCardToClockIn(
        @GetUser() user: JwtUserPayload,
        @Body() createTimeCardToClockInDto: CreateTimeCardToClockInDto,
    ): Promise<HttpResponse> {
        return this.timeCardService.createTimeCardToClockIn(
            user.memberId,
            user.companyId,
            createTimeCardToClockInDto,
        );
    }

    @ApiOperation({ summary: "Update TimeCard To ClockOut" })
    @ApiNotFoundResponse({ description: "TimeCard not found" })
    @Patch("update-time-card-to-clock-out")
    async updateTimeCardToClockOut(
        @GetUser() user: JwtUserPayload,
        @Body() updateTimeCardToClockOutDto: UpdateTimeCardToClockOutDto,
    ): Promise<HttpResponse> {
        return this.timeCardService.updateTimeCardToClockOut(
            user.memberId,
            user.companyId,
            updateTimeCardToClockOutDto,
        );
    }

    @ApiOperation({ summary: "Get Active TimeCards" })
    @Get("get-active-time-cards/member/:memberId")
    async getActiveTimeCards(
        @GetUser() user: JwtUserPayload,

        @Param("memberId", ParseUUIDPipe) memberId: string,
    ): Promise<HttpResponse> {
        return this.timeCardService.getActiveTimeCards(user.companyId, memberId);
    }

    @ApiOperation({ summary: "Update TimeCard To Approve or reject" })
    @ApiNotFoundResponse({ description: "TimeCard not found" })
    @Positions({
        category: "module",
        name: moduleNames.module.timeCards,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed],
    })
    @Patch("update-time-card-to-approve-or-reject")
    async updateTimeCardToApproveOrReject(
        @GetUser() user: JwtUserPayload,
        @Body() updateTimeCardToApproveOrRejectDto: UpdateTimeCardToApproveOrRejectDto,
    ): Promise<HttpResponse> {
        const {
            data: { memberPosition: position },
        } = await this.positionService.getMemberPosition(user._id, user.companyId);
        // const timeCardPermission = position.permissions[0].resources.find(
        //     (item) => item.name === "timeCards",
        // ).permissions;
        return this.timeCardService.updateTimeCardToApproveOrReject(
            user._id,
            user.memberId,
            user.companyId,
            updateTimeCardToApproveOrRejectDto,
            user.teamPermission,
            position.symbol,
        );
    }

    @ApiOperation({ summary: "Create TimeCard" })
    @ApiNotFoundResponse({ description: "TimeCard not found" })
    @Positions({
        category: "module",
        name: moduleNames.module.timeCards,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed],
    })
    @Post("create-time-card")
    async createTimeCard(
        @GetUser() user: JwtUserPayload,
        @Body() createTimeCardDto: CreateTimeCardDto,
    ): Promise<HttpResponse> {
        const {
            data: { memberPosition: position },
        } = await this.positionService.getMemberPosition(user._id, user.companyId);
        // const timeCardPermission = position.permissions[0].resources.find(
        //     (item) => item.name === "timeCards",
        // ).permissions;
        return this.timeCardService.createTimeCard(
            user._id,
            user.memberId,
            user.companyId,
            createTimeCardDto,
            user.teamPermission,
            position.symbol,
        );
    }

    @ApiOperation({ summary: "Create multiple TimeCards" })
    @ApiNotFoundResponse({ description: "TimeCard not found" })
    @Positions({
        category: "module",
        name: moduleNames.module.timeCards,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed],
    })
    @Post("create-time-cards")
    @ApiBody({ type: [CreateTimeCardDto] })
    async createTimeCards(
        @GetUser() user: JwtUserPayload,
        @Body() createTimeCardsDto: CreateTimeCardDto[],
    ): Promise<HttpResponse> {
        const errors: { index: number; error: any }[] = [];

        const promises = createTimeCardsDto.map(async (createTimeCardDto, index) => {
            try {
                const {
                    data: { memberPosition: position },
                } = await this.positionService.getMemberPosition(user._id, user.companyId);
                // const timeCardPermission = position.permissions[0].resources.find(
                //     (item) => item.name === "timeCards",
                // ).permissions;
                await this.timeCardService.createTimeCard(
                    user._id,
                    user.memberId,
                    user.companyId,
                    createTimeCardDto,
                    user.teamPermission,
                    position.symbol,
                );
            } catch (error) {
                errors.push({ index, error: error.message || error });
            }
        });

        await Promise.all(promises);

        return new OkResponse({ message: "Successfully created", errors });
    }

    @ApiOperation({ summary: "Update multiple TimeCards" })
    @ApiNotFoundResponse({ description: "One or more TimeCards not found" })
    @Positions({
        category: "module",
        name: moduleNames.module.timeCards,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed, PermissionsEnum.Self],
    })
    @Patch("update-time-cards")
    @ApiBody({ type: [UpdateTimeCardDto] })
    async updateTimeCards(
        @GetUser() user: JwtUserPayload,
        @Body() updateTimeCardsDto: UpdateTimeCardDto[],
    ): Promise<HttpResponse> {
        const errors: { index: number; error: any }[] = [];

        const promises = updateTimeCardsDto.map(async (updateTimeCardDto, index) => {
            try {
                const {
                    data: { memberPosition: position },
                } = await this.positionService.getMemberPosition(user._id, user.companyId);
                // const timeCardPermission = position.permissions[0].resources.find(
                //     (item) => item.name === "timeCards",
                // ).permissions;
                await this.timeCardService.updateTimeCard(
                    user._id,
                    user.memberId,
                    user.companyId,
                    updateTimeCardDto,
                    user.teamPermission,
                    position.symbol,
                );
            } catch (error) {
                errors.push({ index, error: error.message || error });
            }
        });

        await Promise.all(promises);

        return new OkResponse({ message: "Successfully updated", errors });
    }

    @ApiOperation({ summary: "Update TimeCard" })
    @ApiNotFoundResponse({ description: "TimeCard not found" })
    @Positions({
        category: "module",
        name: moduleNames.module.timeCards,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed, PermissionsEnum.Self],
    })
    @Patch("update-time-card")
    async updateTimeCard(
        @GetUser() user: JwtUserPayload,
        @Body() updateTimeCardDto: UpdateTimeCardDto,
    ): Promise<HttpResponse> {
        const {
            data: { memberPosition: position },
        } = await this.positionService.getMemberPosition(user._id, user.companyId);
        // const timeCardPermission = position.permissions[0].resources.find(
        //     (item) => item.name === "timeCards",
        // ).permissions;
        return this.timeCardService.updateTimeCard(
            user._id,
            user.memberId,
            user.companyId,
            updateTimeCardDto,
            user.teamPermission,
            position.symbol,
        );
    }

    @ApiOperation({ summary: "Get Recent TimeCards" })
    @Positions({
        category: "module",
        name: moduleNames.module.timeCards,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed, PermissionsEnum.Self],
    })
    @Get("get-recent-time-cards/:startDate/:endDate")
    async getRecentTimeCards(
        @GetUser() user: JwtUserPayload,

        @Param("startDate") startDate: Date,
        @Param("endDate") endDate: Date,
        @Query() getMemberTimeCardsDto: GetMemberTimeCardsDto,
    ): Promise<HttpResponse> {
        return this.timeCardService.getRecentTimeCards(
            user._id,
            user.companyId,
            getMemberTimeCardsDto,
            user.teamPermission,
            startDate,
            endDate,
        );
    }

    @ApiOperation({ summary: "Get Member TimeCards" })
    @Positions({
        category: "module",
        name: moduleNames.module.timeCards,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed, PermissionsEnum.Self],
    })
    @Get("get-member-time-cards")
    async getMemberTimeCards(
        @GetUser() user: JwtUserPayload,

        @Query() getMemberTimeCardsDto: GetMemberTimeCardsDto,
    ): Promise<HttpResponse> {
        return this.timeCardService.getMemberTimeCards(
            user._id,
            user.companyId,
            getMemberTimeCardsDto,
            user.teamPermission,
        );
    }

    @ApiOperation({ summary: "Take DayOff" })
    @ApiNotFoundResponse({ description: "TimeCard found" })
    @Positions({
        category: "module",
        name: moduleNames.module.timeCards,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed],
    })
    @Post("day-off")
    async takeDayOff(
        @GetUser() user: JwtUserPayload,
        @Body() takeDayOffDto: TakeDayOffDto,
    ): Promise<HttpResponse> {
        return this.timeCardService.takeDayOff(user._id, user.companyId, takeDayOffDto, user.teamPermission);
    }

    @ApiOperation({ summary: "Cancel DayOff" })
    @ApiNotFoundResponse({ description: "DayOff not found" })
    @Positions({
        category: "module",
        name: moduleNames.module.timeCards,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed],
    })
    @Post("cancel-day-off")
    async cancelDayOff(
        @GetUser() user: JwtUserPayload,
        @Body() cancelDayOffDto: CancelDayOffDto,
    ): Promise<HttpResponse> {
        return this.timeCardService.cancelDayOff(
            user._id,
            user.companyId,
            cancelDayOffDto,
            user.teamPermission,
        );
    }

    @ApiOperation({ summary: "Delete Time card" })
    // @Positions({category: "module", name: "team", actions: [PermissionsEnum.Full, PermissionsEnum.Managed] })
    @Delete("delete-time-card")
    async deleteTimeCard(
        @GetUser() user: JwtUserPayload,
        @Body() cancelDayOffDto: CancelDayOffDto,
    ): Promise<HttpResponse> {
        return this.timeCardService.deleteTimeCard(user._id, cancelDayOffDto);
    }

    @ApiOperation({ summary: "Listing Deleted Time card" })
    @Get("deleted-time-cards")
    async getDeletedTimeCards(
        @GetUser() user: JwtUserPayload,
        @Query() paginationDto: PaginationDto,
    ): Promise<HttpResponse> {
        return this.timeCardService.getDeletedTimeCards(user.companyId, paginationDto);
    }

    @ApiOperation({ summary: "Permanently delete Time card" })
    @Delete("permanently-delete-time-card/:timeCardId")
    async permDeleteTimeCard(
        @GetUser() user: JwtUserPayload,
        @Param("timeCardId") timecardId: string,
    ): Promise<HttpResponse> {
        return this.timeCardService.permDeleteTimeCard(user.companyId, timecardId);
    }

    @ApiOperation({ summary: "Restore Deleted Time card" })
    @Post("restore-time-card/:timeCardId")
    async restoreDeletedTimeCards(
        @GetUser() user: JwtUserPayload,
        @Param("timeCardId") timecardId: string,
    ): Promise<HttpResponse> {
        return this.timeCardService.restoreDeletedTimeCards(user.companyId, timecardId);
    }

    @ApiOperation({ summary: "Get Pitches and Layers of project for Time card" })
    @Get("pitches-layers-for-project/:oppId")
    async getPitchesAndLayersForProject(
        @GetUser() user: JwtUserPayload,

        @Param("oppId") oppId: string,
    ): Promise<HttpResponse> {
        return this.timeCardService.getPitchesAndLayersForProject(user.companyId, oppId);
    }

    @ApiOperation({ summary: "Check & update forgot Time card logout" })
    @Patch("forgot-clockout-update/memberId/:memberId/startDate/:startDate")
    async forgotLogoutUpdate(
        @GetUser() user: JwtUserPayload,

        @Param("memberId") memberId: string,
        @Param("startDate") startDate: Date,
    ): Promise<HttpResponse> {
        return this.timeCardService.forgotLogoutUpdate(user.companyId, memberId, startDate);
    }

    @ApiOperation({ summary: "Update time card activity" })
    @Patch("update-time-card-activity/:timecardId")
    async updateTimeCardActivity(
        @GetUser() user: JwtUserPayload,
        @Param("timecardId", ParseUUIDPipe) timecardId: string,

        @Body() data: any,
    ): Promise<HttpResponse> {
        return this.timeCardService.updateTimeCardActivity(user.memberId, timecardId, user.companyId, data);
    }

    @ApiOperation({ summary: "Create PTO card" })
    @Post("pto")
    async createPto(
        @GetUser() user: JwtUserPayload,
        @Body() createPtoDto: CreatePtoDto,
    ): Promise<HttpResponse> {
        const { response: positionCheck } = await this.positionService.checkPositionPermission(
            user._id,
            user.companyId,
            ["Owner", "Admin", "GeneralManager", "OperationsAdmin", "ProjectManager"],
        );

        return this.timeCardService.createPto(user.companyId, user.memberId, createPtoDto, positionCheck);
    }

    @ApiOperation({ summary: "Create PTO card" })
    @Patch("pto/:timecardId")
    async updatePtoStatus(
        @GetUser() user: JwtUserPayload,
        @Param("timecardId", ParseUUIDPipe) timecardId: string,
    ): Promise<HttpResponse> {
        const { response: positionCheck } = await this.positionService.checkPositionPermission(
            user._id,
            user.companyId,
            ["Owner", "Admin", "GeneralManager", "OperationsAdmin", "ProjectManager"],
        );
        return this.timeCardService.updatePtoStatus(user.companyId, timecardId, positionCheck);
    }

    @ApiOperation({ summary: "Get member pto list" })
    @Get("member-pto/:memberId")
    async getMemberPto(
        @GetUser() user: JwtUserPayload,
        @Param("memberId", ParseUUIDPipe) memberId: string,
    ): Promise<HttpResponse> {
        return this.timeCardService.getMemberPto(user.companyId, memberId);
    }
}
