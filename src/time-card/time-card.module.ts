import { Module } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { CompanyModule } from "src/company/company.module";
import { MemberSchema } from "src/company/schema/member.schema";
import { CrewModule } from "src/crew/crew.module";
import { PieceWorkSchema } from "src/piece-work/schema/piece-work.schema";
import { PositionModule } from "src/position/position.module";
import { RoleModule } from "src/role/role.module";
import { TimeCardSchema } from "./schema/time-card.schema";
import { TimeCardController } from "./time-card.controller";
import { TimeCardService } from "./time-card.service";
import { CompanySettingSchema } from "src/company/schema/company-setting.schema";
import { DailyLogSchema } from "src/daily-log/schema/daily-log.schema";
import { OrderSchema } from "src/project/schema/order.schema";
import { ProjectSchema } from "src/project/schema/project.schema";
import { ProjectTypeSchema } from "src/project/schema/project-type.schema";
import { PieceWorkModule } from "src/piece-work/piece-work.module";
import { WorkTaskSchema } from "src/work-task/schema/work-task.schema";

@Module({
    imports: [
        MongooseModule.forFeature([
            { name: "TimeCard", schema: TimeCardSchema },
            { name: "PieceWork", schema: PieceWorkSchema },
            { name: "Member", schema: MemberSchema },
            { name: "Order", schema: OrderSchema },
            { name: "Project", schema: ProjectSchema },
            { name: "DailyLog", schema: DailyLogSchema },
            { name: "CompanySetting", schema: CompanySettingSchema },
            { name: "ProjectType", schema: ProjectTypeSchema },
            { name: "WorkTask", schema: WorkTaskSchema },
        ]),
        PieceWorkModule,
        PositionModule,
        // RoleModule,
        // CompanyModule,
        CrewModule,
    ],
    providers: [TimeCardService],
    controllers: [TimeCardController],
    exports: [TimeCardService],
})
export class TimeCardModule {}
