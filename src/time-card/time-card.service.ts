import {
    BadRequestException,
    HttpException,
    HttpStatus,
    Injectable,
    InternalServerErrorException,
    NotFoundException,
} from "@nestjs/common";
import { InjectConnection, InjectModel } from "@nestjs/mongoose";
import { Connection, Model } from "mongoose";
import { PieceWorkDocument } from "src/piece-work/schema/piece-work.schema";
import { isWeekend, roundTo2 } from "src/shared/helpers/logics";
import CreatedResponse from "src/shared/http/response/created.http";
import OkResponse from "src/shared/http/response/ok.http";
import { TakeDayOffDto } from "./dto/take-day-off.dto";
import { UpdateTimeCardDto } from "./dto/update-time-card.dto";
import { StatusEnum } from "./enum/status.enum";
import { CancelDayOffDto } from "./dto/cancel-day-off.dto";
import { CreateTimeCardToClockInDto } from "./dto/create-time-card-to-clock-in.dto";
import { TaskTypeEnum } from "./enum/task-type.enum";
import { TimeCardDocument } from "./schema/time-card.schema";
import { GetMemberTimeCardsDto } from "./dto/get-member-recent-time-cards.dto";
import { PositionService } from "src/position/position.service";
import { MemberDocument } from "src/company/schema/member.schema";
import { PermissionsEnum } from "src/shared/enum/permission.enum";
import { CompanyService } from "src/company/company.service";
import NoContentResponse from "src/shared/http/response/no-content.http";
import { UpdateTimeCardToClockOutDto } from "./dto/update-time-card-to-clock-out.dto";
import { CreateTimeCardDto } from "./dto/create-time-card.dto";
import { UpdateTimeCardToApproveOrRejectDto } from "./dto/update-time-card-to-approve-or-reject.dto";
import { CrewService } from "src/crew/crew.service";
import { OrderDocument } from "src/project/schema/order.schema";
import { DailyLogDocument } from "src/daily-log/schema/daily-log.schema";
import { ProjectDocument } from "src/project/schema/project.schema";
import { CompanySettingDocument } from "src/company/schema/company-setting.schema";
import { ProjectTypeDocument } from "src/project/schema/project-type.schema";
import { PieceWorkService } from "src/piece-work/piece-work.service";
import { CreatePieceWorkDto } from "src/piece-work/dto/create-piece-work.dto";
import { UpdatePieceWork } from "src/piece-work/dto/update-piece-work.dto";
import { WorkTaskDocument } from "src/work-task/schema/work-task.schema";
import { PaginationDto } from "src/shared/dto/pagination.dto";
import { CreatePtoDto } from "./dto/create-pto.dto";

@Injectable()
export class TimeCardService {
    constructor(
        private readonly crewService: CrewService,
        private readonly positionService: PositionService,
        private readonly companyService: CompanyService,
        private readonly pieceWorkService: PieceWorkService,
        @InjectConnection() private readonly connection: Connection,
        @InjectModel("Member") private readonly memberModel: Model<MemberDocument>,
        @InjectModel("TimeCard") private readonly timeCardModel: Model<TimeCardDocument>,
        @InjectModel("PieceWork") private readonly pieceWorkModel: Model<PieceWorkDocument>,
        @InjectModel("Order") private readonly orderModel: Model<OrderDocument>,
        @InjectModel("Project") private readonly projectModel: Model<ProjectDocument>,
        @InjectModel("DailyLog") private readonly dailyLogModel: Model<DailyLogDocument>,
        @InjectModel("CompanySetting") private readonly companySettingModel: Model<CompanySettingDocument>,
        @InjectModel("ProjectType") private readonly projectTypeModel: Model<ProjectTypeDocument>,
        @InjectModel("WorkTask") private readonly workTaskModel: Model<WorkTaskDocument>,
    ) {}

    // clock in
    async createTimeCardToClockIn(
        loginMemberId: string,
        companyId: string,
        createTimeCardToClockInDto: CreateTimeCardToClockInDto,
    ) {
        try {
            const isDayOff = await this.isDayOff(
                createTimeCardToClockInDto.memberId,
                createTimeCardToClockInDto.timeIn,
            );
            if (isDayOff) throw new BadRequestException("Can't clock in on a day off");

            const timeCard = await this.timeCardModel
                .findOne({
                    memberId: createTimeCardToClockInDto.memberId,
                    active: true,
                    deleted: false,
                })
                .exec();
            if (timeCard)
                throw new HttpException(
                    "An Active TimeCard Detected!. Please clock out before attempting to clock in.",
                    HttpStatus.BAD_REQUEST,
                );

            const crewId = await this.crewService.getMemberActiveCrewByMemberId(
                companyId,
                createTimeCardToClockInDto.memberId,
            );

            const workTask = await this.workTaskModel.findOne({ _id: createTimeCardToClockInDto.task });

            // check for duplicate cards
            await this.checkForDupeTimeCards(
                createTimeCardToClockInDto.memberId,
                undefined,
                createTimeCardToClockInDto.timeIn,
                undefined,
            );

            const createdTimeCard = new this.timeCardModel({
                crewId: crewId?._id,
                taskName: workTask.name,
                companyId,
                ...createTimeCardToClockInDto,
            });
            await createdTimeCard.save();

            // updating card activity
            this.updateTimeCardActivity(loginMemberId, createdTimeCard._id, companyId, {
                clockIn: createTimeCardToClockInDto.timeIn,
                status: "Time Card Clock In",
            });

            return new CreatedResponse({ message: "Clock In timeCard created successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    // clock out
    async updateTimeCardToClockOut(
        loginMemberId: string,
        companyId: string,
        updateTimeCardToClockOut: UpdateTimeCardToClockOutDto,
    ) {
        try {
            const timeCard = await this.timeCardModel
                .findOne({
                    _id: updateTimeCardToClockOut._id,
                    deleted: false,
                    active: true,
                })
                .exec();
            if (!timeCard) throw new HttpException("No Active TimeCard Found!", HttpStatus.BAD_REQUEST);
            // check for duplicate cards
            await this.checkForDupeTimeCards(
                timeCard.memberId,
                timeCard._id,
                timeCard.timeIn,
                updateTimeCardToClockOut.timeOut,
            );

            const workTask = await this.workTaskModel.findOne({ _id: updateTimeCardToClockOut.task });
            updateTimeCardToClockOut.taskName = workTask?.name;

            const card = await this.timeCardModel.updateOne(
                { _id: updateTimeCardToClockOut._id },
                {
                    $set: {
                        ...updateTimeCardToClockOut,
                        active: false,
                    },
                },
                { new: true },
            );

            // updating card activity
            this.updateTimeCardActivity(loginMemberId, updateTimeCardToClockOut._id, companyId, {
                clockOut: updateTimeCardToClockOut.timeOut,
                status: "Time Card Clock Out",
            });

            if (card.modifiedCount)
                return new OkResponse({ message: "Clock Out timeCard created successfully!" });
            else throw new HttpException("Failed to Clock Out", HttpStatus.BAD_REQUEST);
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    //get recent active timecard
    async getActiveTimeCards(companyId: string, memberId: string) {
        try {
            const activeCard = await this.timeCardModel
                .findOne({ companyId, memberId, active: true, deleted: { $ne: true } })
                .sort({ timeIn: -1 });

            return new OkResponse({ activeCard });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    // approve or reject
    async updateTimeCardToApproveOrReject(
        userId: string,
        loginMemberId: string,
        companyId: string,
        updateTimeCardToApproveOrRejectDto: UpdateTimeCardToApproveOrRejectDto,
        timeCardPermission: number,
        symbol: string,
    ) {
        try {
            const timeCard = await this.timeCardModel
                .findOne({
                    _id: updateTimeCardToApproveOrRejectDto._id,
                    deleted: false,
                })
                .exec();
            if (!timeCard) throw new HttpException("TimeCard does not exist", HttpStatus.BAD_REQUEST);
            const member = await this.memberModel
                .findOne({
                    _id: timeCard.memberId,
                    deleted: false,
                })
                .exec();

            if (!member) throw new HttpException("Member does not exists", HttpStatus.BAD_REQUEST);

            const crew = await this.crewService.getMemberActiveCrewByMemberId(companyId, timeCard.memberId);

            const crewManagerId = symbol === "Foreman" ? crew?.foremanId : crew?.managerId;

            await this.positionService.permissionCheck(
                userId,
                companyId,
                timeCard.memberId, //not used in case of timecard permission check
                member.managerId,
                timeCardPermission,
                crewManagerId,
            );

            if (
                symbol === "Foreman" &&
                (timeCard.status === StatusEnum.Approved ||
                    updateTimeCardToApproveOrRejectDto.status === StatusEnum.Approved)
            )
                throw new HttpException("You are unauthorized to Approve this Card", HttpStatus.BAD_REQUEST);

            await this.timeCardModel.updateOne(
                {
                    _id: updateTimeCardToApproveOrRejectDto._id,
                },
                {
                    status: updateTimeCardToApproveOrRejectDto.status,
                },
            );

            // updating card activity
            this.updateTimeCardActivity(loginMemberId, updateTimeCardToApproveOrRejectDto._id, companyId, {
                status: updateTimeCardToApproveOrRejectDto.status,
            });

            return new OkResponse({ message: "Timecard status changed successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async createTimeCard(
        userId: string,
        loginMemberId: string,
        companyId: string,
        createTimeCardDto: CreateTimeCardDto,
        timeCardPermission: number,
        symbol: string,
    ) {
        let session;
        try {
            // Starting session for Mongo transaction
            session = await this.connection.startSession();
            session.startTransaction();

            const isDayOff = await this.isDayOff(createTimeCardDto?.memberId, createTimeCardDto.timeIn);
            if (isDayOff) throw new BadRequestException("Can’t create time card on a day off");

            const [member, crew, workTask] = await Promise.all([
                this.memberModel
                    .findOne({
                        _id: createTimeCardDto.memberId,
                        deleted: false,
                    })
                    .exec(),
                this.crewService.getMemberActiveCrewByMemberId(companyId, createTimeCardDto.memberId),
                this.workTaskModel.findOne({ _id: createTimeCardDto.task }),
            ]);

            if (!member) throw new HttpException("Member does not exists", HttpStatus.BAD_REQUEST);
            if (symbol === "Foreman" && createTimeCardDto.status === StatusEnum.Approved)
                throw new HttpException("You are unauthorized to Approve this Card", HttpStatus.BAD_REQUEST);

            const crewManagerId = symbol === "Foreman" ? crew?.foremanId : crew?.managerId;
            await this.positionService.permissionCheck(
                userId,
                companyId,
                member._id, //not used in case of timecard permission check
                member.managerId,
                timeCardPermission,
                crewManagerId,
            );

            await this.checkForDupeTimeCards(
                createTimeCardDto.memberId,
                undefined,
                createTimeCardDto.timeIn,
                createTimeCardDto.timeOut,
            );
            const timeCardRequest = {
                companyId,
                memberId: createTimeCardDto.memberId,
                crewId: crew?._id,
                projectId: createTimeCardDto.projectId,
                projectPO: createTimeCardDto.projectPO,
                task: createTimeCardDto.task,
                taskName: workTask.name,
                timeIn: createTimeCardDto.timeIn,
                timeOut: createTimeCardDto.timeOut,
                active: false,
                status: createTimeCardDto.status,
                hrs: createTimeCardDto.hrs,
                createdBy: createTimeCardDto.memberId,
                allHourly: createTimeCardDto.allHourly,
                notes: createTimeCardDto?.notes,
                managerNotes: createTimeCardDto?.managerNotes,
            };
            const createdTimeCard = new this.timeCardModel(timeCardRequest);
            await createdTimeCard.save({ session });
            //For extraTime calculation for Piece Work
            const extraTime = createTimeCardDto?.work?.extraTime;
            if (extraTime) {
                const hrs = extraTime?.extraHrs ? Number(extraTime.extraHrs) : 0;
                const min = extraTime?.extraMin ? Number(extraTime.extraMin) : 0;
                createTimeCardDto.work["extraWorkTime"] = roundTo2(hrs + min / 60);
            }

            //Creating Piece Work for this member timecard
            const pieceWorkRequest: CreatePieceWorkDto = {
                timeCardId: createdTimeCard._id,
                memberId: createTimeCardDto.memberId,
                date: createTimeCardDto.date,
                task: createTimeCardDto.task,
                taskName: workTask.name,
                projectId: createTimeCardDto.projectId,
                work: createTimeCardDto.work,
                // notes: createTimeCardDto.notes,
                timeIn: new Date(createTimeCardDto.timeIn),
                timeOut: new Date(createTimeCardDto.timeOut),
                createdBy: createTimeCardDto.memberId,
                // managerNotes: createTimeCardDto.managerNotes,
                removeLeadBonus: createTimeCardDto.removeLeadBonus,
                allHourly: createTimeCardDto.allHourly,
                foreman: crew?.foremanId === createTimeCardDto.memberId,
            };
            const createdPieceWork = await this.pieceWorkService.upsertPieceWork(
                companyId,
                loginMemberId,
                pieceWorkRequest,
            );

            if (createdPieceWork.statusCode === 201) {
                // ending session of mongodb
                await session.commitTransaction();
                session.endSession();

                // updating card activity
                this.updateTimeCardActivity(loginMemberId, createdTimeCard._id, companyId, {
                    status: "Time Card Created",
                    clockIn: createTimeCardDto.timeIn,
                    clockOut: createTimeCardDto.timeOut,
                });

                return new CreatedResponse({
                    id: createdTimeCard._id,
                    message: "Time card created successfully!",
                });
            }

            //If Failed to update piece of work
            await session.abortTransaction();
            session.endSession();
            throw new HttpException("TimeCard & Piece Work not created", HttpStatus.BAD_REQUEST);
        } catch (error: any) {
            await session.abortTransaction();
            session.endSession();
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async isDayOff(memberId: string, timeIn: Date) {
        const startOfDay = new Date(timeIn);
        startOfDay.setHours(0, 0, 0, 0);

        const endOfDay = new Date(timeIn);
        endOfDay.setHours(23, 59, 59, 999);

        const dayOffTimeCards = await this.timeCardModel.find({
            memberId,
            task: TaskTypeEnum.Day_Off,
            active: false,
            timeIn: {
                $gte: startOfDay,
                $lt: endOfDay,
            },
        });

        const isDayOff = dayOffTimeCards.length > 0;
        return isDayOff;
    }

    async updateTimeCard(
        userId: string,
        loginMemberId: string,
        companyId: string,
        updateTimeCardDto: UpdateTimeCardDto,
        timeCardPermission: number,
        symbol: string,
    ) {
        let session;
        try {
            // Starting session for Mongo transaction
            session = await this.connection.startSession();
            session.startTransaction();

            const [timeCard, member, crew, workTask] = await Promise.all([
                this.timeCardModel
                    .findOne({
                        _id: updateTimeCardDto._id,
                        deleted: false,
                    })
                    .exec(),
                this.memberModel
                    .findOne({
                        _id: updateTimeCardDto.memberId,
                        deleted: false,
                    })
                    .exec(),
                this.crewService.getMemberActiveCrewByMemberId(companyId, updateTimeCardDto.memberId),
                this.workTaskModel.findOne({ _id: updateTimeCardDto.task }),
            ]);

            if (!timeCard) throw new HttpException("TimeCard does not exist", HttpStatus.BAD_REQUEST);
            if (!member) throw new HttpException("Member does not exists", HttpStatus.BAD_REQUEST);
            if (
                symbol === "Foreman" &&
                (timeCard.status === StatusEnum.Approved || updateTimeCardDto.status === StatusEnum.Approved)
            )
                throw new HttpException("You are unauthorized to Approve this Card", HttpStatus.BAD_REQUEST);

            const crewManagerId = symbol === "Foreman" ? crew?.foremanId : crew?.managerId;
            await this.positionService.permissionCheck(
                userId,
                companyId,
                updateTimeCardDto.memberId, //not used in case of timecard permission check
                member.managerId,
                timeCardPermission,
                crewManagerId,
            );

            updateTimeCardDto.taskName = workTask.name;

            if (updateTimeCardDto.timeIn || updateTimeCardDto?.timeOut)
                await this.checkForDupeTimeCards(
                    updateTimeCardDto.memberId,
                    updateTimeCardDto._id,
                    updateTimeCardDto.timeIn,
                    updateTimeCardDto?.timeOut,
                );
            await this.timeCardModel.updateOne(
                { _id: updateTimeCardDto._id },
                {
                    $set: {
                        ...updateTimeCardDto,
                    },
                },
                { session },
            );

            // if not timeout dont create piece work
            if (!updateTimeCardDto.timeOut) {
                // ending session of mongodb
                await session.commitTransaction();
                session.endSession();
                return new OkResponse({ message: "Time card updated successfully!" });
            }

            //For extraTime calculation for Piece Work
            const extraTime = updateTimeCardDto?.work?.extraTime;
            if (extraTime) {
                const hrs = extraTime?.extraHrs ? Number(extraTime.extraHrs) : 0;
                const min = extraTime?.extraMin ? Number(extraTime.extraMin) : 0;
                updateTimeCardDto.work["extraWorkTime"] = roundTo2(hrs + min / 60);
            }

            // updating piecework
            const updatePieceWork: UpdatePieceWork = {
                timeCardId: updateTimeCardDto._id,
                memberId: updateTimeCardDto.memberId,
                date: updateTimeCardDto.date,
                task: updateTimeCardDto.task,
                taskName: workTask?.name,
                projectId: updateTimeCardDto.projectId,
                work: updateTimeCardDto.work,
                // notes: updateTimeCardDto.notes,
                timeIn: updateTimeCardDto.timeIn,
                timeOut: updateTimeCardDto.timeOut,
                createdBy: updateTimeCardDto.memberId,
                // managerNotes: updateTimeCardDto.managerNotes,
                removeLeadBonus: updateTimeCardDto.removeLeadBonus,
                allHourly: updateTimeCardDto.allHourly,
                foreman: crew?.foremanId === updateTimeCardDto.memberId,
            };

            const result = await this.pieceWorkService.upsertPieceWork(
                companyId,
                loginMemberId,
                updatePieceWork,
            );

            if (result.statusCode === 201) {
                // ending session of mongodb
                await session.commitTransaction();
                session.endSession();

                return new OkResponse({ message: "Time card updated successfully!" });
            }

            //If Failed to update piece of work
            await session.abortTransaction();
            session.endSession();
            throw new BadRequestException({ message: "Failed to updated Time card!" });
        } catch (error: any) {
            await session.abortTransaction();
            session.endSession();
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async permissionReq(
        userId: string,
        companyId: string,
        timeCardPermission: number,
        start: Date,
        end: Date,
        memberId?: string,
    ) {
        const member = await this.companyService.getMemberByUserId(userId, companyId);
        const memberManager = memberId ? await this.companyService.getManagerByMemberId(memberId) : undefined;
        const managedRequest =
            memberId && timeCardPermission === PermissionsEnum.Full
                ? {
                      companyId,
                      memberId: memberId,
                      deleted: false,
                      timeIn: { $gte: start, $lte: end },
                  }
                : timeCardPermission === PermissionsEnum.Full
                ? {
                      companyId,
                      deleted: false,
                      timeIn: { $gte: start, $lte: end },
                  }
                : memberId && timeCardPermission === PermissionsEnum.Managed
                ? {
                      $expr: {
                          $or: [
                              {
                                  $and: [
                                      {
                                          $eq: ["$managerId", member.data.member._id],
                                      },
                                      {
                                          $eq: ["$managerId", memberManager],
                                      },
                                  ],
                              },
                              {
                                  $and: [
                                      {
                                          $eq: ["$_id", memberId],
                                      },
                                      {
                                          $eq: ["$_id", member.data.member._id],
                                      },
                                  ],
                              },
                          ],
                      },
                  }
                : timeCardPermission === PermissionsEnum.Managed
                ? {
                      $expr: {
                          $or: [
                              {
                                  $eq: ["$managerId", member.data.member._id],
                              },
                              {
                                  $eq: ["$_id", member.data.member._id],
                              },
                          ],
                      },
                  }
                : memberId && timeCardPermission === PermissionsEnum.Self
                ? {
                      $and: [
                          { companyId },
                          {
                              $expr: {
                                  $and: [
                                      {
                                          $eq: [memberId, member.data.member._id],
                                      },
                                      {
                                          $eq: ["$memberId", member.data.member._id],
                                      },
                                  ],
                              },
                          },
                          { deleted: false },
                          { timeIn: { $gte: start, $lte: end } },
                      ],
                  }
                : {
                      companyId,
                      memberId: member.data.member._id,
                      deleted: false,
                      timeIn: { $gte: start, $lte: end },
                  };
        return managedRequest;
    }

    async getRecentTimeCards(
        userId: string,
        companyId: string,
        getMemberTimeCardsDto: GetMemberTimeCardsDto,
        timeCardPermission: number,
        startDate: Date,
        endDate: Date,
    ) {
        try {
            const start = new Date(startDate);
            const end = new Date(endDate);
            const managedRequest = await this.permissionReq(
                userId,
                companyId,
                timeCardPermission,
                start,
                end,
                getMemberTimeCardsDto.memberId,
            );
            const recentTimeCards =
                timeCardPermission == PermissionsEnum.Full
                    ? await this.timeCardModel.find(managedRequest).sort({ timeIn: -1 })
                    : timeCardPermission == PermissionsEnum.Managed
                    ? await this.timeCardModel.aggregate([
                          {
                              $match: {
                                  companyId,
                                  deleted: false,
                                  timeIn: { $gte: start, $lte: end },
                              },
                          },
                          {
                              $lookup: {
                                  from: "Member",
                                  pipeline: [
                                      {
                                          $match: managedRequest,
                                      },
                                  ],
                                  localField: "memberId",
                                  foreignField: "_id",
                                  as: "result",
                              },
                          },
                          {
                              $unwind: {
                                  path: "$result",
                                  preserveNullAndEmptyArrays: false,
                              },
                          },
                          {
                              $sort: {
                                  timeIn: -1,
                              },
                          },
                      ])
                    : timeCardPermission == PermissionsEnum.Self
                    ? await this.timeCardModel.find(managedRequest).sort({ timeIn: -1 })
                    : [];
            return new OkResponse({ recentTimeCards });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getMemberTimeCards(
        userId: string,
        companyId: string,
        getMemberTimeCardsDto: GetMemberTimeCardsDto,
        timeCardPermission: number,
    ) {
        try {
            const managedRequest = await this.permissionReq(
                userId,
                companyId,
                timeCardPermission,
                new Date(1),
                new Date(),
                getMemberTimeCardsDto.memberId,
            );
            const recentTimeCards =
                timeCardPermission == PermissionsEnum.Full
                    ? await this.timeCardModel.find(managedRequest).sort({ timeIn: -1 })
                    : timeCardPermission == PermissionsEnum.Managed
                    ? await this.timeCardModel.aggregate([
                          {
                              $match: {
                                  companyId,
                                  deleted: false,
                              },
                          },
                          {
                              $lookup: {
                                  from: "Member",
                                  pipeline: [
                                      {
                                          $match: managedRequest,
                                      },
                                  ],
                                  localField: "memberId",
                                  foreignField: "_id",
                                  as: "result",
                              },
                          },
                          {
                              $unwind: {
                                  path: "$result",
                                  preserveNullAndEmptyArrays: false,
                              },
                          },
                          {
                              $sort: {
                                  timeIn: -1,
                              },
                          },
                      ])
                    : timeCardPermission == PermissionsEnum.Self
                    ? await this.timeCardModel.find(managedRequest).sort({ timeIn: -1 })
                    : [];
            return new OkResponse({ recentTimeCards });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async takeDayOff(
        userId: string,
        companyId: string,
        takeDayOffDto: TakeDayOffDto,
        timeCardPermission: number,
    ) {
        try {
            const endOfDay = new Date(takeDayOffDto.timeIn);
            // set 'startOfDay' to start of day
            endOfDay.setDate(endOfDay.getDate() + 1);
            endOfDay.setMilliseconds(endOfDay.getMilliseconds() - 1);
            delete takeDayOffDto.timeOut;

            const member = await this.memberModel
                .findOne({
                    _id: takeDayOffDto.memberId,
                    deleted: false,
                })
                .exec();
            if (!member) throw new HttpException("Member does not exists", HttpStatus.BAD_REQUEST);

            const crew = await this.crewService.getMemberActiveCrewByMemberId(
                companyId,
                takeDayOffDto.memberId,
            );

            await this.positionService.permissionCheck(
                userId,
                companyId,
                takeDayOffDto.memberId, //not used in case of timecard permission check
                member.managerId,
                timeCardPermission,
                crew?.managerId,
            );
            const crewId = await this.crewService.getMemberActiveCrewByMemberId(
                companyId,
                takeDayOffDto.memberId,
            );
            const createdTimeCard = new this.timeCardModel({
                companyId,
                ...takeDayOffDto,
                timeOut: endOfDay,
                crewId: crewId?._id,
                projectId: "",
                projectPO: "",
                task: TaskTypeEnum.Day_Off,
                active: false,
                status: StatusEnum.Approved,
                ptoUsed: false,
            });
            await createdTimeCard.save();

            return new CreatedResponse({ message: "Day Off created successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async cancelDayOff(
        userId: string,
        companyId: string,
        cancelDayOffDto: CancelDayOffDto,
        timeCardPermission: number,
    ) {
        try {
            const timeCard = await this.timeCardModel
                .findOne({
                    _id: cancelDayOffDto.timeCardId,
                    deleted: false,
                })
                .exec();
            if (!timeCard) throw new HttpException("TimeCard does not exist", HttpStatus.BAD_REQUEST);
            const member = await this.memberModel
                .findOne({
                    _id: timeCard.memberId,
                    deleted: false,
                })
                .exec();
            if (!member) throw new HttpException("Member does not exists", HttpStatus.BAD_REQUEST);

            if (timeCard.ptoUsed)
                throw new HttpException("Cannot cancel a Day Off when PTO is used", HttpStatus.BAD_REQUEST);

            const crew = await this.crewService.getMemberActiveCrewByMemberId(companyId, timeCard.memberId);

            await this.positionService.permissionCheck(
                userId,
                companyId,
                timeCard.memberId, //not used in case of timecard permission check
                member.managerId,
                timeCardPermission,
                crew?.managerId,
            );
            await this.timeCardModel.deleteOne({ _id: cancelDayOffDto.timeCardId });

            return new NoContentResponse({ message: "Day Off cancelled successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async checkForDupeTimeCards(memberId: string, timeCardId: string, timeIn: Date, timeOut?: Date) {
        let overlappingCards;
        if (!timeOut) {
            // User is clocking in
            overlappingCards = await this.timeCardModel
                .find({
                    _id: { $ne: timeCardId },
                    memberId,
                    deleted: { $ne: true },
                    $or: [
                        {
                            // Case 1: Overlapping with an existing time card
                            $and: [{ timeIn: { $lt: timeIn } }, { timeOut: { $gt: timeIn } }],
                        },
                        {
                            // Case 2: Ongoing time card (timeOut is null)
                            $and: [{ timeIn: { $lte: timeIn } }, { timeOut: null }],
                        },
                    ],
                })
                .exec();
        } else {
            // User is clocking in and out
            overlappingCards = await this.timeCardModel
                .find({
                    _id: { $ne: timeCardId },
                    memberId,
                    deleted: { $ne: true },
                    $or: [
                        {
                            // Case 1: Overlap with existing time card
                            $and: [{ timeIn: { $lt: timeOut } }, { timeOut: { $gt: timeIn } }],
                        },
                        {
                            // Case 2: Fully contained in an existing card
                            $and: [{ timeIn: { $lte: timeIn } }, { timeOut: { $gte: timeOut } }],
                        },
                    ],
                })
                .exec();
        }
        if (overlappingCards.length) {
            if (overlappingCards.length) {
                const PODate = overlappingCards.map((t) => {
                    const timeInDate = new Date(t?.timeIn);
                    const timeOutDate = new Date(t?.timeOut);

                    // Extract date and times in the desired format
                    const date = `${
                        timeInDate.getMonth() + 1
                    }/${timeInDate.getDate()}/${timeInDate.getFullYear()}`;
                    const timeIn = timeInDate.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
                    const timeOut = timeOutDate.toLocaleTimeString([], {
                        hour: "2-digit",
                        minute: "2-digit",
                    });

                    return t?.timeOut
                        ? `${t.projectPO} - ${date} ${timeIn} -> ${timeOut}`
                        : `${t.projectPO} - ${date} ${timeIn}`;
                });

                throw new HttpException(
                    `Time you've entered overlap with existing -> ${PODate}`,
                    HttpStatus.BAD_REQUEST,
                );
            }
        }
        return true;
    }

    async deleteTimeCard(userId: string, cancelDayOffDto: CancelDayOffDto) {
        let session;
        try {
            // Starting session for Mongo transaction
            session = await this.connection.startSession();
            session.startTransaction();

            const timecard = await this.timeCardModel.updateOne(
                { _id: cancelDayOffDto.timeCardId, deleted: false },
                {
                    $set: {
                        deleted: true,
                    },
                },
                { session },
            );
            const work = await this.pieceWorkModel.updateOne(
                { timeCardId: cancelDayOffDto.timeCardId, deleted: false },
                {
                    $set: {
                        deleted: true,
                    },
                },
                { session },
            );

            if (work.modifiedCount > 0 || timecard.modifiedCount > 0) {
                // ending session of mongodb
                await session.commitTransaction();
                session.endSession();
                return new NoContentResponse({ message: "TimeCard deleted successfully!" });
            } else throw new BadRequestException({ message: "Failed to delete Time card!" });
        } catch (error: any) {
            await session.abortTransaction();
            session.endSession();
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getDeletedTimeCards(companyId: string, paginationDto: PaginationDto) {
        try {
            const limit = paginationDto.limit || 10;
            const offset = limit * (paginationDto.skip || 0);

            const deleteTimeCards = await this.timeCardModel
                .find(
                    { companyId, deleted: true },
                    { memberId: 1, taskName: 1, projectPO: 1, timeIn: 1, timeOut: 1, createdAt: 1 },
                    { limit, skip: offset },
                )
                .populate("memberId", "_id name", "Member")
                .sort({ timeIn: -1 });
            return new OkResponse({ deleteTimeCards });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async permDeleteTimeCard(companyId: string, timecardId: string) {
        try {
            // Check if the time card exists and is marked as deleted
            const timeCard = await this.timeCardModel.findOne({ _id: timecardId, companyId });
            if (!timeCard) throw new NotFoundException("Time card not found");
            if (!timeCard.deleted) throw new BadRequestException("This time card is not deleted");

            await this.timeCardModel.deleteOne({ _id: timecardId, companyId, deleted: true });
            return new OkResponse({ message: "Time card deleted permanently" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async restoreDeletedTimeCards(companyId: string, timeCardId: string) {
        try {
            // Check if the time card exists and is marked as deleted
            const timeCard = await this.timeCardModel.findOne({ _id: timeCardId, companyId, deleted: true });
            if (!timeCard) throw new NotFoundException("Time card not found");

            // Check for overlapping time cards within the specified time range
            try {
                await this.checkForDupeTimeCards(
                    timeCard.memberId,
                    timeCard._id,
                    timeCard.timeIn,
                    timeCard?.timeOut,
                );
            } catch (error) {
                // If the error is the overlapping time cards error, throw a custom message
                if (error instanceof HttpException && error.getStatus() === HttpStatus.BAD_REQUEST) {
                    throw new HttpException(
                        "Can’t restore! Time overlap with existing time cards.",
                        HttpStatus.BAD_REQUEST,
                    );
                }
                throw error; // Re-throw custom message error
            }

            // Restore the deleted time cards by updating the required fields
            await this.timeCardModel.updateOne(
                { _id: timeCardId, companyId, deleted: true },
                { $set: { deleted: false } },
            );
            await this.pieceWorkModel.updateOne(
                { timeCardId: timeCardId, deleted: true },
                {
                    $set: {
                        deleted: true,
                    },
                },
            );

            return new OkResponse({ message: "Time card restored successfully" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getDayProjects(dayProjects: any, dailyLogId: string, companyId: string) {
        const projects = [];
        const variables = await this.companySettingModel.findOne({ companyId, deleted: false });
        for (let i = 0; i < dayProjects.length; i++) {
            const projectTypes = await this.projectTypeModel.findOne({
                _id: dayProjects[i].type,
                companyId,
                deleted: false,
            });

            const obj: any = {};
            const num = dayProjects[i].num;
            obj.num = num;
            obj.oppId = dayProjects["oppId" + num];
            obj.tearOffDone = 0;
            obj.roofingDone = 0;
            if (projectTypes.typeReplacement) {
                obj.tearOffDone = Number(dayProjects["tearOffDone" + num]);
                obj.roofingDone = Number(dayProjects["roofingDone" + num]);
            } else {
                obj.percentDone = Number(dayProjects["percentDone" + num]);
            }
            obj.plywoodReplaced = Number(dayProjects["plywoodReplaced" + num]);
            obj.nonBillPly = Number(dayProjects["nonBillPly" + num]);
            obj.manHours = Number(dayProjects["manHours" + num]);
            obj.nonBillHours = Number(dayProjects["nonBillHours" + num]);
            obj.materialCosts = Number(dayProjects["materialCosts" + num]);
            obj.notes = dayProjects["notes" + num];
            if (
                obj.plywoodReplaced > 0 ||
                obj.manHours > 0 ||
                obj.nonBillHours > 0 ||
                obj.materialCosts > 0 ||
                obj.nonBillPly > 0
            ) {
                obj.extraWork = true;
            } else {
                obj.extraWork = false;
            }
            // calc extra work RR - all man-hour earned, total minus $25/sheet for plywood
            const plywoodRR = obj.plywoodReplaced * (32 * variables.plywoodRate - 25);
            const manHourRR = obj.manHours * variables.manHourRate;
            // add $10/sheet and $25/hr to labor pool for earned piecework
            const plywoodEarned = obj.plywoodReplaced * 10;
            const manHourEarned = (obj.manHours + obj.nonBillHours) * 25;

            if (true) {
                // Pull info to calculate percentages
                const order: any = await this.orderModel.findOne({ oppId: obj.oppId });
                const allWorkOrders = [];
                order.projects.forEach(({ workOrder }) => {
                    allWorkOrders.push(...workOrder);
                });

                const realRev = order.priceTotals.actRev;
                const travelFee = order.priceTotals.travelFee;
                const project: any = await this.projectModel.findOne({ _id: order.projectId });
                obj.type = project.projectType;

                const projectType = await this.projectTypeModel.findOne({
                    _id: project.projectType,
                    companyId,
                    deleted: false,
                });

                const logs = await this.dailyLogModel.find({
                    _id: { $ne: dailyLogId },
                    "projects.oppId": obj.oppId,
                    deleted: { $ne: true },
                });
                // calculate total done and percentages for roof-replacement
                if (projectType.typeReplacement) {
                    let tearOffCost = 0;
                    let roofingCost = 0;
                    for (let i = 0; i < allWorkOrders.length; i++) {
                        if (allWorkOrders[i].worker === "tearOff") {
                            tearOffCost += allWorkOrders[i].cost;
                        }
                        if (allWorkOrders[i].worker === "roofer") {
                            roofingCost += allWorkOrders[i].cost;
                        }
                    }
                    const totalCost = tearOffCost + roofingCost;
                    const tearOffTotalPercent = tearOffCost / totalCost;
                    const roofingTotalPercent = roofingCost / totalCost;
                    tearOffCost += travelFee * tearOffTotalPercent;
                    roofingCost += travelFee * roofingTotalPercent;
                    let toDone = 0;
                    let rDone = 0;
                    for (let i = 0; i < logs.length; i++) {
                        for (let j = 0; j < logs[i].projects.length; j++) {
                            if (logs[i].projects[j].oppId === obj.oppId) {
                                toDone += logs[i].projects[j].tearOffDone;
                                rDone += logs[i].projects[j].roofingDone;
                            }
                        }
                    }
                    const toTotal =
                        project?.customData?.roofSQ.rmvFlat +
                        project?.customData?.roofSQ.rmvLow +
                        project?.customData?.roofSQ.rmvSteep;
                    const rTotal =
                        project?.customData?.roofSQ.instFlat +
                        project?.customData?.roofSQ.instLow +
                        project?.customData?.roofSQ.instSteep;

                    const tearOffDayPercent = toTotal ? obj.tearOffDone / toTotal : 0;
                    const roofingDayPercent = rTotal ? obj.roofingDone / rTotal : 0;
                    const tDayEarned = tearOffDayPercent * tearOffCost;
                    // tDayEarned(tearOffDayPercent)(tearOffCost);
                    const rDayEarned = roofingDayPercent * roofingCost;
                    // rDayEarned(roofingDayPercent)(roofingCost);
                    obj.earned = roundTo2(tDayEarned + rDayEarned + plywoodEarned + manHourEarned);
                    obj.percentDone = roundTo2(
                        (tearOffDayPercent * tearOffTotalPercent + roofingDayPercent * roofingTotalPercent) *
                            100,
                    );
                    if (toDone + obj.tearOffDone > toTotal) return false;

                    if (rDone + obj.roofingDone > rTotal) return false;
                }
                // calculate total done and percentages for repairs and other project types
                else {
                    let pDone = 0;
                    for (let i = 0; i < logs.length; i++) {
                        for (let j = 0; j < logs[i].projects.length; j++) {
                            if (logs[i].projects[j].oppId === obj.oppId) {
                                pDone += logs[i].projects[j].percentDone;
                            }
                        }
                    }
                    if (pDone + obj.percentDone > 100) return false;

                    const totalCost = order.priceTotals.lSubtotal;
                    obj.earned = roundTo2((totalCost * obj.percentDone) / 100);
                }
                obj.realRev = roundTo2(realRev * (obj.percentDone / 100) + plywoodRR + manHourRR);
            }
            projects.push(obj);
        }
        return projects;
    }

    async validateProjects(dayProjects) {
        for (let i = 0; i < dayProjects.length; i++) {
            const num = dayProjects[i].num;
            const oppId = dayProjects[i].oppId;

            const projectTypes = await this.projectTypeModel.findOne({
                _id: dayProjects[i].type,
                // companyId,
                deleted: false,
            });

            //detect fields for roof-replacement project type
            if (projectTypes.typeReplacement) {
                const tearOffDone = dayProjects[i].tearOffDone;
                const roofingDone = dayProjects[i].roofingDone;
                if (
                    tearOffDone === "" ||
                    roofingDone === "" ||
                    !this.positiveNum(tearOffDone) ||
                    !this.positiveNum(roofingDone)
                )
                    return false;
            }
            //detect fields for roof-repair and other project type
            else {
                const percentDone = dayProjects[i].percentDone;
                if (percentDone === "" || !this.positiveNum(percentDone)) return false;
            }
            //detect fields for all project types
            const plywoodReplaced = dayProjects[i].plywoodReplaced;
            const manHours = dayProjects[i].manHours;
            const nonBillHours = dayProjects[i].nonBillHours;
            const materialCosts = dayProjects[i].materialCosts;
            if (
                oppId === "" ||
                plywoodReplaced === "" ||
                manHours === "" ||
                nonBillHours === "" ||
                materialCosts === "" ||
                !this.positiveNum(plywoodReplaced) ||
                !this.positiveNum(manHours) ||
                !this.positiveNum(nonBillHours) ||
                !this.positiveNum(materialCosts)
            )
                return false;
        }
        return true;
    }
    private positiveNum(value: number | string): boolean {
        const numericValue = typeof value === "string" ? parseFloat(value) : value;
        return numericValue > 0;
    }

    async getPitchesAndLayersForProject(companyId: string, oppId: string) {
        try {
            if (oppId === "companyDefaultPO") {
                return {
                    pitches: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20],
                    layers: [1, 2, 3, 4],
                };
            }

            const project: any = (
                await this.projectModel.aggregate([
                    {
                        $match: {
                            companyId,
                            deleted: { $ne: true },
                            orderId: { $exists: true },
                        },
                    },
                    {
                        $lookup: {
                            from: "Opportunity",
                            pipeline: [
                                {
                                    $match: { _id: oppId },
                                },
                                {
                                    $project: {
                                        modPieceWork: 1,
                                    },
                                },
                            ],
                            foreignField: "_id",
                            localField: "oppId",
                            as: "opp",
                        },
                    },
                    {
                        $unwind: {
                            path: "$opp",
                            preserveNullAndEmptyArrays: false,
                        },
                    },
                    {
                        $lookup: {
                            from: "ProjectType",
                            foreignField: "_id",
                            localField: "projectType",
                            as: "projectType",
                        },
                    },
                    {
                        $unwind: {
                            path: "$projectType",
                            preserveNullAndEmptyArrays: false,
                        },
                    },
                    {
                        $project: {
                            _id: 1,
                            companyId: 1,
                            "customData.reroofAreas": 1,
                            oppId: 1,
                            opp: 1,
                            projectType: 1,
                        },
                    },
                ])
            )[0];

            const tearWork = project?.opp?.modPieceWork ?? { pitches: [], layers: [] };

            if (project?.projectType?.typeReplacement) {
                project?.customData?.reroofAreas.map((reroofAreas) => {
                    tearWork.layers.push(+reroofAreas.layers);
                    tearWork.pitches.push(+reroofAreas.pitch);
                });
            }

            return tearWork;
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updateTimeCardActivity(loginMemberId: string, _id: string, companyId: string, data) {
        try {
            // const user = userId?._id ?? userId;

            const member = await this.memberModel
                .findOne({ _id: loginMemberId, company: companyId })
                .select("name");

            await this.timeCardModel.updateOne(
                { _id, companyId },
                {
                    $push: {
                        activities: {
                            name: member.name,
                            createdBy: loginMemberId,
                            createdAt: new Date().toISOString(),
                            data,
                        },
                    },
                },
            );

            return new OkResponse({ message: "TimeCard activity updated successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async forgotLogoutUpdate(companyId: string, memberId: string, startDate: Date) {
        try {
            const currentTimeCard = await this.timeCardModel
                .findOne({
                    memberId: memberId,
                    active: true,
                    deleted: { $ne: true },
                })
                .sort({ createdAt: -1 });
            const today = new Date(startDate);
            // today.setHours(0, 0, 0, 0);

            if (currentTimeCard && currentTimeCard.timeIn < today && currentTimeCard.active === true) {
                const timeOut = new Date(currentTimeCard.timeIn);
                timeOut.setHours(timeOut.getHours() + 1);
                const hrs = 1;

                const update = await this.timeCardModel.updateOne(
                    { _id: currentTimeCard._id },
                    {
                        $set: {
                            timeOut: timeOut,
                            hrs: hrs,
                            active: false,
                            status: "Unapproved",
                            deleted: false,
                            forgot: true,
                        },
                    },
                );

                if (update.modifiedCount > 0)
                    return new OkResponse({
                        timecardId: currentTimeCard._id,
                        timeOut,
                        message:
                            "You forgot to clock out yesterday! Please go to your dashboard and correct your time card and piece work.",
                    });
                else return new NoContentResponse();
            }
            return new NoContentResponse();
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async createPto(
        companyId: string,
        createdBy: string,
        { memberId, startDate, endDate }: CreatePtoDto,
        positionCheck: boolean,
    ) {
        try {
            if (!positionCheck) {
                throw new HttpException(
                    "You are unauthorized to perform this request",
                    HttpStatus.BAD_REQUEST,
                );
            }

            // check if any time card is present
            await this.checkForDupeTimeCards(memberId, undefined, startDate, endDate);

            const dayOffs = [];
            const updateConditions = [];
            const crewId = await this.crewService.getMemberActiveCrewByMemberId(companyId, memberId);
            const companyWeekend = await this.companySettingModel
                .findOne({ companyId })
                .select("weekEndDays");

            for (let i = new Date(startDate); i < endDate; i.setDate(i.getDate() + 1)) {
                const startOfDay = new Date(i);
                const endOfDay = new Date(i);

                // if its weekend then skiping it
                const weekend = isWeekend(companyWeekend.weekEndDays, startOfDay);
                if (weekend) continue;

                // set 'startOfDay' to start of day
                endOfDay.setDate(endOfDay.getDate() + 1);
                endOfDay.setMilliseconds(endOfDay.getMilliseconds() - 1);
                const isDayOff = await this.isDayOff(memberId, startOfDay);

                if (!isDayOff)
                    dayOffs.push({
                        companyId,
                        memberId,
                        hrs: 0,
                        timeIn: startOfDay,
                        timeOut: endOfDay,
                        createdBy,
                        crewId: crewId?._id,
                        projectId: "",
                        projectPO: "",
                        task: TaskTypeEnum.Day_Off,
                        active: false,
                        status: StatusEnum.Approved,
                        ptoUsed: true,
                    });
                else
                    updateConditions.push({
                        memberId,
                        task: TaskTypeEnum.Day_Off,
                        active: false,
                        timeIn: { $gte: startOfDay, $lt: endOfDay },
                    });
            }

            if (updateConditions.length > 0) {
                await this.timeCardModel.updateMany({ $or: updateConditions }, { $set: { ptoUsed: true } });
            }

            if (!dayOffs.length && !updateConditions.length) throw new BadRequestException("Can not add PTO");

            if (dayOffs.length > 0) await this.timeCardModel.insertMany(dayOffs);

            return new OkResponse({ message: dayOffs.length > 0 ? "PTO created" : "PTO updated" });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updatePtoStatus(companyId: string, timecardId: string, positionCheck: boolean) {
        try {
            if (!positionCheck) {
                throw new HttpException(
                    "You are unauthorized to perform this request",
                    HttpStatus.BAD_REQUEST,
                );
            }

            const result = await this.timeCardModel.updateOne(
                { _id: timecardId, companyId },
                { $set: { ptoUsed: false } },
            );

            if (result.modifiedCount > 0) return new OkResponse({ message: "PTO status updated" });
            else throw new BadRequestException({ message: "Failed to update PTO" });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getMemberPto(companyId: string, memberId: string) {
        try {
            const timeOffList = await this.timeCardModel
                .find({
                    companyId,
                    memberId,
                    task: TaskTypeEnum.Day_Off,
                })
                .select("timeIn ptoUsed")
                .sort({ timeIn: -1 });

            return new OkResponse({ timeOffList });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }
}
