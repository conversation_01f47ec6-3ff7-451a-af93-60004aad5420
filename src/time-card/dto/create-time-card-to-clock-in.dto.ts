import { ApiProperty } from "@nestjs/swagger";
import { IsString, IsNotEmpty, IsEnum, IsUUID } from "class-validator";
import { MemberDto } from "src/shared/dto/member.dto";
import { Transform } from "class-transformer";

export class CreateTimeCardToClockInDto extends MemberDto {
    @ApiProperty({ description: "Project id", required: true })
    // @IsUUID()
    @IsString()
    @IsNotEmpty()
    projectId: string;

    @ApiProperty({ description: "Project PO", required: true })
    @IsString()
    @IsNotEmpty()
    projectPO: string;

    @ApiProperty({ description: "Task", required: true })
    @IsString()
    @IsNotEmpty()
    task: string;

    @ApiProperty({ description: "Time In", required: true })
    // @IsString()
    @Transform(({ value }) => new Date(value))
    @IsNotEmpty()
    timeIn: Date;

    @ApiProperty({ description: "Created By", required: true })
    @IsUUID()
    @IsNotEmpty()
    createdBy: string;
}
