import { ApiProperty } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsNotEmpty, IsUUID } from "class-validator";

export class CreatePtoDto {
    @ApiProperty({ description: "member id" })
    @IsUUID()
    @IsNotEmpty()
    memberId: string;

    @ApiProperty({ description: "start date" })
    @Transform(({ value }) => new Date(value))
    @IsNotEmpty()
    startDate: Date;

    @ApiProperty({ description: "end date" })
    @Transform(({ value }) => new Date(value))
    @IsNotEmpty()
    endDate: Date;
}
