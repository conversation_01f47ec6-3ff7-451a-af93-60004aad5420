import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsOptional, IsUUID, IsBoolean } from "class-validator";
import { CreateTimeCardDto } from "./create-time-card.dto";

export class UpdateTimeCardDto extends CreateTimeCardDto {
    @ApiProperty({ description: "Time card Id" })
    @IsUUID()
    @IsNotEmpty()
    _id: string;

    @ApiProperty({ description: "Active" })
    @IsBoolean()
    @IsOptional()
    active: boolean;

    @ApiProperty({ description: "Deleted" })
    @IsBoolean()
    @IsOptional()
    deleted: boolean;
}
