import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import {
    IsString,
    IsNotEmpty,
    IsOptional,
    IsNumber,
    IsBoolean,
    IsEnum,
    IsDate,
    ValidateNested,
    IsArray,
} from "class-validator";
import { MemberDto } from "src/shared/dto/member.dto";
import { StatusEnum } from "../enum/status.enum";
import { Transform, Type } from "class-transformer";

class ExtraTimeDto {
    @IsOptional()
    @IsNumber()
    extraHrs: number;

    @IsOptional()
    @IsNumber()
    extraMin: number;
}

class WorkDto {
    @IsArray()
    @IsOptional()
    workDone: any[];

    @ValidateNested()
    @Type(() => ExtraTimeDto)
    @IsNotEmpty()
    extraTime: ExtraTimeDto;
}

export class CreateTimeCardDto extends MemberDto {
    @ApiProperty({ description: "Project id" })
    @IsString()
    @IsNotEmpty()
    projectId: string;

    @ApiProperty({ description: "Project PO" })
    @IsString()
    @IsNotEmpty()
    projectPO: string;

    @ApiProperty({ description: "Task" })
    @IsString()
    @IsNotEmpty()
    task: string;

    @IsOptional()
    taskName?: string;

    @ApiPropertyOptional({ description: "Hours" })
    @IsOptional()
    @IsNumber()
    // @IsNotEmpty()
    hrs?: number;

    @ApiProperty({ description: "Time In" })
    @IsDate()
    @Transform(({ value }) => new Date(value))
    @IsNotEmpty()
    timeIn: Date;

    @ApiPropertyOptional({ description: "Time Out" })
    @IsOptional()
    @IsDate()
    @Transform(({ value }) => new Date(value))
    // @IsNotEmpty()
    timeOut?: Date;

    @ApiProperty({ description: "Status" })
    @IsEnum(StatusEnum)
    @IsNotEmpty()
    status: StatusEnum;

    @ApiProperty({ description: "Date" })
    @IsDate()
    @Transform(({ value }) => new Date(value))
    @IsNotEmpty()
    date: Date;

    @ApiPropertyOptional({ description: "Work" })
    @ValidateNested()
    @Type(() => WorkDto)
    @IsOptional()
    work?: WorkDto;

    @ApiPropertyOptional({ description: "Notes" })
    @IsString()
    @IsOptional()
    notes?: string;

    @ApiPropertyOptional({ description: "Manager Notes" })
    @IsString()
    @IsOptional()
    managerNotes?: string;

    @ApiPropertyOptional({ description: "Lead" })
    @IsBoolean()
    @IsOptional()
    removeLeadBonus?: boolean;

    @ApiPropertyOptional({ description: "Sq earnings" })
    @IsNumber()
    @IsOptional()
    sqsEarnings?: number;

    @ApiPropertyOptional({ description: "Extra Earnings" })
    @IsNumber()
    @IsOptional()
    extrasEarnings?: number;

    @ApiPropertyOptional({ description: "Hourly earnings" })
    @IsNumber()
    @IsOptional()
    hourlyEarnings?: number;

    @ApiPropertyOptional({ description: "Hourly wages" })
    @IsNumber()
    @IsOptional()
    hourlyWages?: number;

    @ApiPropertyOptional({ description: "Earned" })
    @IsNumber()
    @IsOptional()
    earned?: number;

    @ApiPropertyOptional({ description: "allHourly" })
    @IsBoolean()
    @IsOptional()
    allHourly?: boolean;
}
