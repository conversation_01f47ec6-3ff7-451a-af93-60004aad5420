import { ApiProperty } from "@nestjs/swagger";
import { IsString, IsNotEmpty, IsEnum, IsUUID } from "class-validator";
import { StatusEnum } from "../enum/status.enum";

export class UpdateTimeCardToApproveOrRejectDto {
    @ApiProperty({ description: "Time card Id", required: true })
    @IsUUID()
    @IsNotEmpty()
    _id: string;

    // @ApiProperty({ description: "Company" })
    // @IsString()
    // @IsNotEmpty()
    // companyId: string;

    @ApiProperty({ description: "Status", required: true })
    @IsString()
    @IsNotEmpty()
    @IsEnum(StatusEnum)
    status: StatusEnum;
}
