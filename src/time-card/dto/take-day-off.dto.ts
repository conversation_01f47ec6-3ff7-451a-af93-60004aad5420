import { ApiProperty } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsString, IsNotEmpty, <PERSON>N<PERSON><PERSON> } from "class-validator";

export class TakeDayOffDto {
    // @ApiProperty({ description: "Company" })
    // @IsString()
    // @IsNotEmpty()
    // companyId: string;

    @ApiProperty({ description: "Member" })
    @IsString()
    @IsNotEmpty()
    memberId: string;

    @ApiProperty({ description: "Time In" })
    // @IsString()
    @Transform(({ value }) => new Date(value))
    @IsNotEmpty()
    timeIn: Date;

    @ApiProperty({ description: "Hrs" })
    @IsNumber()
    @IsNotEmpty()
    hrs: number;

    @ApiProperty({ description: "Time Out" })
    // @IsString()
    @Transform(({ value }) => new Date(value))
    @IsNotEmpty()
    timeOut: Date;

    @ApiProperty({ description: "Created By" })
    @IsString()
    @IsNotEmpty()
    createdBy: string;
}
