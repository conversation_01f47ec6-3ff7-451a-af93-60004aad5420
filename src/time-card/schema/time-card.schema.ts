import { Schema, SchemaFactory, Prop } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import { Document } from "mongoose";
import { StatusEnum } from "../enum/status.enum";
import { UUIDProp } from "src/shared/decorator/uuid.decorator";

export type TimeCardDocument = TimeCard & Document;

@Schema({ timestamps: true, id: false, collection: "TimeCard" })
export class TimeCard {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @UUIDProp()
    companyId: string;

    @UUIDProp()
    memberId: string;

    @Prop({ required: false })
    crewId: string;

    @Prop({ required: false })
    projectId: string;

    @Prop({ required: false })
    projectPO: string;

    @Prop({ required: true, type: String })
    task: string;

    @Prop({ required: false, type: String })
    taskName: string;

    @Prop({ required: true })
    timeIn: Date;

    @Prop({ default: true })
    active: boolean;

    @Prop({ default: StatusEnum.Unapproved, type: String, enum: StatusEnum })
    status: StatusEnum;

    @Prop({ required: false })
    timeOut: Date;

    @Prop({ required: false })
    hrs: number;

    @Prop({ required: false })
    notes: string;

    @Prop({ required: false })
    managerNotes: string;

    @Prop({ required: false })
    allHourly?: boolean;

    @UUIDProp()
    createdBy: string;

    @Prop({ default: false })
    deleted: boolean;

    @Prop({ required: false })
    activities?: any[];

    @Prop({ default: false })
    ptoUsed: boolean;

    @Prop()
    createdAt?: Date;

    @Prop()
    modifiedAt?: Date;
}

export const TimeCardSchema = SchemaFactory.createForClass(TimeCard);
