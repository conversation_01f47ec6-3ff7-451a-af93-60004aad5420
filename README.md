# NHR

## Description

[NHR](https://github.com/New-Heights-Roofing/nhrapp) Github

## Installation

```bash
$ yarn install
```

## Pre-Configuration

**package.json**

```json

- Windows:
  "start:dev": "set STAGE=dev& nest start --watch",

- Linux / Ubuntu / Mac:
  "start:dev": "STAGE=dev nest start --watch",

```
## Add Environment variables

Please refer [.env](./.env) for the env variables that is needed

## Running the app

```bash
$ yarn start:dev
```

## Swagger Docs

[http://localhost:3000/swagger](http://localhost:3001/api/)

## Test

```bash
# unit tests
$ yarn run test

# e2e tests
$ yarn run test:e2e

# test coverage
$ yarn run test:cov