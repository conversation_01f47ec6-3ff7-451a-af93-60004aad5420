// 10 missing order id opps
// 0b19f0de-7fe0-4f32-9173-9ecd0a249882 PETR0108 01 opp
// 3678137d-e835-416a-8dd5-24d233b60b78 DEMA1201 01 opp
// 78831a76-3a4b-4cc6-9a23-55dc350604e5 HARD1207 02 opp
// a8948c03-c5c1-48bc-be14-960d26e4e413 FARR1965 01 opp
// accf6f13-05f9-4fb7-8318-5b13f6a8d510 PLOE2122 01 opp
// b516bb84-fc15-47c8-8c2e-c5eabed30ecb DAAN1036 01 opp
// b982583f-aaa0-49b2-8935-71ad3a7858de COWA1223 01 opp
// dd1672c7-b2ba-432f-b1fc-858f71a04c40 SHYD0720 01 opp
// e8fda373-44ae-46f0-b6e5-815593847378 MCLE1479 01 opp
// f640f048-0dd2-4f71-8742-e6a2cf34e44e ROCK2124 01 opp

/**
 * Fixes orders that were created without running setAccepted
 * Finds all orders without corresponding orderId in opportunities and runs setAccepted for them
 * @returns Promise<OkResponse> with count of fixed orders
 */
// async fixMissingOrderIds() {
//     try {
//         // Find all orders
//         const orders = await this.orderModel.find({
//             deleted: false,
//             createdAt: { $gte: new Date("2025-04-29") },
//         });
//         console.log(orders.length, "orders");

//         // Find opportunities missing orderId that have corresponding orders
//         const missingOrderIdOpps = await this.opportunityModel.find({
//             _id: { $in: orders.map((o) => o.oppId) },
//             orderId: { $exists: false },
//             deleted: false,
//         });
//         console.log(missingOrderIdOpps.length, "missing order id opps");
//         // console.log(missingOrderIdOpps);
//         let fixedCount = 0;

//         // Process each missing order
//         for (const opp of missingOrderIdOpps) {
//             console.log(opp._id, opp.PO, opp.num, "opp");
//             const order = orders.find((o) => o.oppId.toString() === opp._id.toString());
//             //     console.log(order, "order");
//             if (order) {
//                 // Run setAccepted for this order
//                 await this.setAccepted(
//                     order.createdBy,
//                     "0f33b070-a7f2-43f3-8d07-54fdfd4378e3",
//                     order.createdBy, // Using createdBy as memberId
//                     {
//                         oppId: order.oppId,
//                         orderId: order._id,
//                         projectId: order.projectId,
//                         contactId: opp.contactId,
//                         soldValue: order.priceTotals.jobTotal,
//                         realRevValue: order.priceTotals.actRev, // Using soldValue as realRevValue
//                         discount: order.priceTotals.discount,
//                         paymentType: "Cash/Check/ACH",
//                     },
//                 );
//                 fixedCount++;
//             }
//         }

//         console.log({
//             message: `Fixed ${fixedCount} orders missing from opportunities`,
//             fixedCount,
//         });
//     } catch (error: any) {
//         console.log(error, "error");
//         if (error instanceof HttpException) {
//             throw error;
//         }
//         throw new InternalServerErrorException(error.message);
//     }
// }

// // score script
// $push: {
//     matList: {
//         $each: [addMaterialToOrderDto.matList],
//         $position: 0,
//     },
// },
// async updateScoreToOpp() {
//     try {
//         const opps = await this.opportunityModel
//             .find({ orderId: { $exists: true } })
//             .populate("orderId", "priceTotals", "Order");
//         console.log("started ");
//         const bulkUpdate = [];
//         for (let i = 0; i < opps.length; i++) {
//             const opp: any = opps[i];

//             const { jobTotal, mTotal, lTotal, commission } = opp.orderId?.priceTotals;
//             const budgetScore = profitScoreCalc(jobTotal, mTotal, lTotal, commission, opp?.financeFee);
//             const updateOp = {
//                 updateOne: {
//                     filter: {
//                         _id: opp._id,
//                     },
//                     update: { $set: { budgetScore } },
//                 },
//             };
//             bulkUpdate.push(updateOp);
//         }

//         console.log("calling update");

//         await this.opportunityModel.bulkWrite(bulkUpdate);

//         console.log("update completed");
//     } catch (error) {
//         console.log(error);
//     }
// }

// // position script

//     // TODO: to be removed
//     async updatePermissionNewSchema() {
//         try {
//             // const fullCrud = { read: true, write: true, update: true, delete: true };
//             // const noneCrud = { read: false, write: false, update: false, delete: false };
//             const fullCrud = { read: true, write: true };
//             const noneCrud = { read: false, write: false };

//             const allPos = await this.positionModel.find();
//             console.log("permissions updated");
//             const bulkUpdate = [];
//             allPos.forEach((p) => {
//                 const permissions = [];
//                 p.permissions.forEach((per: any) => {
//                     const crmGroup = ["sales", "lead", "operations", "client", "project", "crm"];
//                     const category = per.resource.includes("dashboard")
//                         ? "dashboard"
//                         : per.resource.includes("report")
//                         ? "report"
//                         : crmGroup.includes(per.resource)
//                         ? "crm"
//                         : "module";
//                     // : per.resource === "settings"
//                     // ? moduleNames.settings.settings

//                     const resName = per.resource;
//                     let name = resName;
//                     if (resName === "gpsTrack") name = "gps";
//                     if (resName === "client") name = "clients";
//                     if (resName === "lead") name = "leads";

//                     if (resName === "project") name = "projects";
//                     // if (resName === "crm") name = "gps";
//                     if (resName === "timeCard") name = "timeCards";
//                     if (resName === "lead dashboard") name = "leads";
//                     if (resName === "salesPerson dashboard") name = moduleNames.dashboard.oppsToDo;
//                     if (resName === "crewLead dashboard") name = moduleNames.dashboard.missingDailyLogs;
//                     if (resName === "crew dashboard") name = moduleNames.dashboard.missingDailyLogs;
//                     if (resName === "projectManager dashboard")
//                         name = moduleNames.dashboard.timecardsMissingUnapproved;
//                     // const matchedModuleName = this.getModuleName(resName, category, moduleNames);

//                     // console.log(matchedModuleName); // Outputs the closest match
//                     if (per.resource.includes("report")) {
//                         const data = permissions.find((pp) => pp.category === category);
//                         let arrData = [];
//                         if (resName === "project reports")
//                             arrData = [
//                                 {
//                                     name: moduleNames.reports.crewJobCost,
//                                     permissions: per.permissions[0],
//                                     crud: per.permissions[0] !== PermissionsEnum.None ? fullCrud : noneCrud,
//                                 },
//                                 {
//                                     name: moduleNames.reports.jobCost,
//                                     permissions: per.permissions[0],
//                                     crud: per.permissions[0] !== PermissionsEnum.None ? fullCrud : noneCrud,
//                                 },
//                                 {
//                                     name: moduleNames.reports.crewScoreboard,
//                                     permissions: per.permissions[0],
//                                     crud: per.permissions[0] !== PermissionsEnum.None ? fullCrud : noneCrud,
//                                 },
//                             ];
//                         if (resName === "operations reports")
//                             arrData = [
//                                 {
//                                     name: moduleNames.reports.weeklyProduction,
//                                     permissions: per.permissions[0],
//                                     crud: per.permissions[0] !== PermissionsEnum.None ? fullCrud : noneCrud,
//                                 },
//                                 {
//                                     name: moduleNames.reports.production,
//                                     permissions: per.permissions[0],
//                                     crud: per.permissions[0] !== PermissionsEnum.None ? fullCrud : noneCrud,
//                                 },
//                                 {
//                                     name: moduleNames.reports.weeklyProject,
//                                     permissions: per.permissions[0],
//                                     crud: per.permissions[0] !== PermissionsEnum.None ? fullCrud : noneCrud,
//                                 },
//                             ];
//                         if (resName === "commission reports")
//                             arrData = [
//                                 {
//                                     name: moduleNames.reports.commission,
//                                     permissions: per.permissions[0],
//                                     crud: per.permissions[0] !== PermissionsEnum.None ? fullCrud : noneCrud,
//                                 },
//                             ];
//                         if (resName === "sales reports")
//                             arrData = [
//                                 {
//                                     name: moduleNames.reports.salesPerson,
//                                     permissions: per.permissions[0],
//                                     crud: per.permissions[0] !== PermissionsEnum.None ? fullCrud : noneCrud,
//                                 },
//                                 {
//                                     name: moduleNames.reports.customSales,
//                                     permissions: per.permissions[0],
//                                     crud: per.permissions[0] !== PermissionsEnum.None ? fullCrud : noneCrud,
//                                 },
//                                 {
//                                     name: moduleNames.reports.conversion,
//                                     permissions: per.permissions[0],
//                                     crud: per.permissions[0] !== PermissionsEnum.None ? fullCrud : noneCrud,
//                                 },
//                             ];
//                         if (resName === "payroll reports")
//                             arrData = [
//                                 {
//                                     name: moduleNames.reports.crewPayroll,
//                                     permissions: per.permissions[0],
//                                     crud: per.permissions[0] !== PermissionsEnum.None ? fullCrud : noneCrud,
//                                 },
//                                 {
//                                     name: moduleNames.reports.nonCrewPayroll,
//                                     permissions: per.permissions[0],
//                                     crud: per.permissions[0] !== PermissionsEnum.None ? fullCrud : noneCrud,
//                                 },
//                             ];
//                         if (resName === "company reports")
//                             arrData = [
//                                 {
//                                     name: moduleNames.reports.weeklySales,
//                                     permissions: per.permissions[0],
//                                     crud: per.permissions[0] !== PermissionsEnum.None ? fullCrud : noneCrud,
//                                 },
//                                 {
//                                     name: moduleNames.reports.kpi,
//                                     permissions: per.permissions[0],
//                                     crud: per.permissions[0] !== PermissionsEnum.None ? fullCrud : noneCrud,
//                                 },
//                             ];
//                         if (!data)
//                             permissions.push({
//                                 category,
//                                 resources: [...arrData],
//                             });
//                         else data.resources.push(...arrData);
//                     } else {
//                         const data = permissions.find((pp) => pp.category === category);

//                         if (!data) {
//                             if (category === "crm")
//                                 permissions.push({
//                                     category,
//                                     resources: [
//                                         {
//                                             name: moduleNames.crm.opportunity,
//                                             permissions: per.permissions[0],
//                                             crud:
//                                                 per.permissions[0] !== PermissionsEnum.None
//                                                     ? fullCrud
//                                                     : noneCrud,
//                                         },
//                                         {
//                                             name: moduleNames.crm.opportunityInfo,
//                                             permissions: per.permissions[0],
//                                             crud:
//                                                 per.permissions[0] !== PermissionsEnum.None
//                                                     ? fullCrud
//                                                     : noneCrud,
//                                         },
//                                     ],
//                                 });
//                             else
//                                 permissions.push({
//                                     category,
//                                     resources: [
//                                         {
//                                             name,
//                                             permissions: per.permissions[0],
//                                             crud:
//                                                 per.permissions[0] !== PermissionsEnum.None
//                                                     ? fullCrud
//                                                     : noneCrud,
//                                         },
//                                     ],
//                                 });
//                         } else {
//                             const moduledata = data.resources.find((r) => r.name === name);

//                             if (!moduledata)
//                                 data.resources.push({
//                                     name,
//                                     permissions: per.permissions[0],
//                                     crud: per.permissions[0] !== PermissionsEnum.None ? fullCrud : noneCrud,
//                                 });
//                         }
//                     }
//                 });
//                 // if (p.companyId === "0f33b070-a7f2-43f3-8d07-54fdfd4378e3") console.log(p.position, permissions);

//                 const updateOp = {
//                     updateOne: {
//                         filter: {
//                             _id: p._id,
//                         },
//                         update: { $set: { permissions } },
//                     },
//                 };

//                 bulkUpdate.push(updateOp);
//             });
//             console.log("calling update");

//             await this.positionModel.bulkWrite(bulkUpdate);

//             console.log("update completed");
//         } catch (error) {
//             console.log(error);
//         }
//     }
//     // TODO: to be removed
//     async updatePermission() {
//         await this.positionModel.updateMany(
//             {
//                 // $or: [{ position: "Owner" }, { position: "Admin" }],
//             },
//             [
//                 {
//                     $set: {
//                         // Update position field
//                         position: {
//                             $switch: {
//                                 branches: [
//                                     { case: { $eq: ["$position", "Owner"] }, then: "CEO" },
//                                     {
//                                         case: { $eq: ["$position", "Admin"] },
//                                         then: "Administrative Assistant",
//                                     },
//                                 ],
//                                 default: "$position",
//                             },
//                         },
//                         // Remove "settings" category from permissions
//                         permissions: {
//                             $filter: {
//                                 input: "$permissions",
//                                 as: "category",
//                                 cond: { $ne: ["$$category.category", "settings"] },
//                             },
//                         },
//                     },
//                 },
//                 {
//                     $set: {
//                         // Remove duplicates from "dashboard" resources
//                         permissions: {
//                             $map: {
//                                 input: "$permissions",
//                                 as: "category",
//                                 in: {
//                                     $mergeObjects: [
//                                         "$$category",
//                                         {
//                                             resources: {
//                                                 $reduce: {
//                                                     input: "$$category.resources",
//                                                     initialValue: [],
//                                                     in: {
//                                                         $cond: [
//                                                             {
//                                                                 $in: ["$$this.name", "$$value.name"],
//                                                             },
//                                                             "$$value",
//                                                             { $concatArrays: ["$$value", ["$$this"]] },
//                                                         ],
//                                                     },
//                                                 },
//                                             },
//                                         },
//                                     ],
//                                 },
//                             },
//                         },
//                     },
//                 },
//             ],
//         );
//         console.log("permissions updated");
//     }

//     // order mat script
//         // TODO: to be removed
//         async updateOrderScript() {
//             try {
//                 const allOrder = await this.orderModel.find({ _id: "775fa3a4-1732-494d-85cd-c0f2b58a500e" });

//                 for (let i = 0; i < allOrder.length; i++) {
//                     const order = allOrder[i];
//                     const matList = [];
//                     order.matList.forEach((mat) => {
//                         const projectId =
//                             mat?.projectId && mat?.projectId === ""
//                                 ? order.projects[0].projectId
//                                 : mat?.projectId;
//                         delete mat?.ProjectType;
//                         matList.push({ ...mat, projectId });
//                     });

//                     console.log(matList);
//                     await this.orderModel.updateOne(
//                         { _id: order._id },
//                         {
//                             $set: { matList },
//                         },
//                     );
//                 }
//             } catch (e) {
//                 console.log(e);
//             }
//         }

// // change order script
// async updateChangeOrderValues(): Promise<void> {
//     // Fetch all opportunities that need migration
//     const opportunities = await this.opportunityModel.find({
//         changeOrders: { $exists: true },
//         // _id: "4e704bc5-de0d-488b-af16-94a3a6763068",
//     });

//     console.log("HLLOO");

//     // Create bulk operations
//     const bulkOperations = opportunities.map((opp: any) => {
//         // Calculate the new changeOrderValue
//         const changeOrderValue =
//             opp.changeOrders?.reduce((total, order) => {
//                 if (order.deleted) {
//                     // Add the value first and then subtract it
//                     return total + (order.jobCost || 0) - (order.jobCost || 0);
//                 }
//                 // Add the value if not deleted
//                 return total + (order.jobCost || 0);
//             }, 0) || 0;

//         // Calculate the new changeOrderRRValue
//         const changeOrderRRValue =
//             opp.changeOrders?.reduce((total, order) => {
//                 if (order.deleted) {
//                     // Add first, then subtract for deleted orders
//                     return (
//                         total +
//                         ((order.jobCost || 0) - (order.materials || 0)) -
//                         ((order.jobCost || 0) - (order.materials || 0))
//                     );
//                 }
//                 // Add directly for non-deleted orders
//                 return total + ((order.jobCost || 0) - (order.materials || 0));
//             }, 0) || 0;

//         console.log({ changeOrderValue, changeOrderRRValue });
//         // Return the bulkWrite operation
//         return {
//             updateOne: {
//                 filter: { _id: opp._id },
//                 update: {
//                     $set: {
//                         changeOrderValue,
//                         changeOrderRRValue,
//                     },
//                 },
//             },
//         };
//     });

//     console.log(bulkOperations.length);
//     // Execute bulkWrite
//     if (bulkOperations.length > 0) {
//         await this.opportunityModel.bulkWrite(bulkOperations);
//         console.log("Migration completed: Updated all opportunities.");
//     } else {
//         console.log("No opportunities found to update.");
//     }
// }

// async removeSettingsFromModule() {
//     try {
//         const bulkOperations = [];
//         const documents = await this.positionModel.find({ "permissions.category": "module" });

//         documents.forEach((doc) => {
//             const updatedPermissions = doc.permissions.map((permission) => {
//                 if (permission.category === "module") {
//                     const filteredResources = permission.resources.filter(
//                         (resource) => resource.name !== "settings",
//                     );
//                     return { ...permission, resources: filteredResources };
//                 }
//                 return permission;
//             });

//             bulkOperations.push({
//                 updateOne: {
//                     filter: { _id: doc._id },
//                     update: { $set: { permissions: updatedPermissions } },
//                 },
//             });
//         });

//         if (bulkOperations.length > 0) {
//             const result = await this.positionModel.bulkWrite(bulkOperations);
//             console.log("Bulk update result:", result);
//         } else {
//             console.log("No documents require updates.");
//         }
//     } catch (error) {
//         console.error("Error during bulk update:", error);
//     }
// }
///////////////////////////////////////////////////////////////////////////////////

// 6th feb 2025
// async updateCheckpoint() {
//     try {
//         const data = await this.opportunityModel.updateMany(
//             {
//                 $or: [
//                     { "checkpointActivity.oppDate": { $exists: false } },
//                     { "checkpointActivity.newLeadDate": { $exists: false } },
//                     { "checkpointActivity.oppDate.created": null },
//                     { "checkpointActivity.newLeadDate.created": null },
//                 ],
//             },
//             [
//                 {
//                     $set: {
//                         "checkpointActivity.oppDate.created": "$oppDate",
//                         "checkpointActivity.newLeadDate.created": "$newLeadDate",
//                     },
//                 },
//             ],
//         );
//         console.log(data);

//         const data3 = await this.opportunityModel.updateMany(
//             { "checkpointActivity.saleDate": { $exists: false }, saleDate: { $exists: true } },
//             [
//                 {
//                     $set: {
//                         "checkpointActivity.saleDate.created": "$saleDate",
//                     },
//                 },
//             ],
//         );
//         console.log(data3);

//         const data4 = await this.opportunityModel.updateMany(
//             {
//                 "checkpointActivity.presentationDate": { $exists: false },
//                 presentationDate: { $exists: true },
//             },
//             [
//                 {
//                     $set: {
//                         "checkpointActivity.presentationDate.created": "$presentationDate",
//                     },
//                 },
//             ],
//         );
//         console.log(data4);

//         const data5 = await this.opportunityModel.updateMany(
//             {
//                 "checkpointActivity.needsAssessmentDate": { $exists: false },
//                 needsAssessmentDate: { $exists: true },
//             },
//             [
//                 {
//                     $set: {
//                         "checkpointActivity.needsAssessmentDate.created": "$needsAssessmentDate",
//                     },
//                 },
//             ],
//         );
//         console.log(data5);
//     } catch (e) {
//         console.error("Error updating checkpoint:", e);
//         throw e; // Re-throw the error for better debugging
//     }
// }
