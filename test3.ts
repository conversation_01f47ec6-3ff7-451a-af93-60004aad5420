// async updateScript() {
//     try {
//         const data = await this.orderModel.updateMany(
//             {}, // Match all documents
//             [
//                 {
//                     $set: {
//                         matList: {
//                             $filter: {
//                                 input: "$matList",
//                                 as: "item",
//                                 cond: {
//                                     $not: {
//                                         $and: [
//                                             { $eq: ["$$item.amount", 0] },
//                                             { $eq: ["$$item.cost", 0] },
//                                             {
//                                                 $or: [
//                                                     { $not: { $ifNull: ["$$item._id", false] } }, // _id is null/missing
//                                                     { $eq: ["$$item._id", ""] }, // _id is empty string
//                                                     { $eq: ["$$item._id", "none"] }, // _id is "none"
//                                                 ],
//                                             },
//                                         ],
//                                     },
//                                 },
//                             },
//                         },
//                     },
//                 },
//             ],
//         );
//         console.log({ data }, "removed extra data from matList");
//     } catch (error) {
//         console.log(error);
//     }
// }

// async migrateData() {
//     try {
//         // Connect to MongoDB
//         console.log("Connected to MongoDB");

//         // Get all OpportunityMedia documents
//         const opportunityMedias = await this.opportunityMediaModel.find();

//         console.log(`Found ${opportunityMedias.length} OpportunityMedia documents to migrate`);

//         let migratedCount = 0;

//         // Process each OpportunityMedia document
//         for (const oppMedia of opportunityMedias) {
//             // Process each image in the images array
//             for (const image of oppMedia.images) {
//                 // Create a new Media document for each image
//                 const newMedia = new this.mediaModel({
//                     _id: image._id,
//                     companyId: oppMedia.companyId || "0f33b070-a7f2-43f3-8d07-54fdfd4378e3",
//                     oppId: oppMedia.oppId,
//                     name: image.name,
//                     mimetype: image.mimetype,
//                     url: image.url,
//                     thumbnail: image.thumbnail,
//                     tags: image.tags || [],
//                     location: image.location,
//                     formId: image.formId,
//                     oppFormId: image.oppFormId,
//                     createdBy: image.createdBy,
//                     createdAt: image.createdAt,
//                     updatedAt: oppMedia.updatedAt,
//                 });

//                 // Save the new Media document
//                 await newMedia.save();
//                 migratedCount++;
//             }

//             // Optional: Delete the old OpportunityMedia document if needed
//             // await OpportunityMedia.deleteOne({ _id: oppMedia._id });
//         }

//         console.log(`Migration completed. ${migratedCount} Media documents created.`);
//     } catch (error) {
//         console.error("Migration failed:", error);
//     } finally {
//         // Disconnect from MongoDB
//         // await mongoose.disconnect();
//         console.log("Disconnected from MongoDB");
//     }
// }

// new updated code bellow

// async updateScript() {
//     try {
//         const data = await this.orderModel.updateMany(
//             {}, // Match all documents
//             [
//                 {
//                     $set: {
//                         matList: {
//                             $filter: {
//                                 input: "$matList",
//                                 as: "item",
//                                 cond: {
//                                     $not: {
//                                         $and: [
//                                             { $eq: ["$$item.amount", 0] },
//                                             { $eq: ["$$item.cost", 0] },
//                                             {
//                                                 $or: [
//                                                     { $not: { $ifNull: ["$$item._id", false] } }, // _id is null/missing
//                                                     { $eq: ["$$item._id", ""] }, // _id is empty string
//                                                     { $eq: ["$$item._id", "none"] }, // _id is "none"
//                                                 ],
//                                             },
//                                         ],
//                                     },
//                                 },
//                             },
//                         },
//                     },
//                 },
//             ],
//         );
//         console.log({ data }, "removed extra data from matList");
//     } catch (error) {
//         console.log(error);
//     }
// }

// async migrateData() {
//     try {
//         // Connect to MongoDB
//         console.log("Connected to MongoDB");

//         // Get all OpportunityMedia documents
//         const opportunityMedias = await this.opportunityMediaModel.find();

//         console.log(`Found ${opportunityMedias.length} OpportunityMedia documents to migrate`);

//         let migratedCount = 0;
//         console.log(opportunityMedias.length);

//         // Process each OpportunityMedia document
//         for (const oppMedia of opportunityMedias) {
//             // Process each image in the images array
//             if (oppMedia?.images?.length)
//                 for (const image of oppMedia.images) {
//                     // Create a new Media document for each image
//                     const newMedia = new this.mediaModel({
//                         _id: image._id,
//                         companyId: oppMedia.companyId, // || "0f33b070-a7f2-43f3-8d07-54fdfd4378e3",
//                         oppId: oppMedia.oppId,
//                         name: image.name,
//                         mimetype: image.mimetype,
//                         url: image.url,
//                         thumbnail: image?.thumbnail,
//                         tags: image.tags || [],
//                         location: image?.location,
//                         formId: image?.formId,
//                         oppFormId: image?.oppFormId,
//                         createdBy: image.createdBy,
//                         createdAt: image.createdAt,
//                         updatedAt: oppMedia.updatedAt,
//                     });

//                     // Save the new Media document
//                     await newMedia.save();
//                     migratedCount++;
//                 }

//             // Optional: Delete the old OpportunityMedia document if needed
//             // await OpportunityMedia.deleteOne({ _id: oppMedia._id });
//         }

//         console.log(`Migration completed. ${migratedCount} Media documents created.`);
//     } catch (error) {
//         console.error("Migration failed:", error);
//     } finally {
//         // Disconnect from MongoDB
//         // await mongoose.disconnect();
//         console.log("Disconnected from MongoDB");
//     }
// }

// async typeMigrate() {
//     try {
//         // overrideTotalCost
//         const allOPP = await this.opportunityModel.updateMany(
//             { changeOrders: { $exists: true, $ne: [] } },
//             {
//                 $set: {
//                     "changeOrders.$[].overrideTotalCost": true,
//                 },
//             },
//         );
//         console.log({ allOPP });

//         const data = await this.projectTypeModel.updateMany({}, { $set: { minTravelPpl: 1 } });
//         console.log({ data });
//         const data2 = await this.priceModel.updateMany({}, [
//             {
//                 $set: {
//                     "projectType.minTravelPpl": {
//                         $cond: [{ $gte: ["$createdAt", new Date("2025-01-03T01:00:00Z")] }, 1, 2],
//                     },
//                 },
//             },
//         ]);
//         console.log({ data2 });
//         const data3 = await this.companyModel.updateMany({}, { $set: { deleted: false } });
//         console.log({ data3 });
//     } catch (error) {
//         console.log(error);
//     }
// }
