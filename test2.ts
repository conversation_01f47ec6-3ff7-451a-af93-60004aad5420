// async updateScoreToOpp() {
//     try {
//         const opps = await this.opportunityModel
//             .find({ orderId: { $exists: true } })
//             .populate("orderId", "priceTotals", "Order");
//         console.log("started ");
//         const bulkUpdate = [];
//         for (let i = 0; i < opps.length; i++) {
//             const opp: any = opps[i];

//             const { jobTotal, mTotal, lTotal, commission } = opp.orderId?.priceTotals;
//             const budgetScore = profitScoreCalc(jobTotal, mTotal, lTotal, commission, opp?.financeFee);
//             const updateOp = {
//                 updateOne: {
//                     filter: {
//                         _id: opp._id,
//                     },
//                     update: { $set: { budgetScore } },
//                 },
//             };
//             bulkUpdate.push(updateOp);
//         }

//         console.log("calling update");

//         await this.opportunityModel.bulkWrite(bulkUpdate);

//         console.log("update completed");
//     } catch (error) {
//         console.log(error);
//     }
// }

// async crewProjectReport(companyId: string, oppId: string) {
//     try {
//         const [pwSettingAllData, workTaskAllData, timeCards, oppData, variables, crews] =
//             await Promise.all([
//                 this.pieceWorkSettingModel
//                     .find({
//                         companyId: companyId,
//                     })
//                     .sort({ usesPitch: -1, isExtra: 1, sequence: 1 }),
//                 this.workTaskModel.find({ companyId }),
//                 this.crewService.getTimeCardsForQuery({
//                     projectId: oppId, //in time card opp is project
//                     deleted: { $ne: true },
//                     active: { $ne: true },
//                 }),
//                 this.opportunityModel
//                     .findOne({ _id: oppId, companyId })
//                     .populate("clientId", "firstName lastName", "Client")
//                     .populate("stage", "stageGroup", "CrmStage")
//                     .populate("orderId", null, "Order"),
//                 this.companySettingModel.findOne({ companyId }),
//                 this.crewModel.aggregate([
//                     {
//                         $match: {
//                             companyId,
//                         },
//                     },
//                     {
//                         $lookup: {
//                             from: "CrewMember",
//                             localField: "_id",
//                             foreignField: "crewId",
//                             as: "members",
//                         },
//                     },
//                 ]),
//             ]);

//         // using any type for undefined value in schema
//         const opp: any = oppData;

//         const timeIds = timeCards.map((t) => t._id);

//         const pieceWork = await this.pieceWorkModel
//             .find({
//                 // projectId: roofProject.oppId,
//                 timeCardId: { $in: timeIds },
//                 companyId,
//                 deleted: { $ne: true },
//             })
//             ;

//         timeCards.map((card: any) => {
//             const work = pieceWork.find((wrk) => wrk.timeCardId === card._id);
//             card.work = work;
//         });

//         // Initialize the map and sequence in a single reduce function
//         const { pwSettingMap, pwSettingSequence } = pwSettingAllData.reduce(
//             (acc, setting) => {
//                 const { _id, workTask, name } = setting;

//                 // Create the map of settings
//                 acc.pwSettingMap[_id] = setting;

//                 // Initialize the sequence for each workTask
//                 if (!acc.pwSettingSequence[workTask]) {
//                     acc.pwSettingSequence[workTask] = { currentSequence: 1 };
//                 } else {
//                     acc.pwSettingSequence[workTask].currentSequence++;
//                 }

//                 // Assign the current sequence to the task name
//                 acc.pwSettingSequence[workTask][name] = acc.pwSettingSequence[workTask].currentSequence;

//                 return acc;
//             },
//             { pwSettingMap: {}, pwSettingSequence: {} },
//         );

//         // Remove the tracking property (currentSequence) from the final structure
//         const finalPwSettingSequence = Object.entries(pwSettingSequence).reduce(
//             (acc, [workTask, data]) => {
//                 acc[workTask] = Object.fromEntries(
//                     Object.entries(data).filter(([key]) => key !== "currentSequence"),
//                 );
//                 return acc;
//             },
//             {},
//         );

//         const workTaskSequencemapping = workTaskAllData.reduce((map, workTask) => {
//             map[workTask._id] = workTask?.sequence;
//             return map;
//         }, {});

//         timeCards.forEach((card) => {
//             card?.work?.work?.workDone.forEach((pieceWork) => {
//                 const setting = pwSettingMap[pieceWork.id];
//                 if (setting) {
//                     // Adding name and unit to pieceWork
//                     pieceWork.name = setting.name;
//                     pieceWork.unit = setting.unit.split(" (")[0];
//                 }
//             });
//         });

//         const workerArray = timeCards.map((card) => card.memberId);
//         const workers: any = await this.memberModel.aggregate([
//             {
//                 $match: {
//                     company: companyId,
//                     _id: { $in: workerArray },
//                 },
//             },
//             {
//                 $lookup: {
//                     from: "Compensation",
//                     localField: "_id",
//                     foreignField: "memberId",
//                     as: "wage",
//                 },
//             },
//             {
//                 $unwind: {
//                     path: "$wage",
//                     preserveNullAndEmptyArrays: true,
//                 },
//             },
//             {
//                 $addFields: {
//                     name: {
//                         $cond: {
//                             if: {
//                                 $and: [
//                                     { $ifNull: ["$preferredName", false] },
//                                     { $ne: ["$preferredName", ""] },
//                                 ],
//                             },
//                             then: {
//                                 $concat: [
//                                     "$preferredName",
//                                     " ",
//                                     {
//                                         $arrayElemAt: [{ $split: ["$name", " "] }, 1],
//                                     },
//                                 ],
//                             },
//                             else: "$name",
//                         },
//                     },
//                 },
//             },
//         ]);

//         let hrs = 0;
//         let cost = 0;
//         let totalLeadBonus = 0;
//         let pieceWorkData = [];
//         let dates = [];
//         // Crew Lead Bonus
//         let leadIndex = 0;

//         const crewLeadEarningsArray = [];
//         // Create an array to hold days the crew lead was off and won't get the lead bonus

//         const totalTasks = {};
//         const laborReport = [];
//         for (const [idx, worker] of workers.entries()) {
//             worker.pieceWork = [];
//             worker.pwHourly = 0;
//             worker.earned = 0;
//             worker.hrs = 0;
//             worker.leadEarnings = 0;
//             worker.pieceWorkEarned = 0;

//             // let salaryHourly = 0;

//             const salaryDates: { workTask: string; date: string }[] = [];
//             const travelDates: { workTask: string; date: string }[] = [];

//             const hoursPerDay = [];
//             const workerCards = timeCards.filter((card) => card.memberId === worker._id);
//             worker.cards = [];

//             for (const card of workerCards) {
//                 //to calculate total task type hrs on project
//                 const workTaskData = workTaskAllData.find((workTask) => workTask._id === card.task);
//                 const taskType = workTaskData?.name.toLowerCase();
//                 totalTasks[taskType] = (totalTasks[taskType] || 0) + (card?.hrs || 0);

//                 worker.currWage = findCurrentWage(worker?.wage?.wageHistory, new Date(card.timeIn));

//                 worker.salaried =
//                     worker.currWage && worker?.currWage?.wageInterval === WageIntervalEnum.Year
//                         ? true
//                         : false;
//                 worker.hourlyWage =
//                     worker.currWage && worker?.currWage?.wageInterval === WageIntervalEnum.Hour
//                         ? worker?.currWage?.wageAmount
//                         : 0;

//                 // worker salary
//                 // const salaryHourly = worker.hourlyWage
//                 // worker.salaried
//                 //     ? worker.currWage.wageAmount / (52 * 5 * 8)
//                 //     : worker.hourlyWage || 0;

//                 let crewLead = false;
//                 let activeCrew;
//                 for (const crew of crews) {
//                     if (!crew.members.length) continue;
//                     // Figure out which crew this person is a member of
//                     for (const member of crew.members) {
//                         if (
//                             worker._id === member.memberId &&
//                             member.startDate <= card.timeIn &&
//                             (!member.removeDate || member.removeDate > card.timeIn)
//                         ) {
//                             activeCrew = crew;
//                         }
//                     }
//                 }

//                 const crewLeadId = findCrewLeadId(activeCrew, new Date(card.timeIn));

//                 // Is this person the crew lead of that day?
//                 crewLead = worker._id === crewLeadId;

//                 if (crewLead) leadIndex = idx;
//                 const work = card.work;
//                 const hrs = card?.hrs || 0;
//                 worker.pwHourly += work?.hourlyEarnings || 0;
//                 if (work?.work) {
//                     const workTaskId = work.task;
//                     const earned = Number(work.earned) || 0;
//                     const { hourlyEarnings, hourlyWages, sqsEarnings, extrasEarnings } = work;
//                     const { workDone, extraWorkTime } = work?.work;
//                     // const hourlyWages = work.hourlyWages;

//                     if (
//                         worker.pieceWork.find((item) => item.workTask === `Total Hourly Earning`) ===
//                         undefined
//                     ) {
//                         worker.pieceWork.push({
//                             workTask: `Total Hourly Earning`,
//                             pieceWork: 0,
//                         });
//                     }
//                     // Update Total Hourly Earning
//                     worker.pieceWork.find((item) => item.workTask === `Total Hourly Earning`).pieceWork +=
//                         hourlyWages;

//                     // Initialize variables to store the key for extra work time
//                     let extraWorkKey = "Extra Hour";

//                     // If workTaskData.pieceWork is not available, update extraWorkKey
//                     if (workTaskData && !workTaskData?.pieceWork) {
//                         extraWorkKey = "Other Hour";
//                     }

//                     // Find index of existing workTaskId or add a new entry
//                     let existingTaskIndex = worker.pieceWork.findIndex(
//                         (item) => item.workTask === workTaskId,
//                     );

//                     if (existingTaskIndex === -1) {
//                         existingTaskIndex = worker.pieceWork.length;
//                         worker.pieceWork.push({
//                             workTask: workTaskId,
//                             taskName: workTaskData?.name,
//                             extraHours: extraWorkTime || 0,
//                             hrs,
//                             hourlyEarnings,
//                             pieceWork: [],
//                             salaryHourly: hourlyWages,
//                             // roundTo2(hrs * salaryHourly),
//                         });
//                     } else {
//                         worker.pieceWork[existingTaskIndex].extraHours += extraWorkTime;
//                         worker.pieceWork[existingTaskIndex].hourlyEarnings += hourlyEarnings;
//                         worker.pieceWork[existingTaskIndex].hrs += hrs;
//                         worker.pieceWork[existingTaskIndex].salaryHourly += hourlyWages;
//                         // roundTo2(hrs * salaryHourly);
//                     }

//                     // Iterate through workDone and update pieceWork
//                     workDone.forEach((pwSetting) => {
//                         // calculating piece work cost for individual
//                         const { id, earned } = pwSetting;

//                         const existingObjectIndex = worker.pieceWork[
//                             existingTaskIndex
//                         ].pieceWork.findIndex((obj) => obj.name === pwSetting.name);

//                         if (existingObjectIndex !== -1) {
//                             // If an object with the same name exists
//                             const existingObject =
//                                 worker.pieceWork[existingTaskIndex].pieceWork[existingObjectIndex];

//                             // Check if pitch and layers already exist
//                             const existingPitchLayersIndex = existingObject.workDone.findIndex(
//                                 (obj) => obj.pitch === pwSetting.pitch && obj.layers === pwSetting.layers,
//                             );

//                             if (existingPitchLayersIndex !== -1) {
//                                 // If both pitch and layers exist, update their squares
//                                 existingObject.workDone[existingPitchLayersIndex].value +=
//                                     Number(pwSetting.amount) || 0;
//                                 existingObject.workDone[existingPitchLayersIndex].cost += earned;
//                             } else {
//                                 // If either pitch or layers don't exist, push new data
//                                 const dataToPush = {
//                                     pitch: pwSetting.pitch,
//                                     layers: pwSetting.layers,
//                                     value: Number(pwSetting.amount),
//                                     unit: pwSetting.unit,
//                                     cost: earned,
//                                 };
//                                 existingObject.workDone.push(dataToPush);
//                             }
//                         } else {
//                             // If the object doesn't exist, push a new object with name, value, and workDone array
//                             const dataToPush = {};
//                             dataToPush["cost"] = earned;

//                             if (pwSetting.pitch !== undefined) {
//                                 dataToPush["pitch"] = pwSetting.pitch;
//                             }

//                             if (pwSetting.layers !== undefined) {
//                                 dataToPush["layers"] = pwSetting.layers;
//                             }

//                             if (pwSetting.amount !== undefined) {
//                                 dataToPush["value"] = Number(pwSetting.amount);
//                             }

//                             if (pwSetting.unit !== undefined) {
//                                 dataToPush["unit"] = pwSetting.unit;
//                             }

//                             // Check if any of the properties exist before pushing the new object
//                             if (Object.keys(dataToPush).length > 0) {
//                                 worker.pieceWork[existingTaskIndex].pieceWork.push({
//                                     name: pwSetting.name,
//                                     workDone: [dataToPush],
//                                 });
//                             }
//                         }
//                     });

//                     // Add extraWorkTime to the appropriate key
//                     if (worker.pieceWork.find((item) => item.workTask === extraWorkKey) === undefined) {
//                         // If "Extra Hour" or "Other Hour" not present, initialize with 0
//                         worker.pieceWork.push({
//                             workTask: extraWorkKey,
//                             pieceWork: 0,
//                         });
//                     }
//                     // Update extra work time
//                     worker.pieceWork.find((item) => item.workTask === extraWorkKey).pieceWork +=
//                         extraWorkTime;

//                     // Ensure "Other Hour" and "Extra Hour" have a default value of 0
//                     if (
//                         worker.pieceWork.find((item) => item.workTask === extraWorkKey).pieceWork ===
//                         undefined
//                     ) {
//                         worker.pieceWork.find((item) => item.workTask === extraWorkKey).pieceWork = 0;
//                     }

//                     if (
//                         worker.pieceWork.find(
//                             (item) => item.workTask === `${workTaskData?.name} Cost`,
//                         ) === undefined
//                     ) {
//                         // If `${workTaskData?.name} Cost` not present, initialize with 0
//                         worker.pieceWork.push({
//                             workTask: `${workTaskData?.name} Cost`,
//                             pieceWork: 0,
//                         });
//                     }
//                     // Update earned cost
//                     worker.pieceWork.find(
//                         (item) => item.workTask === `${workTaskData?.name} Cost`,
//                     ).pieceWork += earned;
//                 }

//                 if (!crewLead && workTaskData?.showOnScoreboard) {
//                     const leadData = workers.find((w) => w._id === crewLeadId);
//                     const crewPieceWork =
//                         findCurrentWage(leadData?.wage?.wageHistory, new Date(card.timeIn))
//                             ?.crewPieceWork || 0;
//                     const item = {
//                         date: card.timeIn,
//                         lead: crewLeadId,
//                         workTask: workTaskData._id,
//                         earned: card.removeFromLead
//                             ? 0
//                             : (work && roundTo2(work.earned * crewPieceWork)) || 0,
//                     };
//                     crewLeadEarningsArray.push(item);
//                 }
//                 hoursPerDay.push({
//                     date: shortenDate(card.timeIn),
//                     hours: card.hrs,
//                 });

//                 // not using weekend check as the salary/hourly is always counted on weekends as well
//                 // const weekend = isWeekend(variables.weekEndDays, card.timeIn);

//                 // Only count salary on weekdays
//                 // if (!weekend) salaryDates.push(shortenDate(card.timeIn));

//                 salaryDates.push({ workTask: workTaskData._id, date: shortenDate(card.timeIn) });

//                 // Only count travel on Roofing & Tear Off
//                 if (
//                     workTaskData?.pieceWork &&
//                     workTaskData.addTravel &&
//                     card.work?.work?.extraWorkTime !== card.hrs
//                 ) {
//                     travelDates.push({ workTask: workTaskData._id, date: shortenDate(card.timeIn) });
//                 }
//                 worker.earned += work?.earned || 0;
//                 worker.hrs += card.hrs;
//                 dates.push(shortenDate(card.timeIn));

//                 worker.cards.push(card);
//             }

//             // Calculate salary and travel
//             const uniqueSDates = dedupePwObjects(salaryDates);
//             const uniqueTDates = dedupePwObjects(travelDates);

//             const indicesToDelete: number[] = [];
//             for (let i = 0; i < worker.pieceWork.length; i++) {
//                 const pieceWorkItem = worker.pieceWork[i];
//                 const { workTask, taskName, extraHours, hrs, hourlyEarnings, salaryHourly } =
//                     pieceWorkItem;
//                 let { pieceWork } = pieceWorkItem;
//                 pieceWork = JSON.parse(JSON.stringify(pieceWork));
//                 // workTask: '5bb5336b-c884-440c-a8d5-89b75a60adca',
//                 // taskName: 'Meetings',
//                 // extraHours: 0,
//                 // hrs: 0.25,
//                 // hourlyEarnings: 0,
//                 // pieceWork: [],
//                 // salaryHourly: 4.25

//                 if (workTask === "Total Hourly Earning") {
//                     const existingTaskIndex = pieceWorkData.findIndex(
//                         (item) => item.workTask === workTask,
//                     );
//                     if (existingTaskIndex !== -1) {
//                         // If the workTask already exists, update its value
//                         pieceWorkData[existingTaskIndex].pieceWork = roundTo2(
//                             pieceWorkData[existingTaskIndex].pieceWork + pieceWork,
//                         );
//                     } else {
//                         // If the workTask doesn't exist, add a new entry
//                         pieceWorkData.push({
//                             workTask,
//                             pieceWork: roundTo2(pieceWork),
//                         });
//                     }
//                     indicesToDelete.push(i);
//                 } else if (workTask === "Extra Hour" || workTask === "Other Hour") {
//                     const existingTaskIndex = pieceWorkData.findIndex(
//                         (item) => item.workTask === workTask,
//                     );
//                     if (existingTaskIndex !== -1) {
//                         // If the workTask already ex ists, update its value
//                         pieceWorkData[existingTaskIndex].pieceWork = roundTo2(
//                             pieceWorkData[existingTaskIndex].pieceWork + pieceWork,
//                         );
//                     } else {
//                         // If the workTask doesn't exist, add a new entry
//                         pieceWorkData.push({
//                             workTask,
//                             pieceWork: roundTo2(pieceWork),
//                         });
//                     }
//                     indicesToDelete.push(i);
//                 } else if (/Cost$/.test(workTask)) {
//                     const existingTaskIndex = pieceWorkData.findIndex(
//                         (item) => item.workTask === workTask,
//                     );
//                     if (existingTaskIndex !== -1) {
//                         // If the workTask already exists, update its value
//                         pieceWorkData[existingTaskIndex].pieceWork += pieceWork;
//                     } else {
//                         // If the workTask doesn't exist, add a new entry
//                         pieceWorkData.push({
//                             workTask,
//                             pieceWork,
//                         });
//                     }
//                     if (pieceWork) {
//                         worker.pieceWorkEarned += roundTo2(pieceWork) > 0 ? roundTo2(pieceWork) : 0;
//                     }
//                 } else {
//                     // Check if the workTask already exists in pieceWorkData
//                     const existingTaskIndex = pieceWorkData.findIndex(
//                         (item) => item.workTask === workTask,
//                     );
//                     if (existingTaskIndex !== -1) {
//                         // If the workTask already exists, update its data
//                         const existingPieceWork = pieceWorkData[existingTaskIndex].pieceWork;
//                         pieceWorkData[existingTaskIndex].extraHours += extraHours;
//                         pieceWorkData[existingTaskIndex].hrs += hrs;
//                         pieceWorkData[existingTaskIndex].hourlyEarnings += hourlyEarnings;
//                         pieceWorkData[existingTaskIndex].salaryHourly += salaryHourly;
//                         // Iterate over pieceWork of the worker
//                         for (const pieceWorkObj of pieceWork) {
//                             const existingObjectIndex = existingPieceWork.findIndex(
//                                 (obj) => obj.name === pieceWorkObj.name,
//                             );

//                             if (existingObjectIndex !== -1) {
//                                 // If the name already exists, check for pitch and layers
//                                 const existingObject = existingPieceWork[existingObjectIndex];

//                                 for (const pwObj of pieceWorkObj.workDone) {
//                                     const existingWorkDoneIndex = existingObject.workDone.findIndex(
//                                         (existingWorkDone) =>
//                                             (pwObj.pitch === undefined ||
//                                                 existingWorkDone.pitch === pwObj.pitch) &&
//                                             (pwObj.layers === undefined ||
//                                                 existingWorkDone.layers === pwObj.layers),
//                                     );
//                                     if (pwObj.pitch === undefined && pwObj.layers === undefined) {
//                                         // If both pitch and layers are undefined in pwObj
//                                         if (existingWorkDoneIndex !== -1) {
//                                             existingObject.workDone[existingWorkDoneIndex].value +=
//                                                 pwObj.value;
//                                         }
//                                     } else {
//                                         // If either pitch or layers are defined in pwObj
//                                         if (existingWorkDoneIndex !== -1) {
//                                             // If existingWorkDoneIndex is found, update the value
//                                             existingObject.workDone[existingWorkDoneIndex].value +=
//                                                 pwObj.value;
//                                         } else {
//                                             // If existingWorkDoneIndex is not found, push new data
//                                             const dataToPush = {};

//                                             if (pwObj.pitch !== undefined) {
//                                                 dataToPush["pitch"] = pwObj.pitch;
//                                             }

//                                             if (pwObj.layers !== undefined) {
//                                                 dataToPush["layers"] = pwObj.layers;
//                                             }

//                                             if (pwObj.value !== undefined) {
//                                                 dataToPush["value"] = pwObj.value;
//                                             }

//                                             if (pwObj.unit !== undefined) {
//                                                 dataToPush["unit"] = pwObj.unit;
//                                             }

//                                             // If either pitch or layers don't exist, push new data
//                                             if (Object.keys(dataToPush).length > 0) {
//                                                 existingObject.workDone.push(dataToPush);
//                                             }
//                                         }
//                                     }
//                                 }
//                             } else {
//                                 // Otherwise, push the new object
//                                 existingPieceWork.push(pieceWorkObj);
//                             }
//                         }
//                     } else {
//                         // If the workTask doesn't exist, add a new entry
//                         pieceWorkData.push({
//                             workTask,
//                             taskName,
//                             extraHours,
//                             hrs,
//                             hourlyEarnings,
//                             salaryHourly,
//                             pieceWork: pieceWork.map((obj) => ({
//                                 ...obj,
//                                 workDone: obj.workDone,
//                             })), // Make a copy of pieceWork
//                         });
//                     }
//                 }
//             }
//             console.log(uniqueSDates, "3");

//             // Salary calc
//             worker.salary = 0;

//             // worker.dates = uniqueSDates.map(({ workTask, date }) => {
//             //     //getting wage
//             //     // worker.currWage = findCurrentWage(worker?.wage?.wageHistory, new Date(date));
//             //     // worker.salaried =
//             //     //     worker.currWage && worker?.currWage?.wageInterval === WageIntervalEnum.Year
//             //     //         ? true
//             //     //         : false;
//             //     // worker.hourlyWage =
//             //     //     worker.currWage && worker?.currWage?.wageInterval === WageIntervalEnum.Hour
//             //     //         ? worker?.currWage?.wageAmount
//             //     //         : 0;
//             //     // // worker salary
//             //     // const salaryHourly = worker.salaried
//             //     //     ? worker.currWage.wageAmount / (52 * 5 * 8)
//             //     //     : worker.hourlyWage || 0;
//             //     // workTask,
//             //     // taskName,
//             //     // extraHours,
//             //     // hrs,
//             //     // hourlyEarnings,
//             //     // salaryHourly,

//             //     const obj: any = {};
//             //     obj.date = date;
//             //     obj.hours = 0;
//             //     obj.regHours = 0;
//             //     obj.otHours = 0;
//             //     let daySalary = 0;
//             //     hoursPerDay.map((day) => {
//             //         if (day.date === date) obj.hours += day.hours;
//             //     });
//             //     if (obj.hours > 8) {
//             //         obj.regHours = 8;
//             //         obj.otHours = obj.hours - 8;
//             //     } else {
//             //         obj.regHours = obj.hours;
//             //     }
//             //     // if (worker.salaried) daySalary = roundTo2(obj.regHours * salaryHourly);
//             //     // if (worker.hourlyWage)
//             //     //     daySalary = roundTo2(obj.regHours * salaryHourly + obj.otHours * salaryHourly);
//             //     worker.salary += daySalary;
//             //     worker.earned += daySalary;
//             //     const existingTaskIndex = pieceWorkData.findIndex((item) => item.workTask === workTask);

//             //     if (existingTaskIndex !== -1) {
//             //         pieceWorkData[existingTaskIndex] = {
//             //             ...pieceWorkData[existingTaskIndex],
//             //             cost: (pieceWorkData[existingTaskIndex].cost || 0) + daySalary, // Add daySalary to the current cost
//             //             salaryEarned: (pieceWorkData[existingTaskIndex].salaryEarned || 0) + daySalary, // Update salaryEarned with daySalary
//             //         };
//             //     }
//             //     return obj;
//             // });
//             // console.log(pieceWorkData, "4");

//             // Travel calc
//             worker.travel = 0;
//             worker.travelDays = 0;
//             if (opp?.distance && Number(opp?.distance) >= 20) {
//                 const pwFee = roundTo2(Number(opp.distance) * variables.travelFee);
//                 const salFee = roundTo2(Number(opp.distance) * (variables.travelFee / 2));

//                 for (const date of uniqueTDates) {
//                     const dayTravel = worker.salaried || worker.hourlyWage ? salFee : pwFee;
//                     worker.travel += dayTravel;
//                     worker.travelDays++;
//                     worker.earned += dayTravel;

//                     const existingTaskIndex = pieceWorkData.findIndex(
//                         (item) => item.workTask === date.workTask,
//                     );

//                     if (existingTaskIndex !== -1) {
//                         pieceWorkData[existingTaskIndex] = {
//                             ...pieceWorkData[existingTaskIndex],
//                             cost: (pieceWorkData[existingTaskIndex].cost || 0) + dayTravel, // Add dayTravel to the current cost
//                             travelEarned:
//                                 (pieceWorkData[existingTaskIndex].travelEarned || 0) + dayTravel, // Update travelEarned with dayTravel
//                         };
//                     }
//                 }
//             }
//             // console.log(pieceWorkData, "5");

//             // Remove items from the array based on the tracked indices
//             for (let i = indicesToDelete.length - 1; i >= 0; i--) {
//                 worker.pieceWork.splice(indicesToDelete[i], 1);
//             }

//             // Create a map of costs by task name
//             const costMap = worker.pieceWork.reduce((acc, { workTask, pieceWork }) => {
//                 if (workTask.endsWith(" Cost")) {
//                     acc[workTask.replace(" Cost", "")] = pieceWork;
//                 }
//                 return acc;
//             }, {});

//             // Update, filter, and add hours to the worker.pieceWork
//             worker.pieceWork = worker.pieceWork
//                 .filter(({ workTask }) => !workTask.endsWith(" Cost"))
//                 .map((item) => {
//                     const updatedItem =
//                         isUUID(item.workTask) && item.taskName in costMap
//                             ? {
//                                   ...item,
//                                   cost: costMap[item.taskName] + (item?.salaryHourly || 0),
//                               }
//                             : item;

//                     return updatedItem;
//                 });

//             worker.pieceWork.forEach(({ workTask, pieceWork }) => {
//                 if (isUUID(workTask)) {
//                     pieceWork.sort((a, b) => {
//                         const sequenceA =
//                             finalPwSettingSequence[workTask]?.[a.name] ?? Number.MAX_SAFE_INTEGER;
//                         const sequenceB =
//                             finalPwSettingSequence[workTask]?.[b.name] ?? Number.MAX_SAFE_INTEGER;
//                         return sequenceA - sequenceB;
//                     });
//                 }
//             });

//             worker.pieceWork.sort((a, b) => {
//                 const sequenceA = workTaskSequencemapping[a.workTask] ?? Number.MAX_SAFE_INTEGER;
//                 const sequenceB = workTaskSequencemapping[b.workTask] ?? Number.MAX_SAFE_INTEGER;
//                 return sequenceA - sequenceB;
//             });
//             worker.pieceWorkEarned -= worker.pwHourly;
//             hrs += worker.hrs;
//             cost += worker.earned;
//             laborReport.push(worker);
//         }

//         // Apply crew lead earnings to proper person
//         crewLeadEarningsArray.map((item) => {
//             workers.map((worker) => {
//                 if (worker._id === item.lead) {
//                     worker.leadEarnings += item.earned;
//                     worker.earned += item.earned;
//                     totalLeadBonus += roundTo2(item.earned) || 0;
//                     cost += item.earned;

//                     const existingTaskIndex = pieceWorkData.findIndex(
//                         (pw) => pw.workTask === item.workTask,
//                     );

//                     if (existingTaskIndex !== -1) {
//                         pieceWorkData[existingTaskIndex] = {
//                             ...pieceWorkData[existingTaskIndex],
//                             cost: (pieceWorkData[existingTaskIndex].cost || 0) + roundTo2(item.earned), // Add leadEarned to the current cost
//                             leadEarned:
//                                 (pieceWorkData[existingTaskIndex].leadEarned || 0) +
//                                 roundTo2(item.earned), // Update leadEarned with daySalary
//                         };
//                     }
//                 }
//             });
//         });

//         const order = opp?.orderId;
//         const allWorkOrders = [];
//         const estimateBudget = {};
//         let totalEstimateBudget = 0;
//         if (order) {
//             order?.projects?.forEach(({ workOrder }) => {
//                 allWorkOrders.push(...workOrder);
//             });

//             allWorkOrders.forEach((workOrder) => {
//                 const { cost, ttlHours, worker } = workOrder;
//                 if (!estimateBudget[worker]) {
//                     estimateBudget[worker] = { cost: 0, hours: 0 };
//                 }
//                 estimateBudget[worker].cost += cost;
//                 estimateBudget[worker].hours += ttlHours;
//                 totalEstimateBudget += cost;
//             });
//             // adding travel amount in equal ratio for esimated task
//             Object.keys(estimateBudget).forEach((key) => {
//                 estimateBudget[key].cost += roundTo2(
//                     order?.priceTotals?.travelFee * (estimateBudget[key].cost / totalEstimateBudget),
//                 );
//                 //TODO: hrs to check
//                 estimateBudget[key].hours += roundTo2(
//                     (order?.priceTotals?.travelFee * (estimateBudget[key].cost / totalEstimateBudget)) /
//                         order?.priceTotals?.travelHrlyRate,
//                 );
//             });
//         }

//         // Create a map of costs by task name
//         const costMap = pieceWorkData.reduce((acc, { workTask, pieceWork }) => {
//             if (workTask.endsWith(" Cost")) {
//                 acc[workTask.replace(" Cost", "")] = pieceWork;
//             }
//             return acc;
//         }, {});

//         // adding missing peicework task from order if not on timecards
//         Object.keys(estimateBudget).forEach((key) => {
//             const data = pieceWorkData.find((p) => p.workTask === key);
//             if (!data)
//                 pieceWorkData.push({
//                     workTask: key,
//                     taskName: workTaskAllData.find((w) => w._id === key)?.name || "",
//                     extraHours: 0,
//                     pieceWork: [],
//                     salaryEarned: 0,
//                     leadEarned: 0,
//                     cost: 0,
//                     estimateCost: estimateBudget[key]?.cost || 0,
//                     estimateHrs: estimateBudget[key]?.hours || 0,
//                     hrs: 0,
//                 });
//         });

//         // Update, filter, and add hours to the pieceWorkData
//         pieceWorkData = pieceWorkData
//             .filter(({ workTask }) => !workTask.endsWith(" Cost"))
//             .map((item) => {
//                 const updatedItem =
//                     isUUID(item.workTask) && item.taskName in costMap
//                         ? {
//                               ...item,
//                               cost: (item.cost || 0) + costMap[item.taskName],
//                               estimateCost: estimateBudget[item.workTask]?.cost || 0,
//                               estimateHrs: estimateBudget[item.workTask]?.hours || 0,
//                           }
//                         : item;

//                 const taskNameLower = updatedItem.taskName?.toLowerCase();
//                 return taskNameLower && taskNameLower in totalTasks
//                     ? { ...updatedItem, hrs: totalTasks[taskNameLower] }
//                     : updatedItem;
//             });

//         // Sort pieceWork data based on the sequences defined in cleanPwSettingSequence
//         pieceWorkData.forEach(({ workTask, pieceWork }) => {
//             if (isUUID(workTask)) {
//                 pieceWork.sort((a, b) => {
//                     const sequenceA =
//                         finalPwSettingSequence[workTask]?.[a.name] ?? Number.MAX_SAFE_INTEGER;
//                     const sequenceB =
//                         finalPwSettingSequence[workTask]?.[b.name] ?? Number.MAX_SAFE_INTEGER;
//                     return sequenceA - sequenceB;
//                 });
//             }
//         });

//         pieceWorkData.sort((a, b) => {
//             const sequenceA = workTaskSequencemapping[a.workTask] ?? Number.MAX_SAFE_INTEGER;
//             const sequenceB = workTaskSequencemapping[b.workTask] ?? Number.MAX_SAFE_INTEGER;
//             return sequenceA - sequenceB;
//         });

//         dates = dedupeArray(dates);
//         const days = dates.length;
//         let customPo;
//         if (!oppData?.PO) {
//             customPo = await this.customProjectModel.findOne({ _id: oppId });
//         }
//         return {
//             hrs,
//             cost,
//             totalLeadBonus,
//             pieceWorkData,
//             leadIndex,
//             totalTasks,
//             dates,
//             days,
//             laborReport,
//             po: oppData?.PO || customPo?.PO || "",
//             num: oppData?.num || customPo?.num || "",
//             allWorkOrders,
//         };
//     } catch (error) {
//         if (error instanceof HttpException) {
//             throw error;
//         }
//         throw new InternalServerErrorException(error.message);
//     }
// }

/////////////////////////////////////

// async crewProjectReport(companyId: string, oppId: string) {
//     try {
//         const [pwSettingAllData, workTaskAllData, timeCards, oppData, variables, crews] =
//             await Promise.all([
//                 this.pieceWorkSettingModel
//                     .find({
//                         companyId: companyId,
//                     })
//                     .sort({ usesPitch: -1, isExtra: 1, sequence: 1 }),
//                 this.workTaskModel.find({ companyId }),
//                 this.crewService.getTimeCardsForQuery({
//                     projectId: oppId, //in time card opp is project
//                     deleted: { $ne: true },
//                     active: { $ne: true },
//                 }),
//                 this.opportunityModel
//                     .findOne({ _id: oppId, companyId })
//                     .populate("clientId", "firstName lastName", "Client")
//                     .populate("stage", "stageGroup", "CrmStage")
//                     .populate("orderId", null, "Order"),
//                 this.companySettingModel.findOne({ companyId }),
//                 this.crewModel.aggregate([
//                     {
//                         $match: {
//                             companyId,
//                         },
//                     },
//                     {
//                         $lookup: {
//                             from: "CrewMember",
//                             localField: "_id",
//                             foreignField: "crewId",
//                             as: "members",
//                         },
//                     },
//                 ]),
//             ]);

//         // using any type for undefined value in schema
//         const opp: any = oppData;

//         const timeIds = timeCards.map((t) => t._id);

//         const pieceWork = await this.pieceWorkModel
//             .find({
//                 // projectId: roofProject.oppId,
//                 timeCardId: { $in: timeIds },
//                 companyId,
//                 deleted: { $ne: true },
//             })
//             ;

//         timeCards.map((card: any) => {
//             const work = pieceWork.find((wrk) => wrk.timeCardId === card._id);
//             card.work = work;
//         });

//         // Initialize the map and sequence in a single reduce function
//         const { pwSettingMap, pwSettingSequence } = pwSettingAllData.reduce(
//             (acc, setting) => {
//                 const { _id, workTask, name } = setting;

//                 // Create the map of settings
//                 acc.pwSettingMap[_id] = setting;

//                 // Initialize the sequence for each workTask
//                 if (!acc.pwSettingSequence[workTask]) {
//                     acc.pwSettingSequence[workTask] = { currentSequence: 1 };
//                 } else {
//                     acc.pwSettingSequence[workTask].currentSequence++;
//                 }

//                 // Assign the current sequence to the task name
//                 acc.pwSettingSequence[workTask][name] = acc.pwSettingSequence[workTask].currentSequence;

//                 return acc;
//             },
//             { pwSettingMap: {}, pwSettingSequence: {} },
//         );

//         // Remove the tracking property (currentSequence) from the final structure
//         const finalPwSettingSequence = Object.entries(pwSettingSequence).reduce(
//             (acc, [workTask, data]) => {
//                 acc[workTask] = Object.fromEntries(
//                     Object.entries(data).filter(([key]) => key !== "currentSequence"),
//                 );
//                 return acc;
//             },
//             {},
//         );

//         const workTaskSequencemapping = workTaskAllData.reduce((map, workTask) => {
//             map[workTask._id] = workTask?.sequence;
//             return map;
//         }, {});

//         timeCards.forEach((card) => {
//             card?.work?.work?.workDone.forEach((pieceWork) => {
//                 const setting = pwSettingMap[pieceWork.id];
//                 if (setting) {
//                     // Adding name and unit to pieceWork
//                     pieceWork.name = setting.name;
//                     pieceWork.unit = setting.unit.split(" (")[0];
//                 }
//             });
//         });

//         const workerArray = timeCards.map((card) => card.memberId);
//         const workers: any = await this.memberModel.aggregate([
//             {
//                 $match: {
//                     company: companyId,
//                     _id: { $in: workerArray },
//                 },
//             },
//             {
//                 $lookup: {
//                     from: "Compensation",
//                     localField: "_id",
//                     foreignField: "memberId",
//                     as: "wage",
//                 },
//             },
//             {
//                 $unwind: {
//                     path: "$wage",
//                     preserveNullAndEmptyArrays: true,
//                 },
//             },
//             {
//                 $addFields: {
//                     name: {
//                         $cond: {
//                             if: {
//                                 $and: [
//                                     { $ifNull: ["$preferredName", false] },
//                                     { $ne: ["$preferredName", ""] },
//                                 ],
//                             },
//                             then: {
//                                 $concat: [
//                                     "$preferredName",
//                                     " ",
//                                     {
//                                         $arrayElemAt: [{ $split: ["$name", " "] }, 1],
//                                     },
//                                 ],
//                             },
//                             else: "$name",
//                         },
//                     },
//                 },
//             },
//         ]);

//         let hrs = 0;
//         let cost = 0;
//         let totalLeadBonus = 0;
//         let pieceWorkData = [];
//         let dates = [];
//         // Crew Lead Bonus
//         let leadIndex = 0;

//         const crewLeadEarningsArray = [];
//         // Create an array to hold days the crew lead was off and won't get the lead bonus

//         const totalTasks = {};
//         const laborReport = [];
//         for (const [idx, worker] of workers.entries()) {
//             worker.pieceWork = [];
//             worker.pwHourly = 0;
//             worker.earned = 0;
//             worker.hrs = 0;
//             worker.leadEarnings = 0;
//             worker.pieceWorkEarned = 0;

//             // let salaryHourly = 0;

//             const salaryDates: { workTask: string; date: string }[] = [];
//             const travelDates: { workTask: string; date: string }[] = [];

//             const hoursPerDay = [];
//             const workerCards = timeCards.filter((card) => card.memberId === worker._id);
//             worker.cards = [];

//             for (const card of workerCards) {
//                 //to calculate total task type hrs on project
//                 const workTaskData = workTaskAllData.find((workTask) => workTask._id === card.task);
//                 const taskType = workTaskData?.name.toLowerCase();
//                 totalTasks[taskType] = (totalTasks[taskType] || 0) + (card?.hrs || 0);

//                 worker.currWage = findCurrentWage(worker?.wage?.wageHistory, new Date(card.timeIn));

//                 worker.salaried =
//                     worker.currWage && worker?.currWage?.wageInterval === WageIntervalEnum.Year
//                         ? true
//                         : false;
//                 worker.hourlyWage =
//                     worker.currWage && worker?.currWage?.wageInterval === WageIntervalEnum.Hour
//                         ? worker?.currWage?.wageAmount
//                         : 0;

//                 // worker salary
//                 // const salaryHourly = worker.hourlyWage
//                 // worker.salaried
//                 //     ? worker.currWage.wageAmount / (52 * 5 * 8)
//                 //     : worker.hourlyWage || 0;

//                 let crewLead = false;
//                 let activeCrew;
//                 for (const crew of crews) {
//                     if (!crew.members.length) continue;
//                     // Figure out which crew this person is a member of
//                     for (const member of crew.members) {
//                         if (
//                             worker._id === member.memberId &&
//                             member.startDate <= card.timeIn &&
//                             (!member.removeDate || member.removeDate > card.timeIn)
//                         ) {
//                             activeCrew = crew;
//                         }
//                     }
//                 }

//                 const crewLeadId = findCrewLeadId(activeCrew, new Date(card.timeIn));

//                 // Is this person the crew lead of that day?
//                 crewLead = worker._id === crewLeadId;

//                 if (crewLead) leadIndex = idx;
//                 const work = card.work;
//                 const hrs = card?.hrs || 0;
//                 worker.pwHourly += work?.hourlyEarnings || 0;
//                 if (work?.work) {
//                     const workTaskId = work.task;
//                     const earned = Number(work.earned) || 0;
//                     const { hourlyEarnings, hourlyWages, sqsEarnings, extrasEarnings } = work;
//                     const { workDone, extraWorkTime } = work?.work;
//                     // const hourlyWages = work.hourlyWages;

//                     //TODO: hardcoded data to be removed
//                     if (
//                         worker.pieceWork.find((item) => item.workTask === `Total Hourly Earning`) ===
//                         undefined
//                     ) {
//                         worker.pieceWork.push({
//                             workTask: `Total Hourly Earning`,
//                             pieceWork: 0,
//                         });
//                     }
//                     // Update Total Hourly Earning
//                     worker.pieceWork.find((item) => item.workTask === `Total Hourly Earning`).pieceWork +=
//                         hourlyWages;

//                     // Initialize variables to store the key for extra work time
//                     let extraWorkKey = "Extra Hour";

//                     // If workTaskData.pieceWork is not available, update extraWorkKey
//                     if (workTaskData && !workTaskData?.pieceWork) {
//                         extraWorkKey = "Other Hour";
//                     }

//                     // Find index of existing workTaskId or add a new entry
//                     let existingTaskIndex = worker.pieceWork.findIndex(
//                         (item) => item.workTask === workTaskId,
//                     );

//                     if (existingTaskIndex === -1) {
//                         existingTaskIndex = worker.pieceWork.length;
//                         worker.pieceWork.push({
//                             workTask: workTaskId,
//                             taskName: workTaskData?.name,
//                             extraHours: extraWorkTime || 0,
//                             hrs,
//                             hourlyEarnings,
//                             pieceWork: [],
//                             salaryHourly: hourlyWages,
//                             // roundTo2(hrs * salaryHourly),
//                         });
//                     } else {
//                         worker.pieceWork[existingTaskIndex].extraHours += extraWorkTime;
//                         worker.pieceWork[existingTaskIndex].hourlyEarnings += hourlyEarnings;
//                         worker.pieceWork[existingTaskIndex].hrs += hrs;
//                         worker.pieceWork[existingTaskIndex].salaryHourly += hourlyWages;
//                         // roundTo2(hrs * salaryHourly);
//                     }

//                     // Iterate through workDone and update pieceWork
//                     workDone.forEach((pwSetting) => {
//                         // calculating piece work cost for individual
//                         const { id, earned } = pwSetting;

//                         const existingObjectIndex = worker.pieceWork[
//                             existingTaskIndex
//                         ].pieceWork.findIndex((obj) => obj.name === pwSetting.name);

//                         if (existingObjectIndex !== -1) {
//                             // If an object with the same name exists
//                             const existingObject =
//                                 worker.pieceWork[existingTaskIndex].pieceWork[existingObjectIndex];

//                             // Check if pitch and layers already exist
//                             const existingPitchLayersIndex = existingObject.workDone.findIndex(
//                                 (obj) => obj.pitch === pwSetting.pitch && obj.layers === pwSetting.layers,
//                             );

//                             if (existingPitchLayersIndex !== -1) {
//                                 // If both pitch and layers exist, update their squares
//                                 existingObject.workDone[existingPitchLayersIndex].value +=
//                                     Number(pwSetting.amount) || 0;
//                                 existingObject.workDone[existingPitchLayersIndex].cost += earned;
//                             } else {
//                                 // If either pitch or layers don't exist, push new data
//                                 const dataToPush = {
//                                     pitch: pwSetting.pitch,
//                                     layers: pwSetting.layers,
//                                     value: Number(pwSetting.amount),
//                                     unit: pwSetting.unit,
//                                     cost: earned,
//                                 };
//                                 existingObject.workDone.push(dataToPush);
//                             }
//                         } else {
//                             // If the object doesn't exist, push a new object with name, value, and workDone array
//                             const dataToPush = {};
//                             dataToPush["cost"] = earned;

//                             if (pwSetting.pitch !== undefined) {
//                                 dataToPush["pitch"] = pwSetting.pitch;
//                             }

//                             if (pwSetting.layers !== undefined) {
//                                 dataToPush["layers"] = pwSetting.layers;
//                             }

//                             if (pwSetting.amount !== undefined) {
//                                 dataToPush["value"] = Number(pwSetting.amount);
//                             }

//                             if (pwSetting.unit !== undefined) {
//                                 dataToPush["unit"] = pwSetting.unit;
//                             }

//                             // Check if any of the properties exist before pushing the new object
//                             if (Object.keys(dataToPush).length > 0) {
//                                 worker.pieceWork[existingTaskIndex].pieceWork.push({
//                                     name: pwSetting.name,
//                                     workDone: [dataToPush],
//                                 });
//                             }
//                         }
//                     });

//                     // Add extraWorkTime to the appropriate key
//                     if (worker.pieceWork.find((item) => item.workTask === extraWorkKey) === undefined) {
//                         // If "Extra Hour" or "Other Hour" not present, initialize with 0
//                         worker.pieceWork.push({
//                             workTask: extraWorkKey,
//                             pieceWork: 0,
//                         });
//                     }
//                     // Update extra work time
//                     worker.pieceWork.find((item) => item.workTask === extraWorkKey).pieceWork +=
//                         extraWorkTime;

//                     // Ensure "Other Hour" and "Extra Hour" have a default value of 0
//                     if (
//                         worker.pieceWork.find((item) => item.workTask === extraWorkKey).pieceWork ===
//                         undefined
//                     ) {
//                         worker.pieceWork.find((item) => item.workTask === extraWorkKey).pieceWork = 0;
//                     }

//                     if (
//                         worker.pieceWork.find(
//                             (item) => item.workTask === `${workTaskData?.name} Cost`,
//                         ) === undefined
//                     ) {
//                         // If `${workTaskData?.name} Cost` not present, initialize with 0
//                         worker.pieceWork.push({
//                             workTask: `${workTaskData?.name} Cost`,
//                             pieceWork: 0,
//                         });
//                     }
//                     // Update earned cost
//                     worker.pieceWork.find(
//                         (item) => item.workTask === `${workTaskData?.name} Cost`,
//                     ).pieceWork += earned;

//                     // adding data to pieceWorkData
//                 }

//                 if (!crewLead && workTaskData?.showOnScoreboard) {
//                     const leadData = workers.find((w) => w._id === crewLeadId);
//                     const crewPieceWork =
//                         findCurrentWage(leadData?.wage?.wageHistory, new Date(card.timeIn))
//                             ?.crewPieceWork || 0;
//                     const item = {
//                         date: card.timeIn,
//                         lead: crewLeadId,
//                         workTask: workTaskData._id,
//                         earned: card.removeFromLead
//                             ? 0
//                             : (work && roundTo2(work.earned * crewPieceWork)) || 0,
//                     };
//                     crewLeadEarningsArray.push(item);
//                 }
//                 hoursPerDay.push({
//                     date: shortenDate(card.timeIn),
//                     hours: card.hrs,
//                 });

//                 // not using weekend check as the salary/hourly is always counted on weekends as well
//                 // const weekend = isWeekend(variables.weekEndDays, card.timeIn);

//                 // Only count salary on weekdays
//                 // if (!weekend) salaryDates.push(shortenDate(card.timeIn));

//                 salaryDates.push({ workTask: workTaskData._id, date: shortenDate(card.timeIn) });

//                 // Only count travel on Roofing & Tear Off
//                 if (
//                     workTaskData?.pieceWork &&
//                     workTaskData.addTravel &&
//                     card.work?.work?.extraWorkTime !== card.hrs
//                 ) {
//                     travelDates.push({ workTask: workTaskData._id, date: shortenDate(card.timeIn) });
//                 }
//                 worker.earned += work?.earned || 0;
//                 worker.hrs += card.hrs;
//                 dates.push(shortenDate(card.timeIn));

//                 worker.cards.push(card);
//             }

//             // Calculate salary and travel
//             const uniqueSDates = dedupePwObjects(salaryDates);
//             const uniqueTDates = dedupePwObjects(travelDates);

//             const indicesToDelete: number[] = [];
//             for (let i = 0; i < worker.pieceWork.length; i++) {
//                 const pieceWorkItem = worker.pieceWork[i];
//                 const { workTask, taskName, extraHours, hrs, hourlyEarnings, salaryHourly } =
//                     pieceWorkItem;
//                 let { pieceWork } = pieceWorkItem;
//                 pieceWork = JSON.parse(JSON.stringify(pieceWork));

//                 if (workTask === "Total Hourly Earning") {
//                     const existingTaskIndex = pieceWorkData.findIndex(
//                         (item) => item.workTask === workTask,
//                     );
//                     if (existingTaskIndex !== -1) {
//                         // If the workTask already exists, update its value
//                         pieceWorkData[existingTaskIndex].pieceWork = roundTo2(
//                             pieceWorkData[existingTaskIndex].pieceWork + pieceWork,
//                         );
//                     } else {
//                         // If the workTask doesn't exist, add a new entry
//                         pieceWorkData.push({
//                             workTask,
//                             pieceWork: roundTo2(pieceWork),
//                         });
//                     }
//                     indicesToDelete.push(i);
//                 } else if (workTask === "Extra Hour" || workTask === "Other Hour") {
//                     const existingTaskIndex = pieceWorkData.findIndex(
//                         (item) => item.workTask === workTask,
//                     );
//                     if (existingTaskIndex !== -1) {
//                         // If the workTask already ex ists, update its value
//                         pieceWorkData[existingTaskIndex].pieceWork = roundTo2(
//                             pieceWorkData[existingTaskIndex].pieceWork + pieceWork,
//                         );
//                     } else {
//                         // If the workTask doesn't exist, add a new entry
//                         pieceWorkData.push({
//                             workTask,
//                             pieceWork: roundTo2(pieceWork),
//                         });
//                     }
//                     indicesToDelete.push(i);
//                 } else if (/Cost$/.test(workTask)) {
//                     const existingTaskIndex = pieceWorkData.findIndex(
//                         (item) => item.workTask === workTask,
//                     );
//                     if (existingTaskIndex !== -1) {
//                         // If the workTask already exists, update its value
//                         pieceWorkData[existingTaskIndex].pieceWork += pieceWork;
//                         pieceWorkData[existingTaskIndex].extraHours += extraHours;
//                         pieceWorkData[existingTaskIndex].hrs += hrs;
//                         pieceWorkData[existingTaskIndex].hourlyEarnings += hourlyEarnings;
//                         pieceWorkData[existingTaskIndex].salaryHourly += salaryHourly;
//                     } else {
//                         // If the workTask doesn't exist, add a new entry
//                         pieceWorkData.push({
//                             workTask,
//                             pieceWork,
//                             extraHours,
//                             hrs,
//                             hourlyEarnings,
//                             salaryHourly,
//                         });
//                     }
//                     if (pieceWork) {
//                         worker.pieceWorkEarned += roundTo2(pieceWork) > 0 ? roundTo2(pieceWork) : 0;
//                     }
//                 } else {
//                     // Check if the workTask already exists in pieceWorkData
//                     const existingTaskIndex = pieceWorkData.findIndex(
//                         (item) => item.workTask === workTask,
//                     );
//                     if (existingTaskIndex !== -1) {
//                         // If the workTask already exists, update its data
//                         const existingPieceWork = pieceWorkData[existingTaskIndex].pieceWork;
//                         pieceWorkData[existingTaskIndex].extraHours += extraHours;
//                         pieceWorkData[existingTaskIndex].hrs += hrs;
//                         pieceWorkData[existingTaskIndex].hourlyEarnings += hourlyEarnings;
//                         pieceWorkData[existingTaskIndex].salaryHourly += salaryHourly;
//                         // Iterate over pieceWork of the worker
//                         for (const pieceWorkObj of pieceWork) {
//                             const existingObjectIndex = existingPieceWork.findIndex(
//                                 (obj) => obj.name === pieceWorkObj.name,
//                             );

//                             if (existingObjectIndex !== -1) {
//                                 // If the name already exists, check for pitch and layers
//                                 const existingObject = existingPieceWork[existingObjectIndex];

//                                 for (const pwObj of pieceWorkObj.workDone) {
//                                     const existingWorkDoneIndex = existingObject.workDone.findIndex(
//                                         (existingWorkDone) =>
//                                             (pwObj.pitch === undefined ||
//                                                 existingWorkDone.pitch === pwObj.pitch) &&
//                                             (pwObj.layers === undefined ||
//                                                 existingWorkDone.layers === pwObj.layers),
//                                     );
//                                     if (pwObj.pitch === undefined && pwObj.layers === undefined) {
//                                         // If both pitch and layers are undefined in pwObj
//                                         if (existingWorkDoneIndex !== -1) {
//                                             existingObject.workDone[existingWorkDoneIndex].value +=
//                                                 pwObj.value;
//                                         }
//                                     } else {
//                                         // If either pitch or layers are defined in pwObj
//                                         if (existingWorkDoneIndex !== -1) {
//                                             // If existingWorkDoneIndex is found, update the value
//                                             existingObject.workDone[existingWorkDoneIndex].value +=
//                                                 pwObj.value;
//                                         } else {
//                                             // If existingWorkDoneIndex is not found, push new data
//                                             const dataToPush = {};

//                                             if (pwObj.pitch !== undefined) {
//                                                 dataToPush["pitch"] = pwObj.pitch;
//                                             }

//                                             if (pwObj.layers !== undefined) {
//                                                 dataToPush["layers"] = pwObj.layers;
//                                             }

//                                             if (pwObj.value !== undefined) {
//                                                 dataToPush["value"] = pwObj.value;
//                                             }

//                                             if (pwObj.unit !== undefined) {
//                                                 dataToPush["unit"] = pwObj.unit;
//                                             }

//                                             // If either pitch or layers don't exist, push new data
//                                             if (Object.keys(dataToPush).length > 0) {
//                                                 existingObject.workDone.push(dataToPush);
//                                             }
//                                         }
//                                     }
//                                 }
//                             } else {
//                                 // Otherwise, push the new object
//                                 existingPieceWork.push(pieceWorkObj);
//                             }
//                         }
//                     } else {
//                         // If the workTask doesn't exist, add a new entry
//                         pieceWorkData.push({
//                             workTask,
//                             taskName,
//                             extraHours,
//                             hrs,
//                             hourlyEarnings,
//                             salaryHourly,
//                             pieceWork: pieceWork.map((obj) => ({
//                                 ...obj,
//                                 workDone: obj.workDone,
//                             })), // Make a copy of pieceWork
//                         });
//                     }
//                 }
//             }
//             console.log(uniqueSDates, "3");

//             // Salary calc
//             worker.salary = 0;

//             worker.dates = uniqueSDates.map(({ workTask, date }) => {
//                 //getting wage
//                 worker.currWage = findCurrentWage(worker?.wage?.wageHistory, new Date(date));
//                 worker.salaried =
//                     worker.currWage && worker?.currWage?.wageInterval === WageIntervalEnum.Year
//                         ? true
//                         : false;
//                 worker.hourlyWage =
//                     worker.currWage && worker?.currWage?.wageInterval === WageIntervalEnum.Hour
//                         ? worker?.currWage?.wageAmount
//                         : 0;
//                 // worker salary
//                 const salaryHourly = worker.salaried
//                     ? worker.currWage.wageAmount / (52 * 5 * 8)
//                     : worker.hourlyWage || 0;
//                 // workTask,
//                 // taskName,
//                 // extraHours,
//                 // hrs,
//                 // hourlyEarnings,
//                 // salaryHourly,
//                 // workTask: '5bb5336b-c884-440c-a8d5-89b75a60adca',
//                 // taskName: 'Meetings',
//                 // extraHours: 0,
//                 // hrs: 0.25,
//                 // hourlyEarnings: 0,
//                 // pieceWork: [],
//                 // salaryHourly: 4.25

//                 const obj: any = {};
//                 obj.date = date;
//                 obj.hours = 0;
//                 obj.regHours = 0;
//                 obj.otHours = 0;
//                 let daySalary = 0;

//                 hoursPerDay.map((day) => {
//                     if (day.date === date) obj.hours += day.hours;
//                 });
//                 if (obj.hours > 8) {
//                     obj.regHours = 8;
//                     obj.otHours = obj.hours - 8;
//                 } else {
//                     obj.regHours = obj.hours;
//                 }
//                 if (worker.salaried) daySalary = roundTo2(obj.regHours * salaryHourly);
//                 if (worker.hourlyWage)
//                     daySalary = roundTo2(obj.regHours * salaryHourly + obj.otHours * salaryHourly);
//                 worker.salary += daySalary;
//                 worker.earned += daySalary;
//                 const existingTaskIndex = pieceWorkData.findIndex((item) => item.workTask === workTask);

//                 if (existingTaskIndex !== -1) {
//                     pieceWorkData[existingTaskIndex] = {
//                         ...pieceWorkData[existingTaskIndex],
//                         cost: (pieceWorkData[existingTaskIndex].cost || 0) + daySalary, // Add daySalary to the current cost
//                         salaryEarned: (pieceWorkData[existingTaskIndex].salaryEarned || 0) + daySalary, // Update salaryEarned with daySalary
//                     };
//                 }
//                 return obj;
//             });
//             console.log(pieceWorkData, "4");

//             // Travel calc
//             worker.travel = 0;
//             worker.travelDays = 0;
//             if (opp?.distance && Number(opp?.distance) >= 20) {
//                 const pwFee = roundTo2(Number(opp.distance) * variables.travelFee);
//                 const salFee = roundTo2(Number(opp.distance) * (variables.travelFee / 2));

//                 for (const date of uniqueTDates) {
//                     const dayTravel = worker.salaried || worker.hourlyWage ? salFee : pwFee;
//                     worker.travel += dayTravel;
//                     worker.travelDays++;
//                     worker.earned += dayTravel;

//                     const existingTaskIndex = pieceWorkData.findIndex(
//                         (item) => item.workTask === date.workTask,
//                     );

//                     if (existingTaskIndex !== -1) {
//                         pieceWorkData[existingTaskIndex] = {
//                             ...pieceWorkData[existingTaskIndex],
//                             cost: (pieceWorkData[existingTaskIndex].cost || 0) + dayTravel, // Add dayTravel to the current cost
//                             travelEarned:
//                                 (pieceWorkData[existingTaskIndex].travelEarned || 0) + dayTravel, // Update travelEarned with dayTravel
//                         };
//                     }
//                 }
//             }
//             // console.log(pieceWorkData, "5");

//             // Remove items from the array based on the tracked indices
//             for (let i = indicesToDelete.length - 1; i >= 0; i--) {
//                 worker.pieceWork.splice(indicesToDelete[i], 1);
//             }

//             // Create a map of costs by task name
//             const costMap = worker.pieceWork.reduce((acc, { workTask, pieceWork }) => {
//                 if (workTask.endsWith(" Cost")) {
//                     acc[workTask.replace(" Cost", "")] = pieceWork;
//                 }
//                 return acc;
//             }, {});

//             // Update, filter, and add hours to the worker.pieceWork
//             worker.pieceWork = worker.pieceWork
//                 .filter(({ workTask }) => !workTask.endsWith(" Cost"))
//                 .map((item) => {
//                     const updatedItem =
//                         isUUID(item.workTask) && item.taskName in costMap
//                             ? {
//                                   ...item,
//                                   cost: costMap[item.taskName] + (item?.salaryHourly || 0),
//                               }
//                             : item;

//                     return updatedItem;
//                 });

//             worker.pieceWork.forEach(({ workTask, pieceWork }) => {
//                 if (isUUID(workTask)) {
//                     pieceWork.sort((a, b) => {
//                         const sequenceA =
//                             finalPwSettingSequence[workTask]?.[a.name] ?? Number.MAX_SAFE_INTEGER;
//                         const sequenceB =
//                             finalPwSettingSequence[workTask]?.[b.name] ?? Number.MAX_SAFE_INTEGER;
//                         return sequenceA - sequenceB;
//                     });
//                 }
//             });

//             worker.pieceWork.sort((a, b) => {
//                 const sequenceA = workTaskSequencemapping[a.workTask] ?? Number.MAX_SAFE_INTEGER;
//                 const sequenceB = workTaskSequencemapping[b.workTask] ?? Number.MAX_SAFE_INTEGER;
//                 return sequenceA - sequenceB;
//             });
//             worker.pieceWorkEarned -= worker.pwHourly;
//             hrs += worker.hrs;
//             cost += worker.earned;
//             laborReport.push(worker);
//         }

//         // Apply crew lead earnings to proper person
//         crewLeadEarningsArray.map((item) => {
//             workers.map((worker) => {
//                 if (worker._id === item.lead) {
//                     worker.leadEarnings += item.earned;
//                     worker.earned += item.earned;
//                     totalLeadBonus += roundTo2(item.earned) || 0;
//                     cost += item.earned;

//                     const existingTaskIndex = pieceWorkData.findIndex(
//                         (pw) => pw.workTask === item.workTask,
//                     );

//                     if (existingTaskIndex !== -1) {
//                         pieceWorkData[existingTaskIndex] = {
//                             ...pieceWorkData[existingTaskIndex],
//                             cost: (pieceWorkData[existingTaskIndex].cost || 0) + roundTo2(item.earned), // Add leadEarned to the current cost
//                             leadEarned:
//                                 (pieceWorkData[existingTaskIndex].leadEarned || 0) +
//                                 roundTo2(item.earned), // Update leadEarned with daySalary
//                         };
//                     }
//                 }
//             });
//         });

//         // Calculation for estimation
//         const order = opp?.orderId;
//         const allWorkOrders = [];
//         const estimateBudget = {};
//         let totalEstimateBudget = 0;
//         if (order) {
//             order?.projects?.forEach(({ workOrder }) => {
//                 allWorkOrders.push(...workOrder);
//             });

//             allWorkOrders.forEach((workOrder) => {
//                 const { cost, ttlHours, worker } = workOrder;
//                 if (!estimateBudget[worker]) {
//                     estimateBudget[worker] = { cost: 0, hours: 0 };
//                 }
//                 estimateBudget[worker].cost += cost;
//                 estimateBudget[worker].hours += ttlHours;
//                 totalEstimateBudget += cost;
//             });
//             // adding travel amount in equal ratio for esimated task
//             Object.keys(estimateBudget).forEach((key) => {
//                 estimateBudget[key].cost += roundTo2(
//                     order?.priceTotals?.travelFee * (estimateBudget[key].cost / totalEstimateBudget),
//                 );
//                 //TODO: hrs to check
//                 estimateBudget[key].hours += roundTo2(
//                     (order?.priceTotals?.travelFee * (estimateBudget[key].cost / totalEstimateBudget)) /
//                         order?.priceTotals?.travelHrlyRate,
//                 );
//             });
//         }

//         // Create a map of costs by task name
//         const costMap = pieceWorkData.reduce((acc, { workTask, pieceWork }) => {
//             if (workTask.endsWith(" Cost")) {
//                 acc[workTask.replace(" Cost", "")] = pieceWork;
//             }
//             return acc;
//         }, {});

//         // adding missing peicework task from order if not on timecards
//         Object.keys(estimateBudget).forEach((key) => {
//             const data = pieceWorkData.find((p) => p.workTask === key);
//             if (!data)
//                 pieceWorkData.push({
//                     workTask: key,
//                     taskName: workTaskAllData.find((w) => w._id === key)?.name || "",
//                     extraHours: 0,
//                     pieceWork: [],
//                     salaryEarned: 0,
//                     leadEarned: 0,
//                     cost: 0,
//                     estimateCost: estimateBudget[key]?.cost || 0,
//                     estimateHrs: estimateBudget[key]?.hours || 0,
//                     hrs: 0,
//                 });
//         });

//         // Update, filter, and add hours to the pieceWorkData
//         pieceWorkData = pieceWorkData
//             .filter(({ workTask }) => !workTask.endsWith(" Cost"))
//             .map((item) => {
//                 const updatedItem =
//                     isUUID(item.workTask) && item.taskName in costMap
//                         ? {
//                               ...item,
//                               cost: (item.cost || 0) + costMap[item.taskName],
//                               estimateCost: estimateBudget[item.workTask]?.cost || 0,
//                               estimateHrs: estimateBudget[item.workTask]?.hours || 0,
//                           }
//                         : item;

//                 const taskNameLower = updatedItem.taskName?.toLowerCase();
//                 return taskNameLower && taskNameLower in totalTasks
//                     ? { ...updatedItem, hrs: totalTasks[taskNameLower] }
//                     : updatedItem;
//             });

//         // Sort pieceWork data based on the sequences defined in cleanPwSettingSequence
//         pieceWorkData.forEach(({ workTask, pieceWork }) => {
//             if (isUUID(workTask)) {
//                 pieceWork.sort((a, b) => {
//                     const sequenceA =
//                         finalPwSettingSequence[workTask]?.[a.name] ?? Number.MAX_SAFE_INTEGER;
//                     const sequenceB =
//                         finalPwSettingSequence[workTask]?.[b.name] ?? Number.MAX_SAFE_INTEGER;
//                     return sequenceA - sequenceB;
//                 });
//             }
//         });

//         pieceWorkData.sort((a, b) => {
//             const sequenceA = workTaskSequencemapping[a.workTask] ?? Number.MAX_SAFE_INTEGER;
//             const sequenceB = workTaskSequencemapping[b.workTask] ?? Number.MAX_SAFE_INTEGER;
//             return sequenceA - sequenceB;
//         });

//         dates = dedupeArray(dates);
//         const days = dates.length;
//         let customPo;
//         if (!oppData?.PO) {
//             customPo = await this.customProjectModel.findOne({ _id: oppId });
//         }
//         return {
//             hrs,
//             cost,
//             totalLeadBonus,
//             pieceWorkData,
//             leadIndex,
//             totalTasks,
//             dates,
//             days,
//             laborReport,
//             po: oppData?.PO || customPo?.PO || "",
//             num: oppData?.num || customPo?.num || "",
//             allWorkOrders,
//         };
//     } catch (error) {
//         if (error instanceof HttpException) {
//             throw error;
//         }
//         throw new InternalServerErrorException(error.message);
//     }
// }

///////////////////////////////////////////////

// change order script
// async updateChangeOrderValues(): Promise<void> {
//     // Fetch all opportunities that need migration
//     const opportunities = await this.opportunityModel.find({});

//     console.log("HLLOO");

//     // Create bulk operations
//     const bulkOperations = opportunities.map((opp: any) => {
//         // Calculate the new changeOrderValue
//         const changeOrderValue =
//             opp.changeOrders?.reduce((total, order) => {
//                 if (order.deleted) {
//                     return total - (order.total || 0); // Subtract if deleted
//                 }
//                 return total + (order.total || 0); // Add if not deleted
//             }, 0) || 0;

//         // Calculate the new changeOrderRRValue
//         const changeOrderRRValue =
//             opp.changeOrders?.reduce((total, order) => {
//                 if (order.deleted) {
//                     return total - ((order.total || 0) - (order.materials || 0)); // Subtract if deleted
//                 }
//                 return total + ((order.total || 0) - (order.materials || 0)); // Add if not deleted
//             }, 0) || 0;

//         // Return the bulkWrite operation
//         return {
//             updateOne: {
//                 filter: { _id: opp._id },
//                 update: {
//                     $set: {
//                         changeOrderValue,
//                         changeOrderRRValue,
//                     },
//                 },
//             },
//         };
//     });

//     // Execute bulkWrite
//     if (bulkOperations.length > 0) {
//         await this.opportunityModel.bulkWrite(bulkOperations);
//         console.log("Migration completed: Updated all opportunities.");
//     } else {
//         console.log("No opportunities found to update.");
//     }
// }

// async updateScoreToOpp() {
//     try {
//         const opps = await this.opportunityModel
//             .find({ orderId: { $exists: true } })
//             .populate("orderId", "priceTotals", "Order");
//         console.log("started ");
//         const bulkUpdate = [];
//         for (let i = 0; i < opps.length; i++) {
//             const opp: any = opps[i];

//             const { jobTotal, mTotal, lTotal, commission } = opp.orderId?.priceTotals;
//             const budgetScore = profitScoreCalc(jobTotal, mTotal, lTotal, commission, opp?.financeFee);
//             const updateOp = {
//                 updateOne: {
//                     filter: {
//                         _id: opp._id,
//                     },
//                     update: { $set: { budgetScore } },
//                 },
//             };
//             bulkUpdate.push(updateOp);
//         }

//         console.log("calling update");

//         await this.opportunityModel.bulkWrite(bulkUpdate);

//         console.log("update completed");
//     } catch (error) {
//         console.log(error);
//     }
// }

// // TODO: to be removed
// // async updateOrderScript() {
// //     try {
// //         const allOrder = await this.orderModel.find();

// //         for (let i = 0; i < allOrder.length; i++) {
// //             const order = allOrder[i];
// //             const matList = [];
// //             order.matList.forEach((mat) => {
// //                 const projectId =
// //                     mat?.projectId && mat?.projectId === ""
// //                         ? order.projects[0].projectId
// //                         : mat?.projectId;
// //                 delete mat?.ProjectType;
// //                 matList.push({ ...mat, projectId });
// //             });

// //             console.log(matList);
// //             await this.orderModel.updateOne(
// //                 { _id: order._id },
// //                 {
// //                     $set: { matList },
// //                 },
// //             );
// //         }
// //     } catch (e) {
// //         console.log(e);
// //     }
// // }
// async updateOrderScript() {
//     try {
//         const allOrders = await this.orderModel.find();

//         // Prepare bulk operations
//         const bulkOperations = allOrders.map((order) => {
//             const matList = order.matList.map((mat) => {
//                 const projectId =
//                     mat?.projectId && mat?.projectId !== "" ? mat.projectId : order.projects[0].projectId;
//                 delete mat?.ProjectType;
//                 return { ...mat, projectId };
//             });

//             return {
//                 updateOne: {
//                     filter: { _id: order._id },
//                     update: { $set: { matList } },
//                 },
//             };
//         });

//         // Execute bulk write
//         if (bulkOperations.length > 0) {
//             const result = await this.orderModel.bulkWrite(bulkOperations);
//             console.log("Bulk update result:", result);
//         } else {
//             console.log("No orders to update.");
//         }
//     } catch (e) {
//         console.error("Error updating orders:", e);
//     }
// }

////////////////////

// async getOpportunity2(
//     companyId: string,
//     memberId: string,
//     permission: PermissionsEnum,
//     deleted: boolean,
//     stageGroup: StageGroupEnum,
//     getOpportunityDto: GetOpportunityDto,
//     positionSymbol?: string,
// ) {
//     try {
//         // Fetch company settings early (in parallel with other operations)
//         const companySettingsPromise = this.companySettingModel.findOne({ companyId }).lean().exec();

//         // To get list of members managed by logged in member
//         const { members } = await this.positionService.getManagedMembersInternal(
//             memberId,
//             companyId,
//             permission,
//             positionSymbol,
//         );

//         const { status, salesPerson, projectManager, oppType, crew } = getOpportunityDto;
//         const thirtyDaysAgo = new Date();
//         thirtyDaysAgo.setMonth(thirtyDaysAgo.getMonth() - 1);
//         const limit = getOpportunityDto.limit || 10;
//         const offset = limit * (getOpportunityDto.skip || 0);

//         // Build query more efficiently
//         const baseQuery: any = {
//             companyId,
//             deleted,
//         };

//         // Add optional filters only if they exist
//         if (status !== undefined) baseQuery.status = status;
//         if (stageGroup !== undefined) baseQuery["stageData.stageGroup"] = stageGroup;

//         // Create member filters
//         const memberFilters = [];
//         if (members.length > 0) {
//             memberFilters.push({ salesPerson: { $in: members } });
//             memberFilters.push({ projectManager: { $in: members } });
//             memberFilters.push({ "workingCrew.id": { $in: members } });
//         }

//         // Add additional filters if provided
//         if (salesPerson?.length) memberFilters.push({ salesPerson: { $in: salesPerson } });
//         if (projectManager?.length) memberFilters.push({ projectManager: { $in: projectManager } });
//         if (crew?.length) memberFilters.push({ "workingCrew.id": { $in: crew } });

//         // Combine the queries
//         const query = {
//             ...baseQuery,
//             $and: [
//                 {
//                     $or: [
//                         {
//                             "stageData.code": "completed",
//                             jobCompletedDate: { $gte: thirtyDaysAgo },
//                         },
//                         { "stageData.code": { $ne: "completed" } },
//                     ],
//                 },
//             ],
//             $or: memberFilters,
//         };

//         // Add oppType filter if provided
//         if (oppType?.length) query.oppType = { $in: oppType };

//         // Use projection to limit returned fields in lookups
//         const oppPipeline: any[] = [
//             {
//                 $lookup: {
//                     from: "CrmStage",
//                     localField: "stage",
//                     foreignField: "_id",
//                     as: "stageData",
//                     pipeline: [
//                         { $project: { _id: 1, name: 1, code: 1, stageGroup: 1, agingCheckpointId: 1 } },
//                     ],
//                 },
//             },
//             { $match: query },
//             // Apply limit and skip for pagination - this reduces the documents processed in subsequent stages
//             { $sort: { createdAt: -1 } },
//             { $skip: offset },
//             { $limit: limit },
//             {
//                 $lookup: {
//                     from: "CrmCheckpoint",
//                     localField: "stageData.agingCheckpointId",
//                     foreignField: "_id",
//                     as: "checkPointData",
//                 },
//             },
//             {
//                 $lookup: {
//                     from: "ProjectType",
//                     localField: "oppType",
//                     foreignField: "_id",
//                     as: "opp-type",
//                     pipeline: [{ $project: { _id: 1, name: 1, typeReplacement: 1, colorCode: 1 } }],
//                 },
//             },
//             {
//                 $lookup: {
//                     from: "Client",
//                     localField: "clientId",
//                     foreignField: "_id",
//                     as: "clientId",
//                     pipeline: [{ $project: { lastName: 1, firstName: 1 } }],
//                 },
//             },
//             {
//                 $lookup: {
//                     from: "Member",
//                     localField: "createdBy",
//                     foreignField: "_id",
//                     as: "createdBy",
//                     pipeline: [{ $project: { name: 1 } }],
//                 },
//             },
//             {
//                 $lookup: {
//                     from: "Member",
//                     localField: "salesPerson",
//                     foreignField: "_id",
//                     as: "salesPersonData",
//                     pipeline: [{ $project: { name: 1 } }],
//                 },
//             },
//             {
//                 $lookup: {
//                     from: "Member",
//                     localField: "projectManager",
//                     foreignField: "_id",
//                     as: "projectManagerData",
//                     pipeline: [{ $project: { name: 1 } }],
//                 },
//             },
//             {
//                 $lookup: {
//                     from: "Order",
//                     localField: "orderId",
//                     pipeline: [{ $project: { _id: 1, priceTotals: 1 } }],
//                     foreignField: "_id",
//                     as: "orderData",
//                 },
//             },
//             {
//                 $lookup: {
//                     from: "ProjectType",
//                     localField: "acceptedType",
//                     foreignField: "_id",
//                     as: "order-type",
//                     pipeline: [{ $project: { name: 1, typeReplacement: 1, colorCode: 1 } }],
//                 },
//             },
//             {
//                 $lookup: {
//                     from: "Project",
//                     localField: "acceptedProjectId",
//                     pipeline: [
//                         { $project: { _id: 1, "customData.reroofAreas": 1, "customData.pitch": 1 } },
//                     ],
//                     foreignField: "_id",
//                     as: "projectData",
//                 },
//             },
//             {
//                 $project: {
//                     _id: 1,
//                     PO: 1,
//                     companyId: 1,
//                     oppType: 1,
//                     num: 1,
//                     firstName: 1,
//                     lastName: 1,
//                     street: 1,
//                     city: 1,
//                     state: 1,
//                     stage: 1,
//                     stageData: 1,
//                     checkPointData: { $arrayElemAt: ["$checkPointData", 0] },
//                     oppNotes: 1,
//                     jobNote: 1,
//                     salesPerson: 1,
//                     createdBy: {
//                         $arrayElemAt: [
//                             {
//                                 $filter: {
//                                     input: "$memberData",
//                                     as: "m",
//                                     cond: { $eq: ["$$m._id", "$createdBy"] },
//                                 },
//                             },
//                             0,
//                         ],
//                     },
//                     salesPersonName: {
//                         $arrayElemAt: [
//                             {
//                                 $filter: {
//                                     input: "$memberData",
//                                     as: "m",
//                                     cond: { $eq: ["$$m._id", "$salesPerson"] },
//                                 },
//                             },
//                             0,
//                         ],
//                     },
//                     projectManagerName: {
//                         $arrayElemAt: [
//                             {
//                                 $filter: {
//                                     input: "$memberData",
//                                     as: "m",
//                                     cond: { $eq: ["$$m._id", "$projectManager"] },
//                                 },
//                             },
//                             0,
//                         ],
//                     },

//                     // salesPersonName: { $arrayElemAt: ["$salesPersonData.name", 0] },
//                     projectManager: 1,
//                     // projectManagerName: { $arrayElemAt: ["$projectManagerData.name", 0] },
//                     clientId: { $arrayElemAt: ["$clientId", 0] },
//                     // createdBy: { $arrayElemAt: ["$createdBy", 0] },
//                     opportunityId: 1,
//                     dateReceived: 1,
//                     newLeadDate: 1,
//                     oppDate: 1,
//                     jobStartedDate: 1,
//                     checkpointActivity: 1,
//                     placeInLine: 1,
//                     createdAt: 1,
//                     nextAction: 1,
//                     workingCrew: 1,
//                     warrantyType: 1,
//                     orderData: { $arrayElemAt: ["$orderData", 0] },
//                     projectData: { $arrayElemAt: ["$projectData", 0] },
//                     "opp-type": { $arrayElemAt: ["$opp-type", 0] },
//                     "order-type": { $arrayElemAt: ["$order-type", 0] },
//                 },
//             },
//         ];

//         // Execute the query and get company settings in parallel
//         const [opp, variables] = await Promise.all([
//             this.opportunityModel.aggregate(oppPipeline),
//             companySettingsPromise,
//         ]);

//         const weekEndDays = variables?.weekEndDays || [];
//         const weekEndSet = new Set(weekEndDays);
//         const now = new Date();

//         // Memoization for getWorkingDaysDiff to avoid repeated calculations
//         const workingDaysCache = new Map();

//         // Function to calculate working days with memoization
//         const getWorkingDaysDiff = (fromDate: Date, toDate: Date): number => {
//             const cacheKey = `${fromDate.getTime()}-${toDate.getTime()}`;
//             if (workingDaysCache.has(cacheKey)) {
//                 return workingDaysCache.get(cacheKey);
//             }

//             let count = 0;
//             const current = new Date(fromDate);
//             while (current <= toDate) {
//                 const dayName = current.toLocaleString("en-US", { weekday: "long" });
//                 if (!weekEndSet.has(dayName as any)) {
//                     count++;
//                 }
//                 current.setDate(current.getDate() + 1);
//             }

//             workingDaysCache.set(cacheKey, count);
//             return count;
//         };

//         // Helper function for calculating aging value
//         const calculateAgingVal = (o) => {
//             const symbol = o.checkPointData?.symbol;
//             const activityEntry = symbol ? o.checkpointActivity?.[symbol] : null;
//             if (activityEntry?.created) {
//                 const createdDate = new Date(activityEntry.created);
//                 return getWorkingDaysDiff(createdDate, now);
//             }
//             return null;
//         };

//         // Helper function for rounding to 1 decimal place
//         const roundTo1 = (num) => {
//             return num ? Math.round(num * 10) / 10 : 0;
//         };

//         let opportunity = [];
//         if (stageGroup === StageGroupEnum.Operations && opp.length) {
//             const dailyOH = variables.dailyOH;

//             opportunity = opp.map((o) => {
//                 // dayCalc
//                 const dayCalc = roundTo1((o.orderData?.priceTotals?.overhead * 1.3) / dailyOH);

//                 // jobInfo
//                 const pitches = [];
//                 let install = 0;
//                 let remove = 0;

//                 if (o.projectData?.customData?.reroofAreas) {
//                     o.projectData.customData.reroofAreas.forEach((area) => {
//                         pitches.push(area.pitch);
//                         install += area.install;
//                         remove += area.remove;
//                     });
//                 } else if (o.projectData?.customData?.pitch) {
//                     pitches.push(o.projectData.customData.pitch);
//                 }

//                 const pitch = pitches.join(", ");
//                 const agingVal = calculateAgingVal(o);

//                 // Create a new object instead of mutating the original
//                 return {
//                     ...o,
//                     dayCalc,
//                     agingVal,
//                     jobInfo: { pitch, install: roundTo1(install), remove: roundTo1(remove) },
//                     // Remove these properties to save memory/bandwidth
//                     projectData: undefined,
//                     orderData: undefined,
//                 };
//             });
//         } else {
//             opportunity = opp.map((o) => ({
//                 ...o,
//                 agingVal: calculateAgingVal(o),
//             }));
//         }

//         return new OkResponse({ opportunity });
//     } catch (error: any) {
//         if (error instanceof HttpException) {
//             throw error;
//         }
//         throw new InternalServerErrorException(error.message);
//     }
// }
