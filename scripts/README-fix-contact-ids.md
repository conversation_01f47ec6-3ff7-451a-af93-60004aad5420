# Fix Incorrect Contact IDs Script

## Problem Description

After running the `migrateLeadToContact2` migration, some leads have incorrect `contactId` assignments. This happens when:

1. A lead's `_id` doesn't match its `contactId` (indicating the contactId was assigned incorrectly)
2. The lead should be linked to a different contact based on matching phone or email

## Solution

This script identifies and fixes these incorrect assignments by:

1. Finding all leads where `_id ≠ contactId`
2. For each mismatched lead:
   - Try to find the correct contact by matching phone number
   - If no phone match, try to find the correct contact by matching email
   - If no phone or email, log the lead ID for manual review
3. Update the lead with the correct `contactId` and related fields (`oppId`, `newLeadDate`, `status`, etc.)

## Usage Options

### Option 1: Standalone Script (Recommended for Review)

```bash
# Navigate to the project root
cd /path/to/nhrapp

# Run the script in dry-run mode (no changes made)
node scripts/fix-incorrect-contact-ids.js

# After reviewing the output, set DRY_RUN = false in the script and run again
# Edit scripts/fix-incorrect-contact-ids.js and change: const DRY_RUN = false;
node scripts/fix-incorrect-contact-ids.js
```

### Option 2: Using the Service Method

1. Uncomment the line in `src/contacts/contacts.service.ts` constructor:
   ```typescript
   // this.fixIncorrectContactIdAssignments(); // Remove the // to enable
   ```

2. Uncomment the actual update query in the `fixIncorrectContactIdAssignments` method:
   ```typescript
   // Remove the /* and */ around this block:
   /*
   await this.leadModel.updateOne(
       { _id: lead._id },
       { $set: updateData }
   );
   */
   ```

3. Restart your application - the script will run automatically on startup

## What the Script Does

### 1. Identification Phase
- Finds leads where `lead._id !== lead.contactId`
- These are leads that were potentially assigned wrong contactIds during migration

### 2. Matching Phase
For each mismatched lead:
- **Phone Match**: Look for a contact with the same phone number and companyId
- **Email Match**: If no phone match, look for a contact with the same email and companyId
- **No Match**: If neither phone nor email can be matched, log for manual review

### 3. Validation Phase
- Check if the found contact's `_id` is different from the current `contactId`
- Skip if they're already the same (already correct)

### 4. Update Phase
Updates the lead with:
- Correct `contactId`
- Related `oppId` (if an opportunity exists for the correct contact)
- Correct `status` (converted if opportunity exists and contact is active)
- Other fields: `deleted`, `csrId`, `stageId`, `newLeadDate`

## Output Explanation

The script provides detailed logging:

```
--- Processing Lead ID: abc123 ---
Current contactId: wrong-contact-id
Lead phone: +1234567890
Lead email: <EMAIL>
Found contact by phone: correct-contact-id (John Doe)
🔄 Need to update contactId from wrong-contact-id to correct-contact-id (matched by phone)
Update data: { contactId: 'correct-contact-id', status: 'active', ... }
✅ Would update lead abc123 with correct contactId correct-contact-id
```

### Summary Statistics
- **Total leads processed**: Number of leads with mismatched contactIds
- **Leads that would be fixed**: Number of leads where a correct match was found
- **Leads already correct**: Number of leads where the contactId was already correct
- **Leads with no phone/email**: Number of leads that can't be matched (need manual review)
- **Leads with no matching contact**: Number of leads where no matching contact was found

## Safety Features

1. **Dry Run Mode**: By default, no changes are made - only analysis and reporting
2. **Detailed Logging**: Every step is logged for review
3. **Validation**: Multiple checks ensure only valid updates are made
4. **Backup Recommendation**: Always backup your database before running with `DRY_RUN = false`

## Manual Review Required

Leads that will be logged for manual review:
- Leads with no phone or email (can't be matched automatically)
- Leads where no matching contact is found in the database

For these cases, you'll need to:
1. Check the lead data manually
2. Find the correct contact
3. Update the contactId manually or create the missing contact

## Prerequisites

- Node.js environment
- Access to the MongoDB database
- `.env` file with `MONGO_URI` configured
- `mongoose` package installed

## Backup Recommendation

Before running with `DRY_RUN = false`, create a backup:

```bash
# MongoDB backup example
mongodump --uri="your-mongo-uri" --out=backup-before-contact-fix
```
