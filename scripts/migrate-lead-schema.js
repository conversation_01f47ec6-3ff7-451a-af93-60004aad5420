/**
 * Migration script to migrate lead data to match current schema structure
 * and move all extra data into rawTracking field.
 *
 * This script:
 * 1. Identifies leads that have fields not matching the current schema
 * 2. Moves extra fields to rawTracking object
 * 3. Only migrates leads that need migration (not matching current schema)
 *
 * Usage:
 * 1. Review this script carefully
 * 2. Set DRY_RUN to false when ready to apply changes
 * 3. Run: node scripts/migrate-lead-schema.js
 */

const mongoose = require("mongoose");
require("dotenv").config();

// Set to false to actually apply the updates
const DRY_RUN = true;

// MongoDB connection
const connectDB = async () => {
    try {
        await mongoose.connect(process.env.MONGO_URI);
        console.log("Connected to MongoDB");
    } catch (error) {
        console.error("MongoDB connection error:", error);
        process.exit(1);
    }
};

// Define the current Lead schema fields (based on lead.schema.ts)
const VALID_LEAD_FIELDS = new Set([
    "_id",
    "companyId",
    "contactId",
    "oppId",
    "campaignId",
    "leadSourceId",
    "workType",
    "email",
    "firstName",
    "lastName",
    "phone",
    "csrId",
    "stageId",
    "status",
    "referredBy",
    "createdBy",
    "deleted",
    "newLeadDate",
    "lostDate",
    "lostReason",
    "lostBy",
    "unLostReason",
    "unLostDate",
    "unLostBy",
    "invalidLeadReason",
    "checkpointActivity",
    "statusChanges",
    "tracking",
    "rawTracking",
    "zapierLead",
    "createdAt",
    "updatedAt",
    "__v", // MongoDB version field
]);

// Define Lead schema for migration
const leadSchema = new mongoose.Schema(
    {},
    {
        collection: "Lead",
        timestamps: true,
        strict: false, // Allow extra fields during migration
    },
);

const Lead = mongoose.model("Lead", leadSchema);

/**
 * Check if a lead document has fields that don't match the current schema
 */
const hasExtraFields = (leadDoc) => {
    const docFields = Object.keys(leadDoc.toObject());
    const extraFields = docFields.filter((field) => !VALID_LEAD_FIELDS.has(field));
    return extraFields.length > 0 ? extraFields : null;
};

/**
 * Check if a value is meaningful (not empty/null/undefined)
 */
const hasMeaningfulValue = (value) => {
    if (value === null || value === undefined) return false;
    if (typeof value === "string" && value.trim() === "") return false;
    if (Array.isArray(value) && value.length === 0) return false;
    if (typeof value === "object" && Object.keys(value).length === 0) return false;
    return true;
};

/**
 * Migrate a single lead document
 */
const migrateLead = async (lead, extraFields) => {
    try {
        const leadObj = lead.toObject();
        const currentRawTracking = leadObj.rawTracking || {};
        const newRawTracking = { ...currentRawTracking };

        // Track what we're moving
        const movedFields = [];
        const skippedFields = [];

        // Move extra fields to rawTracking
        const updateData = {};
        const unsetData = {};

        extraFields.forEach((field) => {
            const fieldValue = leadObj[field];

            // Add to rawTracking if it has a meaningful value
            if (hasMeaningfulValue(fieldValue)) {
                // Avoid overwriting existing rawTracking data unless it's different
                if (!currentRawTracking.hasOwnProperty(field) || currentRawTracking[field] !== fieldValue) {
                    newRawTracking[field] = fieldValue;
                    movedFields.push(field);
                }
            } else {
                skippedFields.push(field);
            }

            // Always mark field for removal from main document
            unsetData[field] = 1;
        });

        // Only update rawTracking if there are meaningful changes
        const hasChanges = movedFields.length > 0 || Object.keys(unsetData).length > 0;
        if (hasChanges && Object.keys(newRawTracking).length > 0) {
            updateData.rawTracking = newRawTracking;
        }

        return {
            updateData,
            unsetData,
            movedFields,
            skippedFields,
            hasChanges,
        };
    } catch (error) {
        console.error(`Error processing lead ${lead._id}:`, error.message);
        return null;
    }
};

/**
 * Main migration function
 */
const migrateLeadSchema = async () => {
    try {
        console.log("Starting lead schema migration");
        console.log(`DRY RUN MODE: ${DRY_RUN ? "ON" : "OFF"}`);

        let processedCount = 0;
        let migratedCount = 0;
        let errorCount = 0;
        let alreadyCorrectCount = 0;

        // Get all leads (we'll check each one for extra fields)
        const leads = await Lead.find({}).lean(false);
        console.log(`Found ${leads.length} total leads to check`);

        for (const lead of leads) {
            processedCount++;

            try {
                // Check if lead has extra fields
                const extraFields = hasExtraFields(lead);

                if (!extraFields) {
                    alreadyCorrectCount++;
                    if (processedCount % 100 === 0) {
                        console.log(`Processed ${processedCount}/${leads.length} leads...`);
                    }
                    continue;
                }

                console.log(`\n--- Processing Lead ID: ${lead._id} ---`);
                console.log(`Extra fields found: ${extraFields.join(", ")}`);

                // Migrate the lead
                const migrationResult = await migrateLead(lead, extraFields);

                if (!migrationResult) {
                    errorCount++;
                    continue;
                }

                const { updateData, unsetData, movedFields, skippedFields, hasChanges } = migrationResult;

                if (!hasChanges) {
                    console.log(`No meaningful changes needed for lead ${lead._id}`);
                    alreadyCorrectCount++;
                    continue;
                }

                // Log what would be changed
                if (movedFields.length > 0) {
                    console.log(`Would move to rawTracking: ${movedFields.join(", ")}`);
                }
                if (skippedFields.length > 0) {
                    console.log(`Would skip empty fields: ${skippedFields.join(", ")}`);
                }
                if (Object.keys(unsetData).length > 0) {
                    console.log(`Would remove fields: ${Object.keys(unsetData).join(", ")}`);
                }

                // Apply changes if not in dry run mode
                if (!DRY_RUN) {
                    const updateQuery = {};
                    if (Object.keys(updateData).length > 0) {
                        updateQuery.$set = updateData;
                    }
                    if (Object.keys(unsetData).length > 0) {
                        updateQuery.$unset = unsetData;
                    }

                    if (Object.keys(updateQuery).length > 0) {
                        await Lead.updateOne({ _id: lead._id }, updateQuery);
                        console.log(`✅ Successfully migrated lead ${lead._id}`);
                    }
                } else {
                    console.log(`✅ Would migrate lead ${lead._id}`);
                }

                migratedCount++;
            } catch (error) {
                console.error(`❌ Error processing lead ${lead._id}:`, error.message);
                errorCount++;
            }

            // Progress update
            if (processedCount % 100 === 0) {
                console.log(`\nProgress: ${processedCount}/${leads.length} leads processed`);
            }
        }

        // Final summary
        console.log("\n=== MIGRATION SUMMARY ===");
        console.log(`Total leads processed: ${processedCount}`);
        console.log(`Leads already correct: ${alreadyCorrectCount}`);
        console.log(`Leads migrated: ${migratedCount}`);
        console.log(`Leads with errors: ${errorCount}`);
        console.log(`DRY RUN MODE: ${DRY_RUN ? "ON - No changes applied" : "OFF - Changes applied"}`);

        if (DRY_RUN && migratedCount > 0) {
            console.log("\n⚠️  To apply these changes, set DRY_RUN = false and run the script again");
        }
    } catch (error) {
        console.error("Migration failed:", error);
        throw error;
    }
};

// Run the migration
const runMigration = async () => {
    try {
        await connectDB();
        await migrateLeadSchema();
        console.log("\nMigration completed successfully");
    } catch (error) {
        console.error("Migration failed:", error);
        process.exit(1);
    } finally {
        await mongoose.connection.close();
        console.log("Database connection closed");
    }
};

// Execute if run directly
if (require.main === module) {
    runMigration();
}

module.exports = { migrateLeadSchema };
