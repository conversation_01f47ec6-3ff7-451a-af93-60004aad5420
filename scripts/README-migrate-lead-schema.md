# Lead Schema Migration Script

## Problem Description

This migration script addresses the issue where lead documents in the database may contain fields that don't match the current Lead schema structure. Since the Lead schema has `strict: true`, MongoDB will reject documents with extra fields during updates or when creating new documents.

## Solution

The script identifies leads that have fields not defined in the current schema and moves those extra fields into the `rawTracking` object, which is designed to store additional data that doesn't fit the main schema structure.

## What the Script Does

### 1. Schema Validation Phase
- Compares each lead document against the current Lead schema fields
- Identifies any extra fields that don't belong to the schema
- Skips leads that already match the current schema structure

### 2. Migration Phase
For each lead with extra fields:
- **Preserve Data**: Moves extra fields to the `rawTracking` object
- **Clean Schema**: Removes extra fields from the main document structure
- **Merge Strategy**: If `rawTracking` already exists, merges new fields with existing data
- **Data Validation**: Only moves fields that have meaningful values (not null, undefined, or empty)

### 3. Update Phase
- Updates the lead document with the new `rawTracking` data
- Removes the extra fields from the main document structure
- Maintains all existing valid schema fields unchanged

## Current Lead Schema Fields

The script validates against these fields from the current Lead schema:

**Core Fields:**
- `_id`, `companyId`, `contactId`, `oppId`, `campaignId`, `leadSourceId`
- `workType`, `email`, `firstName`, `lastName`, `phone`
- `csrId`, `stageId`, `status`, `referredBy`, `createdBy`

**Status & Tracking Fields:**
- `deleted`, `newLeadDate`, `lostDate`, `lostReason`, `lostBy`
- `unLostReason`, `unLostDate`, `unLostBy`, `invalidLeadReason`
- `checkpointActivity`, `statusChanges`, `tracking`, `rawTracking`

**System Fields:**
- `zapierLead`, `createdAt`, `updatedAt`, `__v`

## Usage Instructions

### Prerequisites
- Node.js environment
- Access to the MongoDB database
- `.env` file with `MONGO_URI` configured
- `mongoose` package installed

### Step 1: Review the Script
```bash
# Review the migration script
cat scripts/migrate-lead-schema.js
```

### Step 2: Run in Dry Run Mode (Default)
```bash
# This will analyze and report what would be changed without making any updates
node scripts/migrate-lead-schema.js
```

### Step 3: Review the Output
The script will show:
- Total leads processed
- Leads that already match the schema
- Leads that need migration
- Specific fields that would be moved to rawTracking

Example output:
```
--- Processing Lead ID: abc123 ---
Extra fields found: oldField1, customData, legacyInfo
Would update rawTracking with: ['oldField1', 'customData', 'legacyInfo']
Would remove fields: ['oldField1', 'customData', 'legacyInfo']
✅ Would migrate lead abc123
```

### Step 4: Apply Changes
```bash
# Edit the script to set DRY_RUN = false
# Then run the migration
node scripts/migrate-lead-schema.js
```

## Safety Features

1. **Dry Run Mode**: By default, no changes are made - only analysis and reporting
2. **Data Preservation**: All extra data is moved to `rawTracking`, nothing is lost
3. **Selective Migration**: Only processes leads that actually need migration
4. **Error Handling**: Continues processing even if individual leads fail
5. **Progress Tracking**: Shows progress every 100 leads processed
6. **Detailed Logging**: Every step is logged for review

## Backup Recommendation

Before running with `DRY_RUN = false`, create a backup:

```bash
# MongoDB backup example
mongodump --uri="your-mongo-uri" --out=backup-before-lead-migration
```

## Expected Results

After migration:
- All leads will conform to the current schema structure
- Extra data will be preserved in the `rawTracking` field
- No data loss will occur
- Future lead operations will work correctly with the strict schema

## Common Extra Fields

Based on the codebase analysis, common extra fields that might be moved to `rawTracking` include:
- Legacy Zapier fields that are no longer in the schema
- Custom tracking data from external sources
- Temporary fields added during previous migrations
- Fields from old schema versions

## Troubleshooting

### If the script fails:
1. Check MongoDB connection string in `.env`
2. Ensure the database is accessible
3. Verify sufficient permissions for read/write operations
4. Check the error logs for specific issues

### If some leads can't be migrated:
- The script will log specific errors for each failed lead
- Review the error messages to understand the issue
- These leads may need manual review and correction

## Post-Migration Verification

After running the migration:
1. Verify lead counts match before and after
2. Check that `rawTracking` contains the expected extra data
3. Test lead creation and updates to ensure schema compliance
4. Review any error logs for issues that need manual attention

## Integration with Existing Code

This migration is designed to work with the existing `createLeadFromZapier` function, which already uses `rawTracking` to store extra Zapier data. After migration, all leads will follow the same pattern of storing extra data in `rawTracking`.
