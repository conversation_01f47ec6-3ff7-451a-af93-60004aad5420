/**
 * <PERSON><PERSON><PERSON> to fix incorrect contactId assignments after migrateLeadToContact2
 * 
 * This script identifies leads where the _id doesn't match the contactId,
 * then attempts to find the correct contact by matching phone or email,
 * and updates the lead with the correct contactId and related fields.
 * 
 * Usage:
 * 1. Review this script carefully
 * 2. Set DRY_RUN to false when ready to apply changes
 * 3. Run: node scripts/fix-incorrect-contact-ids.js
 */

const mongoose = require('mongoose');
require('dotenv').config();

// Set to false to actually apply the updates
const DRY_RUN = true;

// MongoDB connection
const connectDB = async () => {
    try {
        await mongoose.connect(process.env.MONGO_URI);
        console.log('Connected to MongoDB');
    } catch (error) {
        console.error('MongoDB connection error:', error);
        process.exit(1);
    }
};

// Define schemas (simplified versions)
const leadSchema = new mongoose.Schema({
    _id: String,
    contactId: String,
    phone: String,
    email: String,
    companyId: String,
    oppId: String,
    status: String,
    deleted: Boolean,
    csrId: String,
    stageId: String,
    newLeadDate: Date
}, { collection: 'Lead', timestamps: true });

const contactSchema = new mongoose.Schema({
    _id: String,
    phone: String,
    email: String,
    fullName: String,
    companyId: String,
    status: String,
    deleted: Boolean,
    csrId: String,
    stageId: String,
    newLeadDate: Date
}, { collection: 'Contact', timestamps: true });

const opportunitySchema = new mongoose.Schema({
    _id: String,
    contactId: String
}, { collection: 'Opportunity', timestamps: true });

const Lead = mongoose.model('Lead', leadSchema);
const Contact = mongoose.model('Contact', contactSchema);
const Opportunity = mongoose.model('Opportunity', opportunitySchema);

const fixIncorrectContactIdAssignments = async () => {
    try {
        console.log("Starting fix for incorrect contactId assignments");
        console.log(`DRY RUN MODE: ${DRY_RUN ? 'ON' : 'OFF'}`);
        
        let fixedCount = 0;
        let noPhoneNoEmailCount = 0;
        let noMatchFoundCount = 0;
        let alreadyCorrectCount = 0;
        
        // Get all leads where _id is not matching with contactId (indicating potential wrong assignment)
        const leadsWithMismatchedContactId = await Lead.find({
            contactId: { $exists: true },
            $expr: { $ne: ["$_id", "$contactId"] }
        }).select('_id contactId phone email companyId').lean();
        
        console.log(`Found ${leadsWithMismatchedContactId.length} leads with mismatched contactId`);
        
        for (const lead of leadsWithMismatchedContactId) {
            console.log(`\n--- Processing Lead ID: ${lead._id} ---`);
            console.log(`Current contactId: ${lead.contactId}`);
            console.log(`Lead phone: ${lead.phone || 'N/A'}`);
            console.log(`Lead email: ${lead.email || 'N/A'}`);
            
            let correctContact = null;
            let matchType = '';
            
            // First try to match by phone if lead has phone
            if (lead.phone) {
                correctContact = await Contact.findOne({
                    phone: lead.phone,
                    companyId: lead.companyId,
                    deleted: { $ne: true }
                }).select('_id phone email fullName').lean();
                
                if (correctContact) {
                    matchType = 'phone';
                    console.log(`Found contact by phone: ${correctContact._id} (${correctContact.fullName})`);
                }
            }
            
            // If no phone match and lead has email, try to match by email
            if (!correctContact && lead.email) {
                correctContact = await Contact.findOne({
                    email: lead.email,
                    companyId: lead.companyId,
                    deleted: { $ne: true }
                }).select('_id phone email fullName').lean();
                
                if (correctContact) {
                    matchType = 'email';
                    console.log(`Found contact by email: ${correctContact._id} (${correctContact.fullName})`);
                }
            }
            
            // Check if the current contactId is already correct
            if (correctContact && correctContact._id === lead.contactId) {
                console.log(`✓ ContactId is already correct for lead ${lead._id}`);
                alreadyCorrectCount++;
                continue;
            }
            
            // If we found a correct contact and it's different from current contactId
            if (correctContact && correctContact._id !== lead.contactId) {
                console.log(`🔄 Need to update contactId from ${lead.contactId} to ${correctContact._id} (matched by ${matchType})`);
                
                // Get the opportunity for this correct contact
                const opp = await Opportunity.findOne({ 
                    contactId: correctContact._id 
                }, { _id: 1 }).lean();
                
                // Get the correct contact's full data for other fields
                const fullCorrectContact = await Contact.findById(correctContact._id).lean();
                
                // Update the lead with correct contactId and related fields
                const updateData = {
                    contactId: correctContact._id,
                    ...(fullCorrectContact?.status && { 
                        status: opp?._id && fullCorrectContact.status === "active" ? "converted" : fullCorrectContact.status 
                    }),
                    ...(fullCorrectContact?.deleted !== undefined && { deleted: fullCorrectContact.deleted }),
                    ...(fullCorrectContact?.csrId && { csrId: fullCorrectContact.csrId }),
                    ...(fullCorrectContact?.stageId && { stageId: fullCorrectContact.stageId }),
                    ...(opp?._id && { oppId: opp._id }),
                    ...(fullCorrectContact?.newLeadDate && { newLeadDate: fullCorrectContact.newLeadDate }),
                };
                
                console.log(`Update data:`, updateData);
                
                if (!DRY_RUN) {
                    await Lead.updateOne(
                        { _id: lead._id },
                        { $set: updateData }
                    );
                    console.log(`✅ Updated lead ${lead._id} with correct contactId ${correctContact._id}`);
                } else {
                    console.log(`✅ Would update lead ${lead._id} with correct contactId ${correctContact._id}`);
                }
                
                fixedCount++;
            } else if (!correctContact) {
                if (!lead.phone && !lead.email) {
                    console.log(`❌ Lead ${lead._id} has no phone or email to match with`);
                    noPhoneNoEmailCount++;
                } else {
                    console.log(`❌ No matching contact found for lead ${lead._id}`);
                    noMatchFoundCount++;
                }
            }
        }
        
        console.log("\n=== SUMMARY ===");
        console.log(`Total leads processed: ${leadsWithMismatchedContactId.length}`);
        console.log(`Leads ${DRY_RUN ? 'that would be' : ''} fixed: ${fixedCount}`);
        console.log(`Leads already correct: ${alreadyCorrectCount}`);
        console.log(`Leads with no phone/email: ${noPhoneNoEmailCount}`);
        console.log(`Leads with no matching contact: ${noMatchFoundCount}`);
        
        if (DRY_RUN) {
            console.log("\n⚠️  THIS WAS A DRY RUN - NO ACTUAL UPDATES WERE MADE");
            console.log("⚠️  SET DRY_RUN = false TO APPLY CHANGES");
        } else {
            console.log("\n✅ UPDATES HAVE BEEN APPLIED TO THE DATABASE");
        }
        
    } catch (error) {
        console.error("Fix incorrect contactId assignments failed:", error);
        throw error;
    }
};

const main = async () => {
    try {
        await connectDB();
        await fixIncorrectContactIdAssignments();
    } catch (error) {
        console.error('Script failed:', error);
    } finally {
        await mongoose.disconnect();
        console.log('Disconnected from MongoDB');
        process.exit(0);
    }
};

// Run the script
main();
