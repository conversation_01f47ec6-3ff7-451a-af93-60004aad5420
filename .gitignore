# compiled output
/dist
/node_modules
/.env*
/.env.stage.local
/.env.stage.dev
.github/workflows/cicd.yml
.github/
/.github
/.github/workflows/cicd.yml
/package-lock.json
/yarn.lock
.yarnrc.yml
.yarn
yarn.lock
# Logs
logs
*.log
npm-debug.log*
pnpm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# OS
.DS_Store

# Tests
/coverage
/.nyc_output

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
.vscode/settings.json

# Clinic
.clinic